# Changelog

All notable changes to the Vibe Check project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Comprehensive project structure cleanup and reorganization
- Modern Python packaging configuration with pyproject.toml
- Enhanced development tooling configuration (ruff, mypy, black, pytest)
- Proper gitignore patterns for development artifacts
- MIT License file
- MANIFEST.in for proper package data inclusion

### Changed
- Consolidated all configuration into pyproject.toml
- Moved legacy and experimental code to legacy/ directory
- Improved project structure following Python packaging best practices
- Enhanced documentation organization
- Updated gitignore to better handle temporary files and outputs

### Removed
- Redundant setup.py file (replaced by pyproject.toml)
- Temporary files, debug outputs, and analysis results from root directory
- Duplicate configuration files

## [1.0.0] - 2023-11-15

### Added
- Initial release of Vibe Check (renamed from PAT Project Analysis Tool)
- CAW (Contextual Adaptive Wave) architecture implementation
- Actor-based analysis system with parallel processing
- Comprehensive code quality analysis tools integration
- Multiple user interfaces (CLI, TUI, Web)
- Extensible plugin system
- Rich reporting and visualization capabilities
- Backward compatibility with the old package name
- Migration guide for users

### Changed
- Renamed package from "pat-project-analysis" to "vibe-check"
- Updated all references to "PAT" to "Vibe Check"
- Updated documentation to reflect the new name and architecture

### Deprecated
- The "pat_project_analysis" package is now deprecated and will be removed in a future version
