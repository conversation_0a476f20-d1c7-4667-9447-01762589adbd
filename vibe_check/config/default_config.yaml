# Vibe Check - Project Analysis Tool - Default Configuration
# =============================================
#
# This file contains the default configuration for the Vibe Check tool.
# You can override these settings by providing your own configuration file.
#
# To use a custom configuration file:
#   vibe-check /path/to/project --config /path/to/config.yaml

# General settings
# ---------------
file_extensions:
  - .py
analyze_docs: false
exclude_patterns:
  - "**/__pycache__/**"
  - "**/venv/**"
  - "**/env/**"
  - "**/.git/**"
  - "**/node_modules/**"
  - "**/.pytest_cache/**"
  - "**/*.pyc"

# Progress bar configuration
# ------------------------
progress_bar:
  enabled: true
  style: rich  # Can be "rich", "simple", or "none"
  show_details: true
  update_interval: 0.1  # seconds

# Tools configuration
# -----------------
tools:
  # Code quality tools
  ruff:
    enabled: true
    args:
      - "--select=E,F,W,I"
      - "--ignore=E501"  # Ignore line length errors

  flake8:
    enabled: false
    args: []

  black:
    enabled: false
    args:
      - "--line-length=100"

  isort:
    enabled: false
    args: []

  # Type checking
  mypy:
    enabled: true
    args:
      - "--ignore-missing-imports"
    check_all_files: false

  pyright:
    enabled: false
    args: []

  # Additional linters for enhanced issue detection
  pylint:
    enabled: false
    args:
      - "--disable=C0111,C0103"  # Disable some common noisy warnings
      - "--enable=E,F,W,R"       # Enable errors, fatals, warnings, refactors

  pyflakes:
    enabled: false
    args: []

  # Security checks
  bandit:
    enabled: true
    args:
      - "--recursive"

  # Documentation analysis
  pydocstyle:
    enabled: false
    args: []

  markdown:
    enabled: true
    args: []

  # Complexity analysis
  complexity:
    enabled: true

  # Custom rules for enhanced issue detection
  custom_rules:
    enabled: false
    args: []

# Reporting configuration
# ---------------------
reporting:
  formats:
    - markdown
    - json
  generate_summary: true
  generate_issue_report: true
  generate_metrics_report: true
  generate_recommendations: true
  generate_prompts: false
  generate_custom_report: false

  # Custom report configuration
  custom_report:
    format: html
    sections:
      summary: true
      issues: true
      metrics: true
      recommendations: true
      complexity: true
      dependencies: true
      documentation: true
      trends: true
    metrics:
      file_count: true
      line_count: true
      complexity: true
      issues: true
      type_coverage: true
      doc_coverage: true
      dependencies: true

# Visualization configuration
# ------------------------
visualization:
  enabled: true
  formats:
    - png
    - html
  generate_dependency_graph: true
  generate_complexity_heatmap: true
  generate_coverage_charts: true
  generate_dashboard: true

# Pre-analysis configuration
# -----------------------
pre_analysis:
  enabled: true
  auto_ignore: true  # Automatically create .vibecheck_ignore file

# Performance configuration
# ----------------------
performance:
  parallel: true
  max_workers: 4  # Number of workers for parallel processing
  timeout: 60  # Seconds
  cache_results: true
  cache_dir: ".vibe_check_cache"
  profiling:
    enabled: false
    output: "vibe_check_profile.prof"
