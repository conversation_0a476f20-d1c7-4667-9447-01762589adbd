# Vibe Check - Comprehensive Analysis Preset
# ==========================================
#
# This preset provides the most thorough analysis possible,
# enabling all available tools and checks.

# General settings
file_extensions:
  - .py
  - .pyx
  - .pyi
  - .ipynb  # Include Jupyter notebooks
analyze_docs: true
exclude_patterns:
  - "**/__pycache__/**"
  - "**/venv/**"
  - "**/env/**"
  - "**/.venv/**"
  - "**/.env/**"
  - "**/.git/**"
  - "**/node_modules/**"
  - "**/.pytest_cache/**"
  - "**/*.pyc"
  - "**/.tox/**"
  - "**/.eggs/**"
  - "**/build/**"
  - "**/dist/**"
  - "**/*.egg-info/**"

# Use .gitignore patterns if available
use_gitignore: true

# Tools configuration
tools:
  # Code quality tools (all checks)
  ruff:
    enabled: true
    args:
      - "--select=ALL"
      - "--ignore=E501"  # Ignore line length errors

  # Type checking (strict)
  mypy:
    enabled: true
    args:
      - "--ignore-missing-imports"
      - "--disallow-untyped-defs"
      - "--disallow-incomplete-defs"
      - "--check-untyped-defs"
      - "--disallow-untyped-decorators"
      - "--no-implicit-optional"
      - "--warn-redundant-casts"
      - "--warn-unused-ignores"
      - "--warn-return-any"
    check_all_files: true

  # Security checks (thorough)
  bandit:
    enabled: true
    args:
      - "--recursive"
      - "--severity-level=low"
      - "--confidence-level=low"

  # Documentation analysis
  pydocstyle:
    enabled: true
    args: []

  markdown:
    enabled: true
    args: []

  # Complexity analysis
  complexity:
    enabled: true
    threshold: 5  # Very strict threshold

# Analysis priorities (balanced)
priorities:
  quality: 0.33
  security: 0.33
  maintainability: 0.34

# Reporting configuration
reporting:
  formats:
    - markdown
    - json
    - html
  generate_summary: true
  generate_issue_report: true
  generate_metrics_report: true
  generate_recommendations: true
  generate_prompts: false

# Visualization configuration
visualization:
  enabled: true
  formats:
    - png
    - html
  generate_dependency_graph: true
  generate_complexity_heatmap: true
  generate_coverage_charts: true
  generate_dashboard: true

# Performance configuration
performance:
  parallel: true
  max_workers: 4
  timeout: 300  # Long timeout for thorough analysis
  cache_results: true
