# Vibe Check - Quick Analysis Preset
# ===================================
#
# This preset is optimized for speed, providing a quick overview
# of the codebase with minimal resource usage.

# General settings
file_extensions:
  - .py
analyze_docs: false
exclude_patterns:
  - "**/__pycache__/**"
  - "**/venv/**"
  - "**/env/**"
  - "**/.venv/**"
  - "**/.env/**"
  - "**/.git/**"
  - "**/node_modules/**"
  - "**/.pytest_cache/**"
  - "**/*.pyc"
  - "**/.tox/**"
  - "**/.eggs/**"
  - "**/build/**"
  - "**/dist/**"
  - "**/*.egg-info/**"
  - "**/tests/**"  # Skip tests for faster analysis

# Use .gitignore patterns if available
use_gitignore: true

# Tools configuration
tools:
  # Fast linting only
  ruff:
    enabled: true
    args:
      - "--select=E,F,W"  # Basic error checks only
      - "--ignore=E501"  # Ignore line length errors

  # Skip type checking for speed
  mypy:
    enabled: false

  # Basic security checks
  bandit:
    enabled: true
    args:
      - "--recursive"
      - "--severity-level=high"  # Only high severity issues

  # Basic complexity analysis
  complexity:
    enabled: true
    threshold: 15  # Higher threshold to focus on the worst offenders

# Reporting configuration
reporting:
  formats:
    - text
  generate_summary: true
  generate_issue_report: true
  generate_metrics_report: false
  generate_recommendations: false
  generate_prompts: false

# Performance configuration
performance:
  parallel: true
  max_workers: 8  # More workers for faster processing
  timeout: 30  # Short timeout
  cache_results: true
