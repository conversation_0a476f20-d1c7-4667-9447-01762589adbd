# Default configuration preset for Vibe Check
# This preset provides a balanced analysis with moderate settings

file_extensions:
  - .py
  - .pyx
  - .pyi

exclude_patterns:
  - "**/venv/**"
  - "**/.git/**"
  - "**/__pycache__/**"
  - "**/node_modules/**"
  - "**/build/**"
  - "**/dist/**"

analyze_docs: true
max_workers: 4
output_format: "html"
report_verbosity: "standard"

# Tool configuration
tools:
  # Code quality tools
  ruff:
    enabled: true
    args:
      - "--select=E,F,W,I,C"
      - "--ignore=E501"  # Ignore line length errors

  # Type checking
  mypy:
    enabled: true
    args:
      - "--ignore-missing-imports"

  # Security checks
  bandit:
    enabled: true
    args:
      - "--recursive"
      - "--severity-level=medium"

  # Documentation analysis
  pydocstyle:
    enabled: true
    args: []

  # Complexity analysis
  complexity:
    enabled: true
    threshold: 10

# Reporting configuration
reporting:
  generate_summary: true
  generate_issues: true
  generate_metrics: true
  generate_recommendations: true
  generate_prompts: false  # For future implementation
