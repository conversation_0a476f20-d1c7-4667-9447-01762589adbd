# Vibe Check - Enhanced Analysis Preset
# =====================================
#
# This preset provides enhanced issue detection with additional linters
# and static analysis tools.

# General settings
file_extensions:
  - .py
  - .pyx
  - .pyi
analyze_docs: true
exclude_patterns:
  - "**/__pycache__/**"
  - "**/venv/**"
  - "**/env/**"
  - "**/.venv/**"
  - "**/.env/**"
  - "**/.git/**"
  - "**/node_modules/**"
  - "**/.pytest_cache/**"
  - "**/*.pyc"
  - "**/.tox/**"
  - "**/.eggs/**"
  - "**/build/**"
  - "**/dist/**"
  - "**/*.egg-info/**"

# Use .gitignore patterns if available
use_gitignore: true

# Tools configuration
tools:
  # Primary linter (ruff)
  ruff:
    enabled: true
    args:
      - "--select=E,F,W,I,C,N,B,A,COM,D,UP,S,BLE,FBT,T20,PYI,PT,RET,SIM,ARG,PTH,ERA,PL,TRY,FLY,PERF"
      - "--ignore=E501"  # Ignore line length errors

  # Additional linters for enhanced issue detection
  pylint:
    enabled: true
    args:
      - "--disable=C0111,C0103"  # Disable some common noisy warnings
      - "--enable=E,F,W,R"       # Enable errors, fatals, warnings, refactors

  pyflakes:
    enabled: true
    args: []

  # Type checking
  mypy:
    enabled: true
    args:
      - "--ignore-missing-imports"
      - "--disallow-untyped-defs"
      - "--disallow-incomplete-defs"
      - "--check-untyped-defs"
    check_all_files: true

  # Security checks
  bandit:
    enabled: true
    args:
      - "--recursive"
      - "--severity-level=low"
      - "--confidence-level=low"

  # Complexity analysis
  complexity:
    enabled: true
    threshold: 8  # Lower threshold for better quality

  # Custom rules for enhanced issue detection
  custom_rules:
    enabled: true
    args: []

# Analysis priorities
priorities:
  quality: 0.5
  security: 0.3
  maintainability: 0.2

# Reporting configuration
reporting:
  formats:
    - markdown
    - json
    - html
  generate_summary: true
  generate_issue_report: true
  generate_metrics_report: true
  generate_recommendations: true
  generate_prompts: false
  generate_custom_report: true

  # Custom report configuration
  custom_report:
    format: html
    sections:
      summary: true
      issues: true
      metrics: true
      recommendations: true
      complexity: true
      dependencies: true
      documentation: true
      trends: true
    metrics:
      file_count: true
      line_count: true
      complexity: true
      issues: true
      type_coverage: true
      doc_coverage: true
      dependencies: true

# Visualization configuration
visualization:
  enabled: true
  formats:
    - png
    - html
  generate_dependency_graph: true
  generate_complexity_heatmap: true
  generate_coverage_charts: true
  generate_dashboard: true

# Performance configuration
performance:
  parallel: true
  max_workers: 4
  timeout: 120  # Longer timeout for thorough analysis
  cache_results: true
