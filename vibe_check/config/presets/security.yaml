# Vibe Check - Security Analysis Preset
# =====================================
#
# This preset focuses on security analysis, prioritizing security checks
# and vulnerability detection over other aspects.

# General settings
file_extensions:
  - .py
  - .pyx
  - .pyi
analyze_docs: false
exclude_patterns:
  - "**/__pycache__/**"
  - "**/venv/**"
  - "**/env/**"
  - "**/.venv/**"
  - "**/.env/**"
  - "**/.git/**"
  - "**/node_modules/**"
  - "**/.pytest_cache/**"
  - "**/*.pyc"
  - "**/.tox/**"
  - "**/.eggs/**"
  - "**/build/**"
  - "**/dist/**"
  - "**/*.egg-info/**"
  - "**/tests/**"  # Often exclude tests for security analysis

# Use .gitignore patterns if available
use_gitignore: true

# Tools configuration
tools:
  # Security checks (enhanced configuration)
  bandit:
    enabled: true
    args:
      - "--recursive"
      - "--severity-level=low"  # Catch all severity levels
      - "--confidence-level=low"  # Include even low-confidence findings

  # Code quality tools (security-focused)
  ruff:
    enabled: true
    args:
      - "--select=E,F,W,S"  # Include security (S) checks
      - "--ignore=E501"  # Ignore line length errors

  # Type checking (helps prevent certain security issues)
  mypy:
    enabled: true
    args:
      - "--ignore-missing-imports"
      - "--disallow-untyped-defs"  # Stricter type checking
    check_all_files: true

  # Complexity analysis (complex code often hides security issues)
  complexity:
    enabled: true
    threshold: 8  # Lower threshold for security concerns

# Analysis priorities
priorities:
  security: 0.8
  quality: 0.1
  maintainability: 0.1

# Reporting configuration
reporting:
  formats:
    - markdown
    - json
  generate_summary: true
  generate_issue_report: true
  generate_metrics_report: true
  generate_recommendations: true
  generate_prompts: false

# Performance configuration
performance:
  parallel: true
  max_workers: 4
  timeout: 120  # Longer timeout for thorough security checks
  cache_results: true
