# Minimal configuration preset for Vibe Check
# This preset provides a quick analysis with minimal settings

file_extensions:
  - .py

exclude_patterns:
  - "**/venv/**"
  - "**/.git/**"
  - "**/__pycache__/**"
  - "**/tests/**"
  - "**/test/**"

analyze_docs: false
max_workers: 2
output_format: "text"
report_verbosity: "minimal"

# Tool configuration
tools:
  # Code quality tools
  ruff:
    enabled: true
    args:
      - "--select=E,F"  # Only check for errors and fatal errors

  # Type checking
  mypy:
    enabled: false

  # Security checks
  bandit:
    enabled: false

  # Documentation analysis
  pydocstyle:
    enabled: false

  # Complexity analysis
  complexity:
    enabled: true
    threshold: 15  # Higher threshold to only flag very complex code

# Reporting configuration
reporting:
  generate_summary: true
  generate_issues: true
  generate_metrics: false
  generate_recommendations: false
  generate_prompts: false  # For future implementation
