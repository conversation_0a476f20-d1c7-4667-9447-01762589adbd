# Vibe Check - Standard Analysis Preset
# =====================================
#
# This preset provides a balanced analysis focusing on code quality,
# maintainability, and basic security checks.

# General settings
file_extensions:
  - .py
  - .pyx
  - .pyi
analyze_docs: true
exclude_patterns:
  - "**/__pycache__/**"
  - "**/venv/**"
  - "**/env/**"
  - "**/.venv/**"
  - "**/.env/**"
  - "**/.git/**"
  - "**/node_modules/**"
  - "**/.pytest_cache/**"
  - "**/*.pyc"
  - "**/.tox/**"
  - "**/.eggs/**"
  - "**/build/**"
  - "**/dist/**"
  - "**/*.egg-info/**"

# Use .gitignore patterns if available
use_gitignore: true

# Tools configuration
tools:
  # Code quality tools
  ruff:
    enabled: true
    args:
      - "--select=E,F,W,I"
      - "--ignore=E501"  # Ignore line length errors

  # Type checking
  mypy:
    enabled: true
    args:
      - "--ignore-missing-imports"
    check_all_files: false

  # Security checks
  bandit:
    enabled: true
    args:
      - "--recursive"

  # Complexity analysis
  complexity:
    enabled: true
    threshold: 10

# Reporting configuration
reporting:
  formats:
    - markdown
    - json
  generate_summary: true
  generate_issue_report: true
  generate_metrics_report: true
  generate_recommendations: true
  generate_prompts: false

# Performance configuration
performance:
  parallel: true
  max_workers: 4
  timeout: 60
  cache_results: true
