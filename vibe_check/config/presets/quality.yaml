# Vibe Check - Code Quality Analysis Preset
# ========================================
#
# This preset focuses on code quality and maintainability,
# with comprehensive linting and style checks.

# General settings
file_extensions:
  - .py
  - .pyx
  - .pyi
analyze_docs: true
exclude_patterns:
  - "**/__pycache__/**"
  - "**/venv/**"
  - "**/env/**"
  - "**/.venv/**"
  - "**/.env/**"
  - "**/.git/**"
  - "**/node_modules/**"
  - "**/.pytest_cache/**"
  - "**/*.pyc"
  - "**/.tox/**"
  - "**/.eggs/**"
  - "**/build/**"
  - "**/dist/**"
  - "**/*.egg-info/**"

# Use .gitignore patterns if available
use_gitignore: true

# Tools configuration
tools:
  # Code quality tools (comprehensive configuration)
  ruff:
    enabled: true
    args:
      - "--select=E,F,W,I,C,N,B,A,COM,D,UP,S,BLE,FBT,T20,PYI,PT,RET,SIM,ARG,PTH,ERA,PL,TRY,FLY,PERF"
      - "--ignore=E501"  # Ignore line length errors

  # Type checking
  mypy:
    enabled: true
    args:
      - "--ignore-missing-imports"
      - "--disallow-untyped-defs"
      - "--disallow-incomplete-defs"
      - "--check-untyped-defs"
    check_all_files: true

  # Documentation analysis
  pydocstyle:
    enabled: true
    args: []

  markdown:
    enabled: true
    args: []

  # Complexity analysis
  complexity:
    enabled: true
    threshold: 7  # Lower threshold for better quality

  # Security checks (basic)
  bandit:
    enabled: true
    args:
      - "--recursive"

# Analysis priorities
priorities:
  quality: 0.7
  maintainability: 0.2
  security: 0.1

# Reporting configuration
reporting:
  formats:
    - markdown
    - json
    - html
  generate_summary: true
  generate_issue_report: true
  generate_metrics_report: true
  generate_recommendations: true
  generate_prompts: false

# Performance configuration
performance:
  parallel: true
  max_workers: 4
  timeout: 90
  cache_results: true
