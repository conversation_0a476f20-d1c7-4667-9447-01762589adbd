"""
CLI Output Formatters
==================

This module provides functions for formatting analysis results for CLI output.
It handles the presentation layer of the CLI, converting raw data structures
into human-readable text output.
"""

import os
from pathlib import Path
from typing import Dict, Any, List, Tuple

from vibe_check.core.models import ProjectMetrics


def format_results(metrics: ProjectMetrics) -> str:
    """
    Format full analysis results for CLI output.

    Args:
        metrics: ProjectMetrics object with analysis results

    Returns:
        Formatted string representation
    """
    lines = [
        "=== VIBE CHECK PROJECT ANALYSIS RESULTS ===",
        ""
    ]

    # Add summary information
    lines.extend(_format_summary_section(metrics))

    # Add complexity information
    lines.extend(_format_complexity_section(metrics))

    # Add coverage information
    lines.extend(_format_coverage_section(metrics))

    # Add dependency information
    lines.extend(_format_dependency_section(metrics))

    return '\n'.join(lines)


def _format_summary_section(metrics: ProjectMetrics) -> List[str]:
    """Format the summary section of the results."""
    return [
        "PROJECT SUMMARY",
        "---------------",
        f"Total Files: {metrics.total_file_count}",
        f"Python Files: {metrics.python_file_count}",
        f"Markdown Files: {metrics.markdown_file_count}",
        f"Total Directories: {metrics.total_directory_count}",
        ""
    ]


def _format_complexity_section(metrics: ProjectMetrics) -> List[str]:
    """Format the complexity section of the results."""
    lines = [
        "COMPLEXITY METRICS",
        "-----------------",
        f"Average Complexity: {metrics.avg_complexity:.2f}",
        "Most Complex Files:"
    ]

    # Add top 5 complex files
    complex_files = sorted(
        metrics.complexity_scores.items(),
        key=lambda x: x[1],
        reverse=True
    )[:5]

    for path, score in complex_files:
        lines.append(f"  - {path} (complexity: {score})")

    lines.append("")
    return lines


def _format_coverage_section(metrics: ProjectMetrics) -> List[str]:
    """Format the coverage section of the results."""
    return [
        "COVERAGE METRICS",
        "---------------",
        f"Average Documentation Coverage: {metrics.avg_doc_coverage:.2f}%",
        f"Average Type Coverage: {metrics.avg_type_coverage:.2f}%",
        ""
    ]


def _format_dependency_section(metrics: ProjectMetrics) -> List[str]:
    """Format the dependency section of the results."""
    lines = [
        "DEPENDENCY METRICS",
        "-----------------"
    ]

    # Add circular dependencies
    circular_deps = metrics.get_circular_dependencies()
    if circular_deps:
        lines.append(f"Circular Dependencies: {len(circular_deps)} found")
        for i, cycle in enumerate(circular_deps[:3], 1):
            lines.append(f"  {i}. {' -> '.join(cycle)} -> {cycle[0]}")
        if len(circular_deps) > 3:
            lines.append(f"  ... and {len(circular_deps) - 3} more")
    else:
        lines.append("Circular Dependencies: None found")

    lines.append("")

    # Add most imported files
    most_imported = metrics.get_most_imported_files(5)
    if most_imported:
        lines.append("Most Imported Files:")
        for file_path in most_imported:
            lines.append(f"  - {file_path}")

    lines.append("")
    return lines


def format_summary(metrics: ProjectMetrics) -> str:
    """
    Format a brief summary of analysis results for CLI output.

    This is a shorter version of format_results that only includes
    key metrics for quick reviews.

    Args:
        metrics: ProjectMetrics object with analysis results

    Returns:
        Formatted string representation
    """
    lines = [
        "=== VIBE CHECK ANALYSIS SUMMARY ===",
        "",
        f"Total Files Analyzed: {metrics.total_file_count}",
        f"Python Files: {metrics.python_file_count}",
        f"Documentation Files: {metrics.markdown_file_count}",
        f"Average Complexity: {metrics.avg_complexity:.2f}",
        f"Average Documentation Coverage: {metrics.avg_doc_coverage:.2f}%",
        f"Average Type Coverage: {metrics.avg_type_coverage:.2f}%",
    ]

    # Add circular dependency info
    circular_deps = metrics.get_circular_dependencies()
    if circular_deps:
        lines.append(f"Circular Dependencies: {len(circular_deps)} found")
    else:
        lines.append("Circular Dependencies: None found")

    return '\n'.join(lines)


def format_table(headers: List[str], rows: List[List[str]], title: str = None) -> str:
    """
    Format a table for CLI output.

    Args:
        headers: List of column headers
        rows: List of rows, where each row is a list of column values
        title: Optional table title

    Returns:
        Formatted table as a string
    """
    # Calculate column widths
    col_widths = [len(h) for h in headers]
    for row in rows:
        for i, cell in enumerate(row):
            col_widths[i] = max(col_widths[i], len(str(cell)))

    # Add padding
    col_widths = [w + 2 for w in col_widths]

    # Calculate table width
    table_width = sum(col_widths) + len(headers) - 1

    lines = []

    # Add title if provided
    if title:
        lines.append(title)
        lines.append('=' * len(title))
        lines.append('')

    # Add header row
    header_row = ''.join(h.ljust(w) for h, w in zip(headers, col_widths))
    lines.append(header_row)
    lines.append('-' * table_width)

    # Add data rows
    for row in rows:
        row_str = ''.join(str(cell).ljust(w) for cell, w in zip(row, col_widths))
        lines.append(row_str)

    return '\n'.join(lines)


def format_analysis_results(metrics: ProjectMetrics, format_type: str = "text") -> str:
    """
    Format analysis results for output.

    Args:
        metrics: ProjectMetrics object with analysis results
        format_type: Output format type (text, json, html)

    Returns:
        Formatted analysis results as a string
    """
    if format_type == "json":
        import json
        return json.dumps(metrics.to_dict(), indent=2)
    elif format_type == "html":
        # Simple HTML format for now
        html_lines = [
            "<html>",
            "<head><title>Vibe Check Analysis Results</title></head>",
            "<body>",
            "<h1>Vibe Check Analysis Results</h1>",
            "<h2>Project Summary</h2>",
            f"<p>Total Files: {metrics.total_file_count}</p>",
            f"<p>Python Files: {metrics.python_file_count}</p>",
            f"<p>Markdown Files: {metrics.markdown_file_count}</p>",
            f"<p>Total Directories: {metrics.total_directory_count}</p>",
            "<h2>Complexity Metrics</h2>",
            f"<p>Average Complexity: {metrics.avg_complexity:.2f}</p>",
            "<h3>Most Complex Files:</h3>",
            "<ul>"
        ]

        # Add top 5 complex files
        complex_files = sorted(
            metrics.complexity_scores.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]

        for path, score in complex_files:
            html_lines.append(f"<li>{path} (complexity: {score})</li>")

        html_lines.extend([
            "</ul>",
            "<h2>Coverage Metrics</h2>",
            f"<p>Average Documentation Coverage: {metrics.avg_doc_coverage:.2f}%</p>",
            f"<p>Average Type Coverage: {metrics.avg_type_coverage:.2f}%</p>",
            "</body>",
            "</html>"
        ])

        return "\n".join(html_lines)
    else:
        # Default to text format
        return format_results(metrics)
