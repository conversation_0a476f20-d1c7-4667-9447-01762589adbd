"""
CLI Command Handlers
=================

This module provides command handlers for CLI operations, implementing a clean
separation between parsing arguments and executing functionality.
"""

import logging
import os
from pathlib import Path
from typing import Dict, Any, Optional, Union

from vibe_check.core import analyze_project
from vibe_check.core.models import ProjectMetrics

logger = logging.getLogger("vibe_check_cli")


def execute_analysis(
    project_path: Union[str, Path],
    output_dir: Optional[Union[str, Path]] = None,
    config_path: Optional[Union[str, Path]] = None,
    config_override: Optional[Dict[str, Any]] = None,
    show_progress: bool = True
) -> ProjectMetrics:
    """
    Execute project analysis with the specified parameters.

    This is a thin wrapper around the core analyze_project function that
    provides progress feedback and additional logging specifically for CLI use.

    Args:
        project_path: Path to the project to analyze
        output_dir: Directory to store output files
        config_path: Path to a YAML configuration file
        config_override: Dictionary to override configuration
        show_progress: Whether to show progress information

    Returns:
        ProjectMetrics object with analysis results
    """
    logger.info(f"Starting analysis of {project_path}")
    logger.info(f"Output directory: {output_dir or 'default'}")
    logger.info(f"Config path: {config_path or 'default'}")

    try:
        # Execute the analysis
        metrics = analyze_project(
            project_path=project_path,
            output_dir=output_dir,
            config_path=config_path,
            config_override=config_override,
            show_progress=show_progress
        )

        # Get output directory path (may be automatically determined in analyze_project)
        if output_dir is None:
            output_dir = Path(project_path) / "vibe_check_output" / "latest"
        else:
            output_dir = Path(output_dir) / "latest"

        # Log success messages
        logger.info(f"Analysis complete. Results saved to {output_dir}")
        logger.info(f"Total files analyzed: {metrics.total_file_count}")

        return metrics

    except Exception as e:
        logger.error(f"Error during analysis: {e}", exc_info=True)
        raise


def execute_command(command: str, args: Dict[str, Any]) -> Any:
    """
    Execute a CLI command.

    This function dispatches to the appropriate handler based on the command.

    Args:
        command: Command to execute (e.g., "analyze", "list-tools")
        args: Command arguments

    Returns:
        Command result
    """
    if command == "analyze":
        return execute_analysis(
            project_path=args["project_path"],
            output_dir=args.get("output_dir"),
            config_path=args.get("config"),
            config_override={k: v for k, v in args.items()
                            if k not in ("project_path", "output_dir", "config")},
            show_progress=not args.get("quiet", False)
        )
    elif command == "list-tools":
        return list_available_tools()
    elif command == "version":
        from vibe_check.core import __version__
        return {"version": __version__}
    else:
        raise ValueError(f"Unknown command: {command}")


def list_available_tools() -> Dict[str, Any]:
    """
    List all available analysis tools.

    Returns:
        Dictionary with tool information
    """
    # In a real implementation, this would dynamically discover
    # available tools. For now, return a hardcoded list.
    return {
        "tools": [
            {
                "name": "ruff",
                "description": "Fast Python linter",
                "enabled_by_default": True
            },
            {
                "name": "mypy",
                "description": "Static type checker",
                "enabled_by_default": True
            },
            {
                "name": "bandit",
                "description": "Security linter",
                "enabled_by_default": True
            },
            {
                "name": "complexity",
                "description": "Code complexity analyzer",
                "enabled_by_default": True
            },
            {
                "name": "markdown",
                "description": "Markdown documentation analyzer",
                "enabled_by_default": False
            }
        ]
    }
