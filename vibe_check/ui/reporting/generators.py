"""
Report Generators
=============

This module provides functions for generating reports.
"""

import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from ...core.models.project_metrics import ProjectMetrics
from .formatters import format_analysis_results
from .markdown import generate_markdown_report
from .templates import render_template


def generate_report(metrics: ProjectMetrics,
                   output_dir: Union[str, Path],
                   format_type: str = "markdown",
                   template_name: Optional[str] = None) -> str:
    """
    Generate a report from analysis results.
    
    Args:
        metrics: Project metrics
        output_dir: Output directory
        format_type: Format type (markdown, html, text)
        template_name: Optional template name
        
    Returns:
        Path to the generated report
    """
    # Create the output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate the report content
    if format_type == "markdown":
        content = generate_markdown_report(metrics, template_name)
        extension = "md"
    elif format_type == "html":
        content = render_template(template_name or "report.html", {"metrics": metrics})
        extension = "html"
    else:  # text
        content = format_analysis_results(metrics, "text")
        extension = "txt"
    
    # Generate the report file name
    project_name = Path(metrics.project_path).name
    report_path = os.path.join(output_dir, f"{project_name}_report.{extension}")
    
    # Write the report to a file
    with open(report_path, "w", encoding="utf-8") as f:
        f.write(content)
    
    return report_path


def generate_summary(metrics: ProjectMetrics, format_type: str = "text") -> str:
    """
    Generate a summary of analysis results.
    
    Args:
        metrics: Project metrics
        format_type: Format type (text, markdown, html)
        
    Returns:
        Summary of analysis results
    """
    if format_type == "markdown":
        return f"""
# Analysis Summary

- **Project**: {Path(metrics.project_path).name}
- **Files**: {metrics.total_file_count}
- **Lines**: {metrics.total_line_count}
- **Issues**: {metrics.issue_count}
- **Average Complexity**: {metrics.avg_complexity:.2f}

## Top Issues

{_generate_top_issues(metrics, format_type)}
"""
    elif format_type == "html":
        return f"""
<h1>Analysis Summary</h1>

<ul>
  <li><strong>Project</strong>: {Path(metrics.project_path).name}</li>
  <li><strong>Files</strong>: {metrics.total_file_count}</li>
  <li><strong>Lines</strong>: {metrics.total_line_count}</li>
  <li><strong>Issues</strong>: {metrics.issue_count}</li>
  <li><strong>Average Complexity</strong>: {metrics.avg_complexity:.2f}</li>
</ul>

<h2>Top Issues</h2>

{_generate_top_issues(metrics, format_type)}
"""
    else:  # text
        return f"""
Analysis Summary
===============

Project: {Path(metrics.project_path).name}
Files: {metrics.total_file_count}
Lines: {metrics.total_line_count}
Issues: {metrics.issue_count}
Average Complexity: {metrics.avg_complexity:.2f}

Top Issues
---------

{_generate_top_issues(metrics, format_type)}
"""


def _generate_top_issues(metrics: ProjectMetrics, format_type: str = "text") -> str:
    """
    Generate a list of top issues.
    
    Args:
        metrics: Project metrics
        format_type: Format type (text, markdown, html)
        
    Returns:
        List of top issues
    """
    # Collect all issues
    all_issues = []
    for file_path, file_metrics in metrics.file_metrics.items():
        for issue in file_metrics.issues:
            issue_copy = issue.copy()
            issue_copy["file_path"] = file_path
            all_issues.append(issue_copy)
    
    # Sort issues by severity
    severity_order = {"critical": 0, "high": 1, "medium": 2, "low": 3, "info": 4}
    all_issues.sort(key=lambda x: severity_order.get(x.get("severity", "").lower(), 5))
    
    # Take the top 5 issues
    top_issues = all_issues[:5]
    
    # Format the issues
    if format_type == "markdown":
        if not top_issues:
            return "No issues found."
        
        result = ""
        for issue in top_issues:
            file_path = issue.get("file_path", "")
            code = issue.get("code", "")
            message = issue.get("message", "")
            line = issue.get("line", 0)
            severity = issue.get("severity", "")
            
            result += f"- **{severity.upper()}**: {code} in {file_path}:{line} - {message}\n"
        
        return result
    
    elif format_type == "html":
        if not top_issues:
            return "<p>No issues found.</p>"
        
        result = "<ul>\n"
        for issue in top_issues:
            file_path = issue.get("file_path", "")
            code = issue.get("code", "")
            message = issue.get("message", "")
            line = issue.get("line", 0)
            severity = issue.get("severity", "")
            
            result += f"<li><strong>{severity.upper()}</strong>: {code} in {file_path}:{line} - {message}</li>\n"
        
        result += "</ul>"
        return result
    
    else:  # text
        if not top_issues:
            return "No issues found."
        
        result = ""
        for issue in top_issues:
            file_path = issue.get("file_path", "")
            code = issue.get("code", "")
            message = issue.get("message", "")
            line = issue.get("line", 0)
            severity = issue.get("severity", "")
            
            result += f"- {severity.upper()}: {code} in {file_path}:{line} - {message}\n"
        
        return result
