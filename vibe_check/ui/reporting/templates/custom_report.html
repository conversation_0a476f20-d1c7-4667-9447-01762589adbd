<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Vibe Check Custom Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            text-align: left;
            padding: 12px;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .metric {
            display: inline-block;
            margin: 10px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f8f9fa;
            min-width: 200px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
        }
        .metric-label {
            font-size: 14px;
            color: #7f8c8d;
        }
        .metrics-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
        }
        .trend-positive {
            color: #27ae60;
        }
        .trend-negative {
            color: #e74c3c;
        }
        .trend-neutral {
            color: #f39c12;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 5px;
            background-color: #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .timestamp {
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>Vibe Check Analysis Report</h1>
    <p class="timestamp">Generated on: {{ now().strftime('%Y-%m-%d %H:%M:%S') }}</p>
    <p>Project: {{ metrics.project_path }}</p>

    {% if include_sections.get('summary', True) %}
    <div class="section">
        <h2>Summary</h2>
        <div class="metrics-container">
            {% if include_metrics.get('file_count', True) %}
            <div class="metric">
                <div class="metric-value">{{ metrics.total_file_count }}</div>
                <div class="metric-label">Files</div>
            </div>
            {% endif %}
            
            {% if include_metrics.get('line_count', True) %}
            <div class="metric">
                <div class="metric-value">{{ metrics.total_line_count }}</div>
                <div class="metric-label">Lines of Code</div>
            </div>
            {% endif %}
            
            {% if include_metrics.get('complexity', True) %}
            <div class="metric">
                <div class="metric-value">{{ "%.2f"|format(metrics.avg_complexity) }}</div>
                <div class="metric-label">Avg Complexity</div>
            </div>
            {% endif %}
            
            {% if include_metrics.get('issues', True) %}
            <div class="metric">
                <div class="metric-value">{{ metrics.issue_count }}</div>
                <div class="metric-label">Issues</div>
            </div>
            {% endif %}
            
            {% if include_metrics.get('type_coverage', True) %}
            <div class="metric">
                <div class="metric-value">{{ "%.1f"|format(metrics.avg_type_coverage) }}%</div>
                <div class="metric-label">Type Coverage</div>
            </div>
            {% endif %}
            
            {% if include_metrics.get('doc_coverage', True) %}
            <div class="metric">
                <div class="metric-value">{{ "%.1f"|format(metrics.avg_doc_coverage) }}%</div>
                <div class="metric-label">Doc Coverage</div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    {% if include_sections.get('issues', True) and include_metrics.get('issues', True) %}
    <div class="section">
        <h2>Issues</h2>
        
        <h3>Issues by Severity</h3>
        <table>
            <tr>
                <th>Severity</th>
                <th>Count</th>
            </tr>
            {% for severity, count in metrics.issues_by_severity.items() %}
            <tr>
                <td>{{ severity }}</td>
                <td>{{ count }}</td>
            </tr>
            {% endfor %}
        </table>
        
        <h3>Issues by Tool</h3>
        <table>
            <tr>
                <th>Tool</th>
                <th>Count</th>
            </tr>
            {% for tool, count in metrics._issues_by_tool.items() %}
            <tr>
                <td>{{ tool }}</td>
                <td>{{ count }}</td>
            </tr>
            {% endfor %}
        </table>
    </div>
    {% endif %}

    {% if include_sections.get('complexity', True) and include_metrics.get('complexity', True) %}
    <div class="section">
        <h2>Complexity</h2>
        
        <h3>Most Complex Files</h3>
        <table>
            <tr>
                <th>File</th>
                <th>Complexity</th>
            </tr>
            {% for file_path in metrics.highest_complexity_files %}
            <tr>
                <td>{{ file_path }}</td>
                <td>{{ metrics.complexity_scores[file_path] }}</td>
            </tr>
            {% endfor %}
        </table>
    </div>
    {% endif %}

    {% if include_sections.get('dependencies', True) and include_metrics.get('dependencies', True) %}
    <div class="section">
        <h2>Dependencies</h2>
        
        {% set circular_deps = metrics.get_circular_dependencies() %}
        {% if circular_deps %}
        <h3>Circular Dependencies</h3>
        <ul>
            {% for cycle in circular_deps %}
            <li>{{ " → ".join(cycle) }} → {{ cycle[0] }}</li>
            {% endfor %}
        </ul>
        {% else %}
        <p>No circular dependencies found.</p>
        {% endif %}
        
        <h3>Most Imported Files</h3>
        <ul>
            {% for file_path in metrics.get_most_imported_files() %}
            <li>{{ file_path }}</li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}

    {% if include_sections.get('documentation', True) %}
    <div class="section">
        <h2>Documentation</h2>
        
        <div class="metrics-container">
            {% if include_metrics.get('doc_coverage', True) %}
            <div class="metric">
                <div class="metric-value">{{ "%.1f"|format(metrics.avg_doc_coverage) }}%</div>
                <div class="metric-label">Docstring Coverage</div>
            </div>
            {% endif %}
            
            {% if include_metrics.get('type_coverage', True) %}
            <div class="metric">
                <div class="metric-value">{{ "%.1f"|format(metrics.avg_type_coverage) }}%</div>
                <div class="metric-label">Type Hint Coverage</div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    {% if include_sections.get('trends', True) and metrics.trend_results %}
    <div class="section">
        <h2>Trends</h2>
        
        {% if metrics.trend_results.get('has_historical_data', False) %}
            <p>{{ metrics.trend_results.get('summary', 'No trend summary available.') }}</p>
            
            {% if metrics.trend_results.get('changes') %}
            <h3>Metric Changes</h3>
            <table>
                <tr>
                    <th>Metric</th>
                    <th>Current</th>
                    <th>Previous</th>
                    <th>Change</th>
                </tr>
                {% for metric, change in metrics.trend_results.changes.items() %}
                <tr>
                    <td>{{ metric }}</td>
                    <td>{{ "%.2f"|format(change.current) if change.current is float else change.current }}</td>
                    <td>{{ "%.2f"|format(change.previous) if change.previous is float else change.previous }}</td>
                    <td class="trend-{{ change.trend }}">
                        {% if change.absolute_change > 0 %}+{% endif %}{{ "%.2f"|format(change.absolute_change) if change.absolute_change is float else change.absolute_change }}
                        ({{ "%.1f"|format(change.percentage_change) }}%)
                    </td>
                </tr>
                {% endfor %}
            </table>
            {% endif %}
            
            {% if metrics.trend_results.get('severity_changes') %}
            <h3>Issue Severity Changes</h3>
            <table>
                <tr>
                    <th>Severity</th>
                    <th>Current</th>
                    <th>Previous</th>
                    <th>Change</th>
                </tr>
                {% for severity, change in metrics.trend_results.severity_changes.items() %}
                <tr>
                    <td>{{ severity }}</td>
                    <td>{{ change.current }}</td>
                    <td>{{ change.previous }}</td>
                    <td class="trend-{{ change.trend }}">
                        {% if change.absolute_change > 0 %}+{% endif %}{{ change.absolute_change }}
                        ({{ "%.1f"|format(change.percentage_change) }}%)
                    </td>
                </tr>
                {% endfor %}
            </table>
            {% endif %}
            
            {% if metrics.trend_visualizations %}
            <h3>Trend Visualizations</h3>
            <ul>
                {% for chart_name, chart_path in metrics.trend_visualizations.items() %}
                <li><a href="{{ chart_path }}" target="_blank">{{ chart_name }}</a></li>
                {% endfor %}
            </ul>
            {% endif %}
        {% else %}
            <p>{{ metrics.trend_results.get('message', 'No historical data available for trend analysis.') }}</p>
        {% endif %}
    </div>
    {% endif %}
</body>
</html>
