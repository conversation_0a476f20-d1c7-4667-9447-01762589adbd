# Vibe Check Analysis Report

**Project:** {{ metrics.project_path }}  
**Generated on:** {{ now().strftime('%Y-%m-%d %H:%M:%S') }}

{% if include_sections.get('summary', True) %}
## Summary

{% if include_metrics.get('file_count', True) %}
- **Files:** {{ metrics.total_file_count }}
{% endif %}
{% if include_metrics.get('line_count', True) %}
- **Lines of Code:** {{ metrics.total_line_count }}
{% endif %}
{% if include_metrics.get('complexity', True) %}
- **Average Complexity:** {{ "%.2f"|format(metrics.avg_complexity) }}
{% endif %}
{% if include_metrics.get('issues', True) %}
- **Issues:** {{ metrics.issue_count }}
{% endif %}
{% if include_metrics.get('type_coverage', True) %}
- **Type Coverage:** {{ "%.1f"|format(metrics.avg_type_coverage) }}%
{% endif %}
{% if include_metrics.get('doc_coverage', True) %}
- **Documentation Coverage:** {{ "%.1f"|format(metrics.avg_doc_coverage) }}%
{% endif %}
{% endif %}

{% if include_sections.get('issues', True) and include_metrics.get('issues', True) %}
## Issues

### Issues by Severity

| Severity | Count |
|----------|-------|
{% for severity, count in metrics.issues_by_severity.items() %}
| {{ severity }} | {{ count }} |
{% endfor %}

### Issues by Tool

| Tool | Count |
|------|-------|
{% for tool, count in metrics._issues_by_tool.items() %}
| {{ tool }} | {{ count }} |
{% endfor %}
{% endif %}

{% if include_sections.get('complexity', True) and include_metrics.get('complexity', True) %}
## Complexity

### Most Complex Files

| File | Complexity |
|------|------------|
{% for file_path in metrics.highest_complexity_files %}
| {{ file_path }} | {{ metrics.complexity_scores[file_path] }} |
{% endfor %}
{% endif %}

{% if include_sections.get('dependencies', True) and include_metrics.get('dependencies', True) %}
## Dependencies

{% set circular_deps = metrics.get_circular_dependencies() %}
{% if circular_deps %}
### Circular Dependencies

{% for cycle in circular_deps %}
- {{ " → ".join(cycle) }} → {{ cycle[0] }}
{% endfor %}
{% else %}
No circular dependencies found.
{% endif %}

### Most Imported Files

{% for file_path in metrics.get_most_imported_files() %}
- {{ file_path }}
{% endfor %}
{% endif %}

{% if include_sections.get('documentation', True) %}
## Documentation

{% if include_metrics.get('doc_coverage', True) %}
- **Docstring Coverage:** {{ "%.1f"|format(metrics.avg_doc_coverage) }}%
{% endif %}
{% if include_metrics.get('type_coverage', True) %}
- **Type Hint Coverage:** {{ "%.1f"|format(metrics.avg_type_coverage) }}%
{% endif %}
{% endif %}

{% if include_sections.get('trends', True) and metrics.trend_results %}
## Trends

{% if metrics.trend_results.get('has_historical_data', False) %}
{{ metrics.trend_results.get('summary', 'No trend summary available.') }}

{% if metrics.trend_results.get('changes') %}
### Metric Changes

| Metric | Current | Previous | Change |
|--------|---------|----------|--------|
{% for metric, change in metrics.trend_results.changes.items() %}
| {{ metric }} | {{ "%.2f"|format(change.current) if change.current is float else change.current }} | {{ "%.2f"|format(change.previous) if change.previous is float else change.previous }} | {% if change.absolute_change > 0 %}+{% endif %}{{ "%.2f"|format(change.absolute_change) if change.absolute_change is float else change.absolute_change }} ({{ "%.1f"|format(change.percentage_change) }}%) |
{% endfor %}
{% endif %}

{% if metrics.trend_results.get('severity_changes') %}
### Issue Severity Changes

| Severity | Current | Previous | Change |
|----------|---------|----------|--------|
{% for severity, change in metrics.trend_results.severity_changes.items() %}
| {{ severity }} | {{ change.current }} | {{ change.previous }} | {% if change.absolute_change > 0 %}+{% endif %}{{ change.absolute_change }} ({{ "%.1f"|format(change.percentage_change) }}%) |
{% endfor %}
{% endif %}

{% if metrics.trend_visualizations %}
### Trend Visualizations

{% for chart_name, chart_path in metrics.trend_visualizations.items() %}
- [{{ chart_name }}]({{ chart_path }})
{% endfor %}
{% endif %}
{% else %}
{{ metrics.trend_results.get('message', 'No historical data available for trend analysis.') }}
{% endif %}
{% endif %}
