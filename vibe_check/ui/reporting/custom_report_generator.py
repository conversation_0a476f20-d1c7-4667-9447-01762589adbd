"""
Custom Report Generator
==================

This module provides functionality for generating customized reports.
"""

import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union

from ...core.models.project_metrics import ProjectMetrics
from .report_generator import ReportGenerator
from .templates import render_template


class CustomReportGenerator(ReportGenerator):
    """
    Generator for customized reports.
    
    This class provides functionality for generating reports with customizable
    sections and metrics.
    """
    
    def __init__(self, output_dir: Union[str, Path], format_type: str = "html"):
        """
        Initialize the custom report generator.
        
        Args:
            output_dir: Output directory
            format_type: Format type (html, markdown, json)
        """
        super().__init__(output_dir, format_type)
        
        # Default sections to include
        self.include_sections = {
            "summary": True,
            "issues": True,
            "metrics": True,
            "recommendations": True,
            "complexity": True,
            "dependencies": True,
            "documentation": True,
            "trends": True
        }
        
        # Default metrics to include
        self.include_metrics = {
            "file_count": True,
            "line_count": True,
            "complexity": True,
            "issues": True,
            "type_coverage": True,
            "doc_coverage": True,
            "dependencies": True
        }
    
    def set_sections(self, sections: Dict[str, bool]) -> None:
        """
        Set which sections to include in the report.
        
        Args:
            sections: Dictionary mapping section names to boolean values
        """
        self.include_sections.update(sections)
    
    def set_metrics(self, metrics: Dict[str, bool]) -> None:
        """
        Set which metrics to include in the report.
        
        Args:
            metrics: Dictionary mapping metric names to boolean values
        """
        self.include_metrics.update(metrics)
    
    def generate_custom_report(self, metrics: ProjectMetrics, 
                             template_name: Optional[str] = None) -> str:
        """
        Generate a customized report.
        
        Args:
            metrics: Project metrics
            template_name: Optional template name
            
        Returns:
            Path to the generated report
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Create a context with the metrics and customization options
        context = {
            "metrics": metrics,
            "include_sections": self.include_sections,
            "include_metrics": self.include_metrics
        }
        
        # Generate the report content using a template
        if self.format == "html":
            template = template_name or "custom_report.html"
            content = render_template(template, context)
            extension = "html"
        elif self.format == "markdown":
            template = template_name or "custom_report.md"
            content = render_template(template, context)
            extension = "md"
        else:  # json
            import json
            content = json.dumps(self._create_json_report(metrics), indent=2)
            extension = "json"
        
        # Write the report to a file
        report_path = os.path.join(self.output_dir, f"custom_report.{extension}")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return report_path
    
    def _create_json_report(self, metrics: ProjectMetrics) -> Dict[str, Any]:
        """
        Create a JSON report with only the selected sections and metrics.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Dictionary with the selected sections and metrics
        """
        result = {"project_path": metrics.project_path}
        
        # Add summary section if included
        if self.include_sections.get("summary", True):
            result["summary"] = {
                "total_file_count": metrics.total_file_count if self.include_metrics.get("file_count", True) else None,
                "total_line_count": metrics.total_line_count if self.include_metrics.get("line_count", True) else None,
                "avg_complexity": metrics.avg_complexity if self.include_metrics.get("complexity", True) else None,
                "max_complexity": metrics.max_complexity if self.include_metrics.get("complexity", True) else None,
                "issue_count": metrics.issue_count if self.include_metrics.get("issues", True) else None,
                "avg_type_coverage": metrics.avg_type_coverage if self.include_metrics.get("type_coverage", True) else None,
                "avg_docstring_coverage": metrics.avg_doc_coverage if self.include_metrics.get("doc_coverage", True) else None
            }
            
            # Remove None values
            result["summary"] = {k: v for k, v in result["summary"].items() if v is not None}
        
        # Add issues section if included
        if self.include_sections.get("issues", True) and self.include_metrics.get("issues", True):
            result["issues"] = {
                "total": metrics.issue_count,
                "by_severity": metrics.issues_by_severity,
                "by_tool": metrics._issues_by_tool
            }
        
        # Add complexity section if included
        if self.include_sections.get("complexity", True) and self.include_metrics.get("complexity", True):
            result["complexity"] = {
                "avg_complexity": metrics.avg_complexity,
                "max_complexity": metrics.max_complexity,
                "highest_complexity_files": metrics.highest_complexity_files
            }
        
        # Add dependencies section if included
        if self.include_sections.get("dependencies", True) and self.include_metrics.get("dependencies", True):
            result["dependencies"] = {
                "circular_dependencies": metrics.get_circular_dependencies(),
                "most_imported_files": metrics.get_most_imported_files(),
                "most_importing_files": metrics.get_most_importing_files()
            }
        
        # Add documentation section if included
        if self.include_sections.get("documentation", True):
            result["documentation"] = {
                "avg_docstring_coverage": metrics.avg_doc_coverage if self.include_metrics.get("doc_coverage", True) else None,
                "avg_type_coverage": metrics.avg_type_coverage if self.include_metrics.get("type_coverage", True) else None,
                "documentation_files": len(metrics.documentation_files)
            }
            
            # Remove None values
            result["documentation"] = {k: v for k, v in result["documentation"].items() if v is not None}
        
        # Add trends section if included
        if self.include_sections.get("trends", True) and hasattr(metrics, "trend_results"):
            result["trends"] = metrics.trend_results
        
        return result
