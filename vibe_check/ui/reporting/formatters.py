"""
Report Formatters
==============

This module provides functions for formatting analysis results.
"""

from typing import Any, Dict, List, Optional, Union

from ...core.models.project_metrics import ProjectMetrics


def format_issue(issue: Dict[str, Any], format_type: str = "text") -> str:
    """
    Format an issue for display.
    
    Args:
        issue: Issue dictionary
        format_type: Format type (text, markdown, html)
        
    Returns:
        Formatted issue
    """
    code = issue.get("code", "")
    message = issue.get("message", "")
    line = issue.get("line", 0)
    column = issue.get("column", 0)
    severity = issue.get("severity", "")
    
    if format_type == "markdown":
        return f"**{code}** ({severity}): {message} at line {line}, column {column}"
    elif format_type == "html":
        return f"<strong>{code}</strong> ({severity}): {message} at line {line}, column {column}"
    else:  # text
        return f"{code} ({severity}): {message} at line {line}, column {column}"


def format_metrics(metrics: ProjectMetrics, format_type: str = "text") -> str:
    """
    Format project metrics for display.
    
    Args:
        metrics: Project metrics
        format_type: Format type (text, markdown, html)
        
    Returns:
        Formatted metrics
    """
    if format_type == "markdown":
        return f"""
# Project Metrics

- **Project Path**: {metrics.project_path}
- **Total Files**: {metrics.total_file_count}
- **Total Lines**: {metrics.total_line_count}
- **Average Complexity**: {metrics.avg_complexity:.2f}
- **Issue Count**: {metrics.issue_count}

## Tools Used

{', '.join(metrics.tool_results.keys())}
"""
    elif format_type == "html":
        return f"""
<h1>Project Metrics</h1>

<ul>
  <li><strong>Project Path</strong>: {metrics.project_path}</li>
  <li><strong>Total Files</strong>: {metrics.total_file_count}</li>
  <li><strong>Total Lines</strong>: {metrics.total_line_count}</li>
  <li><strong>Average Complexity</strong>: {metrics.avg_complexity:.2f}</li>
  <li><strong>Issue Count</strong>: {metrics.issue_count}</li>
</ul>

<h2>Tools Used</h2>

<p>{', '.join(metrics.tool_results.keys())}</p>
"""
    else:  # text
        return f"""
Project Metrics
==============

Project Path: {metrics.project_path}
Total Files: {metrics.total_file_count}
Total Lines: {metrics.total_line_count}
Average Complexity: {metrics.avg_complexity:.2f}
Issue Count: {metrics.issue_count}

Tools Used
---------

{', '.join(metrics.tool_results.keys())}
"""


def format_analysis_results(metrics: ProjectMetrics, format_type: str = "text") -> str:
    """
    Format analysis results for display.
    
    Args:
        metrics: Project metrics
        format_type: Format type (text, markdown, html)
        
    Returns:
        Formatted analysis results
    """
    # Format the metrics
    result = format_metrics(metrics, format_type)
    
    # Add file metrics
    if format_type == "markdown":
        result += "\n## Files with Issues\n\n"
        
        for file_path, file_metrics in metrics.file_metrics.items():
            if file_metrics.issues:
                result += f"### {file_path}\n\n"
                result += f"- Lines: {file_metrics.line_count}\n"
                result += f"- Complexity: {file_metrics.complexity:.2f}\n"
                result += f"- Issues: {len(file_metrics.issues)}\n\n"
                
                for issue in file_metrics.issues:
                    result += f"- {format_issue(issue, format_type)}\n"
                
                result += "\n"
    
    elif format_type == "html":
        result += "\n<h2>Files with Issues</h2>\n\n"
        
        for file_path, file_metrics in metrics.file_metrics.items():
            if file_metrics.issues:
                result += f"<h3>{file_path}</h3>\n\n"
                result += "<ul>\n"
                result += f"<li>Lines: {file_metrics.line_count}</li>\n"
                result += f"<li>Complexity: {file_metrics.complexity:.2f}</li>\n"
                result += f"<li>Issues: {len(file_metrics.issues)}</li>\n"
                result += "</ul>\n\n"
                
                result += "<ul>\n"
                for issue in file_metrics.issues:
                    result += f"<li>{format_issue(issue, format_type)}</li>\n"
                result += "</ul>\n\n"
    
    else:  # text
        result += "\nFiles with Issues\n================\n\n"
        
        for file_path, file_metrics in metrics.file_metrics.items():
            if file_metrics.issues:
                result += f"{file_path}\n{'-' * len(file_path)}\n\n"
                result += f"Lines: {file_metrics.line_count}\n"
                result += f"Complexity: {file_metrics.complexity:.2f}\n"
                result += f"Issues: {len(file_metrics.issues)}\n\n"
                
                for issue in file_metrics.issues:
                    result += f"- {format_issue(issue, format_type)}\n"
                
                result += "\n"
    
    return result
