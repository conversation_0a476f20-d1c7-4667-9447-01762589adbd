"""
Report Generator
============

This module provides the ReportGenerator class for generating reports.
"""

import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from ...core.models.project_metrics import ProjectMetrics
from ...core.fs_utils import ensure_directory


class ReportGenerator:
    """Base class for report generators."""
    
    def __init__(self, output_dir: Union[str, Path], format_type: str):
        """
        Initialize the report generator.
        
        Args:
            output_dir: Output directory
            format_type: Format type (markdown, html, json, etc.)
        """
        self.output_dir = str(output_dir)
        self.format = format_type
    
    def ensure_output_dir(self) -> None:
        """Ensure the output directory exists."""
        ensure_directory(self.output_dir)
    
    def generate_summary_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate a summary report.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
            
        Raises:
            NotImplementedError: This method must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement generate_summary_report")
    
    def generate_issue_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate an issue report.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
            
        Raises:
            NotImplementedError: This method must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement generate_issue_report")
    
    def generate_metrics_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate a metrics report.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
            
        Raises:
            NotImplementedError: This method must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement generate_metrics_report")
    
    def generate_recommendation_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate a recommendation report.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
            
        Raises:
            NotImplementedError: This method must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement generate_recommendation_report")
    
    def generate_all_reports(self, metrics: ProjectMetrics) -> Dict[str, str]:
        """
        Generate all reports.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Dictionary mapping report types to file paths
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Generate all reports
        reports = {
            "summary": self.generate_summary_report(metrics),
            "issues": self.generate_issue_report(metrics),
            "metrics": self.generate_metrics_report(metrics),
            "recommendations": self.generate_recommendation_report(metrics)
        }
        
        return reports


class MarkdownReportGenerator(ReportGenerator):
    """Report generator for Markdown format."""
    
    def __init__(self, output_dir: Union[str, Path]):
        """
        Initialize the Markdown report generator.
        
        Args:
            output_dir: Output directory
        """
        super().__init__(output_dir, "markdown")
    
    def generate_summary_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate a summary report in Markdown format.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Generate the report content
        content = f"""
# Project Analysis Summary

## Overview

- **Project**: {Path(metrics.project_path).name}
- **Total Files**: {metrics.total_file_count}
- **Total Lines**: {metrics.total_line_count}
- **Average Complexity**: {metrics.avg_complexity:.2f}
- **Total Issues**: {metrics.issue_count}

## Tools Used

{', '.join(metrics.tool_results.keys())}

## Summary

This report provides a summary of the analysis results for the project.
"""
        
        # Write the report to a file
        report_path = os.path.join(self.output_dir, "summary.md")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return report_path
    
    def generate_issue_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate an issue report in Markdown format.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Generate the report content
        content = f"""
# Project Issues

## Issues by Severity

"""
        
        # Count issues by severity
        severity_counts = {}
        for file_path, file_metrics in metrics.file_metrics.items():
            for issue in file_metrics.issues:
                severity = issue.get("severity", "unknown")
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        for severity, count in severity_counts.items():
            content += f"- **{severity}**: {count}\n"
        
        content += "\n## Issues by File\n\n"
        
        # List issues by file
        for file_path, file_metrics in metrics.file_metrics.items():
            if file_metrics.issues:
                content += f"### {file_path}\n\n"
                
                for issue in file_metrics.issues:
                    code = issue.get("code", "")
                    message = issue.get("message", "")
                    line = issue.get("line", 0)
                    severity = issue.get("severity", "")
                    
                    content += f"- **{code}** ({severity}): {message} at line {line}\n"
                
                content += "\n"
        
        # Write the report to a file
        report_path = os.path.join(self.output_dir, "issues.md")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return report_path
    
    def generate_metrics_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate a metrics report in Markdown format.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Generate the report content
        content = f"""
# Project Metrics

## File Metrics

| File | Lines | Complexity | Issues |
|------|-------|------------|--------|
"""
        
        # Add file metrics
        for file_path, file_metrics in metrics.file_metrics.items():
            content += f"| {file_path} | {file_metrics.line_count} | {file_metrics.complexity:.2f} | {len(file_metrics.issues)} |\n"
        
        content += "\n## Directory Metrics\n\n"
        content += "| Directory | Files | Lines | Average Complexity |\n"
        content += "|-----------|-------|-------|-------------------|\n"
        
        # Add directory metrics
        for dir_path, dir_metrics in metrics.directory_metrics.items():
            content += f"| {dir_path} | {dir_metrics.file_count} | {dir_metrics.line_count} | {dir_metrics.avg_complexity:.2f} |\n"
        
        # Write the report to a file
        report_path = os.path.join(self.output_dir, "metrics.md")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return report_path
    
    def generate_recommendation_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate a recommendation report in Markdown format.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Generate the report content
        content = f"""
# Project Recommendations

## Code Quality Recommendations

- Fix the issues identified in the issues report
- Improve code documentation
- Add more tests

## Security Recommendations

- Review dependencies for security vulnerabilities
- Implement proper input validation
- Use secure coding practices

## Performance Recommendations

- Optimize critical code paths
- Use appropriate data structures
- Consider caching frequently accessed data
"""
        
        # Write the report to a file
        report_path = os.path.join(self.output_dir, "recommendations.md")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return report_path


class HTMLReportGenerator(ReportGenerator):
    """Report generator for HTML format."""
    
    def __init__(self, output_dir: Union[str, Path]):
        """
        Initialize the HTML report generator.
        
        Args:
            output_dir: Output directory
        """
        super().__init__(output_dir, "html")
    
    def generate_summary_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate a summary report in HTML format.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Generate the report content using a template
        from .templates import render_template
        content = render_template("summary_report.html", {"metrics": metrics})
        
        # Write the report to a file
        report_path = os.path.join(self.output_dir, "summary.html")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return report_path
    
    def generate_issue_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate an issue report in HTML format.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Generate the report content using a template
        from .templates import render_template
        content = render_template("issue_report.html", {"metrics": metrics})
        
        # Write the report to a file
        report_path = os.path.join(self.output_dir, "issues.html")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return report_path
    
    def generate_metrics_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate a metrics report in HTML format.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Generate the report content using a template
        from .templates import render_template
        content = render_template("metrics_report.html", {"metrics": metrics})
        
        # Write the report to a file
        report_path = os.path.join(self.output_dir, "metrics.html")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return report_path
    
    def generate_recommendation_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate a recommendation report in HTML format.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Generate the report content using a template
        from .templates import render_template
        content = render_template("recommendation_report.html", {"metrics": metrics})
        
        # Write the report to a file
        report_path = os.path.join(self.output_dir, "recommendations.html")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return report_path


class JSONReportGenerator(ReportGenerator):
    """Report generator for JSON format."""
    
    def __init__(self, output_dir: Union[str, Path]):
        """
        Initialize the JSON report generator.
        
        Args:
            output_dir: Output directory
        """
        super().__init__(output_dir, "json")
    
    def generate_summary_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate a summary report in JSON format.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Generate the report content
        import json
        content = json.dumps(metrics.to_dict(), indent=2)
        
        # Write the report to a file
        report_path = os.path.join(self.output_dir, "summary.json")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return report_path
    
    def generate_issue_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate an issue report in JSON format.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Generate the report content
        import json
        
        # Extract issues from metrics
        issues = []
        for file_path, file_metrics in metrics.file_metrics.items():
            for issue in file_metrics.issues:
                issue_copy = issue.copy()
                issue_copy["file_path"] = file_path
                issues.append(issue_copy)
        
        content = json.dumps({"issues": issues}, indent=2)
        
        # Write the report to a file
        report_path = os.path.join(self.output_dir, "issues.json")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return report_path
    
    def generate_metrics_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate a metrics report in JSON format.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Generate the report content
        import json
        
        # Extract metrics from metrics
        file_metrics = {}
        for file_path, file_metrics_obj in metrics.file_metrics.items():
            file_metrics[file_path] = {
                "line_count": file_metrics_obj.line_count,
                "complexity": file_metrics_obj.complexity,
                "issue_count": len(file_metrics_obj.issues)
            }
        
        dir_metrics = {}
        for dir_path, dir_metrics_obj in metrics.directory_metrics.items():
            dir_metrics[dir_path] = {
                "file_count": dir_metrics_obj.file_count,
                "line_count": dir_metrics_obj.line_count,
                "avg_complexity": dir_metrics_obj.avg_complexity
            }
        
        content = json.dumps({
            "file_metrics": file_metrics,
            "directory_metrics": dir_metrics
        }, indent=2)
        
        # Write the report to a file
        report_path = os.path.join(self.output_dir, "metrics.json")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return report_path
    
    def generate_recommendation_report(self, metrics: ProjectMetrics) -> str:
        """
        Generate a recommendation report in JSON format.
        
        Args:
            metrics: Project metrics
            
        Returns:
            Path to the generated report
        """
        # Ensure the output directory exists
        self.ensure_output_dir()
        
        # Generate the report content
        import json
        
        # Generate recommendations based on metrics
        recommendations = {
            "code_quality": [
                "Fix the issues identified in the issues report",
                "Improve code documentation",
                "Add more tests"
            ],
            "security": [
                "Review dependencies for security vulnerabilities",
                "Implement proper input validation",
                "Use secure coding practices"
            ],
            "performance": [
                "Optimize critical code paths",
                "Use appropriate data structures",
                "Consider caching frequently accessed data"
            ]
        }
        
        content = json.dumps(recommendations, indent=2)
        
        # Write the report to a file
        report_path = os.path.join(self.output_dir, "recommendations.json")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        return report_path
