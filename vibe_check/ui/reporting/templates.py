"""
Report Templates
============

This module provides functions for working with report templates.
"""

import os
from pathlib import Path
from typing import Any, Dict, Optional, Union

from jinja2 import Environment, FileSystemLoader, select_autoescape

# Set up Jinja2 environment
template_dirs = [
    # User templates
    os.path.join(os.path.expanduser("~"), ".vibe_check", "templates"),
    # Package templates
    os.path.join(os.path.dirname(__file__), "templates")
]

env = Environment(
    loader=FileSystemLoader(template_dirs),
    autoescape=select_autoescape(["html", "xml"])
)


def get_template(template_name: str) -> Any:
    """
    Get a template by name.
    
    Args:
        template_name: Template name
        
    Returns:
        Template object
        
    Raises:
        TemplateNotFound: If the template is not found
    """
    return env.get_template(template_name)


def render_template(template_name: str, context: Dict[str, Any]) -> str:
    """
    Render a template with the given context.
    
    Args:
        template_name: Template name
        context: Template context
        
    Returns:
        Rendered template
        
    Raises:
        TemplateNotFound: If the template is not found
    """
    template = get_template(template_name)
    return template.render(**context)  # type: ignore[no-any-return]


def get_template_path(template_name: str) -> Optional[str]:
    """
    Get the path to a template.
    
    Args:
        template_name: Template name
        
    Returns:
        Path to the template, or None if not found
    """
    for template_dir in template_dirs:
        template_path = os.path.join(template_dir, template_name)
        if os.path.exists(template_path):
            return template_path
    
    return None


def list_templates() -> Dict[str, str]:
    """
    List all available templates.
    
    Returns:
        Dictionary mapping template names to their paths
    """
    templates = {}
    
    for template_dir in template_dirs:
        if not os.path.exists(template_dir):
            continue
        
        for root, _, files in os.walk(template_dir):
            for file in files:
                if file.endswith((".md", ".html", ".txt")):
                    template_name = os.path.relpath(os.path.join(root, file), template_dir)
                    templates[template_name] = os.path.join(root, file)
    
    return templates
