"""
Reporting Package
==============

This package provides functions and classes for generating reports.
"""

from .formatters import format_analysis_results, format_issue, format_metrics
from .generators import generate_report, generate_summary
from .markdown import generate_markdown_report
from .templates import get_template, render_template
from .report_generator import (
    ReportGenerator,
    MarkdownReportGenerator,
    HTMLReportGenerator,
    JSONReportGenerator
)
from .custom_report_generator import CustomReportGenerator

__all__ = [
    "format_analysis_results",
    "format_issue",
    "format_metrics",
    "generate_report",
    "generate_summary",
    "generate_markdown_report",
    "get_template",
    "render_template",
    "ReportGenerator",
    "MarkdownReportGenerator",
    "HTMLReportGenerator",
    "JSONReportGenerator",
    "CustomReportGenerator"
]
