"""
Markdown Report Generator
=====================

This module provides functions for generating Markdown reports.
"""

from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from ...core.models.project_metrics import ProjectMetrics
from .templates import render_template


def generate_markdown_report(metrics: ProjectMetrics, template_name: Optional[str] = None) -> str:
    """
    Generate a Markdown report from analysis results.
    
    Args:
        metrics: Project metrics
        template_name: Optional template name
        
    Returns:
        Markdown report
    """
    # Use the specified template or the default
    template = template_name or "report.md"
    
    # Render the template
    return render_template(template, {"metrics": metrics})


def generate_markdown_summary(metrics: ProjectMetrics) -> str:
    """
    Generate a Markdown summary of analysis results.
    
    Args:
        metrics: Project metrics
        
    Returns:
        Markdown summary
    """
    project_name = Path(metrics.project_path).name
    
    return f"""
# {project_name} Analysis Summary

## Overview

- **Files**: {metrics.total_file_count}
- **Lines of Code**: {metrics.total_line_count}
- **Issues**: {metrics.issue_count}
- **Average Complexity**: {metrics.avg_complexity:.2f}

## Tools Used

{', '.join(metrics.tool_results.keys())}

## Top Issues

{_generate_top_issues_markdown(metrics)}

## Complexity

{_generate_complexity_markdown(metrics)}
"""


def _generate_top_issues_markdown(metrics: ProjectMetrics, limit: int = 5) -> str:
    """
    Generate a Markdown list of top issues.
    
    Args:
        metrics: Project metrics
        limit: Maximum number of issues to include
        
    Returns:
        Markdown list of top issues
    """
    # Collect all issues
    all_issues = []
    for file_path, file_metrics in metrics.file_metrics.items():
        for issue in file_metrics.issues:
            issue_copy = issue.copy()
            issue_copy["file_path"] = file_path
            all_issues.append(issue_copy)
    
    # Sort issues by severity
    severity_order = {"critical": 0, "high": 1, "medium": 2, "low": 3, "info": 4}
    all_issues.sort(key=lambda x: severity_order.get(x.get("severity", "").lower(), 5))
    
    # Take the top issues
    top_issues = all_issues[:limit]
    
    if not top_issues:
        return "No issues found."
    
    result = ""
    for issue in top_issues:
        file_path = issue.get("file_path", "")
        code = issue.get("code", "")
        message = issue.get("message", "")
        line = issue.get("line", 0)
        severity = issue.get("severity", "")
        
        result += f"- **{severity.upper()}**: {code} in `{file_path}:{line}` - {message}\n"
    
    return result


def _generate_complexity_markdown(metrics: ProjectMetrics, limit: int = 5) -> str:
    """
    Generate a Markdown section about complexity.
    
    Args:
        metrics: Project metrics
        limit: Maximum number of files to include
        
    Returns:
        Markdown section about complexity
    """
    # Sort files by complexity
    sorted_files = sorted(
        metrics.file_metrics.items(),
        key=lambda x: x[1].complexity,
        reverse=True
    )
    
    # Take the top files
    top_files = sorted_files[:limit]
    
    if not top_files:
        return "No complexity data available."
    
    result = "| File | Lines | Complexity |\n"
    result += "|------|-------|------------|\n"
    
    for file_path, file_metrics in top_files:
        result += f"| `{file_path}` | {file_metrics.line_count} | {file_metrics.complexity:.2f} |\n"
    
    return result
