"""
Web UI Interface for Vibe Check
====================

This module provides a web-based user interface for Vibe Check, allowing users to
interact with the tool through a browser.

The web UI is built on Streamlit and provides interactive visualizations of
analysis results.
"""

from .app import create_app, run_app, run_web_ui
from .components import render_results, render_visualization
from .state_manager import StateManager

__all__ = [
    "create_app",
    "run_app",
    "run_web_ui",
    "render_results",
    "render_visualization",
    "StateManager"
]
