"""
TUI Components
===========

This module provides components for rendering the Vibe Check TUI.
These components are built on top of textual or similar TUI frameworks
and provide a rich text-based user interface.
"""

import os
import sys
import time
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union, Callable

# Import TUI framework
try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.progress import Progress, BarColumn, TextColumn
    from rich.layout import Layout
    from rich.text import Text
    from rich.markdown import Markdown
except ImportError:
    # Provide fallback if rich is not available
    class FallbackConsole:
        """Fallback console class if rich is not available."""
        def print(self, *args, **kwargs):
            print(*args)

    Console = FallbackConsole

from vibe_check.core.models import ProjectMetrics
from .state_manager import TUIState, TUIScreen, AnalysisState

# Create console
console = Console()


def render_header(title: str = "Vibe Check - Project Analysis Tool") -> None:
    """
    Render the application header.

    Args:
        title: Application title
    """
    console.print()
    console.print(f"[bold blue]{title}[/bold blue]")
    console.print("[blue]" + "=" * len(title) + "[/blue]")
    console.print()


def render_footer(state: TUIState) -> None:
    """
    Render the application footer.

    Args:
        state: Application state
    """
    console.print()
    console.print("[dim]" + "-" * console.width + "[/dim]")

    # Show navigation hints
    hints = []

    if state.current_screen != TUIScreen.MAIN_MENU:
        hints.append("[b]b[/b]:back")

    hints.append("[b]q[/b]:quit")

    if state.current_screen == TUIScreen.PROJECT_SELECT:
        hints.append("[b]enter[/b]:select")

    if state.current_screen == TUIScreen.CONFIG:
        hints.append("[b]enter[/b]:confirm")
        hints.append("[b]tab[/b]:next field")

    if state.current_screen == TUIScreen.RESULTS:
        hints.append("[b]v[/b]:visualizations")
        hints.append("[b]s[/b]:summary")
        hints.append("[b]i[/b]:issues")
        hints.append("[b]r[/b]:recommendations")

    console.print(" | ".join(hints))


def render_menu(state: TUIState, options: List[Tuple[str, str]],
              title: str = "Main Menu") -> None:
    """
    Render a menu with options.

    Args:
        state: Application state
        options: List of (option_key, option_description) tuples
        title: Menu title
    """
    render_header(title)

    # Create a table for the menu
    table = Table(show_header=False, box=None, padding=(0, 2))
    table.add_column("Key", style="bold cyan")
    table.add_column("Description")

    for key, description in options:
        table.add_row(key, description)

    console.print(table)

    render_footer(state)


def render_main_menu(state: TUIState) -> None:
    """
    Render the main menu.

    Args:
        state: Application state
    """
    options = [
        ("1", "Analyze Project"),
        ("2", "Configuration"),
        ("3", "Recent Projects"),
        ("4", "Help"),
        ("q", "Quit")
    ]

    render_menu(state, options)


def render_project_selection(state: TUIState) -> None:
    """
    Render the project selection screen.

    Args:
        state: Application state
    """
    render_header("Project Selection")

    console.print("\nEnter the path to the project to analyze:")
    console.print("[dim]Example: /path/to/your/project[/dim]\n")

    if state.project_path:
        console.print(f"Current path: [green]{state.project_path}[/green]")

    render_footer(state)


def render_config(state: TUIState) -> None:
    """
    Render the configuration screen.

    Args:
        state: Application state
    """
    render_header("Configuration")

    # Create sections for different config options
    config = state.config

    # Create a table for general config
    table = Table(title="General Configuration", show_lines=True)
    table.add_column("Option", style="bold")
    table.add_column("Value")

    # Add general config options
    table.add_row("Project Path", state.project_path or "Not set")
    table.add_row("Output Directory",
                config.get("output_dir") or "Default")

    extensions = ", ".join(config.get("file_extensions", [".py"]))
    table.add_row("File Extensions", extensions)

    analyze_docs = "Yes" if config.get("analyze_docs", False) else "No"
    table.add_row("Analyze Documentation", analyze_docs)

    console.print(table)
    console.print()

    # Create a table for tool config
    tools_table = Table(title="Tools Configuration")
    tools_table.add_column("Tool", style="bold")
    tools_table.add_column("Enabled")

    tool_config = config.get("tools", {})
    for tool_name, tool_opts in {
        "ruff": {"name": "Ruff Linter"},
        "mypy": {"name": "MyPy Type Checker"},
        "bandit": {"name": "Bandit Security Scanner"},
        "complexity": {"name": "Complexity Analyzer"},
        "markdown": {"name": "Markdown Analyzer"}
    }.items():
        enabled = tool_config.get(tool_name, {}).get("enabled", True)
        enabled_str = "[green]✓[/green]" if enabled else "[red]✗[/red]"
        tools_table.add_row(tool_opts["name"], enabled_str)

    console.print(tools_table)
    console.print()

    # Create a table for CAW options
    caw_table = Table(title="CAW Architecture Options")
    caw_table.add_column("Option", style="bold")
    caw_table.add_column("Value")

    caw_table.add_row("Enable Actor System",
                    "Yes" if config.get("use_actor_system", True) else "No")
    caw_table.add_row("Context Propagation",
                    "Yes" if config.get("use_context_propagation", True) else "No")
    caw_table.add_row("Adaptive Configuration",
                    "Yes" if config.get("use_adaptive_configuration", True) else "No")

    console.print(caw_table)

    render_footer(state)


def render_progress(state: TUIState) -> None:
    """
    Render the progress screen.

    Args:
        state: Application state
    """
    render_header("Analysis in Progress")

    progress = state.progress

    # Extract progress information
    total_files = progress.get("total_files", 0)
    processed_files = progress.get("processed_files", 0)
    current_phase = progress.get("current_phase", "")
    phase_progress = progress.get("phase_progress", 0)
    current_file = progress.get("current_file", "")

    # Create progress bars
    with Progress() as progress_bar:
        # Overall progress
        total_task = progress_bar.add_task(
            "[cyan]Overall Progress",
            total=max(1, total_files),
            completed=processed_files
        )

        # Phase progress
        if current_phase:
            phase_task = progress_bar.add_task(
                f"[green]{current_phase}",
                total=100,
                completed=int(phase_progress * 100)
            )

        # Just for demonstration, we'll manually advance the progress
        # In a real impl this would be updated by state changes
        if total_files > 0:
            progress_bar.update(total_task, completed=processed_files)

            if current_phase:
                progress_bar.update(phase_task, completed=int(phase_progress * 100))

            # Just to demonstrate the rendering
            progress_bar.refresh()

    if current_file:
        console.print(f"Processing: [bold]{current_file}[/bold]")

    # Show status
    if total_files > 0:
        pct = int(100 * processed_files / total_files)
        console.print(f"Status: {processed_files}/{total_files} files ({pct}% complete)")

    render_footer(state)


def render_results(state: TUIState) -> None:
    """
    Render the results screen.

    Args:
        state: Application state
    """
    view = state.selected_view or "summary"
    metrics = state.metrics

    if metrics is None:
        render_header("Results")
        console.print("[yellow]No analysis results available[/yellow]")
        render_footer(state)
        return

    if view == "summary":
        render_summary_view(state, metrics)
    elif view == "issues":
        render_issues_view(state, metrics)
    elif view == "recommendations":
        render_recommendations_view(state, metrics)
    else:
        # Default to summary
        render_summary_view(state, metrics)


def render_summary_view(state: TUIState, metrics: ProjectMetrics) -> None:
    """
    Render the summary view.

    Args:
        state: Application state
        metrics: Analysis metrics
    """
    render_header("Analysis Summary")

    # Create a table for key metrics
    table = Table(title="Project Metrics", show_footer=True)
    table.add_column("Metric", style="bold", footer="Total")
    table.add_column("Value", justify="right", footer=str(metrics.total_file_count))

    table.add_row("Python Files", str(metrics.python_file_count))
    table.add_row("Markdown Files", str(metrics.markdown_file_count))
    table.add_row("Directories", str(metrics.total_directory_count))
    table.add_row("Avg. Complexity", f"{metrics.avg_complexity:.2f}")
    table.add_row("Avg. Documentation Coverage", f"{metrics.avg_doc_coverage:.2f}%")
    table.add_row("Avg. Type Coverage", f"{metrics.avg_type_coverage:.2f}%")

    console.print(table)
    console.print()

    # Show complex files if available
    if metrics.complexity_scores:
        complex_files = sorted(
            metrics.complexity_scores.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]

        complex_table = Table(title="Most Complex Files")
        complex_table.add_column("File", style="blue")
        complex_table.add_column("Complexity", justify="right", style="cyan")

        for file_path, score in complex_files:
            complex_table.add_row(os.path.basename(file_path), str(score))

        console.print(complex_table)

    # Show circular dependencies if available
    circular_deps = metrics.get_circular_dependencies()
    if circular_deps:
        console.print()
        console.print(Panel(
            f"[yellow]Warning: Found {len(circular_deps)} circular dependencies![/yellow]",
            title="Circular Dependencies"
        ))

        for i, cycle in enumerate(circular_deps[:3], 1):
            cycle_str = " -> ".join(os.path.basename(p) for p in cycle)
            console.print(f"  {i}. {cycle_str} -> {os.path.basename(cycle[0])}")

        if len(circular_deps) > 3:
            console.print(f"  ... and {len(circular_deps) - 3} more")

    render_footer(state)


def render_issues_view(state: TUIState, metrics: ProjectMetrics) -> None:
    """
    Render the issues view.

    Args:
        state: Application state
        metrics: Analysis metrics
    """
    render_header("Analysis Issues")

    # Collect all issues
    all_issues = []
    for file_path, file_metric in metrics.files.items():
        for tool_name, tool_result in file_metric.tool_results.items():
            if "issues" in tool_result:
                for issue in tool_result["issues"]:
                    all_issues.append({
                        "file": file_path,
                        "tool": tool_name,
                        "code": issue.get("code", "unknown"),
                        "message": issue.get("message", "Unknown issue"),
                        "line": issue.get("line", 0),
                        "severity": issue.get("severity", "MEDIUM")
                    })

    if not all_issues:
        console.print("[green]No issues found! 🎉[/green]")
        render_footer(state)
        return

    # Group issues by severity
    severity_groups = {
        "HIGH": [],
        "MEDIUM": [],
        "LOW": []
    }

    for issue in all_issues:
        severity = issue["severity"]
        if severity in severity_groups:
            severity_groups[severity].append(issue)
        else:
            severity_groups["MEDIUM"].append(issue)

    # Display counts
    console.print(f"Found [bold]{len(all_issues)} issues[/bold]:")
    console.print(f"  [red]High severity: {len(severity_groups['HIGH'])}[/red]")
    console.print(f"  [yellow]Medium severity: {len(severity_groups['MEDIUM'])}[/yellow]")
    console.print(f"  [blue]Low severity: {len(severity_groups['LOW'])}[/blue]")
    console.print()

    # Show high severity issues first
    if severity_groups["HIGH"]:
        console.print("[bold red]HIGH SEVERITY ISSUES[/bold red]")
        for issue in severity_groups["HIGH"]:
            file_name = os.path.basename(issue["file"])
            message = issue["message"]
            code = issue["code"]
            line = issue["line"]

            console.print(f"[red]{file_name}:{line}[/red] - {code}: {message}")
            console.print(f"  Tool: {issue['tool']}")
            console.print()

    # Show medium severity issues
    if severity_groups["MEDIUM"]:
        console.print("[bold yellow]MEDIUM SEVERITY ISSUES[/bold yellow]")
        for issue in severity_groups["MEDIUM"][:5]:  # Show only first 5
            file_name = os.path.basename(issue["file"])
            message = issue["message"]
            code = issue["code"]
            line = issue["line"]

            console.print(f"[yellow]{file_name}:{line}[/yellow] - {code}: {message}")
            console.print(f"  Tool: {issue['tool']}")
            console.print()

        if len(severity_groups["MEDIUM"]) > 5:
            console.print(f"  ... and {len(severity_groups['MEDIUM']) - 5} more medium severity issues")
            console.print()

    render_footer(state)


def render_recommendations_view(state: TUIState, metrics: ProjectMetrics) -> None:
    """
    Render the recommendations view.

    Args:
        state: Application state
        metrics: Analysis metrics
    """
    render_header("Recommendations")

    # Generate recommendations based on metrics
    recommendations = []

    # Check for circular dependencies
    circular_deps = metrics.get_circular_dependencies()
    if circular_deps:
        recommendations.append({
            "title": "Resolve Circular Dependencies",
            "description": f"Found {len(circular_deps)} circular dependencies. These can make the code harder to maintain.",
            "actions": [
                "Identify the root cause of the circular dependencies",
                "Consider using dependency injection or other design patterns",
                "Refactor the code to break the cycles"
            ],
            "severity": "high"
        })

    # Check for high complexity files
    high_complexity_files = [(p, c) for p, c in metrics.complexity_scores.items() if c > 10]
    if high_complexity_files:
        file_list = ", ".join([os.path.basename(p) for p, _ in high_complexity_files[:3]])
        if len(high_complexity_files) > 3:
            file_list += f" and {len(high_complexity_files) - 3} more"

        recommendations.append({
            "title": "Reduce Code Complexity",
            "description": f"Found {len(high_complexity_files)} files with high complexity. High complexity makes code harder to maintain and test.",
            "actions": [
                "Break down complex functions into smaller, more focused functions",
                "Refactor complex conditional logic",
                "Consider using design patterns to simplify the code"
            ],
            "examples": file_list,
            "severity": "medium"
        })

    # Check for low type coverage
    low_type_coverage_files = [(p, c) for p, c in metrics.type_coverage.items() if c < 50]
    if low_type_coverage_files:
        recommendations.append({
            "title": "Improve Type Coverage",
            "description": f"Found {len(low_type_coverage_files)} files with low type annotation coverage. Type annotations help catch errors early.",
            "actions": [
                "Add type annotations to function parameters and return values",
                "Use a type checker like mypy in your development workflow",
                "Consider using a tool to automatically add annotations"
            ],
            "severity": "medium"
        })

    # Render recommendations
    if not recommendations:
        console.print("[green]No specific recommendations. The project looks good! 🎉[/green]")
    else:
        for rec in recommendations:
            severity_style = {
                "high": "bold red",
                "medium": "bold yellow",
                "low": "bold blue"
            }.get(rec["severity"], "bold")

            console.print(f"[{severity_style}]{rec['title']}[/{severity_style}]")
            console.print(rec["description"])
            console.print()

            console.print("[bold]Recommended Actions:[/bold]")
            for action in rec["actions"]:
                console.print(f"  • {action}")

            if "examples" in rec:
                console.print(f"\n[bold]Examples:[/bold] {rec['examples']}")

            console.print("\n" + "-" * 50 + "\n")

    render_footer(state)


def render_visualization(state: TUIState) -> None:
    """
    Render visualizations in text mode.

    Args:
        state: Application state
    """
    render_header("Visualizations")

    # Visualizations are limited in pure text mode
    # For ASCII-based charts, we could use libraries like:
    # - asciichartpy
    # - termgraph
    # - plotille

    console.print("[bold]Text-based visualization options:[/bold]")
    console.print("1. Dependency Graph (basic ASCII)")
    console.print("2. Complexity Bar Chart")
    console.print("3. Coverage Statistics")
    console.print()
    console.print("[dim]Note: For richer visualizations, use the web UI instead.[/dim]")

    render_footer(state)
