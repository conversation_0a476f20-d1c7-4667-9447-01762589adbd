"""
Chart Generation
============

This module provides functions for generating charts.
"""

import json
from collections import Counter, defaultdict
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

from ...core.models.project_metrics import ProjectMetrics


def create_chart(chart_type: str, data: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Create a chart specification.
    
    Args:
        chart_type: Type of chart (bar, line, pie, etc.)
        data: Chart data
        options: Optional chart options
        
    Returns:
        Chart specification
    """
    # Default options
    default_options = {
        "width": 800,
        "height": 400,
        "padding": 50,
        "background": "#ffffff"
    }
    
    # Merge options
    chart_options = {**default_options, **(options or {})}
    
    # Create chart specification
    chart_spec = {
        "type": chart_type,
        "data": data,
        "options": chart_options
    }
    
    return chart_spec


def create_complexity_chart(metrics: ProjectMetrics, limit: int = 10) -> Dict[str, Any]:
    """
    Create a chart showing file complexity.
    
    Args:
        metrics: Project metrics
        limit: Maximum number of files to include
        
    Returns:
        Chart specification
    """
    # Sort files by complexity
    sorted_files = sorted(
        metrics.file_metrics.items(),
        key=lambda x: x[1].complexity,
        reverse=True
    )
    
    # Take the top files
    top_files = sorted_files[:limit]
    
    # Prepare data
    labels = [Path(file_path).name for file_path, _ in top_files]
    values = [file_metrics.complexity for _, file_metrics in top_files]
    
    # Create chart data
    data = {
        "labels": labels,
        "datasets": [
            {
                "label": "Complexity",
                "data": values,
                "backgroundColor": "rgba(54, 162, 235, 0.5)",
                "borderColor": "rgba(54, 162, 235, 1)",
                "borderWidth": 1
            }
        ]
    }
    
    # Create chart options
    options = {
        "title": {
            "display": True,
            "text": "File Complexity"
        },
        "scales": {
            "yAxes": [
                {
                    "ticks": {
                        "beginAtZero": True
                    }
                }
            ]
        }
    }
    
    # Create chart specification
    return create_chart("bar", data, options)


def create_issues_chart(metrics: ProjectMetrics) -> Dict[str, Any]:
    """
    Create a chart showing issue counts by severity.
    
    Args:
        metrics: Project metrics
        
    Returns:
        Chart specification
    """
    # Count issues by severity
    severity_counts = Counter()
    
    for file_path, file_metrics in metrics.file_metrics.items():
        for issue in file_metrics.issues:
            severity = issue.get("severity", "unknown").lower()
            severity_counts[severity] += 1
    
    # Prepare data
    labels = list(severity_counts.keys())
    values = list(severity_counts.values())
    
    # Define colors for each severity
    colors = {
        "critical": "rgba(255, 0, 0, 0.5)",
        "high": "rgba(255, 99, 71, 0.5)",
        "medium": "rgba(255, 165, 0, 0.5)",
        "low": "rgba(255, 255, 0, 0.5)",
        "info": "rgba(0, 128, 0, 0.5)",
        "unknown": "rgba(128, 128, 128, 0.5)"
    }
    
    background_colors = [colors.get(severity, colors["unknown"]) for severity in labels]
    border_colors = [color.replace("0.5", "1") for color in background_colors]
    
    # Create chart data
    data = {
        "labels": labels,
        "datasets": [
            {
                "label": "Issues",
                "data": values,
                "backgroundColor": background_colors,
                "borderColor": border_colors,
                "borderWidth": 1
            }
        ]
    }
    
    # Create chart options
    options = {
        "title": {
            "display": True,
            "text": "Issues by Severity"
        }
    }
    
    # Create chart specification
    return create_chart("pie", data, options)


def create_issues_by_tool_chart(metrics: ProjectMetrics) -> Dict[str, Any]:
    """
    Create a chart showing issue counts by tool.
    
    Args:
        metrics: Project metrics
        
    Returns:
        Chart specification
    """
    # Count issues by tool
    tool_counts = defaultdict(int)
    
    for file_path, file_metrics in metrics.file_metrics.items():
        for issue in file_metrics.issues:
            tool = issue.get("tool", "unknown")
            tool_counts[tool] += 1
    
    # Prepare data
    labels = list(tool_counts.keys())
    values = list(tool_counts.values())
    
    # Create chart data
    data = {
        "labels": labels,
        "datasets": [
            {
                "label": "Issues",
                "data": values,
                "backgroundColor": "rgba(75, 192, 192, 0.5)",
                "borderColor": "rgba(75, 192, 192, 1)",
                "borderWidth": 1
            }
        ]
    }
    
    # Create chart options
    options = {
        "title": {
            "display": True,
            "text": "Issues by Tool"
        },
        "scales": {
            "yAxes": [
                {
                    "ticks": {
                        "beginAtZero": True
                    }
                }
            ]
        }
    }
    
    # Create chart specification
    return create_chart("bar", data, options)


def create_lines_of_code_chart(metrics: ProjectMetrics, limit: int = 10) -> Dict[str, Any]:
    """
    Create a chart showing lines of code by file.
    
    Args:
        metrics: Project metrics
        limit: Maximum number of files to include
        
    Returns:
        Chart specification
    """
    # Sort files by line count
    sorted_files = sorted(
        metrics.file_metrics.items(),
        key=lambda x: x[1].line_count,
        reverse=True
    )
    
    # Take the top files
    top_files = sorted_files[:limit]
    
    # Prepare data
    labels = [Path(file_path).name for file_path, _ in top_files]
    values = [file_metrics.line_count for _, file_metrics in top_files]
    
    # Create chart data
    data = {
        "labels": labels,
        "datasets": [
            {
                "label": "Lines of Code",
                "data": values,
                "backgroundColor": "rgba(153, 102, 255, 0.5)",
                "borderColor": "rgba(153, 102, 255, 1)",
                "borderWidth": 1
            }
        ]
    }
    
    # Create chart options
    options = {
        "title": {
            "display": True,
            "text": "Lines of Code by File"
        },
        "scales": {
            "yAxes": [
                {
                    "ticks": {
                        "beginAtZero": True
                    }
                }
            ]
        }
    }
    
    # Create chart specification
    return create_chart("bar", data, options)
