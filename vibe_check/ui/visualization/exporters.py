"""
Visualization Exporters
===================

This module provides functions for exporting visualizations.
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from ...core.models.project_metrics import ProjectMetrics


def export_chart(chart_spec: Dict[str, Any], output_path: Union[str, Path], format_type: str = "json") -> str:
    """
    Export a chart to a file.
    
    Args:
        chart_spec: Chart specification
        output_path: Output file path
        format_type: Output format (json, html)
        
    Returns:
        Path to the exported file
    """
    # Create the output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    if format_type == "json":
        # Export as JSON
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(chart_spec, f, indent=2)
    
    elif format_type == "html":
        # Export as HTML with embedded JavaScript
        html = _generate_chart_html(chart_spec)
        
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(html)
    
    else:
        raise ValueError(f"Unsupported format: {format_type}")
    
    return str(output_path)


def export_visualization(metrics: ProjectMetrics, output_dir: Union[str, Path], format_type: str = "html") -> List[str]:
    """
    Export visualizations for project metrics.
    
    Args:
        metrics: Project metrics
        output_dir: Output directory
        format_type: Output format (json, html)
        
    Returns:
        List of paths to the exported files
    """
    from .charts import (
        create_complexity_chart,
        create_issues_chart,
        create_issues_by_tool_chart,
        create_lines_of_code_chart
    )
    
    # Create the output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate chart specifications
    charts = {
        "complexity": create_complexity_chart(metrics),
        "issues": create_issues_chart(metrics),
        "issues_by_tool": create_issues_by_tool_chart(metrics),
        "lines_of_code": create_lines_of_code_chart(metrics)
    }
    
    # Export each chart
    exported_files = []
    
    for chart_name, chart_spec in charts.items():
        output_path = os.path.join(output_dir, f"{chart_name}.{format_type}")
        exported_files.append(export_chart(chart_spec, output_path, format_type))
    
    return exported_files


def _generate_chart_html(chart_spec: Dict[str, Any]) -> str:
    """
    Generate HTML for a chart.
    
    Args:
        chart_spec: Chart specification
        
    Returns:
        HTML string
    """
    # Convert chart specification to JSON
    chart_json = json.dumps(chart_spec)
    
    # Generate HTML
    html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Vibe Check Visualization</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .chart-container {{ width: 800px; height: 400px; margin: 0 auto; }}
    </style>
</head>
<body>
    <div class="chart-container">
        <canvas id="chart"></canvas>
    </div>
    
    <script>
        // Chart specification
        const chartSpec = {chart_json};
        
        // Create chart
        const ctx = document.getElementById('chart').getContext('2d');
        new Chart(ctx, {{
            type: chartSpec.type,
            data: chartSpec.data,
            options: chartSpec.options
        }});
    </script>
</body>
</html>
"""
    
    return html
