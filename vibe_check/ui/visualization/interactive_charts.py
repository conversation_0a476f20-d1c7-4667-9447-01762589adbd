"""
Interactive Charts
==============

This module provides functions for creating interactive charts using Chart.js.
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from ...core.models.project_metrics import ProjectMetrics


def create_interactive_chart(chart_type: str, data: Dict[str, Any], 
                           options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Create an interactive chart specification using Chart.js.
    
    Args:
        chart_type: Type of chart (bar, line, pie, etc.)
        data: Chart data
        options: Optional chart options
        
    Returns:
        Chart specification for Chart.js
    """
    # Default options
    default_options = {
        "responsive": True,
        "maintainAspectRatio": False,
        "plugins": {
            "tooltip": {
                "mode": "index",
                "intersect": False
            },
            "legend": {
                "position": "top",
            },
            "title": {
                "display": True,
                "text": "Chart"
            }
        }
    }
    
    # Merge options
    chart_options = default_options.copy()
    if options:
        _deep_update(chart_options, options)
    
    # Create chart specification
    chart_spec = {
        "type": chart_type,
        "data": data,
        "options": chart_options
    }
    
    return chart_spec


def create_interactive_complexity_chart(metrics: ProjectMetrics, limit: int = 10) -> Dict[str, Any]:
    """
    Create an interactive chart showing complexity by file.
    
    Args:
        metrics: Project metrics
        limit: Maximum number of files to include
        
    Returns:
        Chart specification for Chart.js
    """
    # Sort files by complexity
    sorted_files = sorted(
        metrics.complexity_scores.items(),
        key=lambda x: x[1],
        reverse=True
    )
    
    # Limit the number of files
    sorted_files = sorted_files[:limit]
    
    # Extract file names and complexity scores
    file_names = [Path(file_path).name for file_path, _ in sorted_files]
    complexity_scores = [score for _, score in sorted_files]
    
    # Create chart data
    data = {
        "labels": file_names,
        "datasets": [
            {
                "label": "Complexity",
                "data": complexity_scores,
                "backgroundColor": "rgba(54, 162, 235, 0.5)",
                "borderColor": "rgba(54, 162, 235, 1)",
                "borderWidth": 1
            }
        ]
    }
    
    # Create chart options
    options = {
        "plugins": {
            "title": {
                "text": "File Complexity"
            }
        },
        "scales": {
            "y": {
                "beginAtZero": True,
                "title": {
                    "display": True,
                    "text": "Complexity Score"
                }
            },
            "x": {
                "title": {
                    "display": True,
                    "text": "File"
                }
            }
        }
    }
    
    # Create chart specification
    return create_interactive_chart("bar", data, options)


def create_interactive_issues_chart(metrics: ProjectMetrics) -> Dict[str, Any]:
    """
    Create an interactive chart showing issues by severity.
    
    Args:
        metrics: Project metrics
        
    Returns:
        Chart specification for Chart.js
    """
    # Count issues by severity
    severity_counts = {}
    for file_metrics in metrics.files.values():
        for issue in file_metrics.issues:
            severity = issue.get("severity", "UNKNOWN")
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
    
    # Create chart data
    labels = list(severity_counts.keys())
    counts = list(severity_counts.values())
    
    # Define colors for each severity
    colors = {
        "HIGH": "rgba(255, 99, 132, 0.5)",
        "MEDIUM": "rgba(255, 159, 64, 0.5)",
        "LOW": "rgba(255, 205, 86, 0.5)",
        "INFO": "rgba(75, 192, 192, 0.5)",
        "UNKNOWN": "rgba(201, 203, 207, 0.5)"
    }
    
    background_colors = [colors.get(severity, "rgba(201, 203, 207, 0.5)") for severity in labels]
    border_colors = [color.replace("0.5", "1") for color in background_colors]
    
    data = {
        "labels": labels,
        "datasets": [
            {
                "label": "Issues",
                "data": counts,
                "backgroundColor": background_colors,
                "borderColor": border_colors,
                "borderWidth": 1
            }
        ]
    }
    
    # Create chart options
    options = {
        "plugins": {
            "title": {
                "text": "Issues by Severity"
            }
        },
        "scales": {
            "y": {
                "beginAtZero": True,
                "title": {
                    "display": True,
                    "text": "Count"
                }
            }
        }
    }
    
    # Create chart specification
    return create_interactive_chart("pie", data, options)


def create_interactive_issues_by_tool_chart(metrics: ProjectMetrics) -> Dict[str, Any]:
    """
    Create an interactive chart showing issues by tool.
    
    Args:
        metrics: Project metrics
        
    Returns:
        Chart specification for Chart.js
    """
    # Count issues by tool
    tool_counts = {}
    for file_metrics in metrics.files.values():
        for issue in file_metrics.issues:
            tool = issue.get("tool", "UNKNOWN")
            tool_counts[tool] = tool_counts.get(tool, 0) + 1
    
    # Create chart data
    labels = list(tool_counts.keys())
    counts = list(tool_counts.values())
    
    data = {
        "labels": labels,
        "datasets": [
            {
                "label": "Issues",
                "data": counts,
                "backgroundColor": "rgba(153, 102, 255, 0.5)",
                "borderColor": "rgba(153, 102, 255, 1)",
                "borderWidth": 1
            }
        ]
    }
    
    # Create chart options
    options = {
        "plugins": {
            "title": {
                "text": "Issues by Tool"
            }
        },
        "scales": {
            "y": {
                "beginAtZero": True,
                "title": {
                    "display": True,
                    "text": "Count"
                }
            }
        }
    }
    
    # Create chart specification
    return create_interactive_chart("bar", data, options)


def create_interactive_coverage_chart(metrics: ProjectMetrics, limit: int = 10) -> Dict[str, Any]:
    """
    Create an interactive chart showing type and docstring coverage by file.
    
    Args:
        metrics: Project metrics
        limit: Maximum number of files to include
        
    Returns:
        Chart specification for Chart.js
    """
    # Get files with both type and docstring coverage
    files_with_coverage = []
    for file_path, file_metrics in metrics.files.items():
        if file_metrics.type_coverage > 0 or file_metrics.docstring_coverage > 0:
            files_with_coverage.append((
                file_path,
                file_metrics.type_coverage,
                file_metrics.docstring_coverage
            ))
    
    # Sort files by average coverage
    sorted_files = sorted(
        files_with_coverage,
        key=lambda x: (x[1] + x[2]) / 2,
        reverse=True
    )
    
    # Limit the number of files
    sorted_files = sorted_files[:limit]
    
    # Extract file names and coverage values
    file_names = [Path(file_path).name for file_path, _, _ in sorted_files]
    type_coverage = [tc for _, tc, _ in sorted_files]
    docstring_coverage = [dc for _, _, dc in sorted_files]
    
    # Create chart data
    data = {
        "labels": file_names,
        "datasets": [
            {
                "label": "Type Coverage",
                "data": type_coverage,
                "backgroundColor": "rgba(54, 162, 235, 0.5)",
                "borderColor": "rgba(54, 162, 235, 1)",
                "borderWidth": 1
            },
            {
                "label": "Docstring Coverage",
                "data": docstring_coverage,
                "backgroundColor": "rgba(255, 99, 132, 0.5)",
                "borderColor": "rgba(255, 99, 132, 1)",
                "borderWidth": 1
            }
        ]
    }
    
    # Create chart options
    options = {
        "plugins": {
            "title": {
                "text": "Type and Docstring Coverage"
            }
        },
        "scales": {
            "y": {
                "beginAtZero": True,
                "max": 100,
                "title": {
                    "display": True,
                    "text": "Coverage (%)"
                }
            }
        }
    }
    
    # Create chart specification
    return create_interactive_chart("bar", data, options)


def create_interactive_dependency_graph(metrics: ProjectMetrics, limit: int = 20) -> Dict[str, Any]:
    """
    Create an interactive dependency graph visualization.
    
    Args:
        metrics: Project metrics
        limit: Maximum number of nodes to include
        
    Returns:
        Graph specification for visualization
    """
    # Extract nodes and edges from dependency graph
    nodes = []
    edges = []
    
    # Get the most connected files
    file_connections = {}
    for file_path in metrics.files:
        file_connections[file_path] = len(metrics.files[file_path].imports) + len(metrics.files[file_path].imported_by)
    
    # Sort files by number of connections
    sorted_files = sorted(
        file_connections.items(),
        key=lambda x: x[1],
        reverse=True
    )
    
    # Limit the number of files
    sorted_files = sorted_files[:limit]
    included_files = {file_path for file_path, _ in sorted_files}
    
    # Create nodes
    for file_path in included_files:
        file_name = Path(file_path).name
        complexity = metrics.complexity_scores.get(file_path, 0)
        
        nodes.append({
            "id": file_path,
            "label": file_name,
            "size": min(30, 10 + complexity),
            "color": _get_color_for_complexity(complexity)
        })
    
    # Create edges
    for file_path in included_files:
        for import_path in metrics.files[file_path].imports:
            if import_path in included_files:
                edges.append({
                    "from": file_path,
                    "to": import_path,
                    "arrows": "to"
                })
    
    # Create graph specification
    graph_spec = {
        "nodes": nodes,
        "edges": edges,
        "options": {
            "nodes": {
                "shape": "dot",
                "font": {
                    "size": 12,
                    "face": "Tahoma"
                }
            },
            "edges": {
                "width": 1,
                "smooth": {
                    "type": "continuous"
                }
            },
            "physics": {
                "stabilization": True,
                "barnesHut": {
                    "gravitationalConstant": -80,
                    "springConstant": 0.001,
                    "springLength": 200
                }
            },
            "interaction": {
                "navigationButtons": True,
                "keyboard": True
            }
        }
    }
    
    return graph_spec


def create_interactive_complexity_heatmap(metrics: ProjectMetrics, limit: int = 20) -> Dict[str, Any]:
    """
    Create an interactive complexity heatmap.
    
    Args:
        metrics: Project metrics
        limit: Maximum number of files to include
        
    Returns:
        Heatmap specification for visualization
    """
    # Sort files by complexity
    sorted_files = sorted(
        metrics.complexity_scores.items(),
        key=lambda x: x[1],
        reverse=True
    )
    
    # Limit the number of files
    sorted_files = sorted_files[:limit]
    
    # Extract file names and complexity scores
    file_names = [Path(file_path).name for file_path, _ in sorted_files]
    complexity_scores = [score for _, score in sorted_files]
    
    # Create heatmap data
    data = []
    for i, file_name in enumerate(file_names):
        data.append({
            "x": 0,
            "y": i,
            "value": complexity_scores[i],
            "name": file_name
        })
    
    # Create heatmap specification
    heatmap_spec = {
        "data": data,
        "options": {
            "xAxis": {
                "visible": False
            },
            "yAxis": {
                "categories": file_names,
                "title": None
            },
            "colorAxis": {
                "min": 0,
                "max": max(complexity_scores),
                "stops": [
                    [0, "#00FF00"],
                    [0.5, "#FFFF00"],
                    [1, "#FF0000"]
                ]
            },
            "legend": {
                "align": "right",
                "layout": "vertical",
                "margin": 0,
                "verticalAlign": "top",
                "y": 25,
                "symbolHeight": 280
            },
            "tooltip": {
                "formatter": "function() { return '<b>' + this.point.name + '</b>: ' + this.point.value + ' complexity'; }"
            }
        }
    }
    
    return heatmap_spec


def export_interactive_chart(chart_spec: Dict[str, Any], output_path: Union[str, Path]) -> str:
    """
    Export an interactive chart to an HTML file.
    
    Args:
        chart_spec: Chart specification
        output_path: Output file path
        
    Returns:
        Path to the exported file
    """
    # Create the output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Convert chart specification to JSON
    chart_json = json.dumps(chart_spec)
    
    # Generate HTML
    html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Vibe Check Interactive Visualization</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .chart-container {{ width: 800px; height: 500px; margin: 0 auto; }}
    </style>
</head>
<body>
    <div class="chart-container">
        <canvas id="chart"></canvas>
    </div>
    <script>
        const ctx = document.getElementById('chart').getContext('2d');
        const chartSpec = {chart_json};
        new Chart(ctx, chartSpec);
    </script>
</body>
</html>
"""
    
    # Write HTML to file
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(html)
    
    return str(output_path)


def export_interactive_dependency_graph(graph_spec: Dict[str, Any], output_path: Union[str, Path]) -> str:
    """
    Export an interactive dependency graph to an HTML file.
    
    Args:
        graph_spec: Graph specification
        output_path: Output file path
        
    Returns:
        Path to the exported file
    """
    # Create the output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Convert graph specification to JSON
    graph_json = json.dumps(graph_spec)
    
    # Generate HTML
    html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Vibe Check Dependency Graph</title>
    <script src="https://cdn.jsdelivr.net/npm/vis-network@9.1.2/dist/vis-network.min.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 0; }}
        #graph-container {{ width: 100%; height: 100vh; }}
    </style>
</head>
<body>
    <div id="graph-container"></div>
    <script>
        const graphSpec = {graph_json};
        const container = document.getElementById('graph-container');
        const network = new vis.Network(container, {{
            nodes: new vis.DataSet(graphSpec.nodes),
            edges: new vis.DataSet(graphSpec.edges)
        }}, graphSpec.options);
    </script>
</body>
</html>
"""
    
    # Write HTML to file
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(html)
    
    return str(output_path)


def export_interactive_complexity_heatmap(heatmap_spec: Dict[str, Any], output_path: Union[str, Path]) -> str:
    """
    Export an interactive complexity heatmap to an HTML file.
    
    Args:
        heatmap_spec: Heatmap specification
        output_path: Output file path
        
    Returns:
        Path to the exported file
    """
    # Create the output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Convert heatmap specification to JSON
    heatmap_json = json.dumps(heatmap_spec)
    
    # Generate HTML
    html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Vibe Check Complexity Heatmap</title>
    <script src="https://code.highcharts.com/highcharts.js"></script>
    <script src="https://code.highcharts.com/modules/heatmap.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        #heatmap-container {{ width: 800px; height: 600px; margin: 0 auto; }}
    </style>
</head>
<body>
    <div id="heatmap-container"></div>
    <script>
        const heatmapSpec = {heatmap_json};
        Highcharts.chart('heatmap-container', {{
            chart: {{
                type: 'heatmap'
            }},
            title: {{
                text: 'File Complexity Heatmap'
            }},
            xAxis: heatmapSpec.options.xAxis,
            yAxis: heatmapSpec.options.yAxis,
            colorAxis: heatmapSpec.options.colorAxis,
            legend: heatmapSpec.options.legend,
            tooltip: {{
                formatter: new Function('return ' + heatmapSpec.options.tooltip.formatter)()
            }},
            series: [{{
                name: 'Complexity',
                data: heatmapSpec.data,
                dataLabels: {{
                    enabled: true,
                    color: '#000000'
                }}
            }}]
        }});
    </script>
</body>
</html>
"""
    
    # Write HTML to file
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(html)
    
    return str(output_path)


def _deep_update(d: Dict[str, Any], u: Dict[str, Any]) -> Dict[str, Any]:
    """
    Recursively update a dictionary.
    
    Args:
        d: Dictionary to update
        u: Dictionary with updates
        
    Returns:
        Updated dictionary
    """
    for k, v in u.items():
        if isinstance(v, dict) and k in d and isinstance(d[k], dict):
            _deep_update(d[k], v)
        else:
            d[k] = v
    return d


def _get_color_for_complexity(complexity: int) -> str:
    """
    Get a color for a complexity value.
    
    Args:
        complexity: Complexity value
        
    Returns:
        Color string
    """
    if complexity <= 5:
        return "#00FF00"  # Green
    elif complexity <= 10:
        return "#FFFF00"  # Yellow
    elif complexity <= 20:
        return "#FFA500"  # Orange
    else:
        return "#FF0000"  # Red
