"""
Plugin Manager
===========

This module provides the plugin manager for Vibe Check, which handles
plugin discovery, loading, initialization, and lifecycle management.
"""

import importlib
import logging
import pkgutil
import sys
from functools import wraps
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set, Type, TypeVar, Union, cast

from vibe_check.core.utils.config_utils import read_config

from .plugin_base import Plugin

logger = logging.getLogger("vibe_check_plugin_manager")

# Type variables for type checking
T = TypeVar('T', bound=Plugin)
F = TypeVar('F', bound=Callable[..., Any])


def plugin_hook(plugin_type: str) -> Callable[[F], F]:
    """
    Decorator to mark a function as a plugin hook.

    Args:
        plugin_type: Type of plugin this hook is for

    Returns:
        Decorated function
    """
    def decorator(func: F) -> F:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            return func(*args, **kwargs)

        setattr(wrapper, '_vibe_check_plugin_hook', True)
        setattr(wrapper, '_vibe_check_plugin_type', plugin_type)
        return cast(F, wrapper)

    return decorator


class PluginManager:
    """Manager for Vibe Check plugins."""

    def __init__(self, plugin_dirs: Optional[List[Path]] = None,
               config: Optional[Dict[str, Any]] = None):
        """
        Initialize the plugin manager.

        Args:
            plugin_dirs: List of directories to search for plugins
            config: Plugin configuration
        """
        self.plugin_dirs = plugin_dirs or []
        self.config = config or {}
        self.plugin_dir = Path.home() / ".vibe_check" / "plugins" if not plugin_dirs else plugin_dirs[0]

        # Map of plugin name to plugin instance
        self.plugins: Dict[str, Plugin] = {}

        # Map of plugin type to list of plugin names
        self.plugin_types: Dict[str, List[str]] = {}

        # Set of loaded plugin module paths
        self.loaded_modules: Set[str] = set()

        # Plugin hooks
        self.hooks: Dict[str, List[Callable[..., Any]]] = {}

    def discover_plugins(self) -> None:
        """
        Discover plugins from all sources.

        This includes entry points and plugin directories.
        """
        # Discover from entry points
        self._discover_from_entry_points()

        # Discover from plugin directories
        for plugin_dir in self.plugin_dirs:
            self._discover_from_directory(plugin_dir)

    def _discover_from_entry_points(self) -> None:
        """Discover plugins from entry points."""
        try:
            import importlib.metadata as metadata
        except ImportError:
            # Python < 3.8
            import importlib_metadata as metadata  # type: ignore[no-redef]

        try:
            for entry_point in metadata.entry_points(group="vibe_check.plugins"):  # type: ignore[call-arg]
                try:
                    plugin_class = entry_point.load()  # type: ignore[attr-defined]
                    plugin = plugin_class()
                    self._register_plugin(plugin)
                except Exception as e:
                    logger.error(f"Error loading plugin {entry_point.name}: {e}")  # type: ignore[attr-defined]
        except Exception as e:
            logger.error(f"Error discovering plugins from entry points: {e}")

    def _discover_from_directory(self, directory: Path) -> None:
        """
        Discover plugins from a directory.

        Args:
            directory: Directory to search for plugins
        """
        if not directory.exists() or not directory.is_dir():
            logger.warning(f"Plugin directory does not exist: {directory}")
            return

        # Add directory to sys.path if not already there
        dir_str = str(directory.absolute())
        if dir_str not in sys.path:
            sys.path.insert(0, dir_str)

        # Search for plugin modules
        for _, name, is_pkg in pkgutil.iter_modules([dir_str]):
            if is_pkg:
                # Try to load as a package
                try:
                    module = importlib.import_module(name)
                    self._scan_module_for_plugins(module)
                except Exception as e:
                    logger.error(f"Error loading plugin package {name}: {e}")
            else:
                # Try to load as a module
                try:
                    module = importlib.import_module(name)
                    self._scan_module_for_plugins(module)
                except Exception as e:
                    logger.error(f"Error loading plugin module {name}: {e}")

    def _scan_module_for_plugins(self, module: Any) -> None:
        """
        Scan a module for plugin classes.

        Args:
            module: Module to scan
        """
        module_path = getattr(module, "__file__", "")
        if module_path in self.loaded_modules:
            return

        self.loaded_modules.add(module_path)

        # Find all Plugin subclasses in the module
        for name in dir(module):
            try:
                item = getattr(module, name)
                if isinstance(item, type) and issubclass(item, Plugin) and item is not Plugin:
                    # Create an instance and register it
                    try:
                        plugin = item()  # type: ignore[call-arg]
                        self._register_plugin(plugin)
                    except Exception as e:
                        logger.error(f"Error instantiating plugin {name}: {e}")
            except Exception:
                # Ignore errors getting attributes
                pass

        # Find all plugin hooks
        for name in dir(module):
            try:
                item = getattr(module, name)
                if callable(item) and getattr(item, "_vibe_check_plugin_hook", False):
                    plugin_type = getattr(item, "_vibe_check_plugin_type", "unknown")
                    if plugin_type not in self.hooks:
                        self.hooks[plugin_type] = []
                    self.hooks[plugin_type].append(item)
            except Exception:
                # Ignore errors getting attributes
                pass

    def _register_plugin(self, plugin: Plugin) -> None:
        """
        Register a plugin.

        Args:
            plugin: Plugin instance
        """
        name = plugin.name
        if name in self.plugins:
            logger.warning(f"Plugin {name} already registered, ignoring duplicate")
            return

        # Get plugin type
        plugin_type = self._get_plugin_type(plugin)
        if plugin_type not in self.plugin_types:
            self.plugin_types[plugin_type] = []

        self.plugins[name] = plugin
        self.plugin_types[plugin_type].append(name)

        logger.info(f"Registered plugin {name} (type: {plugin_type})")

    def _get_plugin_type(self, plugin: Plugin) -> str:
        """
        Get the type of a plugin.

        Args:
            plugin: Plugin instance

        Returns:
            Plugin type name
        """
        # Check plugin class hierarchy
        for cls in plugin.__class__.__mro__:
            if cls.__name__ == "Plugin":
                continue
            if issubclass(cls, Plugin):
                return cls.__name__.replace("Plugin", "").lower()

        return "unknown"

    def initialize_plugins(self, actor_system=None) -> None:  # type: ignore[no-untyped-def]
        """
        Initialize all registered plugins.

        Args:
            actor_system: Optional actor system to pass to plugins
        """
        # Check if plugins have already been discovered
        if not self.plugins:
            self.discover_plugins()

        # Initialize plugins in dependency order
        remaining = set(self.plugins.keys())
        initialized: Set[str] = set()

        while remaining:
            progress = False

            for name in list(remaining):
                plugin = self.plugins[name]
                dependencies = set(plugin.dependencies)

                # Skip if dependencies aren't satisfied
                if dependencies - initialized:
                    continue

                # Initialize plugin
                try:
                    if actor_system is not None:
                        plugin.initialize(actor_system)
                    else:
                        plugin_config = self.config.get(name, {})
                        plugin.initialize(plugin_config)
                    logger.info(f"Initialized plugin {name}")
                    initialized.add(name)
                    remaining.remove(name)
                    progress = True
                except Exception as e:
                    logger.error(f"Error initializing plugin {name}: {e}")
                    remaining.remove(name)
                    progress = True

            # Check for circular dependencies
            if not progress and remaining:
                logger.error(f"Circular dependencies detected among plugins: {remaining}")
                break

    def get_plugin(self, name: str) -> Optional[Plugin]:
        """
        Get a plugin by name.

        Args:
            name: Plugin name

        Returns:
            Plugin instance or None if not found
        """
        return self.plugins.get(name)

    def get_plugins_by_type(self, plugin_type: str) -> List[Plugin]:
        """
        Get all plugins of a given type.

        Args:
            plugin_type: Plugin type

        Returns:
            List of plugin instances
        """
        if plugin_type not in self.plugin_types:
            return []

        return [self.plugins[name] for name in self.plugin_types[plugin_type]]

    def get_typed_plugins(self, plugin_class: Type[T]) -> List[T]:
        """
        Get all plugins of a given class type.

        Args:
            plugin_class: Plugin class

        Returns:
            List of plugin instances
        """
        plugin_type = plugin_class.__name__.replace("Plugin", "").lower()
        plugins = self.get_plugins_by_type(plugin_type)
        return [p for p in plugins if isinstance(p, plugin_class)]

    def list_plugins(self) -> List[Plugin]:
        """
        List all registered plugins.

        Returns:
            List of plugin instances
        """
        return list(self.plugins.values())

    def load_plugin(self, name: str) -> Optional[Plugin]:
        """
        Load a plugin by name.

        Args:
            name: Plugin name

        Returns:
            Plugin instance or None if not found
        """
        # Check if plugin is already loaded
        if name in self.plugins:
            return self.plugins[name]

        # Try to import the plugin module
        try:
            module = importlib.import_module(name)

            # Check if the module has a get_plugin function
            if not hasattr(module, "get_plugin") or not callable(module.get_plugin):
                logger.error(f"Plugin module {name} does not have a get_plugin function")
                return None

            # Get the plugin instance
            plugin = module.get_plugin()

            # Check if the plugin is a valid Plugin instance
            if not isinstance(plugin, Plugin):
                logger.error(f"Plugin module {name} returned an invalid plugin object")
                return None

            # Register the plugin
            self._register_plugin(plugin)
            return plugin

        except ImportError as e:
            logger.error(f"Failed to import plugin module {name}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error loading plugin {name}: {e}")
            return None

    def install_plugin(self, name: str) -> bool:
        """
        Install a plugin.

        Args:
            name: Plugin name

        Returns:
            True if the plugin was installed successfully, False otherwise
        """
        try:
            # Import pip
            pip = importlib.import_module("pip")

            # Install the plugin
            result = pip.main(["install", name])

            # Check if the installation was successful
            if result == 0:
                # Load the plugin
                plugin = self.load_plugin(name)
                return plugin is not None

            return False

        except Exception as e:
            logger.error(f"Error installing plugin {name}: {e}")
            return False

    def uninstall_plugin(self, name: str) -> bool:
        """
        Uninstall a plugin.

        Args:
            name: Plugin name

        Returns:
            True if the plugin was uninstalled successfully, False otherwise
        """
        try:
            # Import pip
            pip = importlib.import_module("pip")

            # Uninstall the plugin
            result = pip.main(["uninstall", "-y", name])

            # Check if the uninstallation was successful
            if result == 0:
                # Remove the plugin from the registry
                if name in self.plugins:
                    del self.plugins[name]

                    # Remove from plugin types
                    for plugin_type, plugins in self.plugin_types.items():
                        if name in plugins:
                            plugins.remove(name)

                return True

            return False

        except Exception as e:
            logger.error(f"Error uninstalling plugin {name}: {e}")
            return False

    def register_tools(self) -> List[str]:
        """
        Register tools from all plugins.

        Returns:
            List of registered tool names
        """
        tools = []

        for name, plugin in self.plugins.items():
            try:
                plugin_tools = plugin.register_tools()
                tools.extend(plugin_tools)
            except Exception as e:
                logger.error(f"Error registering tools from plugin {name}: {e}")

        return tools

    def call_hooks(self, hook_type: str, *args: Any, **kwargs: Any) -> List[Any]:
        """
        Call all hooks of a given type.

        Args:
            hook_type: Hook type
            *args: Positional arguments to pass to hooks
            **kwargs: Keyword arguments to pass to hooks

        Returns:
            List of hook results
        """
        results = []

        if hook_type in self.hooks:
            for hook in self.hooks[hook_type]:
                try:
                    result = hook(*args, **kwargs)
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error calling hook {hook.__name__}: {e}")

        return results

    def shutdown(self) -> None:
        """Shut down all plugins."""
        for name, plugin in self.plugins.items():
            try:
                plugin.shutdown()
                logger.info(f"Shut down plugin {name}")
            except Exception as e:
                logger.error(f"Error shutting down plugin {name}: {e}")


# Global plugin manager instance
_plugin_manager = None


def get_plugin_manager() -> PluginManager:
    """
    Get the global plugin manager instance.

    Returns:
        PluginManager instance
    """
    global _plugin_manager
    if _plugin_manager is None:
        _plugin_manager = PluginManager()
        _plugin_manager.discover_plugins()
        _plugin_manager.initialize_plugins()
    return _plugin_manager


def list_plugins() -> List[Dict[str, Any]]:
    """
    List all available plugins.

    Returns:
        List of plugin information dictionaries
    """
    manager = get_plugin_manager()
    result = []

    for name, plugin in manager.plugins.items():
        result.append({
            "name": name,
            "type": manager._get_plugin_type(plugin),
            "description": plugin.description,
            "version": plugin.version,
            "enabled": plugin.enabled
        })

    return result


def load_plugin(name: str) -> Optional[Plugin]:
    """
    Load a plugin by name.

    Args:
        name: Plugin name

    Returns:
        Plugin instance or None if not found
    """
    return get_plugin_manager().get_plugin(name)


def register_plugin(plugin: Plugin) -> None:
    """
    Register a plugin.

    Args:
        plugin: Plugin instance
    """
    get_plugin_manager()._register_plugin(plugin)
