"""
Vibe Check Plugin System
==============

This module provides a plugin system for Vibe Check, enabling users to extend
the functionality of the tool with custom analyzers, runners, parsers,
and integrations.

Plugins follow a consistent interface and can be dynamically loaded
at runtime based on configuration.
"""

from .manager import Plugin<PERSON>anager, plugin_hook, list_plugins, load_plugin, register_plugin
from .plugin_base import Plugin, AnalyzerPlugin, ReporterPlugin, VisualizerPlugin

__all__ = [
    "PluginManager",
    "plugin_hook",
    "Plugin",
    "AnalyzerPlugin",
    "ReporterPlugin",
    "VisualizerPlugin",
    "list_plugins",
    "load_plugin",
    "register_plugin"
]
