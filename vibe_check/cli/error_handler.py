"""
Error Handler Module
=================

This module provides functions for handling errors in the CLI.
"""

import logging
import sys
from typing import Any, Dict, Optional

import click

logger = logging.getLogger("vibe_check_cli.error_handler")


def format_error_results(results: Dict[str, Any]) -> str:
    """
    Format error results for display.

    Args:
        results: Error results dictionary

    Returns:
        Formatted error string
    """
    output = []

    # Add error header
    output.append("=== Analysis Error ===")
    output.append(f"Error: {results.get('error', 'Unknown error')}")

    # Add error details if available
    if "error_details" in results:
        output.append("\nError Details:")
        output.append(results["error_details"])

    # Add suggestions for fixing the error
    output.append("\nSuggestions:")

    # Check for specific error types
    error_msg = results.get("error", "").lower()

    if "no actors were successfully initialized" in error_msg:
        output.append("- The actor system failed to initialize. Try the following:")
        output.append("  * Use the --use-simple-analyzer flag to bypass the actor system")
        output.append("  * Run with --debug-actor-system to get more detailed logs")
        output.append("  * Check the log file for more details")
    elif "timeout" in error_msg:
        output.append("- The operation timed out. Try the following:")
        output.append("  * Increase the timeout in the configuration")
        output.append("  * Use the --use-simple-analyzer flag for smaller projects")
        output.append("  * Run with fewer parallel actors")
    elif "registry" in error_msg:
        output.append("- There was an issue with the actor registry. Try the following:")
        output.append("  * Restart the application")
        output.append("  * Use the --use-simple-analyzer flag to bypass the actor system")
    else:
        output.append("- Try using the --use-simple-analyzer flag to bypass the actor system")
        output.append("- Run with --debug or --debug-actor-system for more detailed logs")
        output.append("- Check the log file for more details")

    # Add diagnostics information if available
    if "diagnostics" in results:
        output.append("\nDiagnostics Information:")
        for key, value in results["diagnostics"].items():
            output.append(f"- {key}: {value}")

    # Add visualizations information if available
    if "visualizations" in results:
        output.append("\nVisualizations:")
        for key, value in results["visualizations"].items():
            output.append(f"- {key}: {value}")

    return "\n".join(output)


def handle_analysis_error(results: Dict[str, Any]) -> None:
    """
    Handle analysis error.

    Args:
        results: Error results dictionary
    """
    # Format and display the error
    formatted_error = format_error_results(results)
    click.echo(formatted_error)

    # Log the error
    logger.error(f"Analysis failed: {results.get('error', 'Unknown error')}")

    # Check for specific error types
    error_msg = results.get("error", "").lower()

    if "no actors were successfully initialized" in error_msg:
        logger.error("Actor system initialization failed. Recommend using --use-simple-analyzer flag.")
        click.echo("\nRecommendation: Use the --use-simple-analyzer flag to bypass the actor system.")
    elif "timeout" in error_msg:
        logger.error("Operation timed out. Recommend increasing timeout or using simple analyzer.")
        click.echo("\nRecommendation: Use the --use-simple-analyzer flag for smaller projects.")

    # Exit with error code
    sys.exit(1)
