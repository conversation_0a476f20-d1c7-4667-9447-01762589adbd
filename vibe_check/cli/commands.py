"""
CLI Commands
=========

This module provides the command functions for the CLI.
"""

import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from ..core.config import load_config
from vibe_check import analyze_project
from ..plugins.manager import list_plugins


def analyze_command(project_path: str,
                   config_path: Optional[str] = None,
                   output_dir: Optional[str] = None,
                   verbose: bool = False,
                   quiet: bool = False,
                   config_override: Optional[Dict[str, Any]] = None,
                   analyze_trends: bool = False,
                   report_progress: bool = False,
                   use_simple_analyzer: bool = False,
                   **kwargs: Any) -> Dict[str, Any]:
    """
    Run the analyze command.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to a configuration file
        output_dir: Optional directory to write output files to
        verbose: Whether to enable verbose output
        quiet: Whether to suppress output
        config_override: Optional dictionary to override configuration values
        analyze_trends: Whether to analyze trends compared to previous runs
        report_progress: Whether to report progress between analyses with the same output directory
        use_simple_analyzer: Whether to use the simple analyzer instead of the actor system
        **kwargs: Additional keyword arguments

    Returns:
        Analysis results
    """
    import logging
    import traceback
    import time
    from pathlib import Path

    # Set up a logger for this function
    logger = logging.getLogger("vibe_check_cli.analyze_command")

    try:
        start_time = time.time()
        logger.debug("Starting analyze_command")
        logger.debug(f"Project path: {project_path}")
        logger.debug(f"Config path: {config_path}")
        logger.debug(f"Output directory: {output_dir}")
        logger.debug(f"Verbose: {verbose}, Quiet: {quiet}")
        logger.debug(f"Config override: {config_override}")
        logger.debug(f"Analyze trends: {analyze_trends}, Report progress: {report_progress}")
        logger.debug(f"Use simple analyzer: {use_simple_analyzer}")
        logger.debug(f"Additional kwargs: {kwargs}")

        # Set up logging
        if verbose:
            from ..core.logging import setup_logging
            logger.debug("Setting up logging with debug=True")
            setup_logging(debug=True)
        elif quiet:
            from ..core.logging import setup_logging
            logger.debug("Setting up logging with quiet=True")
            setup_logging(quiet=True)

        # Validate project path
        import os
        if not os.path.exists(project_path):
            logger.error(f"Project path does not exist: {project_path}")
            return {"error": f"Project path does not exist: {project_path}"}

        if not os.path.isdir(project_path):
            logger.error(f"Project path is not a directory: {project_path}")
            return {"error": f"Project path is not a directory: {project_path}"}

        # Create output directory if it doesn't exist
        if output_dir and not os.path.exists(output_dir):
            logger.debug(f"Creating output directory: {output_dir}")
            os.makedirs(output_dir, exist_ok=True)

        # Load configuration if specified
        config = None
        if config_path:
            from ..core.config import load_config
            logger.debug(f"Loading configuration from {config_path}")
            config = load_config(config_path)
            logger.debug(f"Loaded configuration: {config}")

        # Apply preset if specified
        if config_override and "preset" in config_override:
            preset_name = config_override["preset"]
            logger.debug(f"Applying preset: {preset_name}")

            try:
                from ..core.config import load_preset
                preset_config = load_preset(preset_name)
                logger.debug(f"Loaded preset configuration: {preset_config}")

                # Merge preset with existing config
                if config is None:
                    config = preset_config
                else:
                    # Deep merge the configurations
                    from ..core.utils.dict_utils import deep_merge
                    config = deep_merge(config, preset_config)
                    logger.debug(f"Merged configuration: {config}")
            except Exception as e:
                logger.error(f"Error loading preset {preset_name}: {e}")
                logger.error(traceback.format_exc())
                return {"error": f"Error loading preset {preset_name}: {e}", "error_details": traceback.format_exc()}

        # Apply other config overrides
        if config_override:
            if config is None:
                config = {}

            # Remove preset key as it's already been processed
            config_override_copy = config_override.copy()
            if "preset" in config_override_copy:
                del config_override_copy["preset"]

            # Apply remaining overrides
            if config_override_copy:
                from ..core.utils.dict_utils import deep_merge
                config = deep_merge(config, config_override_copy)
                logger.debug(f"Applied config overrides: {config}")

        # Use simple analyzer if requested
        if use_simple_analyzer or kwargs.get("context", {}).get("use_simple_analyzer", False):
            logger.info("Using simple analyzer instead of actor system")

            try:
                from ..core.simple_analyzer import simple_analyze_project

                # Run the simple analyzer
                metrics = simple_analyze_project(
                    project_path=project_path,
                    output_dir=output_dir,
                    config=config
                )

                end_time = time.time()
                logger.info(f"Analysis completed in {end_time - start_time:.2f} seconds")

                # Convert metrics to dictionary if needed
                if not isinstance(metrics, dict):
                    # Try to convert to dictionary
                    try:
                        metrics_dict = metrics.__dict__ if hasattr(metrics, '__dict__') else {"metrics": metrics}
                        return metrics_dict
                    except Exception as e:
                        logger.warning(f"Could not convert metrics to dictionary: {e}")
                        # Return as is and let the formatter handle it
                        return {"metrics": metrics}
                return metrics
            except Exception as e:
                logger.error(f"Error in simple_analyze_project: {e}")
                logger.error(traceback.format_exc())
                return {"error": f"Error in simple analysis: {e}", "error_details": traceback.format_exc()}
        else:
            # Run the analysis using the actor system
            logger.debug("Using actor system for analysis")
            logger.debug("Calling analyze_project")

            try:
                from vibe_check import analyze_project

                # Set up logging for the actor system
                import logging
                actor_logger = logging.getLogger("vibe_check_actor_system")

                # Check for enhanced logging options
                debug = kwargs.get("debug", False)
                debug_actor_system = kwargs.get("debug_actor_system", False)
                log_file = kwargs.get("log_file", None)
                save_diagnostics = kwargs.get("save_diagnostics", False)
                visualize_actor_system = kwargs.get("visualize_actor_system", False)

                # Set up enhanced logging if available
                try:
                    from vibe_check.core.actor_system.logging import (
                        setup_actor_system_logging,
                        enable_debug_mode,
                        set_log_file,
                        save_logs_to_file,
                        visualize_actor_system as visualize_actor_system_func
                    )

                    # Configure enhanced logging
                    if debug or debug_actor_system or verbose:
                        logger.info("Setting up enhanced actor system logging with debug mode")
                        setup_actor_system_logging(
                            log_level=logging.DEBUG,
                            log_file=log_file,
                            debug_mode=True
                        )
                        enable_debug_mode()
                        actor_logger.setLevel(logging.DEBUG)
                    else:
                        setup_actor_system_logging(
                            log_level=logging.INFO,
                            log_file=log_file,
                            debug_mode=False
                        )

                    # Set log file if specified
                    if log_file:
                        logger.info(f"Setting actor system log file to {log_file}")
                        set_log_file(log_file)

                    logger.info("Enhanced actor system logging configured successfully")
                except ImportError:
                    logger.warning("Enhanced actor system logging not available")
                    # Fall back to basic logging
                    if verbose or debug or debug_actor_system:
                        actor_logger.setLevel(logging.DEBUG)

                # Initialize the actor system diagnostics
                try:
                    # Try to import the diagnostics module
                    try:
                        from vibe_check.core.actor_system.diagnostics import initialize_tracker
                        tracker = initialize_tracker()
                        logger.info("Initialized actor system diagnostics tracker")
                    except (ImportError, AttributeError):
                        # Fall back to direct initialization if the function is not available
                        from vibe_check.core.actor_system.diagnostics import InitializationTracker
                        tracker = InitializationTracker()
                        logger.info("Initialized actor system diagnostics tracker (fallback method)")
                except Exception as e:
                    logger.warning(f"Could not initialize actor system diagnostics: {e}")

                # Run the analysis with the actor system
                result = analyze_project(
                    project_path=project_path,
                    config_path=config_path,
                    output_dir=output_dir,
                    config_override=config_override,
                    show_progress=not quiet,
                    analyze_trends=analyze_trends,
                    report_progress=report_progress,
                    **kwargs
                )

                end_time = time.time()
                logger.info(f"Analysis completed in {end_time - start_time:.2f} seconds")
                logger.debug(f"analyze_project returned: {result}")

                # Save diagnostics if requested
                if save_diagnostics and 'visualize_actor_system_func' in locals():
                    try:
                        # Create diagnostics directory
                        diagnostics_dir = os.path.join(output_dir or ".", "diagnostics")
                        os.makedirs(diagnostics_dir, exist_ok=True)

                        # Save logs to file
                        logger.info(f"Saving actor system logs to {diagnostics_dir}")
                        log_file_path = save_logs_to_file(diagnostics_dir)
                        logger.info(f"Actor system logs saved to {log_file_path}")

                        # Add to result
                        if isinstance(result, dict):
                            result.setdefault("diagnostics", {})
                            result["diagnostics"]["log_file"] = str(log_file_path)
                    except Exception as e:
                        logger.error(f"Error saving diagnostics: {e}")
                        logger.error(traceback.format_exc())

                # Visualize actor system if requested
                if visualize_actor_system and 'visualize_actor_system_func' in locals():
                    try:
                        # Create visualization directory
                        viz_dir = os.path.join(output_dir or ".", "visualizations")
                        os.makedirs(viz_dir, exist_ok=True)

                        # Generate visualization
                        logger.info(f"Generating actor system visualization in {viz_dir}")
                        viz_file_path = visualize_actor_system_func(viz_dir)

                        if viz_file_path:
                            logger.info(f"Actor system visualization saved to {viz_file_path}")

                            # Add to result
                            if isinstance(result, dict):
                                result.setdefault("visualizations", {})
                                result["visualizations"]["actor_system"] = str(viz_file_path)
                        else:
                            logger.warning("Actor system visualization could not be generated")
                    except Exception as e:
                        logger.error(f"Error visualizing actor system: {e}")
                        logger.error(traceback.format_exc())

                # Convert result to dictionary if needed
                if not isinstance(result, dict):
                    # Try to convert to dictionary
                    try:
                        result_dict = result.__dict__ if hasattr(result, '__dict__') else {"result": result}
                        return result_dict
                    except Exception as e:
                        logger.warning(f"Could not convert result to dictionary: {e}")
                        # Return as is and let the formatter handle it
                        return {"result": result}
                return result
            except Exception as e:
                logger.error(f"Error in analyze_project: {e}")
                logger.error(traceback.format_exc())
                return {"error": f"Error in actor system analysis: {e}", "error_details": traceback.format_exc()}

    except Exception as e:
        logger.error(f"Error in analyze_command: {e}")
        logger.error(traceback.format_exc())
        return {"error": str(e), "error_details": traceback.format_exc()}


def tui_command(project_path: str,
               config_path: Optional[str] = None) -> None:
    """
    Run the TUI command.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to a configuration file
    """
    try:
        from ..ui.tui import run_tui
        run_tui(project_path=project_path, config_path=config_path)
    except ImportError:
        print("TUI is not available. Please install the required dependencies.")
        print("pip install vibe_check[tui]")
        sys.exit(1)


def web_command(project_path: str,
               config_path: Optional[str] = None,
               host: str = "localhost",
               port: int = 8000) -> None:
    """
    Run the web command.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to a configuration file
        host: Host to bind to
        port: Port to bind to
    """
    try:
        from ..ui.web import run_web_server
        run_web_server(project_path, config_path, host, port)
    except ImportError:
        print("Web UI is not available. Please install the required dependencies.")
        print("pip install vibe_check[web]")
        sys.exit(1)


def debug_command(project_path: str,
               output_dir: Optional[str] = None,
               verbose: bool = False,
               timeout: float = 120.0) -> Dict[str, Any]:
    """
    Run the debug command for the actor system.

    This command enables comprehensive debugging for the actor system,
    including detailed logging, diagnostic tracing, and error trapping.
    It helps diagnose and resolve issues with the actor system initialization.

    Args:
        project_path: Path to the project to analyze
        output_dir: Optional directory to write output files to
        verbose: Whether to enable verbose output
        timeout: Maximum time in seconds to wait for initialization

    Returns:
        Dictionary with debugging results
    """
    import logging
    import traceback
    import time
    import asyncio
    from pathlib import Path

    # Set up a logger for this function
    logger = logging.getLogger("vibe_check_cli.debug_command")

    try:
        start_time = time.time()
        logger.info("Starting debug_command")
        logger.info(f"Project path: {project_path}")
        logger.info(f"Output directory: {output_dir}")
        logger.info(f"Verbose: {verbose}")
        logger.info(f"Timeout: {timeout}")

        # Create output directory if it doesn't exist
        if output_dir:
            output_dir_path = Path(output_dir)
            output_dir_path.mkdir(parents=True, exist_ok=True)
        else:
            # Use default output directory
            output_dir_path = Path("vibe_check_debug")
            output_dir_path.mkdir(parents=True, exist_ok=True)
            output_dir = str(output_dir_path)

        # Import the debugging utilities
        try:
            from ..core.actor_system.debug_utils import ActorSystemDebugger
            from ..core.actor_system.logging.initialization_debug import enable_init_debugging
        except ImportError as e:
            logger.error(f"Error importing debugging utilities: {e}")
            return {
                "error": f"Debugging utilities not available: {e}",
                "error_details": traceback.format_exc()
            }

        # Create the debugger
        debugger = ActorSystemDebugger(output_dir=output_dir)

        # Enable debugging
        log_level = logging.DEBUG if verbose else logging.INFO
        debugger.enable_debugging(log_level=log_level)

        # Enable initialization debugging
        enable_init_debugging(log_level=log_level, log_file=debugger.log_file)

        # Run a minimal analysis to trigger the actor system
        logger.info("Running minimal analysis to debug the actor system")

        # Use the simple analyzer first to get basic project information
        try:
            from ..core.simple_analyzer import simple_analyze_project

            # Run a quick analysis to get project structure
            logger.info("Running quick analysis with simple analyzer")
            simple_result = simple_analyze_project(
                project_path=project_path,
                output_dir=None,  # Don't generate output files
                config={"minimal": True}  # Use minimal configuration
            )

            logger.info(f"Quick analysis completed: {len(simple_result.files)} files found")
        except Exception as e:
            logger.warning(f"Error in simple analysis: {e}")
            logger.warning("Continuing with debugging anyway")

        # Now try to run the actor system with debugging enabled
        logger.info("Initializing actor system with debugging enabled")

        # Run the analysis asynchronously
        async def run_debug_analysis() -> Dict[str, Any]:
            try:
                # Collect initial system state
                logger.info("Collecting initial system state")
                initial_state = await debugger.collect_system_state()

                # Try to initialize the actor system
                logger.info("Initializing actor system")
                from ..core.actor_system.consolidated_initializer import get_initializer

                # Get the consolidated initializer
                initializer = get_initializer()

                # Initialize the system by registering and initializing actors
                logger.info("Initializing the actor system")

                # Get the actor registry
                from ..core.actor_system.actor_registry import get_registry
                registry = get_registry()

                # Register and initialize all actors
                for actor in registry.get_all_actors():
                    actor_id = actor.actor_id
                    logger.info(f"Registering actor {actor_id}")
                    await initializer.register_actor(actor_id=actor_id, actor_type=actor.__class__.__name__)
                    logger.info(f"Initializing actor {actor_id}")
                    await initializer.initialize_actor(actor)

                # Collect system state after initialization
                logger.info("Collecting system state after initialization")
                post_init_state = await debugger.collect_system_state()

                # Check for deadlocks
                logger.info("Checking for deadlocks")
                deadlocks = await debugger.check_for_deadlocks()

                # Analyze initialization issues
                logger.info("Analyzing initialization issues")
                issues = await debugger.analyze_initialization_issues()

                # Analyze timeout issues
                logger.info("Analyzing timeout issues")
                timeout_analysis = await debugger.analyze_timeout_issues()

                # Analyze dependency resolution
                logger.info("Analyzing dependency resolution")
                dependency_analysis = await debugger.analyze_dependency_resolution()

                # Analyze registry synchronization
                logger.info("Analyzing registry synchronization")
                registry_sync_analysis = await debugger.analyze_registry_synchronization()

                # Generate initialization timeline visualization
                logger.info("Generating initialization timeline visualization")
                timeline_path = await debugger.generate_initialization_timeline()

                # Generate dependency graph visualization
                logger.info("Generating dependency graph visualization")
                dependency_graph_path = await debugger.generate_dependency_graph()

                # Generate registry visualization
                logger.info("Generating registry visualization")
                registry_viz_path = await debugger.generate_registry_visualization()

                # Generate registry synchronization visualization
                logger.info("Generating registry synchronization visualization")
                registry_sync_viz_path = await debugger.generate_registry_synchronization_visualization()

                # Generate suggestions
                logger.info("Generating suggestions")
                suggestions = await debugger.suggest_fixes(issues)

                # Generate debug report
                logger.info("Generating debug report")
                report_path = await debugger.generate_debug_report()

                # Return results
                return {
                    "success": True,
                    "initial_state": initial_state,
                    "post_init_state": post_init_state,
                    "deadlocks": deadlocks,
                    "issues": issues,
                    "timeout_analysis": timeout_analysis,
                    "dependency_analysis": dependency_analysis,
                    "registry_sync_analysis": registry_sync_analysis,
                    "suggestions": suggestions,
                    "report_path": report_path,
                    "timeline_path": timeline_path,
                    "dependency_graph_path": dependency_graph_path,
                    "registry_viz_path": registry_viz_path,
                    "registry_sync_viz_path": registry_sync_viz_path
                }
            except Exception as e:
                logger.error(f"Error in debug analysis: {e}")
                logger.error(traceback.format_exc())

                # Try to generate a report anyway
                try:
                    report_path = await debugger.generate_debug_report()
                    return {
                        "success": False,
                        "error": str(e),
                        "error_details": traceback.format_exc(),
                        "report_path": report_path
                    }
                except Exception as report_error:
                    logger.error(f"Error generating debug report: {report_error}")
                    return {
                        "success": False,
                        "error": str(e),
                        "error_details": traceback.format_exc()
                    }

        # Run the debug analysis with a timeout
        try:
            logger.info(f"Running debug analysis with timeout {timeout}s")
            result = asyncio.run(asyncio.wait_for(run_debug_analysis(), timeout=timeout))

            # Add timing information
            end_time = time.time()
            result["duration"] = end_time - start_time

            logger.info(f"Debug analysis completed in {end_time - start_time:.2f} seconds")
            return result
        except asyncio.TimeoutError:
            logger.error(f"Timeout in debug analysis after {timeout} seconds")

            # Try to generate a report anyway
            try:
                report_path = asyncio.run(debugger.generate_debug_report())
                return {
                    "success": False,
                    "error": f"Timeout after {timeout} seconds",
                    "report_path": report_path
                }
            except Exception as report_error:
                logger.error(f"Error generating debug report: {report_error}")
                return {
                    "success": False,
                    "error": f"Timeout after {timeout} seconds"
                }
    except Exception as e:
        logger.error(f"Error in debug_command: {e}")
        logger.error(traceback.format_exc())
        return {"error": str(e), "error_details": traceback.format_exc()}


def plugin_command(action: str, plugin_name: Optional[str] = None) -> None:
    """
    Run the plugin command.

    Args:
        action: Action to perform (list, install, uninstall)
        plugin_name: Name of the plugin
    """
    if action == "list":
        plugins = list_plugins()
        if not plugins:
            print("No plugins installed.")
            return

        print("Installed plugins:")
        for plugin in plugins:
            print(f"  {plugin}")
    elif action == "install":
        if not plugin_name:
            print("Please specify a plugin name.")
            sys.exit(1)

        try:
            from ..plugins.manager import install_plugin
            install_plugin(plugin_name)
            print(f"Plugin {plugin_name} installed successfully.")
        except Exception as e:
            print(f"Failed to install plugin {plugin_name}: {e}")
            sys.exit(1)
    elif action == "uninstall":
        if not plugin_name:
            print("Please specify a plugin name.")
            sys.exit(1)

        try:
            from ..plugins.manager import uninstall_plugin
            uninstall_plugin(plugin_name)
            print(f"Plugin {plugin_name} uninstalled successfully.")
        except Exception as e:
            print(f"Failed to uninstall plugin {plugin_name}: {e}")
            sys.exit(1)
    else:
        print(f"Unknown action: {action}")
        print("Available actions: list, install, uninstall")
        sys.exit(1)
