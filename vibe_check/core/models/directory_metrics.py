"""
Directory Metrics Model
=====================

This module defines the DirectoryMetrics class, which stores analysis results
for a directory in the project.
"""

from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

from .file_metrics import FileMetrics


@dataclass
class DirectoryMetrics:
    """
    Metrics for a directory.

    This is a core domain entity representing analysis results for
    an entire directory in the project.

    Attributes:
        path: Directory path (relative to project root)
        files: List of files in the directory
        total_lines: Total number of lines across all files
        avg_lines: Average number of lines per file
        max_file_lines: Maximum number of lines in any file
        max_file: Path of the file with the most lines
        description: Brief description of the directory's purpose
        doc_files_count: Number of documentation files
        total_doc_size: Total size of documentation files in bytes
        avg_doc_quality: Average documentation quality score (0-100)
        line_count: Total number of lines across all files (alias for total_lines)
        avg_complexity: Average complexity score across all files
        max_complexity: Maximum complexity score across all files
        issue_count: Total number of issues across all files
    """
    path: str
    files: List[str] = field(default_factory=list)
    total_lines: int = 0
    avg_lines: float = 0.0
    max_file_lines: int = 0
    max_file: str = ""
    description: str = "No description available"

    # Documentation-specific metrics
    doc_files_count: int = 0
    total_doc_size: int = 0
    avg_doc_quality: float = 0.0

    # Directory hierarchy relationships
    parent_dir: str = ""
    child_directories: List[str] = field(default_factory=list)

    # Type and docstring coverage
    type_coverage: float = 0.0
    docstring_coverage: float = 0.0

    # Metrics for test compatibility
    _file_metrics: List[FileMetrics] = field(default_factory=list)
    _avg_complexity: float = 0.0
    _max_complexity: int = 0
    _issue_count: int = 0

    @property
    def file_count(self) -> int:
        """Get the number of files in the directory."""
        return len(self.files)

    @property
    def line_count(self) -> int:
        """Get the total number of lines in the directory (alias for total_lines)."""
        return self.total_lines

    @property
    def avg_complexity(self) -> float:
        """Get the average complexity score across all files."""
        return self._avg_complexity

    @property
    def max_complexity(self) -> int:
        """Get the maximum complexity score across all files."""
        return self._max_complexity

    @property
    def issue_count(self) -> int:
        """Get the total number of issues across all files."""
        return self._issue_count

    @property
    def has_docs(self) -> bool:
        """Check if the directory has documentation files."""
        return self.doc_files_count > 0

    @property
    def basename(self) -> str:
        """Get the base name of the directory."""
        return Path(self.path).name

    @property
    def parent_directory(self) -> str:
        """Get the parent directory."""
        if self.parent_dir:
            return self.parent_dir
        return str(Path(self.path).parent)

    def add_file_metrics(self, file_metrics: FileMetrics) -> None:
        """
        Add file metrics to the directory metrics.

        Args:
            file_metrics: FileMetrics object to add
        """
        self._file_metrics.append(file_metrics)

        # Update line count
        self.total_lines += file_metrics.lines

        # Update complexity metrics
        total_complexity = sum(fm.complexity for fm in self._file_metrics)
        self._avg_complexity = total_complexity / len(self._file_metrics) if self._file_metrics else 0
        self._max_complexity = max((fm.complexity for fm in self._file_metrics), default=0)

        # Update issue count
        self._issue_count = sum(len(fm.issues) for fm in self._file_metrics)

        # Update file list if not already present
        if file_metrics.path not in self.files:
            self.files.append(file_metrics.path)

        # Update max file lines
        if file_metrics.lines > self.max_file_lines:
            self.max_file_lines = file_metrics.lines
            self.max_file = file_metrics.path

        # Update documentation metrics
        if file_metrics.is_documentation:
            self.doc_files_count += 1
            self.total_doc_size += file_metrics.size

        # Recalculate averages
        if self.files:
            self.avg_lines = self.total_lines / len(self.files)

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the directory metrics to a dictionary.

        Returns:
            Dictionary representation of the directory metrics
        """
        return {
            "path": self.path,
            "file_count": self.file_count,
            "line_count": self.total_lines,
            "avg_lines": self.avg_lines,
            "max_file_lines": self.max_file_lines,
            "max_file": self.max_file,
            "description": self.description,
            "doc_files_count": self.doc_files_count,
            "total_doc_size": self.total_doc_size,
            "avg_doc_quality": self.avg_doc_quality,
            "avg_complexity": self._avg_complexity,
            "max_complexity": self._max_complexity,
            "issue_count": self._issue_count,
            "parent_dir": self.parent_dir,
            "child_directories": self.child_directories,
            "type_coverage": self.type_coverage,
            "docstring_coverage": self.docstring_coverage,
            "basename": self.basename
        }

    @classmethod
    def from_path(cls, path: str) -> 'DirectoryMetrics':
        """Create a DirectoryMetrics instance from a directory path."""
        # Initialize with just the path; other metrics will be calculated later
        return cls(path=path)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DirectoryMetrics':
        """
        Create a DirectoryMetrics instance from a dictionary.

        Args:
            data: Dictionary containing directory metrics data

        Returns:
            A new DirectoryMetrics instance
        """
        # Extract required path
        path = data.get("path", "")
        if not path:
            raise ValueError("DirectoryMetrics requires a path")

        # Create instance with all available fields
        metrics = cls(path=path)

        # Set basic metrics
        metrics.files = data.get("files", [])
        metrics.total_lines = data.get("line_count", data.get("total_lines", 0))
        metrics.avg_lines = data.get("avg_lines", 0.0)
        metrics.max_file_lines = data.get("max_file_lines", 0)
        metrics.max_file = data.get("max_file", "")
        metrics.description = data.get("description", "No description available")

        # Set documentation metrics
        metrics.doc_files_count = data.get("doc_files_count", 0)
        metrics.total_doc_size = data.get("total_doc_size", 0)
        metrics.avg_doc_quality = data.get("avg_doc_quality", 0.0)

        # Set directory hierarchy relationships
        metrics.parent_dir = data.get("parent_dir", "")
        metrics.child_directories = data.get("child_directories", [])

        # Set type and docstring coverage
        metrics.type_coverage = data.get("type_coverage", 0.0)
        metrics.docstring_coverage = data.get("docstring_coverage", 0.0)

        # Set metrics for test compatibility
        metrics._avg_complexity = data.get("avg_complexity", 0.0)
        metrics._max_complexity = data.get("max_complexity", 0)
        metrics._issue_count = data.get("issue_count", 0)

        return metrics

    def update_with_file_metrics(self, file_paths: List[str],
                                file_metrics_map: Dict[str, 'FileMetrics']) -> None:
        """
        Update directory metrics based on file metrics.

        Args:
            file_paths: List of file paths in this directory
            file_metrics_map: Dictionary mapping file paths to FileMetrics objects
        """
        self.files = file_paths

        # Calculate directory-level metrics from file metrics
        self.total_lines = 0
        self.max_file_lines = 0
        self.max_file = ""
        self.doc_files_count = 0
        self.total_doc_size = 0
        total_doc_quality = 0.0

        # Reset file metrics list
        self._file_metrics = []

        for file_path in file_paths:
            if file_path in file_metrics_map:
                file_metric = file_metrics_map[file_path]
                self._file_metrics.append(file_metric)

                # Update line counts
                self.total_lines += file_metric.lines
                if file_metric.lines > self.max_file_lines:
                    self.max_file_lines = file_metric.lines
                    self.max_file = file_metric.path

                # Update documentation metrics
                if file_metric.is_documentation:
                    self.doc_files_count += 1
                    self.total_doc_size += file_metric.size
                    total_doc_quality += file_metric.documentation_quality

        # Calculate averages
        if self.files:
            self.avg_lines = self.total_lines / len(self.files)

        if self.doc_files_count > 0:
            self.avg_doc_quality = total_doc_quality / self.doc_files_count

        # Update complexity metrics
        if self._file_metrics:
            total_complexity = sum(fm.complexity for fm in self._file_metrics)
            self._avg_complexity = total_complexity / len(self._file_metrics)
            self._max_complexity = max(fm.complexity for fm in self._file_metrics)

        # Update issue count
        self._issue_count = sum(len(fm.issues) for fm in self._file_metrics)

        # Calculate type and docstring coverage
        if self._file_metrics:
            self.type_coverage = sum(fm.type_coverage for fm in self._file_metrics) / len(self._file_metrics)
            self.docstring_coverage = sum(fm.docstring_coverage for fm in self._file_metrics) / len(self._file_metrics)