"""
Orchestrator Module
================

This module defines the Orchestrator class, which initializes and coordinates
the actor system for project analysis. It implements the CAW principle of
choreographed interactions by setting up communication paths between actors.

The orchestrator doesn't centrally control the flow; instead, it establishes
the initial conditions for emergent behavior among actors.

This version of the Orchestrator has been refactored to use a more modular
architecture, with components extracted into separate modules for better
maintainability and testability.
"""

import asyncio
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict, Optional, Set, Tuple, Union

from .actor_system import Actor, get_registry, reset_registry
from .orchestration import (
    ActorSystemBuilder,
    ActorConnector,
    ActorLifecycleManager,
    ExecutionModeManager,
    ExecutionMode
)
from .utils.config_utils import load_config

logger = logging.getLogger("vibe_check_orchestrator")


class Orchestrator:
    """
    Orchestrator for the Vibe Check analysis system.

    This class initializes and coordinates the actor system, setting up
    the communication paths between actors but not controlling their interactions.
    It implements the CAW principle of choreographed interactions.
    
    Implementation:
        This class has been refactored to use a more modular architecture, with
        components extracted into separate modules for better maintainability and
        testability. It now delegates to specialized components for actor system
        building, actor connection, actor lifecycle management, and execution mode
        management.
    """

    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """
        Initialize the orchestrator.

        Args:
            config_path: Optional path to a configuration file
        """
        # Load configuration
        self.config = load_config(config_path)

        # Initialize the actors
        self.actors: Dict[str, Actor] = {}
        self.project_actor = None
        self.report_actor = None
        self.visualization_actor = None
        self.project_path = None  # Will be set when analyze_project is called

        # Set default output directory
        self.output_dir = Path(self.config.get("output_dir", "vibe_check_output"))
        os.makedirs(self.output_dir, exist_ok=True)

        # Tracking for analysis progress
        self.analysis_task = None
        self.is_running = False

        # Create component managers
        self.execution_mode_manager = ExecutionModeManager(self.config)
        
        # These will be initialized when create_actor_system is called
        self.actor_system_builder = None
        self.actor_connector = None
        self.actor_lifecycle_manager = None

    def create_actor_system(self) -> Dict[str, Actor]:
        """
        Create the actor system with all necessary actors.

        Enhanced to use the actor registry and actor pool for better scalability
        and resilience. This method implements the CAW principle of choreographed
        interactions by setting up the initial conditions for emergent behavior.

        Returns:
            Dictionary of actor IDs to actor instances
        """
        try:
            # Create the actor system builder
            self.actor_system_builder = ActorSystemBuilder(
                self.config,
                self.output_dir,
                self.project_path
            )
            
            # Build the actor system
            self.actors = self.actor_system_builder.build()
            
            # Get references to important actors
            self.project_actor = self.actor_system_builder.project_actor
            self.report_actor = self.actor_system_builder.report_actor
            self.visualization_actor = self.actor_system_builder.visualization_actor
            
            # Create the actor connector
            self.actor_connector = ActorConnector(self.actors)
            
            # Connect all actors to each other
            self.actor_connector.connect_actors()
            
            # Create the actor lifecycle manager
            self.actor_lifecycle_manager = ActorLifecycleManager(self.actors, self.config)
            
            return self.actors
        except Exception as e:
            import traceback
            logger.error(f"Error creating actor system: {e}\n{traceback.format_exc()}")
            raise

    async def start_actors(self) -> None:
        """
        Start all actors in the actor system.

        Enhanced to implement a two-phase initialization process with explicit
        synchronization points to ensure all actors are properly initialized
        before they start communicating.

        Enhanced with robust error handling, cleanup on failure, and configurable
        fail-fast option.
        """
        if not self.actor_lifecycle_manager:
            raise RuntimeError("Actor lifecycle manager not initialized. Call create_actor_system first.")
            
        await self.actor_lifecycle_manager.start_actors()

    async def stop_actors(self) -> None:
        """
        Stop all actors in the actor system.

        Enhanced with robust error handling and cleanup.
        """
        if not self.actor_lifecycle_manager:
            logger.warning("Actor lifecycle manager not initialized. No actors to stop.")
            return
            
        await self.actor_lifecycle_manager.stop_actors()

    async def analyze_project(self, project_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Analyze a project using the actor system.

        Enhanced with adaptive execution mode, progress tracking, and error recovery.

        Args:
            project_path: Path to the project to analyze

        Returns:
            Dictionary with analysis results
        """
        # Set the project path
        self.project_path = Path(project_path)
        
        # Check if the project path exists
        if not self.project_path.exists():
            raise FileNotFoundError(f"Project path does not exist: {self.project_path}")
            
        # Check if we're already running
        if self.is_running:
            logger.warning("Analysis is already running. Please wait for it to complete.")
            return {"status": "already_running"}
            
        # Set running flag
        self.is_running = True
        
        try:
            # Create the actor system
            self.create_actor_system()
            
            # Start the actors
            await self.start_actors()
            
            # Create a context for the analysis
            context = self._create_analysis_context()
            
            # Start the analysis
            analysis_result = await self._run_analysis(context)
            
            return analysis_result
        except Exception as e:
            logger.error(f"Error analyzing project: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
            # Record the failure
            self.execution_mode_manager.record_failure("analysis_error")
            
            return {
                "status": "error",
                "error": str(e),
                "project_path": str(self.project_path)
            }
        finally:
            # Stop the actors
            await self.stop_actors()
            
            # Reset running flag
            self.is_running = False

    def _create_analysis_context(self) -> Dict[str, Any]:
        """
        Create a context for the analysis.

        Returns:
            Dictionary with analysis context
        """
        return {
            "project_path": str(self.project_path),
            "output_dir": str(self.output_dir),
            "execution_mode": self.execution_mode_manager.get_execution_mode(),
            "timestamp": time.time(),
            "config": self.config
        }

    async def _run_analysis(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Run the analysis using the actor system.

        Args:
            context: Analysis context

        Returns:
            Dictionary with analysis results
        """
        if not self.project_actor:
            raise RuntimeError("Project actor not initialized")
            
        # Create a future to wait for the analysis to complete
        analysis_complete = asyncio.Future()
        
        # Register a callback for when the analysis is complete
        async def on_analysis_complete(result: Dict[str, Any]) -> None:
            if not analysis_complete.done():
                analysis_complete.set_result(result)
                
        # Set the callback on the project actor
        self.project_actor.set_analysis_complete_callback(on_analysis_complete)
        
        # Start the analysis
        await self.project_actor.start_analysis(context)
        
        # Wait for the analysis to complete
        try:
            result = await asyncio.wait_for(analysis_complete, timeout=self.config.get("analysis_timeout", 3600))
            
            # Record success
            self.execution_mode_manager.record_success()
            
            return result
        except asyncio.TimeoutError:
            logger.error("Analysis timed out")
            
            # Record the failure
            self.execution_mode_manager.record_failure("analysis_timeout")
            
            return {
                "status": "timeout",
                "project_path": str(self.project_path),
                "execution_mode": self.execution_mode_manager.get_execution_mode()
            }
