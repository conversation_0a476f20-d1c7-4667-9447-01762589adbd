"""
Simple Analyzer Module
==================

This module provides a simplified version of the project analyzer that doesn't
rely on the actor system. It's designed to be used in environments where
the actor system might not work well, such as in Streamlit applications.

This implementation uses the shared analysis core to provide a linear execution
flow while maintaining consistency with the actor-based system.
"""

import logging
import os
import time
import asyncio
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union

from vibe_check.core.models import ProjectMetrics
from vibe_check.core.utils import generate_reports
from vibe_check.core.analysis import ProjectAnalyzer
from vibe_check.core.utils.async_utils import run_async

logger = logging.getLogger("vibe_check_simple_analyzer")


def simple_analyze_project(
    project_path: Union[str, Path],
    output_dir: Optional[Union[str, Path]] = None,
    config: Optional[Dict[str, Any]] = None
) -> ProjectMetrics:
    """
    Analyze a project using a simplified approach that doesn't rely on the actor system.

    This function uses the shared analysis core to execute the analysis linearly,
    processing each file and running each tool sequentially.

    Args:
        project_path: Path to the project directory
        output_dir: Optional directory to write output files to
        config: Optional configuration dictionary

    Returns:
        ProjectMetrics object with analysis results
    """
    logger.info(f"Starting simple analysis of project: {project_path}")
    start_time = time.time()

    # Create a project analyzer
    analyzer = ProjectAnalyzer(
        project_path=project_path,
        config=config,
        output_dir=output_dir
    )

    # Run the analysis
    metrics = run_async(analyzer.analyze_project)

    # Log completion
    end_time = time.time()
    logger.info(f"Analysis completed in {end_time - start_time:.2f} seconds")
    logger.info(f"Total files: {metrics.total_file_count}")
    logger.info(f"Total lines: {metrics.total_line_count}")
    logger.info(f"Average complexity: {metrics.avg_complexity:.2f}")
    logger.info(f"Total issues: {metrics.issue_count}")

    # Generate reports if output_dir is provided
    if output_dir:
        generate_reports(metrics, Path(output_dir))

    return metrics
