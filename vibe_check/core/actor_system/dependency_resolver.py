"""
Dependency Resolver Module
======================

This module provides tools for managing actor dependencies, including:
- Dependency graph construction and visualization
- Circular dependency detection
- Topological sorting for initialization order
- Dependency validation

These tools help ensure that actors are initialized in the correct order
and that circular dependencies are detected and prevented.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Set, Tuple, Any, Callable
import json
from pathlib import Path
import os

logger = logging.getLogger("vibe_check_dependency_resolver")


class CircularDependencyError(Exception):
    """Exception raised when a circular dependency is detected."""

    def __init__(self, cycle: List[str]):
        """
        Initialize the exception.

        Args:
            cycle: List of actor IDs forming the cycle
        """
        self.cycle = cycle
        cycle_str = " -> ".join(cycle)
        super().__init__(f"Circular dependency detected: {cycle_str}")


class DependencyResolver:
    """
    Manages actor dependencies and ensures proper initialization order.

    This class builds a dependency graph, detects circular dependencies,
    and determines the optimal initialization order for actors.
    """

    def __init__(self):
        """Initialize the dependency resolver."""
        self._dependencies: Dict[str, Set[str]] = {}
        self._reverse_dependencies: Dict[str, Set[str]] = {}
        self._lock = asyncio.Lock()
        self._initialization_order: List[str] = []
        self._critical_dependencies: Dict[str, Set[str]] = {}
        self._optional_dependencies: Dict[str, Set[str]] = {}

    async def register_dependency(
        self, 
        actor_id: str, 
        dependency_id: str, 
        critical: bool = True
    ) -> None:
        """
        Register a dependency between actors.

        Args:
            actor_id: ID of the dependent actor
            dependency_id: ID of the actor that must be ready first
            critical: Whether this is a critical dependency (required for operation)

        Raises:
            CircularDependencyError: If adding this dependency would create a cycle
        """
        async with self._lock:
            # Initialize sets if needed
            if actor_id not in self._dependencies:
                self._dependencies[actor_id] = set()
            if dependency_id not in self._reverse_dependencies:
                self._reverse_dependencies[dependency_id] = set()
            
            # Add dependency
            self._dependencies[actor_id].add(dependency_id)
            self._reverse_dependencies[dependency_id].add(actor_id)
            
            # Track critical/optional dependencies
            if critical:
                if actor_id not in self._critical_dependencies:
                    self._critical_dependencies[actor_id] = set()
                self._critical_dependencies[actor_id].add(dependency_id)
            else:
                if actor_id not in self._optional_dependencies:
                    self._optional_dependencies[actor_id] = set()
                self._optional_dependencies[actor_id].add(dependency_id)
            
            # Check for circular dependencies
            try:
                await self._check_circular_dependencies()
                # Recalculate initialization order
                await self._calculate_initialization_order()
                logger.info(f"Registered dependency: {actor_id} depends on {dependency_id} (critical={critical})")
            except CircularDependencyError as e:
                # Rollback the dependency addition
                self._dependencies[actor_id].remove(dependency_id)
                self._reverse_dependencies[dependency_id].remove(actor_id)
                if critical and actor_id in self._critical_dependencies:
                    self._critical_dependencies[actor_id].discard(dependency_id)
                elif not critical and actor_id in self._optional_dependencies:
                    self._optional_dependencies[actor_id].discard(dependency_id)
                
                logger.error(f"Rejected dependency {actor_id} -> {dependency_id}: {str(e)}")
                raise

    async def _check_circular_dependencies(self) -> None:
        """
        Check for circular dependencies in the graph.

        Raises:
            CircularDependencyError: If a circular dependency is detected
        """
        # Use depth-first search to detect cycles
        visited: Set[str] = set()
        path: List[str] = []
        path_set: Set[str] = set()
        
        async def dfs(node: str) -> None:
            if node in path_set:
                # Found a cycle
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                raise CircularDependencyError(cycle)
            
            if node in visited:
                return
            
            visited.add(node)
            path.append(node)
            path_set.add(node)
            
            for neighbor in self._dependencies.get(node, set()):
                await dfs(neighbor)
            
            path.pop()
            path_set.remove(node)
        
        # Check from each node
        for node in self._dependencies:
            if node not in visited:
                await dfs(node)

    async def _calculate_initialization_order(self) -> None:
        """
        Calculate the optimal initialization order for actors.

        This uses a topological sort to determine the order.
        """
        # Reset the order
        self._initialization_order = []
        
        # Create a copy of the dependencies for processing
        in_degree: Dict[str, int] = {}
        for actor_id in self._dependencies:
            in_degree[actor_id] = 0
        
        # Calculate in-degree for each node
        for actor_id, deps in self._dependencies.items():
            for dep_id in deps:
                if dep_id not in in_degree:
                    in_degree[dep_id] = 0
        
        for actor_id, deps in self._dependencies.items():
            for dep_id in deps:
                in_degree[actor_id] += 1
        
        # Start with nodes that have no dependencies
        queue = [actor_id for actor_id, degree in in_degree.items() if degree == 0]
        
        # Process the queue
        while queue:
            current = queue.pop(0)
            self._initialization_order.append(current)
            
            # Reduce in-degree of neighbors
            for neighbor in self._reverse_dependencies.get(current, set()):
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)
        
        # Check if we processed all nodes
        if len(self._initialization_order) != len(in_degree):
            logger.warning("Could not determine initialization order for all actors")

    async def get_initialization_order(self) -> List[str]:
        """
        Get the optimal initialization order for actors.

        Returns:
            List of actor IDs in initialization order
        """
        async with self._lock:
            return self._initialization_order.copy()

    async def get_dependencies(self, actor_id: str) -> Set[str]:
        """
        Get all dependencies for an actor.

        Args:
            actor_id: ID of the actor

        Returns:
            Set of dependency actor IDs
        """
        async with self._lock:
            return self._dependencies.get(actor_id, set()).copy()

    async def get_critical_dependencies(self, actor_id: str) -> Set[str]:
        """
        Get critical dependencies for an actor.

        Args:
            actor_id: ID of the actor

        Returns:
            Set of critical dependency actor IDs
        """
        async with self._lock:
            return self._critical_dependencies.get(actor_id, set()).copy()

    async def get_optional_dependencies(self, actor_id: str) -> Set[str]:
        """
        Get optional dependencies for an actor.

        Args:
            actor_id: ID of the actor

        Returns:
            Set of optional dependency actor IDs
        """
        async with self._lock:
            return self._optional_dependencies.get(actor_id, set()).copy()

    async def get_dependents(self, actor_id: str) -> Set[str]:
        """
        Get all actors that depend on this actor.

        Args:
            actor_id: ID of the actor

        Returns:
            Set of dependent actor IDs
        """
        async with self._lock:
            return self._reverse_dependencies.get(actor_id, set()).copy()

    async def export_dependency_graph(self, output_dir: str, filename: Optional[str] = None) -> Optional[str]:
        """
        Export the dependency graph to a JSON file.

        Args:
            output_dir: Directory to save the file
            filename: Optional filename to use

        Returns:
            Path to the saved file, or None if saving failed
        """
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)
            
            # Create a default filename if none provided
            if not filename:
                import time
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"dependency_graph_{timestamp}.json"
            
            filepath = Path(output_dir) / filename
            
            # Prepare data for serialization
            data = {
                "dependencies": {
                    actor_id: list(deps)
                    for actor_id, deps in self._dependencies.items()
                },
                "critical_dependencies": {
                    actor_id: list(deps)
                    for actor_id, deps in self._critical_dependencies.items()
                },
                "optional_dependencies": {
                    actor_id: list(deps)
                    for actor_id, deps in self._optional_dependencies.items()
                },
                "initialization_order": self._initialization_order
            }
            
            # Save to file
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Exported dependency graph to {filepath}")
            return str(filepath)
        
        except Exception as e:
            logger.error(f"Failed to export dependency graph: {e}")
            return None


# Singleton instance
_resolver = None


def get_resolver() -> DependencyResolver:
    """
    Get the singleton resolver instance.

    Returns:
        The dependency resolver instance
    """
    global _resolver
    
    if _resolver is None:
        _resolver = DependencyResolver()
        logger.info("Created new dependency resolver")
    
    return _resolver


def reset_resolver() -> None:
    """Reset the singleton resolver instance."""
    global _resolver
    
    _resolver = None
    logger.info("Reset dependency resolver")
