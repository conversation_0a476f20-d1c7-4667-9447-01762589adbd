"""
Debug Report Generator
===================

This module provides utilities for generating comprehensive debug reports
for the actor system, including initialization analysis, registry analysis,
and timeout analysis.
"""

import os
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Tuple, Union, cast

from .initialization_analyzer import InitializationAnalyzer
from .registry_analyzer import RegistryAnalyzer
from ..logging.initialization_debug import (
    get_init_log_buffer,
    save_init_debug_logs
)
from ..logging.registry_debug import (
    get_registry_operations
)

# Configure logging
logger = logging.getLogger("vibe_check_actor_system_debug.report_generator")


class DebugReportGenerator:
    """
    Utility class for generating debug reports.

    This class provides methods for:
    - Generating comprehensive debug reports
    - Saving debug logs
    - Creating specialized reports for specific issues
    """

    def __init__(self, output_dir: str) -> None:
        """
        Initialize the report generator.

        Args:
            output_dir: Directory to write output files to
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        self.initialization_analyzer = InitializationAnalyzer()
        self.registry_analyzer = RegistryAnalyzer()

    async def generate_comprehensive_report(self) -> str:
        """
        Generate a comprehensive debug report.

        This method creates a detailed report that includes:
        - Initialization analysis
        - Registry analysis
        - Timeout analysis
        - Recommendations

        Returns:
            Path to the generated report
        """
        # Create a timestamp for the report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = os.path.join(self.output_dir, f"actor_system_debug_report_{timestamp}.html")

        # Analyze initialization timing
        timing_analysis = await self.initialization_analyzer.analyze_initialization_timing()

        # Analyze initialization timeouts
        timeout_analysis = await self.initialization_analyzer.analyze_initialization_timeouts()

        # Analyze registry operations
        registry_analysis = await self.registry_analyzer.analyze_registry_operations()

        # Analyze registry synchronization
        sync_analysis = await self.registry_analyzer.analyze_registry_synchronization()

        # Check registry consistency
        consistency_results = await self.registry_analyzer.check_registry_consistency()

        # Generate recommendations
        recommendations = self.generate_recommendations(
            timing_analysis, timeout_analysis, registry_analysis, sync_analysis, consistency_results
        )

        # Create the report HTML
        html = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Actor System Debug Report - {timestamp}</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 20px;
                    border-radius: 5px;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                }}
                h1, h2, h3 {{
                    color: #333;
                }}
                .section {{
                    margin-bottom: 30px;
                    padding: 20px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                }}
                .grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
                    gap: 20px;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }}
                th, td {{
                    padding: 8px 12px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }}
                th {{
                    background-color: #f2f2f2;
                }}
                tr:hover {{
                    background-color: #f5f5f5;
                }}
                .issue {{
                    background-color: #fff0f0;
                }}
                .warning {{
                    color: #856404;
                    background-color: #fff3cd;
                    padding: 10px;
                    border-radius: 5px;
                }}
                .recommendation {{
                    background-color: #e8f4f8;
                    padding: 15px;
                    border-left: 5px solid #2196F3;
                    margin-bottom: 10px;
                }}
                .critical {{
                    background-color: #fde8e8;
                    border-left-color: #e53e3e;
                }}
                .important {{
                    background-color: #fef3c7;
                    border-left-color: #f59e0b;
                }}
                pre {{
                    background-color: #f5f5f5;
                    padding: 10px;
                    border-radius: 5px;
                    overflow-x: auto;
                }}
                .summary-box {{
                    background-color: #e8f5e9;
                    border-left: 5px solid #4caf50;
                    padding: 15px;
                    margin-bottom: 20px;
                }}
                .error-box {{
                    background-color: #ffebee;
                    border-left: 5px solid #f44336;
                    padding: 15px;
                    margin-bottom: 20px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Actor System Debug Report</h1>
                <p>Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
                
                <div class="section">
                    <h2>Executive Summary</h2>
                    <div class="summary-box">
                        <h3>Key Findings</h3>
                        <ul>
                            <li>Total Initialization Time: {timing_analysis.get("total_time", 0):.2f} seconds</li>
                            <li>Number of Actors: {timing_analysis.get("total_actors", 0)}</li>
                            <li>Number of Timeouts: {timeout_analysis.get("timeout_count", 0)}</li>
                            <li>Registry Inconsistencies: {consistency_results.get("inconsistencies_count", 0)}</li>
                        </ul>
                    </div>
                    
                    <h3>Recommendations</h3>
                    {self.format_recommendations_html(recommendations)}
                </div>
                
                <div class="section">
                    <h2>Initialization Analysis</h2>
                    
                    <h3>Timing Overview</h3>
                    <table>
                        <tr>
                            <th>Metric</th>
                            <th>Value</th>
                        </tr>
                        <tr>
                            <td>Total Initialization Time</td>
                            <td>{timing_analysis.get("total_time", 0):.2f} seconds</td>
                        </tr>
                        <tr>
                            <td>Number of Actors</td>
                            <td>{timing_analysis.get("total_actors", 0)}</td>
                        </tr>
                        <tr>
                            <td>Number of Steps</td>
                            <td>{timing_analysis.get("total_steps", 0)}</td>
                        </tr>
                    </table>
                    
                    <h3>Slow Actors</h3>
                    <table>
                        <tr>
                            <th>Actor ID</th>
                            <th>Total Time (s)</th>
                            <th>Steps Count</th>
                            <th>Slowest Step</th>
                        </tr>
                        {self.format_slow_actors_html(timing_analysis.get("slow_actors", []))}
                    </table>
                    
                    <h3>Critical Path</h3>
                    <p>The critical path is the longest sequence of dependent actors that must be initialized sequentially.</p>
                    <table>
                        <tr>
                            <th>Actor ID</th>
                            <th>Time (s)</th>
                            <th>Dependencies</th>
                        </tr>
                        {self.format_critical_path_html(timing_analysis.get("critical_path", []))}
                    </table>
                    
                    <h3>Timeout Analysis</h3>
                    <table>
                        <tr>
                            <th>Metric</th>
                            <th>Value</th>
                        </tr>
                        <tr>
                            <td>Total Timeouts</td>
                            <td>{timeout_analysis.get("timeout_count", 0)}</td>
                        </tr>
                        {self.format_timeout_patterns_html(timeout_analysis.get("timeout_patterns", {}))}
                    </table>
                    
                    <h3>Actors with Timeouts</h3>
                    <table>
                        <tr>
                            <th>Actor ID</th>
                            <th>Timeout Count</th>
                        </tr>
                        {self.format_timeout_actors_html(timeout_analysis.get("timeout_actors", {}))}
                    </table>
                </div>
                
                <div class="section">
                    <h2>Registry Analysis</h2>
                    
                    <h3>Registry Operations</h3>
                    <table>
                        <tr>
                            <th>Operation Type</th>
                            <th>Count</th>
                            <th>Avg Duration (s)</th>
                        </tr>
                        {self.format_registry_operations_html(
                            registry_analysis.get("operation_types", {}),
                            registry_analysis.get("avg_durations", {})
                        )}
                    </table>
                    
                    <h3>Registry Consistency</h3>
                    <table>
                        <tr>
                            <th>Metric</th>
                            <th>Value</th>
                        </tr>
                        <tr>
                            <td>Inconsistencies Count</td>
                            <td>{consistency_results.get("inconsistencies_count", 0)}</td>
                        </tr>
                        <tr>
                            <td>Actors Count</td>
                            <td>{consistency_results.get("actors_count", 0)}</td>
                        </tr>
                        <tr>
                            <td>Actor Types Count</td>
                            <td>{consistency_results.get("actor_types_count", 0)}</td>
                        </tr>
                    </table>
                    
                    <h3>Registry Synchronization</h3>
                    <table>
                        <tr>
                            <th>Metric</th>
                            <th>Value</th>
                        </tr>
                        <tr>
                            <td>Registry Actors Count</td>
                            <td>{sync_analysis.get("registry_actors_count", 0)}</td>
                        </tr>
                        <tr>
                            <td>Manager Actors Count</td>
                            <td>{sync_analysis.get("manager_actors_count", 0)}</td>
                        </tr>
                        <tr>
                            <td>Common Actors Count</td>
                            <td>{sync_analysis.get("common_actors_count", 0)}</td>
                        </tr>
                        <tr>
                            <td>Registry-Only Actors Count</td>
                            <td>{sync_analysis.get("registry_only_actors_count", 0)}</td>
                        </tr>
                        <tr>
                            <td>Manager-Only Actors Count</td>
                            <td>{sync_analysis.get("manager_only_actors_count", 0)}</td>
                        </tr>
                        <tr>
                            <td>State Inconsistencies Count</td>
                            <td>{sync_analysis.get("state_inconsistencies_count", 0)}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </body>
        </html>
        """

        # Write the HTML to a file
        with open(report_path, "w") as f:
            f.write(html)

        return report_path

    def generate_recommendations(
        self,
        timing_analysis: Dict[str, Any],
        timeout_analysis: Dict[str, Any],
        registry_analysis: Dict[str, Any],
        sync_analysis: Dict[str, Any],
        consistency_results: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Generate recommendations based on the analysis results.

        Args:
            timing_analysis: Results of initialization timing analysis
            timeout_analysis: Results of timeout analysis
            registry_analysis: Results of registry operations analysis
            sync_analysis: Results of registry synchronization analysis
            consistency_results: Results of registry consistency check

        Returns:
            List of recommendations
        """
        recommendations = []

        # Add timeout recommendations
        if "recommendations" in timeout_analysis:
            for recommendation in timeout_analysis["recommendations"]:
                recommendations.append({
                    "type": "TIMEOUT",
                    "priority": "HIGH" if recommendation.get("recommendation") == "INCREASE_TIMEOUT" else "MEDIUM",
                    "recommendation": recommendation.get("recommendation", ""),
                    "reason": recommendation.get("reason", ""),
                    "suggested_action": recommendation.get("suggested_action", "")
                })

        # Add critical path recommendations
        if "critical_path" in timing_analysis and timing_analysis["critical_path"]:
            critical_path = timing_analysis["critical_path"]
            if len(critical_path) > 5:
                recommendations.append({
                    "type": "PERFORMANCE",
                    "priority": "HIGH",
                    "recommendation": "OPTIMIZE_CRITICAL_PATH",
                    "reason": f"Critical path contains {len(critical_path)} actors",
                    "suggested_action": "Consider optimizing the initialization of actors in the critical path or reducing dependencies"
                })

        # Add registry synchronization recommendations
        if sync_analysis.get("state_inconsistencies_count", 0) > 0:
            recommendations.append({
                "type": "CONSISTENCY",
                "priority": "HIGH",
                "recommendation": "FIX_REGISTRY_INCONSISTENCIES",
                "reason": f"Found {sync_analysis.get('state_inconsistencies_count', 0)} state inconsistencies between registry and initialization manager",
                "suggested_action": "Review actor registration and initialization process to ensure consistency"
            })

        # Add registry consistency recommendations
        if consistency_results.get("inconsistencies_count", 0) > 0:
            recommendations.append({
                "type": "CONSISTENCY",
                "priority": "HIGH",
                "recommendation": "FIX_REGISTRY_INCONSISTENCIES",
                "reason": f"Found {consistency_results.get('inconsistencies_count', 0)} inconsistencies in the registry",
                "suggested_action": "Review registry operations to ensure data structure consistency"
            })

        return recommendations

    def format_recommendations_html(self, recommendations: List[Dict[str, Any]]) -> str:
        """
        Format recommendations as HTML.

        Args:
            recommendations: List of recommendations

        Returns:
            HTML string with formatted recommendations
        """
        if not recommendations:
            return "<p>No recommendations available.</p>"

        html = ""
        for recommendation in recommendations:
            priority = recommendation.get("priority", "MEDIUM")
            priority_class = "critical" if priority == "HIGH" else "important" if priority == "MEDIUM" else ""
            
            html += f"""
            <div class="recommendation {priority_class}">
                <h4>{recommendation.get("recommendation", "")}</h4>
                <p><strong>Reason:</strong> {recommendation.get("reason", "")}</p>
                <p><strong>Suggested Action:</strong> {recommendation.get("suggested_action", "")}</p>
                <p><strong>Priority:</strong> {priority}</p>
            </div>
            """

        return html

    def format_slow_actors_html(self, slow_actors: List[Dict[str, Any]]) -> str:
        """
        Format slow actors as HTML table rows.

        Args:
            slow_actors: List of slow actors

        Returns:
            HTML string with formatted table rows
        """
        if not slow_actors:
            return "<tr><td colspan='4'>No slow actors found.</td></tr>"

        html = ""
        for actor in slow_actors:
            html += f"""
            <tr>
                <td>{actor.get("actor_id", "")}</td>
                <td>{actor.get("total_time", 0):.3f}</td>
                <td>{actor.get("steps_count", 0)}</td>
                <td>{actor.get("slowest_step", "")}</td>
            </tr>
            """

        return html

    def format_critical_path_html(self, critical_path: List[Dict[str, Any]]) -> str:
        """
        Format critical path as HTML table rows.

        Args:
            critical_path: List of actors in the critical path

        Returns:
            HTML string with formatted table rows
        """
        if not critical_path:
            return "<tr><td colspan='3'>No critical path found.</td></tr>"

        html = ""
        for actor in critical_path:
            dependencies = ", ".join(actor.get("dependencies", []))
            html += f"""
            <tr>
                <td>{actor.get("actor_id", "")}</td>
                <td>{actor.get("time", 0):.3f}</td>
                <td>{dependencies}</td>
            </tr>
            """

        return html

    def format_timeout_patterns_html(self, timeout_patterns: Dict[str, int]) -> str:
        """
        Format timeout patterns as HTML table rows.

        Args:
            timeout_patterns: Dictionary of timeout patterns and counts

        Returns:
            HTML string with formatted table rows
        """
        if not timeout_patterns:
            return "<tr><td colspan='2'>No timeout patterns found.</td></tr>"

        html = ""
        for pattern, count in timeout_patterns.items():
            html += f"""
            <tr>
                <td>{pattern}</td>
                <td>{count}</td>
            </tr>
            """

        return html

    def format_timeout_actors_html(self, timeout_actors: Dict[str, int]) -> str:
        """
        Format timeout actors as HTML table rows.

        Args:
            timeout_actors: Dictionary of actor IDs and timeout counts

        Returns:
            HTML string with formatted table rows
        """
        if not timeout_actors:
            return "<tr><td colspan='2'>No actors with timeouts found.</td></tr>"

        html = ""
        for actor_id, count in timeout_actors.items():
            html += f"""
            <tr>
                <td>{actor_id}</td>
                <td>{count}</td>
            </tr>
            """

        return html

    def format_registry_operations_html(
        self,
        operation_types: Dict[str, int],
        avg_durations: Dict[str, float]
    ) -> str:
        """
        Format registry operations as HTML table rows.

        Args:
            operation_types: Dictionary of operation types and counts
            avg_durations: Dictionary of operation types and average durations

        Returns:
            HTML string with formatted table rows
        """
        if not operation_types:
            return "<tr><td colspan='3'>No registry operations found.</td></tr>"

        html = ""
        for op_type, count in operation_types.items():
            avg_duration = avg_durations.get(op_type, 0.0)
            html += f"""
            <tr>
                <td>{op_type}</td>
                <td>{count}</td>
                <td>{avg_duration:.6f}</td>
            </tr>
            """

        return html
