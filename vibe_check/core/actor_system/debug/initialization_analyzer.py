"""
Initialization Analyzer
=====================

This module provides utilities for analyzing actor initialization,
including dependency resolution, timeout analysis, and initialization
sequence visualization.
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Tuple, Union, cast

from ..actor_state import ActorState
from ..consolidated_initializer import get_initializer
from ..logging.initialization_debug import (
    InitializationStep,
    get_init_timing_data,
    get_init_log_buffer,
    get_dependency_resolution_events,
    get_dependency_relationships,
    get_dependency_resolution_path,
    build_dependency_graph,
    analyze_dependency_resolution
)

# Configure logging
logger = logging.getLogger("vibe_check_actor_system_debug.initialization_analyzer")


class InitializationAnalyzer:
    """
    Utility class for analyzing actor initialization.

    This class provides methods for:
    - Analyzing initialization timing
    - Analyzing dependency resolution
    - Analyzing initialization timeouts
    """

    async def analyze_initialization_timing(self) -> Dict[str, Any]:
        """
        Analyze initialization timing data.

        This method analyzes the timing data collected during actor initialization
        to identify slow operations and bottlenecks.

        Returns:
            Dictionary with timing analysis results
        """
        # Get initialization timing data
        timing_data = get_init_timing_data()

        # Analyze actor initialization times
        actor_times: Dict[str, Dict[str, Any]] = {}
        for entry in timing_data:
            actor_id = entry.get("actor_id", "unknown")
            step = entry.get("step", "unknown")
            duration = entry.get("duration", 0.0)
            timestamp = entry.get("timestamp", 0.0)

            if actor_id not in actor_times:
                actor_times[actor_id] = {
                    "total_time": 0.0,
                    "steps": {},
                    "first_timestamp": timestamp,
                    "last_timestamp": timestamp
                }

            # Update total time
            actor_times[actor_id]["total_time"] += duration

            # Update step time
            if step not in actor_times[actor_id]["steps"]:
                actor_times[actor_id]["steps"][step] = {
                    "count": 0,
                    "total_time": 0.0,
                    "max_time": 0.0,
                    "min_time": float("inf")
                }

            actor_times[actor_id]["steps"][step]["count"] += 1
            actor_times[actor_id]["steps"][step]["total_time"] += duration
            actor_times[actor_id]["steps"][step]["max_time"] = max(
                actor_times[actor_id]["steps"][step]["max_time"], duration
            )
            actor_times[actor_id]["steps"][step]["min_time"] = min(
                actor_times[actor_id]["steps"][step]["min_time"], duration
            )

            # Update timestamps
            actor_times[actor_id]["first_timestamp"] = min(
                actor_times[actor_id]["first_timestamp"], timestamp
            )
            actor_times[actor_id]["last_timestamp"] = max(
                actor_times[actor_id]["last_timestamp"], timestamp
            )

        # Calculate average times for each step
        for actor_id, data in actor_times.items():
            for step, step_data in data["steps"].items():
                if step_data["count"] > 0:
                    step_data["avg_time"] = step_data["total_time"] / step_data["count"]

        # Identify slow actors and steps
        slow_actors = []
        slow_steps = []

        # Sort actors by total time
        sorted_actors = sorted(
            actor_times.items(),
            key=lambda x: x[1]["total_time"],
            reverse=True
        )

        # Get the top 5 slowest actors
        for actor_id, data in sorted_actors[:5]:
            slow_actors.append({
                "actor_id": actor_id,
                "total_time": data["total_time"],
                "steps_count": sum(step_data["count"] for step_data in data["steps"].values()),
                "slowest_step": max(
                    data["steps"].items(),
                    key=lambda x: x[1]["total_time"],
                    default=("none", {"total_time": 0.0})
                )[0]
            })

        # Get the top 10 slowest individual steps across all actors
        all_steps = []
        for actor_id, data in actor_times.items():
            for step, step_data in data["steps"].items():
                all_steps.append({
                    "actor_id": actor_id,
                    "step": step,
                    "total_time": step_data["total_time"],
                    "count": step_data["count"],
                    "avg_time": step_data.get("avg_time", 0.0),
                    "max_time": step_data["max_time"]
                })

        # Sort steps by total time
        sorted_steps = sorted(
            all_steps,
            key=lambda x: x["total_time"],
            reverse=True
        )

        # Get the top 10 slowest steps
        slow_steps = sorted_steps[:10]

        # Calculate overall statistics
        total_actors = len(actor_times)
        total_steps = sum(
            sum(step_data["count"] for step_data in data["steps"].values())
            for data in actor_times.values()
        )
        total_time = sum(data["total_time"] for data in actor_times.values())

        # Calculate the critical path (longest sequence of dependent actors)
        critical_path = await self.calculate_critical_path()

        # Compile the timing analysis results
        timing_analysis = {
            "total_actors": total_actors,
            "total_steps": total_steps,
            "total_time": total_time,
            "slow_actors": slow_actors,
            "slow_steps": slow_steps,
            "critical_path": critical_path,
            "actor_times": actor_times
        }

        return timing_analysis

    async def calculate_critical_path(self) -> List[Dict[str, Any]]:
        """
        Calculate the critical path in the initialization sequence.

        The critical path is the longest sequence of dependent actors that
        must be initialized sequentially, determining the minimum time
        required for the entire initialization process.

        Returns:
            List of actors in the critical path with timing information
        """
        # Get initialization timing data
        timing_data = get_init_timing_data()

        # Get dependency relationships
        dependencies = get_dependency_relationships()

        # Build a graph of actor dependencies
        dependency_graph = build_dependency_graph()

        # Get actor initialization times
        actor_times: Dict[str, float] = {}
        for entry in timing_data:
            actor_id = entry.get("actor_id", "unknown")
            duration = entry.get("duration", 0.0)

            if actor_id not in actor_times:
                actor_times[actor_id] = 0.0

            actor_times[actor_id] += duration

        # Find the critical path using a topological sort
        # and dynamic programming approach
        critical_path = []
        if dependency_graph:
            # Perform a topological sort
            sorted_actors = []
            visited = set()
            temp_visited = set()

            def dfs(actor_id: str) -> None:
                if actor_id in temp_visited:
                    # Cycle detected
                    return
                if actor_id in visited:
                    return

                temp_visited.add(actor_id)

                # Visit all dependencies
                for dep in dependency_graph.get(actor_id, []):
                    dfs(dep)

                temp_visited.remove(actor_id)
                visited.add(actor_id)
                sorted_actors.append(actor_id)

            # Perform DFS for each actor
            for actor_id in dependency_graph:
                if actor_id not in visited:
                    dfs(actor_id)

            # Reverse the topological sort
            sorted_actors.reverse()

            # Calculate the longest path to each actor
            longest_path: Dict[str, float] = {}
            predecessor: Dict[str, Optional[str]] = {}

            for actor_id in sorted_actors:
                longest_path[actor_id] = actor_times.get(actor_id, 0.0)
                predecessor[actor_id] = None

                # Check all dependencies
                for dep in dependency_graph.get(actor_id, []):
                    if dep in longest_path:
                        path_length = longest_path[dep] + actor_times.get(actor_id, 0.0)
                        if path_length > longest_path[actor_id]:
                            longest_path[actor_id] = path_length
                            predecessor[actor_id] = dep

            # Find the actor with the longest path
            end_actor = max(longest_path.items(), key=lambda x: x[1])[0]

            # Reconstruct the critical path
            current = end_actor
            path = []
            while current:
                path.append(current)
                current = predecessor[current]

            # Reverse the path to get the correct order
            path.reverse()

            # Build the critical path with timing information
            for actor_id in path:
                critical_path.append({
                    "actor_id": actor_id,
                    "time": actor_times.get(actor_id, 0.0),
                    "dependencies": dependency_graph.get(actor_id, [])
                })

        return critical_path

    async def analyze_initialization_timeouts(self) -> Dict[str, Any]:
        """
        Analyze initialization timeouts.

        This method analyzes the initialization logs to identify timeout issues
        and provide recommendations for timeout adjustments.

        Returns:
            Dictionary with timeout analysis results
        """
        # Get initialization timing data
        timing_data = get_init_timing_data()

        # Get initialization log buffer
        log_buffer = get_init_log_buffer()

        # Extract timeout-related logs
        timeout_logs = []
        for log in log_buffer:
            message = log.get("message", "")
            if "timeout" in message.lower() or "timed out" in message.lower():
                timeout_logs.append(log)

        # Analyze timeout patterns
        timeout_patterns: Dict[str, int] = {}
        timeout_actors: Dict[str, List[Dict[str, Any]]] = {}

        for log in timeout_logs:
            message = log.get("message", "")
            actor_id = log.get("actor_id", "unknown")
            timestamp = log.get("timestamp", 0.0)

            # Extract timeout pattern
            if "waiting for dependencies" in message.lower():
                pattern = "DEPENDENCY_WAIT_TIMEOUT"
            elif "initialization timed out" in message.lower():
                pattern = "INITIALIZATION_TIMEOUT"
            elif "synchronization point" in message.lower():
                pattern = "SYNCHRONIZATION_TIMEOUT"
            else:
                pattern = "OTHER_TIMEOUT"

            # Count timeout patterns
            timeout_patterns[pattern] = timeout_patterns.get(pattern, 0) + 1

            # Track timeout actors
            if actor_id not in timeout_actors:
                timeout_actors[actor_id] = []

            timeout_actors[actor_id].append({
                "timestamp": timestamp,
                "message": message,
                "pattern": pattern
            })

        # Generate timeout recommendations
        recommendations = []

        # Recommend increasing timeouts for actors with multiple timeouts
        for actor_id, timeouts in timeout_actors.items():
            if len(timeouts) > 1:
                recommendations.append({
                    "actor_id": actor_id,
                    "recommendation": "INCREASE_TIMEOUT",
                    "reason": f"Actor {actor_id} experienced {len(timeouts)} timeouts",
                    "suggested_action": "Consider increasing the timeout for this actor"
                })

        # Recommend dependency review for dependency wait timeouts
        if timeout_patterns.get("DEPENDENCY_WAIT_TIMEOUT", 0) > 0:
            recommendations.append({
                "recommendation": "REVIEW_DEPENDENCIES",
                "reason": f"Found {timeout_patterns.get('DEPENDENCY_WAIT_TIMEOUT', 0)} dependency wait timeouts",
                "suggested_action": "Review actor dependencies for potential cycles or missing dependencies"
            })

        # Recommend synchronization review for synchronization timeouts
        if timeout_patterns.get("SYNCHRONIZATION_TIMEOUT", 0) > 0:
            recommendations.append({
                "recommendation": "REVIEW_SYNCHRONIZATION",
                "reason": f"Found {timeout_patterns.get('SYNCHRONIZATION_TIMEOUT', 0)} synchronization timeouts",
                "suggested_action": "Review synchronization points and consider increasing timeouts"
            })

        # Compile the timeout analysis results
        timeout_analysis = {
            "timeout_count": len(timeout_logs),
            "timeout_patterns": timeout_patterns,
            "timeout_actors": {actor_id: len(timeouts) for actor_id, timeouts in timeout_actors.items()},
            "recommendations": recommendations,
            "timeout_logs": timeout_logs
        }

        return timeout_analysis
