"""
Registry Analyzer
===============

This module provides utilities for analyzing the actor registry,
including operations, consistency, and synchronization with the
initialization manager.
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Tuple, Union, cast

from ..actor_registry import get_registry
from ..consolidated_initializer import get_initializer
from ..logging.registry_debug import (
    RegistryOperationType,
    get_registry_operations,
    get_registry_states,
    get_registry_inconsistencies,
    get_registry_lookup_retries,
    get_correlated_operations
)

# Configure logging
logger = logging.getLogger("vibe_check_actor_system_debug.registry_analyzer")


class RegistryAnalyzer:
    """
    Utility class for analyzing the actor registry.

    This class provides methods for:
    - Analyzing registry operations
    - Checking registry consistency
    - Analyzing registry synchronization with the initialization manager
    """

    async def analyze_registry_operations(self) -> Dict[str, Any]:
        """
        Analyze registry operations.

        This method analyzes registry operations to identify patterns and issues.

        Returns:
            Dictionary with registry operation analysis results
        """
        # Get registry operations
        operations = get_registry_operations()

        # Get registry states
        states = get_registry_states()

        # Get registry inconsistencies
        inconsistencies = get_registry_inconsistencies()

        # Get registry lookup retries
        lookup_retries = get_registry_lookup_retries()

        # Analyze operation types
        operation_types: Dict[str, int] = {}
        for op in operations:
            op_type = op.get("operation_type", "UNKNOWN")
            operation_types[op_type] = operation_types.get(op_type, 0) + 1

        # Analyze operation durations
        operation_durations: Dict[str, List[float]] = {}
        for op in operations:
            op_type = op.get("operation_type", "UNKNOWN")
            details = op.get("details", {})
            if "duration" in details:
                if op_type not in operation_durations:
                    operation_durations[op_type] = []
                operation_durations[op_type].append(details["duration"])

        # Calculate average durations
        avg_durations: Dict[str, float] = {}
        for op_type, durations in operation_durations.items():
            if durations:
                avg_durations[op_type] = sum(durations) / len(durations)

        # Analyze lookup retries
        retry_stats: Dict[str, Any] = {
            "total_retries": 0,
            "successful_retries": 0,
            "failed_retries": 0,
            "actors_with_retries": len(lookup_retries),
            "max_retries_per_actor": 0,
            "actors_with_most_retries": []
        }

        for actor_id, retries in lookup_retries.items():
            retry_stats["total_retries"] += len(retries)

            # Count successful and failed retries
            for retry in retries:
                if retry.get("success") is True:
                    retry_stats["successful_retries"] += 1
                elif retry.get("success") is False:
                    retry_stats["failed_retries"] += 1

            # Track actor with most retries
            if len(retries) > retry_stats["max_retries_per_actor"]:
                retry_stats["max_retries_per_actor"] = len(retries)
                retry_stats["actors_with_most_retries"] = [actor_id]
            elif len(retries) == retry_stats["max_retries_per_actor"]:
                retry_stats["actors_with_most_retries"].append(actor_id)

        # Analyze inconsistencies
        inconsistency_stats: Dict[str, Any] = {
            "total_inconsistencies": len(inconsistencies),
            "resolved_inconsistencies": 0,
            "unresolved_inconsistencies": 0,
            "inconsistency_types": {}
        }

        for inconsistency in inconsistencies:
            # Count resolved and unresolved inconsistencies
            if inconsistency.get("resolved", False):
                inconsistency_stats["resolved_inconsistencies"] += 1
            else:
                inconsistency_stats["unresolved_inconsistencies"] += 1

            # Count inconsistency types
            inconsistency_type = inconsistency.get("inconsistency_type", "UNKNOWN")
            inconsistency_stats["inconsistency_types"][inconsistency_type] = inconsistency_stats["inconsistency_types"].get(inconsistency_type, 0) + 1

        # Analyze registry state evolution
        state_evolution: Dict[str, Any] = {
            "total_states": len(states),
            "actor_count_evolution": [],
            "actor_type_count_evolution": []
        }

        for state in states:
            state_data = state.get("state", {})
            timestamp = state.get("timestamp", "")

            # Track actor count evolution
            actors_count = state_data.get("actors_count", 0)
            state_evolution["actor_count_evolution"].append({
                "timestamp": timestamp,
                "count": actors_count
            })

            # Track actor type count evolution
            actor_types_count = state_data.get("actor_types_count", 0)
            state_evolution["actor_type_count_evolution"].append({
                "timestamp": timestamp,
                "count": actor_types_count
            })

        # Compile the analysis results
        analysis_results = {
            "operation_types": operation_types,
            "avg_durations": avg_durations,
            "retry_stats": retry_stats,
            "inconsistency_stats": inconsistency_stats,
            "state_evolution": state_evolution,
            "raw_data": {
                "operations_count": len(operations),
                "states_count": len(states),
                "inconsistencies_count": len(inconsistencies),
                "lookup_retries_count": sum(len(retries) for retries in lookup_retries.values())
            }
        }

        return analysis_results

    async def check_registry_consistency(self) -> Dict[str, Any]:
        """
        Check the consistency of the registry.

        This method checks for inconsistencies in the registry data structures.

        Returns:
            Dictionary with consistency check results
        """
        # Get the registry
        registry = get_registry()

        # Check consistency
        consistency_results = registry.check_consistency()

        return consistency_results

    async def analyze_registry_synchronization(self) -> Dict[str, Any]:
        """
        Analyze the synchronization between the registry and the initialization manager.

        This method checks for inconsistencies between the registry and the initialization manager.

        Returns:
            Dictionary with synchronization analysis results
        """
        # Get the registry
        registry = get_registry()

        # Get the initialization manager
        manager = get_initializer()

        # Collect registry actors
        registry_actors = set(registry._actors.keys())

        # Collect initialization manager actors
        manager_actors = set(manager._actor_states.keys())

        # Find actors in registry but not in manager
        registry_only_actors = registry_actors - manager_actors

        # Find actors in manager but not in registry
        manager_only_actors = manager_actors - registry_actors

        # Find actors in both
        common_actors = registry_actors.intersection(manager_actors)

        # Check state consistency for common actors
        state_inconsistencies = []
        for actor_id in common_actors:
            registry_actor = registry._actors.get(actor_id)
            manager_state = manager._actor_states.get(actor_id)

            if registry_actor and manager_state:
                # Check if the actor is initialized in the registry but not in the manager
                if registry_actor.is_initialized and manager_state != ActorState.INITIALIZED:
                    state_inconsistencies.append({
                        "actor_id": actor_id,
                        "registry_state": "INITIALIZED",
                        "manager_state": manager_state.value,
                        "inconsistency_type": "REGISTRY_INITIALIZED_MANAGER_NOT"
                    })
                # Check if the actor is initialized in the manager but not in the registry
                elif not registry_actor.is_initialized and manager_state == ActorState.INITIALIZED:
                    state_inconsistencies.append({
                        "actor_id": actor_id,
                        "registry_state": "NOT_INITIALIZED",
                        "manager_state": manager_state.value,
                        "inconsistency_type": "MANAGER_INITIALIZED_REGISTRY_NOT"
                    })

        # Compile the synchronization analysis results
        sync_analysis = {
            "registry_actors_count": len(registry_actors),
            "manager_actors_count": len(manager_actors),
            "common_actors_count": len(common_actors),
            "registry_only_actors_count": len(registry_only_actors),
            "manager_only_actors_count": len(manager_only_actors),
            "state_inconsistencies_count": len(state_inconsistencies),
            "registry_only_actors": list(registry_only_actors),
            "manager_only_actors": list(manager_only_actors),
            "state_inconsistencies": state_inconsistencies
        }

        return sync_analysis
