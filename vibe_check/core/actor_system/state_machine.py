"""
Actor State Machine Module
======================

This module provides a robust state machine for managing actor states.
It ensures that state transitions are valid, provides hooks for state
change events, and includes error handling for invalid transitions.

The state machine is designed to handle all possible transitions,
especially during error conditions, and provides recovery paths for
error states.
"""

import asyncio
import logging
import time
from enum import Enum
from typing import Dict, List, Optional, Set, Tuple, Any, Callable, Union, Awaitable
import json
from pathlib import Path
import os
import threading

from .actor_initializer import ActorState
from .protocols import StateMachineProtocol

logger = logging.getLogger("vibe_check_state_machine")


class StateTransitionError(Exception):
    """Exception raised when an invalid state transition is attempted."""

    def __init__(self, actor_id: str, current_state: ActorState, target_state: ActorState):
        """
        Initialize the exception.

        Args:
            actor_id: ID of the actor
            current_state: Current state of the actor
            target_state: Target state that was invalid
        """
        self.actor_id = actor_id
        self.current_state = current_state
        self.target_state = target_state
        super().__init__(
            f"Invalid state transition for actor {actor_id}: "
            f"{current_state.value} -> {target_state.value}"
        )


class StateChangeEvent:
    """Class representing a state change event."""

    def __init__(
        self,
        actor_id: str,
        old_state: ActorState,
        new_state: ActorState,
        timestamp: float,
        details: Optional[Dict[str, Any]] = None,
        error: Optional[Exception] = None
    ):
        """
        Initialize the event.

        Args:
            actor_id: ID of the actor
            old_state: Previous state
            new_state: New state
            timestamp: Time when the event occurred
            details: Optional details about the event
            error: Optional error that occurred during the transition
        """
        self.actor_id = actor_id
        self.old_state = old_state
        self.new_state = new_state
        self.timestamp = timestamp
        self.details = details or {}
        self.error = error
        self.error_str = str(error) if error else None

    def to_dict(self) -> Dict[str, Any]:
        """Convert the event to a dictionary for serialization."""
        return {
            "actor_id": self.actor_id,
            "old_state": self.old_state.value,
            "new_state": self.new_state.value,
            "timestamp": self.timestamp,
            "details": self.details,
            "error": self.error_str
        }


# Types for state change callbacks and hooks
StateChangeCallback = Callable[[StateChangeEvent], None]
StateChangeHook = Callable[[StateChangeEvent], Any]
AsyncStateChangeHook = Callable[[StateChangeEvent], Awaitable[Any]]


class ActorStateMachine(StateMachineProtocol):
    """
    Manages actor state transitions with validation and event hooks.

    This class ensures that state transitions are valid, provides hooks
    for state change events, and includes error handling for invalid
    transitions.

    Enhanced with:
    - State entry and exit hooks for specific states
    - Transition hooks for specific state transitions
    - Thread-safe hook execution
    - Comprehensive logging for hook execution
    - Integration with dependency injection system
    - Visualization capabilities

    Implements the StateMachineProtocol for better type checking and
    dependency injection support.
    """

    def __init__(self, output_dir: Optional[str] = None):
        """
        Initialize the state machine.

        Args:
            output_dir: Optional directory to save state change logs
        """
        self._actor_states: Dict[str, ActorState] = {}
        self._state_change_events: List[StateChangeEvent] = []
        self._state_lock = threading.RLock()  # Reentrant lock for thread safety
        self._callbacks: List[StateChangeCallback] = []
        self._output_dir = output_dir
        self._start_time = time.time()

        # Valid state transitions
        self._valid_transitions: Dict[ActorState, Set[ActorState]] = {
            ActorState.CREATED: {ActorState.INITIALIZING, ActorState.FAILED, ActorState.ROLLBACK, ActorState.STOPPING},
            ActorState.INITIALIZING: {ActorState.INITIALIZED, ActorState.FAILED, ActorState.ROLLBACK, ActorState.STOPPING},
            ActorState.INITIALIZED: {ActorState.STARTING, ActorState.FAILED, ActorState.ROLLBACK, ActorState.STOPPING},
            ActorState.STARTING: {ActorState.READY, ActorState.FAILED, ActorState.ROLLBACK, ActorState.STOPPING},
            ActorState.READY: {ActorState.STOPPING, ActorState.FAILED},
            ActorState.STOPPING: {ActorState.STOPPED, ActorState.FAILED},
            ActorState.STOPPED: {ActorState.CREATED, ActorState.INITIALIZING},  # Allow restart
            ActorState.FAILED: {ActorState.ROLLBACK, ActorState.STOPPED, ActorState.CREATED},  # Allow recovery
            ActorState.ROLLBACK: {ActorState.STOPPED, ActorState.CREATED}  # Allow reset
        }

        # Enhanced hook system
        # State entry hooks: Called when entering a specific state
        self._state_entry_hooks: Dict[ActorState, List[Callable[[StateChangeEvent], Any]]] = {
            state: [] for state in ActorState
        }

        # State exit hooks: Called when exiting a specific state
        self._state_exit_hooks: Dict[ActorState, List[Callable[[StateChangeEvent], Any]]] = {
            state: [] for state in ActorState
        }

        # Transition hooks: Called for specific state transitions
        self._transition_hooks: Dict[Tuple[ActorState, ActorState], List[Callable[[StateChangeEvent], Any]]] = {}

        # Create output directory if specified
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

    async def register_actor(self, actor_id: str, initial_state: ActorState = ActorState.CREATED) -> None:
        """
        Register an actor with the state machine.

        Args:
            actor_id: ID of the actor to register
            initial_state: Initial state for the actor
        """
        with self._state_lock:
            if actor_id in self._actor_states:
                logger.warning(f"Actor {actor_id} already registered with state machine")
                return

            self._actor_states[actor_id] = initial_state
            logger.info(f"Registered actor {actor_id} with initial state {initial_state.value}")

            # Record the initial state as an event
            event = StateChangeEvent(
                actor_id=actor_id,
                old_state=initial_state,  # Same as new_state for initial registration
                new_state=initial_state,
                timestamp=time.time(),
                details={"action": "registration"}
            )
            self._state_change_events.append(event)

            # Notify callbacks
            for callback in self._callbacks:
                try:
                    callback(event)
                except Exception as e:
                    logger.error(f"Error in state change callback: {e}")

    async def set_actor_state(
        self,
        actor_id: str,
        state: ActorState,
        details: Optional[Dict[str, Any]] = None,
        error: Optional[Exception] = None,
        force: bool = False
    ) -> bool:
        """
        Set the state of an actor with validation.

        Enhanced with hook execution for state transitions, including:
        - Exit hooks for the old state
        - Transition hooks for the specific transition
        - Entry hooks for the new state

        Args:
            actor_id: ID of the actor
            state: New state for the actor
            details: Optional details about the state change
            error: Optional error that caused a state change to FAILED
            force: Whether to force the state change even if invalid

        Returns:
            True if the state was changed, False otherwise

        Raises:
            StateTransitionError: If the transition is invalid and force is False
        """
        with self._state_lock:
            if actor_id not in self._actor_states:
                logger.warning(f"Cannot set state: Actor {actor_id} not registered")
                return False

            old_state = self._actor_states[actor_id]

            # Check if the actor is already in the target state
            if old_state == state:
                logger.debug(f"Actor {actor_id} is already in state {state.value}, no change needed")
                # Allow same state transitions for CREATED state to fix initialization issues
                if old_state != ActorState.CREATED:
                    return False

            # Validate state transition
            if state not in self._valid_transitions.get(old_state, set()) and old_state != state:
                error_msg = f"Invalid state transition for actor {actor_id}: {old_state.value} -> {state.value}"
                logger.error(error_msg)
                if not force:
                    raise StateTransitionError(actor_id, old_state, state)
                # If force is True, we'll allow the invalid transition but log the error
                logger.warning(f"Forcing invalid state transition for actor {actor_id}: {old_state.value} -> {state.value}")

            # Create event before updating state
            timestamp = time.time()
            event = StateChangeEvent(
                actor_id=actor_id,
                old_state=old_state,
                new_state=state,
                timestamp=timestamp,
                details=details or {},
                error=error
            )

            # Execute exit hooks for the old state
            exit_hooks = self._state_exit_hooks.get(old_state, [])
            if exit_hooks:
                logger.debug(f"Executing {len(exit_hooks)} exit hooks for state {old_state.value}")
                for hook in exit_hooks:
                    try:
                        hook(event)
                    except Exception as e:
                        logger.error(f"Error in state exit hook for {old_state.value}: {e}")

            # Execute transition hooks
            transition_key = (old_state, state)
            transition_hooks = self._transition_hooks.get(transition_key, [])
            if transition_hooks:
                logger.debug(f"Executing {len(transition_hooks)} transition hooks for {old_state.value} -> {state.value}")
                for hook in transition_hooks:
                    try:
                        hook(event)
                    except Exception as e:
                        logger.error(f"Error in transition hook for {old_state.value} -> {state.value}: {e}")

            # Update state
            self._actor_states[actor_id] = state

            # Add event to history
            self._state_change_events.append(event)

            # Log the state change
            if error:
                logger.error(f"Actor {actor_id} state changed: {old_state.value} -> {state.value} (error: {error})")
            else:
                logger.info(f"Actor {actor_id} state changed: {old_state.value} -> {state.value}")

            # Execute entry hooks for the new state
            entry_hooks = self._state_entry_hooks.get(state, [])
            if entry_hooks:
                logger.debug(f"Executing {len(entry_hooks)} entry hooks for state {state.value}")
                for hook in entry_hooks:
                    try:
                        hook(event)
                    except Exception as e:
                        logger.error(f"Error in state entry hook for {state.value}: {e}")

            # Notify general callbacks
            for callback in self._callbacks:
                try:
                    callback(event)
                except Exception as e:
                    logger.error(f"Error in state change callback: {e}")

            return True

    def get_actor_state(self, actor_id: str) -> Optional[ActorState]:
        """
        Get the current state of an actor.

        Args:
            actor_id: ID of the actor

        Returns:
            Current state of the actor or None if not registered
        """
        with self._state_lock:
            return self._actor_states.get(actor_id)

    def is_valid_transition(self, current_state: ActorState, target_state: ActorState) -> bool:
        """
        Check if a state transition is valid.

        Args:
            current_state: Current state
            target_state: Target state

        Returns:
            True if the transition is valid, False otherwise
        """
        return target_state in self._valid_transitions.get(current_state, set())

    def add_state_change_callback(self, callback: StateChangeCallback) -> None:
        """
        Add a callback to be called when an actor's state changes.

        Args:
            callback: Function to call with the state change event
        """
        self._callbacks.append(callback)

    def remove_state_change_callback(self, callback: StateChangeCallback) -> None:
        """
        Remove a state change callback.

        Args:
            callback: Callback to remove
        """
        if callback in self._callbacks:
            self._callbacks.remove(callback)

    def add_state_entry_hook(self, state: ActorState, hook: StateChangeHook) -> None:
        """
        Add a hook to be called when an actor enters a specific state.

        Args:
            state: The state to hook into
            hook: Function to call with the state change event
        """
        with self._state_lock:
            self._state_entry_hooks[state].append(hook)
            logger.debug(f"Added state entry hook for state {state.value}")

    def remove_state_entry_hook(self, state: ActorState, hook: StateChangeHook) -> None:
        """
        Remove a state entry hook.

        Args:
            state: The state the hook is registered for
            hook: The hook function to remove
        """
        with self._state_lock:
            if hook in self._state_entry_hooks[state]:
                self._state_entry_hooks[state].remove(hook)
                logger.debug(f"Removed state entry hook for state {state.value}")

    def add_state_exit_hook(self, state: ActorState, hook: StateChangeHook) -> None:
        """
        Add a hook to be called when an actor exits a specific state.

        Args:
            state: The state to hook into
            hook: Function to call with the state change event
        """
        with self._state_lock:
            self._state_exit_hooks[state].append(hook)
            logger.debug(f"Added state exit hook for state {state.value}")

    def remove_state_exit_hook(self, state: ActorState, hook: StateChangeHook) -> None:
        """
        Remove a state exit hook.

        Args:
            state: The state the hook is registered for
            hook: The hook function to remove
        """
        with self._state_lock:
            if hook in self._state_exit_hooks[state]:
                self._state_exit_hooks[state].remove(hook)
                logger.debug(f"Removed state exit hook for state {state.value}")

    def add_transition_hook(self, from_state: ActorState, to_state: ActorState,
                           hook: StateChangeHook) -> None:
        """
        Add a hook to be called for a specific state transition.

        Args:
            from_state: The starting state of the transition
            to_state: The ending state of the transition
            hook: Function to call with the state change event
        """
        with self._state_lock:
            transition_key = (from_state, to_state)
            if transition_key not in self._transition_hooks:
                self._transition_hooks[transition_key] = []
            self._transition_hooks[transition_key].append(hook)
            logger.debug(f"Added transition hook for {from_state.value} -> {to_state.value}")

    def remove_transition_hook(self, from_state: ActorState, to_state: ActorState,
                              hook: StateChangeHook) -> None:
        """
        Remove a transition hook.

        Args:
            from_state: The starting state of the transition
            to_state: The ending state of the transition
            hook: The hook function to remove
        """
        with self._state_lock:
            transition_key = (from_state, to_state)
            if transition_key in self._transition_hooks and hook in self._transition_hooks[transition_key]:
                self._transition_hooks[transition_key].remove(hook)
                logger.debug(f"Removed transition hook for {from_state.value} -> {to_state.value}")

    async def execute_hooks_async(self, hooks: List[Union[StateChangeHook, AsyncStateChangeHook]],
                                 event: StateChangeEvent, description: str) -> None:
        """
        Execute a list of hooks asynchronously.

        This method supports both synchronous and asynchronous hooks. Synchronous hooks
        are executed directly, while asynchronous hooks are awaited.

        Args:
            hooks: List of hooks to execute
            event: The state change event to pass to the hooks
            description: Description of the hooks for logging
        """
        if not hooks:
            return

        logger.debug(f"Executing {len(hooks)} {description} asynchronously")

        for hook in hooks:
            try:
                result = hook(event)
                if asyncio.iscoroutine(result):
                    await result
            except Exception as e:
                logger.error(f"Error in {description}: {e}")

    async def set_actor_state_async(
        self,
        actor_id: str,
        state: ActorState,
        details: Optional[Dict[str, Any]] = None,
        error: Optional[Exception] = None,
        force: bool = False
    ) -> bool:
        """
        Set the state of an actor with validation, executing hooks asynchronously.

        This method is similar to set_actor_state, but it executes hooks asynchronously,
        which is useful for hooks that need to perform I/O operations.

        Args:
            actor_id: ID of the actor
            state: New state for the actor
            details: Optional details about the state change
            error: Optional error that caused a state change to FAILED
            force: Whether to force the state change even if invalid

        Returns:
            True if the state was changed, False otherwise

        Raises:
            StateTransitionError: If the transition is invalid and force is False
        """
        with self._state_lock:
            if actor_id not in self._actor_states:
                logger.warning(f"Cannot set state: Actor {actor_id} not registered")
                return False

            old_state = self._actor_states[actor_id]

            # Check if the actor is already in the target state
            if old_state == state:
                logger.debug(f"Actor {actor_id} is already in state {state.value}, no change needed")
                # Allow same state transitions for CREATED state to fix initialization issues
                if old_state != ActorState.CREATED:
                    return False

            # Validate state transition
            if state not in self._valid_transitions.get(old_state, set()) and old_state != state:
                error_msg = f"Invalid state transition for actor {actor_id}: {old_state.value} -> {state.value}"
                logger.error(error_msg)
                if not force:
                    raise StateTransitionError(actor_id, old_state, state)
                # If force is True, we'll allow the invalid transition but log the error
                logger.warning(f"Forcing invalid state transition for actor {actor_id}: {old_state.value} -> {state.value}")

            # Create event before updating state
            timestamp = time.time()
            event = StateChangeEvent(
                actor_id=actor_id,
                old_state=old_state,
                new_state=state,
                timestamp=timestamp,
                details=details or {},
                error=error
            )

            # Execute exit hooks for the old state asynchronously
            await self.execute_hooks_async(
                self._state_exit_hooks.get(old_state, []),
                event,
                f"exit hooks for state {old_state.value}"
            )

            # Execute transition hooks asynchronously
            transition_key = (old_state, state)
            await self.execute_hooks_async(
                self._transition_hooks.get(transition_key, []),
                event,
                f"transition hooks for {old_state.value} -> {state.value}"
            )

            # Update state
            self._actor_states[actor_id] = state

            # Add event to history
            self._state_change_events.append(event)

            # Log the state change
            if error:
                logger.error(f"Actor {actor_id} state changed: {old_state.value} -> {state.value} (error: {error})")
            else:
                logger.info(f"Actor {actor_id} state changed: {old_state.value} -> {state.value}")

            # Execute entry hooks for the new state asynchronously
            await self.execute_hooks_async(
                self._state_entry_hooks.get(state, []),
                event,
                f"entry hooks for state {state.value}"
            )

            # Notify general callbacks (these are not async)
            for callback in self._callbacks:
                try:
                    callback(event)
                except Exception as e:
                    logger.error(f"Error in state change callback: {e}")

            return True

    def get_actors_in_state(self, state: ActorState) -> List[str]:
        """
        Get all actors in a specific state.

        Args:
            state: State to filter by

        Returns:
            List of actor IDs in the specified state
        """
        with self._state_lock:
            return [
                actor_id for actor_id, actor_state in self._actor_states.items()
                if actor_state == state
            ]

    def get_state_change_history(self, actor_id: str) -> List[StateChangeEvent]:
        """
        Get the state change history for an actor.

        Args:
            actor_id: ID of the actor

        Returns:
            List of state change events for the actor
        """
        return [
            event for event in self._state_change_events
            if event.actor_id == actor_id
        ]

    async def save_state_history(self, filename: Optional[str] = None) -> Optional[str]:
        """
        Save state change history to a file.

        Args:
            filename: Optional filename to save to

        Returns:
            Path to the saved file, or None if saving failed
        """
        if not self._output_dir:
            logger.warning("Cannot save state history: no output directory specified")
            return None

        try:
            # Create a default filename if none provided
            if not filename:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"state_history_{timestamp}.json"

            filepath = Path(self._output_dir) / filename

            # Prepare data for serialization
            data = {
                "events": [event.to_dict() for event in self._state_change_events],
                "current_states": {
                    actor_id: state.value
                    for actor_id, state in self._actor_states.items()
                },
                "timestamp": time.time(),
                "duration": time.time() - self._start_time
            }

            # Save to file
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Saved state history to {filepath}")
            return str(filepath)

        except Exception as e:
            logger.error(f"Failed to save state history: {e}")
            return None

    async def generate_visualization(self, filename: Optional[str] = None,
                                    format: str = "svg") -> Optional[str]:
        """
        Generate a visualization of the state machine.

        This method creates a graph visualization of the state machine, showing:
        - All possible states
        - Valid transitions between states
        - Current state of each actor
        - State transition history

        Args:
            filename: Optional filename to save to
            format: Output format (svg, png, pdf)

        Returns:
            Path to the saved file, or None if visualization failed
        """
        if not self._output_dir:
            logger.warning("Cannot generate visualization: no output directory specified")
            return None

        try:
            # Check if graphviz is available
            try:
                import graphviz  # type: ignore
            except ImportError:
                logger.error("Cannot generate visualization: graphviz package not installed")
                return None

            # Create a default filename if none provided
            if not filename:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"state_machine_{timestamp}"

            # Create a new graph
            dot = graphviz.Digraph(
                name="Actor State Machine",
                comment="Visualization of the actor state machine",
                format=format
            )

            # Set graph attributes
            dot.attr(rankdir="LR", size="8,5", ratio="fill")
            dot.attr("node", shape="box", style="filled", fontname="Arial")
            dot.attr("edge", fontname="Arial")

            # Add states as nodes
            for state in ActorState:
                # Use different colors for different state types
                if state in [ActorState.FAILED, ActorState.ROLLBACK]:
                    # Error states in red
                    dot.node(state.value, state.value, fillcolor="lightcoral")
                elif state in [ActorState.READY]:
                    # Success states in green
                    dot.node(state.value, state.value, fillcolor="palegreen")
                elif state in [ActorState.INITIALIZING, ActorState.STARTING]:
                    # Transition states in yellow
                    dot.node(state.value, state.value, fillcolor="lightyellow")
                else:
                    # Other states in light blue
                    dot.node(state.value, state.value, fillcolor="lightblue")

            # Add transitions as edges
            for from_state, to_states in self._valid_transitions.items():
                for to_state in to_states:
                    # Count how many times this transition has occurred
                    transition_count = sum(
                        1 for event in self._state_change_events
                        if event.old_state == from_state and event.new_state == to_state
                    )

                    # Add edge with transition count as label
                    if transition_count > 0:
                        dot.edge(
                            from_state.value,
                            to_state.value,
                            label=str(transition_count),
                            penwidth=str(1 + min(transition_count, 5)),
                            color="blue"
                        )
                    else:
                        dot.edge(from_state.value, to_state.value)

            # Add a subgraph for current actor states
            with dot.subgraph(name="cluster_actors") as c:
                c.attr(label="Current Actor States", style="filled", fillcolor="lightgrey")

                # Group actors by state
                actors_by_state: Dict[ActorState, List[str]] = {}
                for actor_id, state in self._actor_states.items():
                    if state not in actors_by_state:
                        actors_by_state[state] = []
                    actors_by_state[state].append(actor_id)

                # Add nodes for each state with its actors
                for state, actors in actors_by_state.items():
                    if actors:
                        actor_list = "\\n".join(actors)
                        c.node(
                            f"actors_{state.value}",
                            f"{state.value}\\n{actor_list}",
                            shape="box",
                            style="filled",
                            fillcolor="white"
                        )

            # Render the graph
            filepath = Path(self._output_dir) / filename
            dot.render(filepath, cleanup=True)

            rendered_file = f"{filepath}.{format}"
            logger.info(f"Generated state machine visualization: {rendered_file}")
            return rendered_file

        except Exception as e:
            logger.error(f"Failed to generate visualization: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None


# Singleton instance
_state_machine = None


def get_state_machine(output_dir: Optional[str] = None) -> ActorStateMachine:
    """
    Get the singleton state machine instance.

    Args:
        output_dir: Optional directory to save state change logs

    Returns:
        The actor state machine instance
    """
    global _state_machine

    if _state_machine is None:
        _state_machine = ActorStateMachine(output_dir)
        logger.info("Created new actor state machine")

        # Register with dependency injection system if available
        try:
            from .dependency_injection import get_container
            container = get_container()
            container.register_instance(_state_machine, "state_machine")
            logger.info("Registered state machine with dependency injection container")
        except (ImportError, AttributeError) as e:
            logger.debug(f"Could not register state machine with dependency injection: {e}")

    return _state_machine


def reset_state_machine() -> None:
    """Reset the singleton state machine instance."""
    global _state_machine

    # Unregister from dependency injection system if available
    if _state_machine is not None:
        try:
            from .dependency_injection import get_container
            container = get_container()
            # We can't directly unregister, but we can register a new instance
            # when get_state_machine is called again
            logger.debug("State machine will be re-registered with dependency injection on next get_state_machine call")
        except (ImportError, AttributeError) as e:
            logger.debug(f"Could not interact with dependency injection: {e}")

    _state_machine = None
    logger.info("Reset actor state machine")


def register_with_dependency_injection() -> None:
    """
    Register the state machine with the dependency injection system.

    This method is called automatically by get_state_machine, but can be
    called manually if needed.

    Enhanced with:
    - Better error handling
    - Integration with the dependency injection system
    - Support for state transition hooks
    """
    try:
        from .dependency_injection import get_container, Component, ComponentScope
        from .dependency_injection.interfaces import Injectable

        # Get the container
        container = get_container()

        # Register the ActorStateMachine class
        if not hasattr(ActorStateMachine, '__component__'):
            setattr(ActorStateMachine, '__component__', True)
            setattr(ActorStateMachine, '__component_scope__', ComponentScope.SINGLETON)
            setattr(ActorStateMachine, '__component_name__', "ActorStateMachine")

            # Register the class with the container
            container.register(ActorStateMachine, scope=ComponentScope.SINGLETON)
            logger.info("Registered ActorStateMachine class with dependency injection")

        # Register the singleton instance if it exists
        if _state_machine is not None:
            container.register_instance(_state_machine, "state_machine")
            logger.info("Registered state machine instance with dependency injection")

            # Register state transition hooks for dependency injection
            try:
                # Add a hook to detect circular dependencies when actors are initialized
                _state_machine.add_state_entry_hook(
                    ActorState.INITIALIZING,
                    lambda event: _check_actor_dependencies(event, container)
                )
                logger.info("Registered state transition hooks for dependency injection")
            except Exception as hook_error:
                logger.warning(f"Could not register state transition hooks: {hook_error}")

    except (ImportError, AttributeError) as e:
        logger.warning(f"Could not register with dependency injection: {e}")


def _check_actor_dependencies(event: 'StateChangeEvent', container: Any) -> None:
    """
    Check for circular dependencies in the actor system.

    This function is called when an actor enters the INITIALIZING state.
    It checks for circular dependencies in the dependency injection system
    and logs warnings if any are found.

    Args:
        event: The state change event
        container: The dependency injection container
    """
    try:
        # Detect cycles in the dependency graph
        cycles = container.detect_cycles()

        if cycles:
            # Log a warning for each cycle
            for cycle in cycles:
                cycle_str = " -> ".join(cycle)
                logger.warning(f"Circular dependency detected in actor system: {cycle_str}")

                # Check if the current actor is part of the cycle
                if event.actor_id in cycle:
                    logger.error(f"Actor {event.actor_id} is part of a circular dependency: {cycle_str}")

                    # Add the cycle to the event details
                    if "dependency_cycles" not in event.details:
                        event.details["dependency_cycles"] = []
                    event.details["dependency_cycles"].append(cycle)
    except Exception as e:
        logger.warning(f"Error checking actor dependencies: {e}")
