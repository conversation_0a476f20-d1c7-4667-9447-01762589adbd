"""
Supervisor Component Module
========================

This module defines the SupervisorComponent class, which is responsible for
handling supervision-related functionality for actors.

The SupervisorComponent implements the SupervisorProtocol and provides methods
for supervising other actors, handling heartbeats, and managing errors.

Enhanced with:
- Support for different supervision strategies
- Improved monitoring of supervised actors
- Support for hierarchical supervision
- Enhanced error handling and recovery mechanisms
- Comprehensive metrics collection
"""

import asyncio
import enum
import logging
import time
import traceback
from typing import Dict, Any, Optional, Set, List, Callable, Tuple, TYPE_CHECKING

from ..context_wave import ContextWave
from ..message import Message, MessageType
from ..protocols import SupervisorProtocol

# Avoid circular imports
if TYPE_CHECKING:
    from ..actor import Actor


class SupervisionStrategy(enum.Enum):
    """
    Enumeration of supervision strategies.

    These strategies determine how a supervisor responds to errors in supervised actors.
    """

    # Restart the actor on error
    RESTART = "restart"

    # Stop the actor on error
    STOP = "stop"

    # Escalate the error to the supervisor's supervisor
    ESCALATE = "escalate"

    # Resume the actor, ignoring the error
    RESUME = "resume"


class ActorStatus(enum.Enum):
    """
    Enumeration of actor status values.

    These values represent the current status of an actor in the supervision hierarchy.
    """

    # Actor is running normally
    RUNNING = "running"

    # Actor is stopped
    STOPPED = "stopped"

    # Actor is restarting
    RESTARTING = "restarting"

    # Actor is degraded (running with errors)
    DEGRADED = "degraded"

    # Actor status is unknown (no heartbeats received)
    UNKNOWN = "unknown"


class SupervisorComponent(SupervisorProtocol):
    """
    Handles supervision-related functionality for actors.

    This component is responsible for supervising other actors, handling
    heartbeats, and managing errors. It implements the SupervisorProtocol.

    Enhanced with support for different supervision strategies, improved monitoring
    of supervised actors, support for hierarchical supervision, enhanced error
    handling and recovery mechanisms, and comprehensive metrics collection.

    Attributes:
        actor: The actor that owns this component
        logger: Logger for this component
        supervised_actors: Set of actor IDs being supervised
        last_heartbeat: Timestamp of the last heartbeat sent
        heartbeat_interval: Interval between heartbeats in seconds
        restart_count: Number of restarts performed in the current window
        max_restarts: Maximum number of restarts allowed in the restart window
        restart_window: Time window for counting restarts in seconds
        last_restart_time: Timestamp of the last restart
        default_strategy: Default supervision strategy for supervised actors
        actor_status: Dictionary mapping actor IDs to their current status
        actor_strategies: Dictionary mapping actor IDs to their supervision strategies
        actor_last_heartbeat: Dictionary mapping actor IDs to their last heartbeat timestamp
        actor_error_count: Dictionary mapping actor IDs to their error count
        heartbeat_check_task: Task for checking heartbeats periodically
        heartbeat_timeout: Timeout for heartbeats in seconds
    """

    def __init__(self, actor: 'Actor') -> None:
        """
        Initialize the supervisor component.

        Args:
            actor: The actor that owns this component
        """
        self.actor = actor
        self.logger = logging.getLogger("vibe_check_actor_system.supervisor")

        # Supervision state
        self._supervised_actors: Set[str] = set()
        self._last_heartbeat: float = time.time()
        self._heartbeat_interval: float = 10.0  # seconds
        self._restart_count: int = 0
        self._max_restarts: int = 3
        self._restart_window: float = 60.0  # seconds
        self._last_restart_time: float = 0.0

        # Enhanced supervision state
        self._default_strategy: SupervisionStrategy = SupervisionStrategy.RESTART
        self._actor_status: Dict[str, ActorStatus] = {}
        self._actor_strategies: Dict[str, SupervisionStrategy] = {}
        self._actor_last_heartbeat: Dict[str, float] = {}
        self._actor_error_count: Dict[str, int] = {}
        self._heartbeat_check_task: Optional[asyncio.Task] = None
        self._heartbeat_timeout: float = 30.0  # seconds

        # Supervision hierarchy
        self._children: Dict[str, Set[str]] = {}  # actor_id -> set of child actor_ids
        self._parent: Dict[str, str] = {}  # actor_id -> parent actor_id

    @property
    def supervised_actors(self) -> Set[str]:
        """Get the set of supervised actor IDs."""
        return self._supervised_actors

    @property
    def default_strategy(self) -> SupervisionStrategy:
        """Get the default supervision strategy."""
        return self._default_strategy

    @default_strategy.setter
    def default_strategy(self, strategy: SupervisionStrategy) -> None:
        """
        Set the default supervision strategy.

        Args:
            strategy: The new default strategy
        """
        self._default_strategy = strategy
        self.logger.info(f"Actor {self.actor.actor_id} set default supervision strategy to {strategy.name}")

    def get_actor_status(self, actor_id: str) -> ActorStatus:
        """
        Get the status of a supervised actor.

        Args:
            actor_id: ID of the actor to get status for

        Returns:
            The actor's status

        Raises:
            ValueError: If the actor is not being supervised
        """
        if actor_id not in self._supervised_actors:
            raise ValueError(f"Actor {actor_id} is not being supervised")

        return self._actor_status.get(actor_id, ActorStatus.UNKNOWN)

    def get_actor_strategy(self, actor_id: str) -> SupervisionStrategy:
        """
        Get the supervision strategy for an actor.

        Args:
            actor_id: ID of the actor to get strategy for

        Returns:
            The actor's supervision strategy

        Raises:
            ValueError: If the actor is not being supervised
        """
        if actor_id not in self._supervised_actors:
            raise ValueError(f"Actor {actor_id} is not being supervised")

        return self._actor_strategies.get(actor_id, self._default_strategy)

    def set_actor_strategy(self, actor_id: str, strategy: SupervisionStrategy) -> None:
        """
        Set the supervision strategy for an actor.

        Args:
            actor_id: ID of the actor to set strategy for
            strategy: The new strategy

        Raises:
            ValueError: If the actor is not being supervised
        """
        if actor_id not in self._supervised_actors:
            raise ValueError(f"Actor {actor_id} is not being supervised")

        self._actor_strategies[actor_id] = strategy
        self.logger.info(f"Actor {self.actor.actor_id} set supervision strategy for {actor_id} to {strategy.name}")

    async def supervise(self, actor_id: str, strategy: Optional[SupervisionStrategy] = None,
                      parent_id: Optional[str] = None) -> None:
        """
        Start supervising another actor.

        Enhanced to support supervision strategies and hierarchical supervision.

        Args:
            actor_id: ID of the actor to supervise
            strategy: Optional supervision strategy for this actor
            parent_id: Optional parent actor ID for hierarchical supervision

        Raises:
            ValueError: If the actor is already being supervised
            RuntimeError: If there is an error sending the supervise message
        """
        if actor_id in self._supervised_actors:
            self.logger.warning(f"Actor {self.actor.actor_id} is already supervising {actor_id}")
            return

        # Add to supervised actors
        self._supervised_actors.add(actor_id)

        # Set initial status
        self._actor_status[actor_id] = ActorStatus.UNKNOWN
        self._actor_last_heartbeat[actor_id] = time.time()
        self._actor_error_count[actor_id] = 0

        # Set strategy
        if strategy is not None:
            self._actor_strategies[actor_id] = strategy
        else:
            self._actor_strategies[actor_id] = self._default_strategy

        # Set up supervision hierarchy
        if parent_id is not None:
            self._parent[actor_id] = parent_id
            if parent_id not in self._children:
                self._children[parent_id] = set()
            self._children[parent_id].add(actor_id)

        # Send supervise message to the actor
        try:
            supervise_payload = {
                "supervisor_id": self.actor.actor_id,
                "timestamp": time.time(),
                "strategy": self._actor_strategies[actor_id].value,
                "parent_id": parent_id
            }
            await self.actor.send(actor_id, MessageType.SUPERVISE, supervise_payload)
            self.logger.info(f"Actor {self.actor.actor_id} started supervising {actor_id} with strategy {self._actor_strategies[actor_id].name}")

            # Start heartbeat check task if not already running
            if self._heartbeat_check_task is None or self._heartbeat_check_task.done():
                self._heartbeat_check_task = asyncio.create_task(self._check_heartbeats())
                self.logger.debug(f"Started heartbeat check task for actor {self.actor.actor_id}")
        except Exception as e:
            self.logger.error(f"Failed to start supervising actor {actor_id}: {e}")
            self._supervised_actors.remove(actor_id)
            if actor_id in self._actor_status:
                del self._actor_status[actor_id]
            if actor_id in self._actor_last_heartbeat:
                del self._actor_last_heartbeat[actor_id]
            if actor_id in self._actor_error_count:
                del self._actor_error_count[actor_id]
            if actor_id in self._actor_strategies:
                del self._actor_strategies[actor_id]
            if parent_id is not None and parent_id in self._children and actor_id in self._children[parent_id]:
                self._children[parent_id].remove(actor_id)
            if actor_id in self._parent:
                del self._parent[actor_id]
            raise RuntimeError(f"Failed to start supervising actor {actor_id}: {e}") from e

    async def unsupervise(self, actor_id: str, cleanup_hierarchy: bool = True) -> None:
        """
        Stop supervising an actor.

        Enhanced to support hierarchical supervision and cleanup of supervision state.

        Args:
            actor_id: ID of the actor to stop supervising
            cleanup_hierarchy: Whether to clean up the supervision hierarchy

        Raises:
            ValueError: If the actor is not being supervised
            RuntimeError: If there is an error sending the unsupervise message
        """
        if actor_id not in self._supervised_actors:
            self.logger.warning(f"Actor {self.actor.actor_id} is not supervising {actor_id}")
            return

        # Remove from supervised actors
        self._supervised_actors.remove(actor_id)

        # Clean up supervision state
        if actor_id in self._actor_status:
            del self._actor_status[actor_id]
        if actor_id in self._actor_last_heartbeat:
            del self._actor_last_heartbeat[actor_id]
        if actor_id in self._actor_error_count:
            del self._actor_error_count[actor_id]
        if actor_id in self._actor_strategies:
            del self._actor_strategies[actor_id]

        # Clean up supervision hierarchy
        if cleanup_hierarchy:
            # Get parent and children
            parent_id = self._parent.get(actor_id)
            children = self._children.get(actor_id, set())

            # Remove from parent's children
            if parent_id is not None and parent_id in self._children:
                if actor_id in self._children[parent_id]:
                    self._children[parent_id].remove(actor_id)
                if not self._children[parent_id]:
                    del self._children[parent_id]

            # Remove from parent mapping
            if actor_id in self._parent:
                del self._parent[actor_id]

            # Reassign children to this actor's parent
            if parent_id is not None:
                for child_id in children:
                    if child_id in self._parent:
                        self._parent[child_id] = parent_id
                    if parent_id not in self._children:
                        self._children[parent_id] = set()
                    self._children[parent_id].add(child_id)

            # Remove from children mapping
            if actor_id in self._children:
                del self._children[actor_id]

        # Send unsupervise message to the actor
        try:
            unsupervise_payload = {
                "supervisor_id": self.actor.actor_id,
                "timestamp": time.time(),
                "cleanup_hierarchy": cleanup_hierarchy
            }
            await self.actor.send(actor_id, MessageType.UNSUPERVISE, unsupervise_payload)
            self.logger.info(f"Actor {self.actor.actor_id} stopped supervising {actor_id}")

            # Stop heartbeat check task if no more supervised actors
            if not self._supervised_actors and self._heartbeat_check_task is not None and not self._heartbeat_check_task.done():
                self._heartbeat_check_task.cancel()
                try:
                    await self._heartbeat_check_task
                except asyncio.CancelledError:
                    pass
                self._heartbeat_check_task = None
                self.logger.debug(f"Stopped heartbeat check task for actor {self.actor.actor_id}")
        except Exception as e:
            self.logger.error(f"Failed to stop supervising actor {actor_id}: {e}")
            # Add back to supervised actors to maintain consistency
            self._supervised_actors.add(actor_id)
            raise RuntimeError(f"Failed to stop supervising actor {actor_id}: {e}") from e

    async def handle_heartbeat(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a heartbeat message from a supervised actor.

        Enhanced to update actor status and metrics, and to detect actor recovery.

        Args:
            payload: Heartbeat payload
            context: Message context

        Raises:
            ValueError: If the actor is not being supervised
        """
        actor_id = payload.get("actor_id")
        timestamp = payload.get("timestamp")
        metrics = payload.get("metrics", {})
        status = payload.get("status")
        supervision_confirmed = payload.get("supervision_confirmed", False)

        if not actor_id:
            self.logger.warning("Received heartbeat without actor_id")
            return

        if actor_id not in self._supervised_actors:
            self.logger.warning(f"Received heartbeat from unsupervised actor {actor_id}")
            return

        # Update last heartbeat time
        self._actor_last_heartbeat[actor_id] = time.time()

        # Update actor status if provided
        if status is not None:
            try:
                new_status = ActorStatus(status)
                old_status = self._actor_status.get(actor_id, ActorStatus.UNKNOWN)
                self._actor_status[actor_id] = new_status

                # Log status change
                if new_status != old_status:
                    self.logger.info(f"Actor {actor_id} status changed from {old_status.value} to {new_status.value}")

                    # If actor has recovered from DEGRADED or RESTARTING, reset error count
                    if old_status in (ActorStatus.DEGRADED, ActorStatus.RESTARTING) and new_status == ActorStatus.RUNNING:
                        self._actor_error_count[actor_id] = 0
                        self.logger.info(f"Actor {actor_id} has recovered, reset error count")
            except ValueError:
                self.logger.warning(f"Received invalid status '{status}' from actor {actor_id}")
        else:
            # If no status provided, assume RUNNING
            self._actor_status[actor_id] = ActorStatus.RUNNING

        # Log supervision confirmation
        if supervision_confirmed:
            self.logger.info(f"Actor {actor_id} confirmed supervision by {self.actor.actor_id}")

        self.logger.debug(f"Actor {self.actor.actor_id} received heartbeat from {actor_id} at {timestamp}")

        # Process metrics if needed
        if metrics:
            # Update actor metrics in the actor system registry if available
            try:
                from ..actor_registry import get_registry
                registry = get_registry()
                if registry is not None:
                    registry.update_actor_metrics(actor_id, metrics)
            except (ImportError, AttributeError):
                pass

            # Log interesting metrics
            if "errors" in metrics and metrics["errors"] > 0:
                self.logger.warning(f"Actor {actor_id} reported {metrics['errors']} errors")
            if "messages_processed" in metrics and "avg_processing_time" in metrics:
                self.logger.debug(f"Actor {actor_id} processed {metrics['messages_processed']} messages with avg time {metrics['avg_processing_time']:.6f}s")

            self.logger.debug(f"Actor {actor_id} metrics: {metrics}")

    async def handle_error(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle an error message from a supervised actor.

        Enhanced to support different supervision strategies and error categorization.

        Args:
            payload: Error payload
            context: Message context
        """
        error = payload.get("error", "Unknown error")
        original_type = payload.get("original_message_type", "Unknown")
        actor_id = payload.get("actor_id", payload.get("sender_id"))
        fatal = payload.get("fatal", False)
        error_category = payload.get("category", "general")
        stack_trace = payload.get("stack_trace")

        if actor_id and actor_id in self._supervised_actors:
            # This is an error from a supervised actor
            self.logger.error(f"Supervised actor {actor_id} reported error: {error}")

            # Log stack trace if available
            if stack_trace:
                self.logger.error(f"Stack trace for actor {actor_id}:\n{stack_trace}")

            # Update actor status to DEGRADED
            old_status = self._actor_status.get(actor_id, ActorStatus.UNKNOWN)
            self._actor_status[actor_id] = ActorStatus.DEGRADED
            if old_status != ActorStatus.DEGRADED:
                self.logger.info(f"Actor {actor_id} status changed from {old_status.value} to {ActorStatus.DEGRADED.value}")

            # Increment error count
            self._actor_error_count[actor_id] = self._actor_error_count.get(actor_id, 0) + 1
            error_count = self._actor_error_count[actor_id]
            self.logger.info(f"Actor {actor_id} error count: {error_count}")

            # Get supervision strategy for this actor
            strategy = self._actor_strategies.get(actor_id, self._default_strategy)

            # Apply supervision strategy
            if fatal or error_count >= self._max_restarts:
                if strategy == SupervisionStrategy.RESTART:
                    # Restart the actor
                    await self._restart_supervised_actor(actor_id)
                elif strategy == SupervisionStrategy.STOP:
                    # Stop the actor
                    await self._stop_supervised_actor(actor_id)
                elif strategy == SupervisionStrategy.ESCALATE:
                    # Escalate to parent supervisor
                    await self._escalate_error(actor_id, error, error_category, stack_trace)
                elif strategy == SupervisionStrategy.RESUME:
                    # Resume the actor, ignoring the error
                    self.logger.info(f"Resuming actor {actor_id} despite error (strategy: {strategy.name})")
                    # Send resume message
                    await self._send_resume_message(actor_id)
            else:
                self.logger.info(f"Non-fatal error in actor {actor_id}, continuing with strategy {strategy.name}")
        else:
            # This is an error for this actor or from an unknown actor
            if actor_id:
                self.logger.error(f"Received error from non-supervised actor {actor_id}: {error}")
            else:
                self.logger.error(f"Actor {self.actor.actor_id} received error: {error} (from message type: {original_type})")

            # If this actor has a supervisor, escalate the error
            if self.actor.supervisor_id:
                error_payload = {
                    "error": error,
                    "actor_id": self.actor.actor_id,
                    "original_message_type": original_type,
                    "category": error_category,
                    "stack_trace": stack_trace,
                    "fatal": fatal
                }
                try:
                    await self.actor.send(self.actor.supervisor_id, MessageType.ERROR, error_payload)
                    self.logger.info(f"Escalated error to supervisor {self.actor.supervisor_id}")
                except Exception as e:
                    self.logger.error(f"Failed to escalate error to supervisor {self.actor.supervisor_id}: {e}")

    async def _restart_supervised_actor(self, actor_id: str) -> None:
        """
        Restart a supervised actor.

        Enhanced to update actor status and handle restart failures.

        Args:
            actor_id: ID of the actor to restart

        Raises:
            ValueError: If the actor is not being supervised
            RuntimeError: If the actor has been restarted too many times
        """
        if actor_id not in self._supervised_actors:
            raise ValueError(f"Actor {actor_id} is not being supervised")

        # Update actor status
        self._actor_status[actor_id] = ActorStatus.RESTARTING

        # Check if we've restarted too many times
        current_time = time.time()
        if current_time - self._last_restart_time < self._restart_window:
            self._restart_count += 1
        else:
            self._restart_count = 1
            self._last_restart_time = current_time

        if self._restart_count > self._max_restarts:
            self.logger.error(f"Actor {actor_id} has been restarted {self._restart_count} times in {self._restart_window}s, giving up")

            # Apply escalation if available
            if self.actor.supervisor_id:
                await self._escalate_error(
                    actor_id,
                    f"Actor has been restarted {self._restart_count} times in {self._restart_window}s, giving up",
                    "restart_limit_exceeded",
                    None
                )

            # Stop supervising the actor
            await self.unsupervise(actor_id)
            raise RuntimeError(f"Actor {actor_id} has been restarted too many times")

        self.logger.info(f"Restarting actor {actor_id} (restart {self._restart_count} of {self._max_restarts})")

        try:
            # Send restart message to the actor
            restart_payload = {
                "supervisor_id": self.actor.actor_id,
                "timestamp": time.time(),
                "restart_count": self._restart_count,
                "strategy": self._actor_strategies.get(actor_id, self._default_strategy).value
            }
            await self.actor.send(actor_id, MessageType.RESTART, restart_payload)
            self.logger.info(f"Sent restart message to actor {actor_id}")
        except Exception as e:
            self.logger.error(f"Failed to restart actor {actor_id}: {e}")

            # Update error count
            self._actor_error_count[actor_id] = self._actor_error_count.get(actor_id, 0) + 1

            # Try to escalate if available
            if self.actor.supervisor_id:
                await self._escalate_error(
                    actor_id,
                    f"Failed to restart actor: {e}",
                    "restart_failure",
                    traceback.format_exc()
                )

            raise RuntimeError(f"Failed to restart actor {actor_id}: {e}") from e

    async def _stop_supervised_actor(self, actor_id: str) -> None:
        """
        Stop a supervised actor.

        Args:
            actor_id: ID of the actor to stop

        Raises:
            ValueError: If the actor is not being supervised
            RuntimeError: If there is an error stopping the actor
        """
        if actor_id not in self._supervised_actors:
            raise ValueError(f"Actor {actor_id} is not being supervised")

        # Update actor status
        self._actor_status[actor_id] = ActorStatus.STOPPED

        try:
            # Send stop message to the actor
            stop_payload = {
                "supervisor_id": self.actor.actor_id,
                "timestamp": time.time(),
                "reason": "Stopped by supervisor due to errors"
            }
            await self.actor.send(actor_id, MessageType.STOP, stop_payload)
            self.logger.info(f"Sent stop message to actor {actor_id}")

            # Stop supervising the actor
            await self.unsupervise(actor_id)
        except Exception as e:
            self.logger.error(f"Failed to stop actor {actor_id}: {e}")

            # Try to escalate if available
            if self.actor.supervisor_id:
                await self._escalate_error(
                    actor_id,
                    f"Failed to stop actor: {e}",
                    "stop_failure",
                    traceback.format_exc()
                )

            raise RuntimeError(f"Failed to stop actor {actor_id}: {e}") from e

    async def _escalate_error(self, actor_id: str, error: str, category: str, stack_trace: Optional[str]) -> None:
        """
        Escalate an error to this actor's supervisor.

        Args:
            actor_id: ID of the actor that had the error
            error: Error message
            category: Error category
            stack_trace: Optional stack trace

        Raises:
            RuntimeError: If there is an error escalating the error
        """
        if not self.actor.supervisor_id:
            self.logger.warning(f"Cannot escalate error for actor {actor_id}: no supervisor")
            return

        try:
            # Create error payload
            error_payload = {
                "error": error,
                "actor_id": actor_id,
                "escalated_by": self.actor.actor_id,
                "category": category,
                "stack_trace": stack_trace,
                "timestamp": time.time(),
                "fatal": True
            }

            # Send error to supervisor
            await self.actor.send(self.actor.supervisor_id, MessageType.ERROR, error_payload)
            self.logger.info(f"Escalated error for actor {actor_id} to supervisor {self.actor.supervisor_id}")
        except Exception as e:
            self.logger.error(f"Failed to escalate error for actor {actor_id}: {e}")
            raise RuntimeError(f"Failed to escalate error for actor {actor_id}: {e}") from e

    async def _send_resume_message(self, actor_id: str) -> None:
        """
        Send a resume message to an actor.

        Args:
            actor_id: ID of the actor to resume

        Raises:
            ValueError: If the actor is not being supervised
            RuntimeError: If there is an error sending the resume message
        """
        if actor_id not in self._supervised_actors:
            raise ValueError(f"Actor {actor_id} is not being supervised")

        try:
            # Send resume message to the actor
            resume_payload = {
                "supervisor_id": self.actor.actor_id,
                "timestamp": time.time()
            }
            await self.actor.send(actor_id, MessageType.RESUME, resume_payload)
            self.logger.info(f"Sent resume message to actor {actor_id}")
        except Exception as e:
            self.logger.error(f"Failed to send resume message to actor {actor_id}: {e}")
            raise RuntimeError(f"Failed to send resume message to actor {actor_id}: {e}") from e

    async def _check_heartbeats(self) -> None:
        """
        Check heartbeats from supervised actors periodically.

        This method runs in a loop, checking heartbeats at the specified interval.
        If an actor hasn't sent a heartbeat within the timeout period, it's marked
        as UNKNOWN and potentially restarted.
        """
        try:
            while self.actor.is_running and self._supervised_actors:
                current_time = time.time()

                # Check each supervised actor
                for actor_id in list(self._supervised_actors):
                    if actor_id in self._actor_last_heartbeat:
                        last_heartbeat = self._actor_last_heartbeat[actor_id]
                        time_since_heartbeat = current_time - last_heartbeat

                        # If no heartbeat received within timeout, mark as UNKNOWN
                        if time_since_heartbeat > self._heartbeat_timeout:
                            old_status = self._actor_status.get(actor_id, ActorStatus.UNKNOWN)
                            if old_status != ActorStatus.UNKNOWN:
                                self._actor_status[actor_id] = ActorStatus.UNKNOWN
                                self.logger.warning(f"Actor {actor_id} status changed to {ActorStatus.UNKNOWN.value} (no heartbeat for {time_since_heartbeat:.1f}s)")

                                # Get supervision strategy for this actor
                                strategy = self._actor_strategies.get(actor_id, self._default_strategy)

                                # Apply supervision strategy for heartbeat timeout
                                if strategy == SupervisionStrategy.RESTART:
                                    # Restart the actor
                                    try:
                                        await self._restart_supervised_actor(actor_id)
                                    except Exception as e:
                                        self.logger.error(f"Failed to restart actor {actor_id} after heartbeat timeout: {e}")
                                elif strategy == SupervisionStrategy.STOP:
                                    # Stop the actor
                                    try:
                                        await self._stop_supervised_actor(actor_id)
                                    except Exception as e:
                                        self.logger.error(f"Failed to stop actor {actor_id} after heartbeat timeout: {e}")
                                elif strategy == SupervisionStrategy.ESCALATE:
                                    # Escalate to parent supervisor
                                    try:
                                        await self._escalate_error(
                                            actor_id,
                                            f"Heartbeat timeout (no heartbeat for {time_since_heartbeat:.1f}s)",
                                            "heartbeat_timeout",
                                            None
                                        )
                                    except Exception as e:
                                        self.logger.error(f"Failed to escalate error for actor {actor_id} after heartbeat timeout: {e}")

                # Wait for next check interval
                await asyncio.sleep(self._heartbeat_interval)
        except asyncio.CancelledError:
            self.logger.info(f"Heartbeat check task cancelled for actor {self.actor.actor_id}")
        except Exception as e:
            self.logger.error(f"Error in heartbeat check task for actor {self.actor.actor_id}: {e}")
            error_details = traceback.format_exc()
            self.logger.error(error_details)

    async def handle_supervise(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a supervision request from another actor.

        This method is called when another actor wants to supervise this actor.
        It updates the supervisor_id and sends a heartbeat to confirm the supervision.

        Enhanced to support supervision strategies and hierarchical supervision.

        Args:
            payload: Supervision payload
            context: Message context
        """
        supervisor_id = payload.get("supervisor_id")
        strategy_value = payload.get("strategy")
        parent_id = payload.get("parent_id")

        if not supervisor_id:
            self.logger.warning(f"Actor {self.actor.actor_id} received supervise message without supervisor_id")
            return

        # Update supervisor ID
        old_supervisor = self.actor.supervisor_id
        self.actor._supervisor_id = supervisor_id

        # Update supervision hierarchy
        if parent_id is not None:
            self.logger.info(f"Actor {self.actor.actor_id} has parent {parent_id}")

        # Update supervision strategy if provided
        if strategy_value is not None:
            try:
                strategy = SupervisionStrategy(strategy_value)
                self.logger.info(f"Actor {self.actor.actor_id} will be supervised with strategy {strategy.name}")
            except ValueError:
                self.logger.warning(f"Received invalid supervision strategy '{strategy_value}' from supervisor {supervisor_id}")

        self.logger.info(f"Actor {self.actor.actor_id} is now supervised by {supervisor_id} (was {old_supervisor})")

        # Send a heartbeat to confirm supervision
        try:
            # Create heartbeat payload
            heartbeat_payload = {
                "actor_id": self.actor.actor_id,
                "timestamp": time.time(),
                "metrics": self.actor._metrics,
                "status": ActorStatus.RUNNING.value,
                "supervision_confirmed": True,
                "parent_id": parent_id
            }

            # Send heartbeat
            await self.actor.send(
                supervisor_id,
                MessageType.HEARTBEAT,
                heartbeat_payload,
                context.propagate()
            )

            # Update last heartbeat time
            self._last_heartbeat = time.time()

            self.logger.info(f"Actor {self.actor.actor_id} sent confirmation heartbeat to new supervisor {supervisor_id}")
        except Exception as e:
            self.logger.error(f"Failed to send confirmation heartbeat to supervisor {supervisor_id}: {e}")
            error_details = traceback.format_exc()
            self.logger.error(error_details)

            # Reset supervisor ID if we couldn't confirm supervision
            self.actor._supervisor_id = old_supervisor
            self.logger.info(f"Reset supervisor ID to {old_supervisor} due to heartbeat failure")

            raise RuntimeError(f"Failed to send confirmation heartbeat to supervisor {supervisor_id}: {e}") from e
