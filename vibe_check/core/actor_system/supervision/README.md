# Supervision Components

This directory contains the supervision components for the actor system. These components are responsible for handling supervision-related functionality, including supervising other actors, handling heartbeats, and managing errors.

## Components

### SupervisorComponent

The `SupervisorComponent` class is responsible for supervising other actors, handling heartbeats, and managing errors. It implements the `SupervisorProtocol` and provides methods for supervising other actors, handling heartbeats, and managing errors.

```python
# Example usage
supervisor = SupervisorComponent(actor)
await supervisor.supervise("actor_id", strategy=SupervisionStrategy.RESTART)
```

### SupervisionStrategy

The `SupervisionStrategy` enum defines the different strategies that can be used for supervision:

- `RESTART`: Restart the actor on error
- `STOP`: Stop the actor on error
- `ESCALATE`: Escalate the error to the supervisor's supervisor
- `RESUME`: Resume the actor, ignoring the error

```python
# Example usage
strategy = SupervisionStrategy.RESTART
```

### ActorStatus

The `ActorStatus` enum defines the different status values that an actor can have:

- `RUNNING`: Actor is running normally
- `STOPPED`: Actor is stopped
- `RESTARTING`: Actor is restarting
- `DEGRADED`: Actor is running with errors
- `UNKNOWN`: Actor status is unknown (no heartbeats received)

```python
# Example usage
status = ActorStatus.RUNNING
```

## Protocols

These components implement the following protocols:

- `SupervisorProtocol`: Defines the interface for actor supervisors

## Integration with Actor System

These components are used by the `Actor` class to handle supervision-related functionality. The `Actor` class delegates to these components to reduce its complexity and make it easier to test and maintain.

```python
# Example integration in Actor class
class Actor:
    def __init__(self, actor_id, ...):
        # ...
        self._supervisor_component = SupervisorComponent(self)
        # ...

    async def supervise(self, actor_id, strategy=None, parent_id=None):
        await self._supervisor_component.supervise(actor_id, strategy, parent_id)

    async def handle_heartbeat(self, payload, context):
        await self._supervisor_component.handle_heartbeat(payload, context)

    async def handle_error(self, payload, context):
        await self._supervisor_component.handle_error(payload, context)
```

## Supervision Hierarchy

The supervision components support hierarchical supervision, where actors can be organized in a tree-like structure. Each actor can have a parent and multiple children, and errors can be escalated up the hierarchy.

```
supervisor_actor
├── actor1
│   ├── actor3
│   └── actor4
└── actor2
    └── actor5
```

In this example, `supervisor_actor` supervises `actor1` and `actor2`, `actor1` supervises `actor3` and `actor4`, and `actor2` supervises `actor5`. If an error occurs in `actor3`, it can be escalated to `actor1`, and if `actor1` can't handle it, it can be escalated to `supervisor_actor`.

## Error Handling

The supervision components provide robust error handling, including:

- Different supervision strategies for different types of errors
- Error categorization for more fine-grained handling
- Heartbeat monitoring to detect actor failures
- Automatic restart of failed actors
- Escalation of errors to parent supervisors
- Comprehensive metrics collection for monitoring and debugging

## Future Enhancements

- Support for custom supervision strategies
- More sophisticated error handling based on error categories
- Better integration with the actor system's metrics collection
- Support for distributed supervision across multiple nodes
