"""
Message Module for Actor System
==============================

This module defines the Message class, MessageType enum, and MessagePriority enum
for the actor system. Messages are passed between actors and contain both concrete
data (particle aspect) and contextual information (wave aspect), implementing the
CAW principle of wave-particle duality.
"""

from dataclasses import asdict, dataclass, field
from enum import Enum, auto, IntEnum
from typing import Any, Dict, Optional

from .context_wave import ContextWave


class MessagePriority(IntEnum):
    """
    Priority levels for messages.

    These priorities determine the order in which messages are processed,
    with higher values indicating higher priority.

    Attributes:
        BACKGROUND: Lowest priority, for non-essential background tasks
        LOW: Low priority, for tasks that can wait
        NORMAL: Default priority for most messages
        HIGH: High priority, for important messages
        CRITICAL: Highest priority, for urgent messages
    """
    BACKGROUND = 0
    LOW = 2
    NORMAL = 5
    HIGH = 7
    CRITICAL = 9


class MessageType(Enum):
    """Types of messages that can be sent between actors."""
    # System messages
    REGISTER = auto()  # Register an actor with the system
    DISCOVER = auto()  # Discover actors in the system
    HEARTBEAT = auto()  # Heartbeat message for health monitoring
    METRICS = auto()   # Actor performance metrics

    # Lifecycle messages
    INITIALIZE = auto() # Initialize an actor
    START = auto()     # Start an actor
    STOP = auto()      # Stop an actor
    RESTART = auto()   # Restart an actor
    SUSPEND = auto()   # Suspend an actor
    RESUME = auto()    # Resume a suspended actor

    # Supervision messages
    SUPERVISE = auto()    # Start supervising an actor
    UNSUPERVISE = auto()  # Stop supervising an actor
    MONITOR = auto()      # Monitor an actor's health
    ESCALATE = auto()     # Escalate an issue to a supervisor

    # Analysis initialization
    INIT_ANALYSIS = auto()
    ANALYZE_PROJECT = auto()
    ANALYSIS_COMPLETE = auto()  # Analysis has completed
    ANALYSIS_PROGRESS = auto()  # Progress update on analysis

    # File-related messages
    FILE_METADATA = auto()
    FILE_CONTENT = auto()
    FILE_DISCOVERED = auto()  # New file discovered
    FILE_CHANGED = auto()     # File has changed
    FILE_DELETED = auto()     # File has been deleted

    # Directory-related messages
    DIRECTORY_METADATA = auto()
    DIRECTORY_CONTENT = auto()
    DIRECTORY_DISCOVERED = auto()  # New directory discovered
    DIRECTORY_CHANGED = auto()     # Directory has changed
    DIRECTORY_DELETED = auto()     # Directory has been deleted

    # Request-related messages
    REQUEST = auto()          # Generic request message

    # Analysis-related messages
    REQUEST_ANALYSIS = auto()
    EXECUTE_TOOL = auto()
    ANALYSIS_RESULT = auto()
    TOOL_RESULT = auto()      # Result from a specific tool
    TOOL_RESULTS = auto()     # Results from multiple tools
    ANALYZE_FILE = auto()     # Request to analyze a file
    SECURITY_ISSUE = auto()   # Security issue found
    QUALITY_ISSUE = auto()    # Code quality issue found
    COMPLEXITY_ISSUE = auto() # Complexity issue found
    TYPE_ISSUE = auto()       # Type checking issue found
    DOC_ISSUE = auto()        # Documentation issue found
    TOOL_CONFIGURED = auto()  # Tool has been configured
    TOOL_CONFIG = auto()      # Tool configuration
    LIST_TOOLS = auto()       # Request to list available tools
    TOOL_LIST = auto()        # List of available tools
    TOOL_ERROR = auto()       # Error from a tool

    # Reporting-related messages
    GENERATE_REPORT = auto()
    REQUEST_VISUALIZATION = auto()
    VISUALIZATION = auto()
    GENERATE_VISUALIZATION = auto()
    FINAL_REPORT = auto()
    FILE_METRICS = auto()     # Metrics for a file
    DIRECTORY_METRICS = auto() # Metrics for a directory
    PROJECT_METRICS = auto()  # Metrics for the project
    AGGREGATE_METRICS = auto() # Request to aggregate metrics

    # Visualization-related messages
    VISUALIZATION_CONFIGURED = auto()  # Visualization has been configured
    VISUALIZATION_COMPLETED = auto()   # Visualization has been completed
    VISUALIZATION_RESULT = auto()      # Result of a visualization request

    # Stream-related messages
    SUBSCRIBE = auto()        # Subscribe to a stream
    UNSUBSCRIBE = auto()      # Unsubscribe from a stream
    PUBLISH = auto()          # Publish to a stream
    STREAM_DATA = auto()      # Data from a stream

    # State persistence messages
    SAVE_STATE = auto()       # Save actor state
    LOAD_STATE = auto()       # Load actor state
    STATE_SAVED = auto()      # State has been saved
    STATE_LOADED = auto()     # State has been loaded

    # Distribution messages
    REMOTE_INVOKE = auto()    # Invoke a method on a remote actor
    REMOTE_RESULT = auto()    # Result from a remote invocation
    NODE_JOINED = auto()      # A new node has joined the cluster
    NODE_LEFT = auto()        # A node has left the cluster

    # Pool-related messages
    POOL_ASSIGN = auto()      # Assign a task to a pool
    POOL_RESULT = auto()      # Result from a pool
    POOL_STATUS = auto()      # Status of a pool

    # Control messages
    ERROR = auto()
    STATUS = auto()
    CONFIG_UPDATE = auto()
    SYSTEM_CONFIG = auto()    # System configuration update
    SHUTDOWN = auto()         # Shutdown the system

    # Testing messages
    TEST = auto()            # Test message
    FAIL = auto()            # Fail message for testing
    CUSTOM = auto()          # Custom message for testing

    # Actor pool messages
    TASK_ASSIGN = auto()     # Assign a task to an actor in a pool
    TASK_RESULT = auto()     # Result from a task assigned to an actor
    TASK_COMPLETE = auto()   # Task has been completed
    TASK_FAILED = auto()     # Task has failed

    # Initialization messages
    READY = auto()           # Actor is ready to process messages
    INITIALIZED = auto()     # Actor has been initialized

    # Default
    UNKNOWN = auto()


@dataclass
class Message:
    """
    Message passed between actors.

    Implements the CAW principle of wave-particle duality by combining
    concrete message data (particle) with contextual information (wave).

    Enhanced with additional features for advanced actor system capabilities:
    - Message ID for tracking and correlation
    - Sender ID for identifying the source
    - Reply-to ID for request-response patterns
    - Stream ID for publish-subscribe patterns
    - Priority for message prioritization
    - TTL (Time-to-Live) for message expiration
    - Timestamp for timing and ordering
    - Correlation ID for tracking related messages
    - Serialization format for distributed actors
    """
    type: MessageType
    payload: Dict[str, Any] = field(default_factory=dict)
    context: Optional[ContextWave] = None
    recipient_id: Optional[str] = None
    sender_id: Optional[str] = None
    message_id: Optional[str] = None
    reply_to: Optional[str] = None
    stream_id: Optional[str] = None
    priority: int = MessagePriority.NORMAL
    ttl: Optional[int] = None
    timestamp: Optional[float] = None
    correlation_id: Optional[str] = None
    serialization_format: str = "json"

    def __post_init__(self) -> None:
        """
        Initialize default values if not provided and validate the message.

        This method ensures that all required fields are present and valid,
        generating defaults where appropriate and inferring values from context
        when possible.
        """
        import logging
        import time
        import uuid

        logger = logging.getLogger("vibe_check_actor_system")

        # Create context if not provided
        if self.context is None:
            self.context = ContextWave()

        # Generate message ID if not provided
        if self.message_id is None:
            self.message_id = str(uuid.uuid4())

        # Set timestamp if not provided
        if self.timestamp is None:
            self.timestamp = time.time()

        # Try to infer sender_id from context if not provided
        if self.sender_id is None and self.context is not None:
            self.sender_id = self.context.metadata.get("sender_id")

        # Try to infer recipient_id from context if not provided
        if self.recipient_id is None and self.context is not None:
            self.recipient_id = self.context.metadata.get("recipient_id")

            # Log a warning if recipient_id is still None
            if self.recipient_id is None:
                logger.warning(
                    f"Message of type {self.type.name} created without recipient_id. "
                    f"This may cause routing issues. Message ID: {self.message_id}"
                )

        # Validate priority
        if not isinstance(self.priority, int) or self.priority < MessagePriority.BACKGROUND or self.priority > MessagePriority.CRITICAL:
            logger.warning(
                f"Invalid priority {self.priority} for message of type {self.type.name}. "
                f"Using default priority {MessagePriority.NORMAL}. Message ID: {self.message_id}"
            )
            self.priority = MessagePriority.NORMAL

        # Validate TTL
        if self.ttl is not None and (not isinstance(self.ttl, (int, float)) or self.ttl <= 0):
            logger.warning(
                f"Invalid TTL {self.ttl} for message of type {self.type.name}. "
                f"TTL must be a positive number. Setting to None. Message ID: {self.message_id}"
            )
            self.ttl = None

        # Add message metadata to context for tracing
        self.context.metadata["message_id"] = self.message_id
        self.context.metadata["message_type"] = self.type.name
        self.context.metadata["timestamp"] = self.timestamp

        if self.sender_id:
            self.context.metadata["sender_id"] = self.sender_id
        if self.recipient_id:
            self.context.metadata["recipient_id"] = self.recipient_id
        if self.correlation_id:
            self.context.metadata["correlation_id"] = self.correlation_id

    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary for serialization."""
        return {
            "type": self.type.name,
            "payload": self.payload,
            "context": asdict(self.context) if self.context is not None else None,
            "recipient_id": self.recipient_id,
            "sender_id": self.sender_id,
            "message_id": self.message_id,
            "reply_to": self.reply_to,
            "stream_id": self.stream_id,
            "priority": self.priority,
            "ttl": self.ttl,
            "timestamp": self.timestamp,
            "correlation_id": self.correlation_id,
            "serialization_format": self.serialization_format
        }

    def is_expired(self) -> bool:
        """
        Check if the message has expired based on TTL.

        Returns:
            True if the message has expired, False otherwise
        """
        import time

        # If TTL is None, the message never expires
        if self.ttl is None:
            return False

        # If timestamp is None (should never happen due to __post_init__),
        # use a safe default
        if self.timestamp is None:
            return False

        # Calculate age of message
        current_time = time.time()
        message_age = current_time - self.timestamp

        # Check if message has exceeded its TTL
        return message_age > self.ttl

    def create_reply(self, reply_type: MessageType, reply_payload: Dict[str, Any]) -> 'Message':
        """
        Create a reply message to this message.

        This method creates a new message that is a reply to the current message,
        propagating the context and setting appropriate sender/recipient IDs.

        Args:
            reply_type: Type of the reply message
            reply_payload: Payload for the reply message

        Returns:
            A new Message instance configured as a reply to this message

        Raises:
            ValueError: If the original message doesn't have a sender_id to reply to
        """
        # Ensure we have a sender to reply to
        if not self.sender_id:
            import logging
            logger = logging.getLogger("vibe_check_actor_system")
            logger.warning(
                f"Cannot create reply to message {self.message_id} of type {self.type.name} "
                f"because it has no sender_id"
            )
            # Use recipient_id as a fallback if available
            if not self.recipient_id:
                raise ValueError(
                    f"Cannot create reply to message {self.message_id} of type {self.type.name} "
                    f"because it has no sender_id or recipient_id"
                )

        # Propagate context to maintain history and metadata
        reply_context = self.context.propagate() if self.context is not None else ContextWave()

        # Create the reply message
        return Message(
            type=reply_type,
            payload=reply_payload,
            context=reply_context,
            recipient_id=self.sender_id,
            sender_id=self.recipient_id,
            reply_to=self.message_id,
            correlation_id=self.correlation_id or self.message_id,
            # Inherit priority from original message
            priority=self.priority
        )

    def create_forward(self, new_recipient_id: str) -> 'Message':
        """
        Create a forwarded copy of this message to a new recipient.

        This method creates a new message that forwards the current message to a new
        recipient, propagating the context and updating sender/recipient IDs appropriately.

        Args:
            new_recipient_id: ID of the new recipient for the forwarded message

        Returns:
            A new Message instance configured as a forward of this message

        Raises:
            ValueError: If new_recipient_id is None or empty
        """
        # Validate new recipient ID
        if not new_recipient_id:
            import logging
            logger = logging.getLogger("vibe_check_actor_system")
            logger.error(
                f"Cannot forward message {self.message_id} of type {self.type.name} "
                f"because new_recipient_id is None or empty"
            )
            raise ValueError("new_recipient_id cannot be None or empty")

        # Propagate context to maintain history and metadata
        forward_context = self.context.propagate() if self.context is not None else ContextWave()

        # Add forwarding information to context
        forward_context.metadata["forwarded_from"] = self.recipient_id
        forward_context.metadata["original_sender"] = self.sender_id

        # Create the forwarded message
        return Message(
            type=self.type,
            payload=self.payload.copy(),  # Create a copy of the payload to avoid shared references
            context=forward_context,
            recipient_id=new_recipient_id,
            sender_id=self.recipient_id,  # The forwarder becomes the sender
            correlation_id=self.correlation_id or self.message_id,
            # Preserve other properties
            priority=self.priority,
            ttl=self.ttl,  # Note: TTL is not reset, so forwarded messages inherit the original expiration
            reply_to=self.reply_to  # Preserve reply chain
        )

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """
        Create a Message instance from a dictionary.

        This method deserializes a Message from a dictionary representation,
        typically used when receiving messages from remote actors or loading
        from storage.

        Enhanced with better error handling for message type deserialization.

        Args:
            data: Dictionary containing message data

        Returns:
            A new Message instance created from the dictionary

        Raises:
            KeyError: If required fields are missing from the dictionary
            ValueError: If the message type is invalid
        """
        import logging
        import traceback
        logger = logging.getLogger("vibe_check_actor_system")

        # Create context from dictionary if provided
        context = None
        if "context" in data:
            try:
                context = ContextWave.from_dict(data["context"])
            except Exception as e:
                logger.error(f"Error parsing context: {e}")
                context = ContextWave()

        if context is None:
            context = ContextWave()

        # Handle message type conversion with better error handling
        try:
            message_type_str = data.get("type")
            if not message_type_str:
                logger.error(f"Missing message type in data: {data}")
                message_type = MessageType.UNKNOWN
            else:
                try:
                    # If it's already an enum instance, use it directly
                    if isinstance(message_type_str, MessageType):
                        message_type = message_type_str
                    else:
                        # Try to convert string to enum
                        message_type = MessageType[message_type_str]
                except (KeyError, ValueError):
                    logger.error(f"Unknown message type: {message_type_str}")
                    # Try to match by name ignoring case
                    for mt in MessageType:
                        if mt.name.lower() == str(message_type_str).lower():
                            message_type = mt
                            logger.warning(f"Matched message type {message_type_str} to {mt.name} (case-insensitive)")
                            break
                    else:
                        # If we get here, we couldn't match the message type
                        message_type = MessageType.UNKNOWN
                        # Add original type to payload for debugging
                        if "payload" not in data:
                            data["payload"] = {}
                        if isinstance(data["payload"], dict):
                            data["payload"]["original_message_type"] = str(message_type_str)
        except Exception as e:
            error_details = traceback.format_exc()
            logger.error(f"Error parsing message type: {e}\n{error_details}")
            message_type = MessageType.UNKNOWN
            # Add error details to context for debugging
            context.metadata["message_type_error"] = str(e)
            context.metadata["original_message_type"] = str(data.get("type", "unknown"))

        # Create the message with all available fields
        try:
            return cls(
                type=message_type,
                payload=data.get("payload", {}),
                context=context,
                recipient_id=data.get("recipient_id"),
                sender_id=data.get("sender_id"),
                message_id=data.get("message_id"),
                reply_to=data.get("reply_to"),
                stream_id=data.get("stream_id"),
                priority=data.get("priority", 0),
                ttl=data.get("ttl"),
                timestamp=data.get("timestamp"),
                correlation_id=data.get("correlation_id"),
                serialization_format=data.get("serialization_format", "json")
            )
        except Exception as e:
            error_details = traceback.format_exc()
            logger.error(f"Error creating message from dictionary: {e}\n{error_details}")
            # Create a minimal valid message as a fallback
            return cls(
                type=MessageType.ERROR,
                payload={
                    "error": f"Failed to deserialize message: {e}",
                    "original_data": str(data)[:200],  # Truncate for log size
                    "error_details": error_details
                },
                context=context
            )
