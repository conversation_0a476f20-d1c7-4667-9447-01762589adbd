"""
Error Isolation Module
=================

This module provides error isolation mechanisms for the actor system,
including circuit breakers to prevent cascading failures, error categorization
and tracking, isolation boundaries, and graceful degradation strategies.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Set, Tuple, Any, Callable, Union
import json
from pathlib import Path
import os
from enum import Enum
import functools
import random

from .actor import Actor
from .message import Message, MessageType

logger = logging.getLogger("vibe_check_error_isolation")


class CircuitState(Enum):
    """Enum representing the possible states of a circuit breaker."""
    CLOSED = "closed"      # Normal operation, requests are allowed
    OPEN = "open"          # Circuit is open, requests are blocked
    HALF_OPEN = "half_open"  # Testing if the circuit can be closed again


class ErrorCategory(Enum):
    """Enum representing categories of errors."""
    INITIALIZATION = "initialization"  # Errors during actor initialization
    MESSAGE_HANDLING = "message_handling"  # Errors during message handling
    DEPENDENCY = "dependency"  # Errors related to dependencies
    TIMEOUT = "timeout"  # Timeout errors
    RESOURCE = "resource"  # Resource-related errors (memory, CPU, etc.)
    NETWORK = "network"  # Network-related errors
    UNKNOWN = "unknown"  # Unknown errors


class CircuitBreaker:
    """
    Circuit breaker to prevent cascading failures.

    This class implements the circuit breaker pattern, which prevents
    an actor from making requests to a failing actor, allowing the
    failing actor to recover.
    """

    def __init__(
        self,
        name: str,
        failure_threshold: int = 5,
        reset_timeout: float = 60.0,
        half_open_max_calls: int = 1
    ):
        """
        Initialize the circuit breaker.

        Args:
            name: Name of the circuit breaker
            failure_threshold: Number of failures before opening the circuit
            reset_timeout: Time in seconds before trying to close the circuit again
            half_open_max_calls: Maximum number of calls allowed in half-open state
        """
        self.name = name
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.half_open_max_calls = half_open_max_calls
        
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._last_failure_time = 0.0
        self._last_state_change_time = time.time()
        self._half_open_calls = 0
        self._lock = asyncio.Lock()
        self._failures: List[Dict[str, Any]] = []
        self._successes: List[float] = []  # List of timestamps

    async def execute(self, func: Callable, *args: Any, **kwargs: Any) -> Any:
        """
        Execute a function with circuit breaker protection.

        Args:
            func: Function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function

        Returns:
            Result of the function

        Raises:
            Exception: If the circuit is open or the function fails
        """
        async with self._lock:
            # Check if circuit is open
            if self._state == CircuitState.OPEN:
                # Check if reset timeout has elapsed
                if time.time() - self._last_state_change_time > self.reset_timeout:
                    # Transition to half-open state
                    self._state = CircuitState.HALF_OPEN
                    self._half_open_calls = 0
                    self._last_state_change_time = time.time()
                    logger.info(f"Circuit breaker {self.name} transitioned from OPEN to HALF_OPEN")
                else:
                    # Circuit is still open
                    raise CircuitBreakerOpenError(f"Circuit breaker {self.name} is open")
            
            # Check if circuit is half-open and we've reached the max calls
            if self._state == CircuitState.HALF_OPEN and self._half_open_calls >= self.half_open_max_calls:
                raise CircuitBreakerOpenError(f"Circuit breaker {self.name} is half-open and at max calls")
            
            # Increment half-open calls if in half-open state
            if self._state == CircuitState.HALF_OPEN:
                self._half_open_calls += 1
        
        # Execute the function
        try:
            result = await func(*args, **kwargs)
            
            # Record success
            async with self._lock:
                self._successes.append(time.time())
                
                # If in half-open state and successful, close the circuit
                if self._state == CircuitState.HALF_OPEN:
                    self._state = CircuitState.CLOSED
                    self._failure_count = 0
                    self._last_state_change_time = time.time()
                    logger.info(f"Circuit breaker {self.name} transitioned from HALF_OPEN to CLOSED")
            
            return result
        
        except Exception as e:
            # Record failure
            async with self._lock:
                self._failure_count += 1
                self._last_failure_time = time.time()
                
                # Record failure details
                failure = {
                    "timestamp": self._last_failure_time,
                    "error": str(e),
                    "error_type": type(e).__name__
                }
                self._failures.append(failure)
                
                # Check if we should open the circuit
                if self._state == CircuitState.CLOSED and self._failure_count >= self.failure_threshold:
                    self._state = CircuitState.OPEN
                    self._last_state_change_time = time.time()
                    logger.warning(f"Circuit breaker {self.name} transitioned from CLOSED to OPEN after {self._failure_count} failures")
                
                # If in half-open state and failed, open the circuit again
                if self._state == CircuitState.HALF_OPEN:
                    self._state = CircuitState.OPEN
                    self._last_state_change_time = time.time()
                    logger.warning(f"Circuit breaker {self.name} transitioned from HALF_OPEN to OPEN after failure")
            
            # Re-raise the exception
            raise

    @property
    def state(self) -> CircuitState:
        """Get the current state of the circuit breaker."""
        return self._state

    @property
    def failure_count(self) -> int:
        """Get the current failure count."""
        return self._failure_count

    @property
    def last_failure_time(self) -> float:
        """Get the timestamp of the last failure."""
        return self._last_failure_time

    def reset(self) -> None:
        """Reset the circuit breaker to closed state."""
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._last_state_change_time = time.time()
        self._half_open_calls = 0
        logger.info(f"Circuit breaker {self.name} manually reset to CLOSED")

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get metrics about the circuit breaker.

        Returns:
            Dictionary of metrics
        """
        return {
            "name": self.name,
            "state": self._state.value,
            "failure_count": self._failure_count,
            "last_failure_time": self._last_failure_time,
            "last_state_change_time": self._last_state_change_time,
            "half_open_calls": self._half_open_calls,
            "failure_threshold": self.failure_threshold,
            "reset_timeout": self.reset_timeout,
            "half_open_max_calls": self.half_open_max_calls,
            "recent_failures": self._failures[-10:] if self._failures else [],
            "recent_successes": self._successes[-10:] if self._successes else []
        }


class CircuitBreakerOpenError(Exception):
    """Exception raised when a circuit breaker is open."""
    pass


class ErrorIsolator:
    """
    Provides error isolation mechanisms for the actor system.

    This class implements circuit breakers, error categorization and tracking,
    isolation boundaries, and graceful degradation strategies.
    """

    def __init__(self, output_dir: Optional[str] = None):
        """
        Initialize the error isolator.

        Args:
            output_dir: Optional directory to save error isolation data
        """
        self._circuit_breakers: Dict[str, CircuitBreaker] = {}
        self._error_categories: Dict[str, Dict[ErrorCategory, int]] = {}
        self._error_history: Dict[str, List[Dict[str, Any]]] = {}
        self._output_dir = output_dir
        self._lock = asyncio.Lock()
        
        # Create output directory if specified
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

    def get_circuit_breaker(
        self,
        name: str,
        failure_threshold: int = 5,
        reset_timeout: float = 60.0,
        half_open_max_calls: int = 1
    ) -> CircuitBreaker:
        """
        Get a circuit breaker by name, creating it if it doesn't exist.

        Args:
            name: Name of the circuit breaker
            failure_threshold: Number of failures before opening the circuit
            reset_timeout: Time in seconds before trying to close the circuit again
            half_open_max_calls: Maximum number of calls allowed in half-open state

        Returns:
            The circuit breaker instance
        """
        if name not in self._circuit_breakers:
            self._circuit_breakers[name] = CircuitBreaker(
                name=name,
                failure_threshold=failure_threshold,
                reset_timeout=reset_timeout,
                half_open_max_calls=half_open_max_calls
            )
        
        return self._circuit_breakers[name]

    def circuit_breaker(
        self,
        name: str,
        failure_threshold: int = 5,
        reset_timeout: float = 60.0,
        half_open_max_calls: int = 1
    ) -> Callable:
        """
        Decorator to apply circuit breaker to a function.

        Args:
            name: Name of the circuit breaker
            failure_threshold: Number of failures before opening the circuit
            reset_timeout: Time in seconds before trying to close the circuit again
            half_open_max_calls: Maximum number of calls allowed in half-open state

        Returns:
            Decorator function
        """
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            async def wrapper(*args: Any, **kwargs: Any) -> Any:
                circuit_breaker = self.get_circuit_breaker(
                    name=name,
                    failure_threshold=failure_threshold,
                    reset_timeout=reset_timeout,
                    half_open_max_calls=half_open_max_calls
                )
                
                return await circuit_breaker.execute(func, *args, **kwargs)
            
            return wrapper
        
        return decorator

    async def record_error(
        self,
        actor_id: str,
        error: Exception,
        category: ErrorCategory = ErrorCategory.UNKNOWN,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Record an error for an actor.

        Args:
            actor_id: ID of the actor
            error: The error that occurred
            category: Category of the error
            details: Optional details about the error
        """
        async with self._lock:
            # Initialize error tracking for actor if needed
            if actor_id not in self._error_categories:
                self._error_categories[actor_id] = {cat: 0 for cat in ErrorCategory}
            
            if actor_id not in self._error_history:
                self._error_history[actor_id] = []
            
            # Increment error count for category
            self._error_categories[actor_id][category] += 1
            
            # Record error details
            error_details = {
                "timestamp": time.time(),
                "error": str(error),
                "error_type": type(error).__name__,
                "category": category.value,
                "details": details or {}
            }
            self._error_history[actor_id].append(error_details)
            
            # Log the error
            logger.error(f"Actor {actor_id} error ({category.value}): {error}")

    def get_error_metrics(self, actor_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get error metrics for an actor or all actors.

        Args:
            actor_id: Optional ID of the actor to get metrics for

        Returns:
            Dictionary of error metrics
        """
        if actor_id:
            # Get metrics for a specific actor
            return {
                "actor_id": actor_id,
                "error_categories": {
                    cat.value: count
                    for cat, count in self._error_categories.get(actor_id, {}).items()
                },
                "error_history": self._error_history.get(actor_id, [])[-10:],  # Last 10 errors
                "total_errors": sum(self._error_categories.get(actor_id, {}).values())
            }
        else:
            # Get metrics for all actors
            return {
                "actors": [
                    {
                        "actor_id": actor_id,
                        "total_errors": sum(categories.values()),
                        "error_categories": {cat.value: count for cat, count in categories.items()}
                    }
                    for actor_id, categories in self._error_categories.items()
                ],
                "circuit_breakers": [
                    circuit_breaker.get_metrics()
                    for circuit_breaker in self._circuit_breakers.values()
                ]
            }

    async def export_error_metrics(self, filename: Optional[str] = None) -> Optional[str]:
        """
        Export error metrics to a JSON file.

        Args:
            filename: Optional filename to save to

        Returns:
            Path to the saved file, or None if saving failed
        """
        if not self._output_dir:
            logger.warning("Cannot export error metrics: no output directory specified")
            return None
        
        try:
            # Create a default filename if none provided
            if not filename:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"error_metrics_{timestamp}.json"
            
            filepath = Path(self._output_dir) / filename
            
            # Prepare data for serialization
            data = {
                "error_categories": {
                    actor_id: {cat.value: count for cat, count in categories.items()}
                    for actor_id, categories in self._error_categories.items()
                },
                "circuit_breakers": {
                    name: circuit_breaker.get_metrics()
                    for name, circuit_breaker in self._circuit_breakers.items()
                },
                "timestamp": time.time()
            }
            
            # Save to file
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Exported error metrics to {filepath}")
            return str(filepath)
        
        except Exception as e:
            logger.error(f"Failed to export error metrics: {e}")
            return None


# Singleton instance
_isolator = None


def get_error_isolator(output_dir: Optional[str] = None) -> ErrorIsolator:
    """
    Get the singleton error isolator instance.

    Args:
        output_dir: Optional directory to save error isolation data

    Returns:
        The error isolator instance
    """
    global _isolator
    
    if _isolator is None:
        _isolator = ErrorIsolator(output_dir)
        logger.info("Created new error isolator")
    
    return _isolator


def reset_error_isolator() -> None:
    """Reset the singleton error isolator instance."""
    global _isolator
    
    _isolator = None
    logger.info("Reset error isolator")
