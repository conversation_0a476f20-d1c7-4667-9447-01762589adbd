"""
Actor Registry Module
=================

This module provides a registry for actors in the system, enabling dynamic
discovery and communication between actors without explicit connections.

The registry implements the CAW principle of choreographed interactions by
allowing actors to discover and communicate with each other at runtime.
"""

import asyncio
import logging
import re
import time
import uuid
from typing import Any, Callable, Dict, List, Optional, Pattern, Set, Type, Union, cast

from .actor import Actor
from .message import Message, MessageType
from .logging.registry_debug import (
    RegistryOperationType,
    is_registry_debugging_enabled,
    log_registry_operation,
    log_registry_state,
    log_registry_inconsistency,
    log_lookup_retry,
    update_lookup_retry_result,
    start_correlation,
    end_correlation
)

logger = logging.getLogger("vibe_check_actor_registry")


class ActorRegistry:
    """
    Registry for actors in the system.

    Implements the CAW principle of dynamic discovery and choreographed
    interactions by allowing actors to discover and communicate with each
    other at runtime.
    """

    def __init__(self) -> None:
        """Initialize the actor registry."""
        self._actors: Dict[str, Actor] = {}
        self._actor_types: Dict[str, Set[str]] = {}
        self._actor_tags: Dict[str, Set[str]] = {}
        self._actor_capabilities: Dict[str, Set[str]] = {}
        self._actor_patterns: Dict[Pattern, Set[str]] = {}  # Changed from List to Set
        self._subscriptions: Dict[str, Set[str]] = {}
        self._last_heartbeat: Dict[str, float] = {}
        self._discovery_callbacks: List[Callable[[str, Actor], None]] = []

    def _log_registry_state(self, message: str) -> None:
        """
        Log the current state of the registry.

        Args:
            message: Message to include with the state snapshot
        """
        if not is_registry_debugging_enabled():
            return

        # Create a snapshot of the registry state
        state = {
            "actors_count": len(self._actors),
            "actor_types_count": len(self._actor_types),
            "actor_ids": list(self._actors.keys()),
            "actor_types": {
                actor_type: list(actors)
                for actor_type, actors in self._actor_types.items()
            },
            "actor_tags": {
                actor_id: list(tags)
                for actor_id, tags in self._actor_tags.items()
            },
            "actor_capabilities": {
                actor_id: list(capabilities)
                for actor_id, capabilities in self._actor_capabilities.items()
            },
            "subscriptions_count": len(self._subscriptions),
            "patterns_count": len(self._actor_patterns),
            "timestamp": time.time()
        }

        # Log the state
        log_registry_state(state, message)

    def register_actor(self, actor_id: str, actor: Actor,
                      actor_type: Optional[str] = None,
                      tags: Optional[Set[str]] = None,
                      capabilities: Optional[Set[str]] = None) -> None:
        """
        Register an actor with the registry.

        Args:
            actor_id: Unique ID for the actor
            actor: The actor instance
            actor_type: Optional type of the actor
            tags: Optional set of tags for the actor
            capabilities: Optional set of capabilities the actor provides
        """
        # Start timing the operation
        start_time = time.time()

        # Start a correlation context for this operation
        if is_registry_debugging_enabled():
            correlation_id = f"register_{actor_id}_{int(start_time * 1000)}"
            start_correlation(correlation_id)

            # Log the operation start
            log_registry_operation(
                operation_type=RegistryOperationType.REGISTER,
                actor_id=actor_id,
                message=f"Registering actor {actor_id}",
                details={
                    "actor_type": actor_type,
                    "has_tags": bool(tags),
                    "has_capabilities": bool(capabilities),
                    "start_time": start_time
                }
            )

        # Check if actor is already registered
        already_registered = actor_id in self._actors
        if already_registered:
            logger.warning(f"Actor {actor_id} already registered, updating")

            if is_registry_debugging_enabled():
                log_registry_operation(
                    operation_type=RegistryOperationType.REGISTER,
                    actor_id=actor_id,
                    message=f"Actor {actor_id} already registered, updating",
                    details={
                        "already_registered": True,
                        "previous_type": next((t for t, ids in self._actor_types.items() if actor_id in ids), None)
                    }
                )

        # Register the actor
        self._actors[actor_id] = actor

        # Register actor type
        if actor_type:
            if actor_type not in self._actor_types:
                self._actor_types[actor_type] = set()
            self._actor_types[actor_type].add(actor_id)

            if is_registry_debugging_enabled():
                log_registry_operation(
                    operation_type=RegistryOperationType.REGISTER,
                    actor_id=actor_id,
                    message=f"Registered actor type: {actor_type}",
                    details={
                        "actor_type": actor_type,
                        "type_actors_count": len(self._actor_types[actor_type])
                    }
                )

        # Register tags
        if tags:
            self._actor_tags[actor_id] = tags

            if is_registry_debugging_enabled():
                log_registry_operation(
                    operation_type=RegistryOperationType.ADD_TAG,
                    actor_id=actor_id,
                    message=f"Added tags to actor {actor_id}",
                    details={
                        "tags": list(tags)
                    }
                )

        # Register capabilities
        if capabilities:
            self._actor_capabilities[actor_id] = capabilities

            if is_registry_debugging_enabled():
                log_registry_operation(
                    operation_type=RegistryOperationType.ADD_CAPABILITY,
                    actor_id=actor_id,
                    message=f"Added capabilities to actor {actor_id}",
                    details={
                        "capabilities": list(capabilities)
                    }
                )

        # Register with pattern matching
        self._update_patterns(actor_id)

        # Record heartbeat
        self._last_heartbeat[actor_id] = time.time()

        # Notify discovery callbacks
        callback_errors = []
        for callback in self._discovery_callbacks:
            try:
                callback(actor_id, actor)
            except Exception as e:
                error_msg = f"Error in discovery callback: {e}"
                logger.error(error_msg)
                callback_errors.append(str(e))

                if is_registry_debugging_enabled():
                    log_registry_operation(
                        operation_type=RegistryOperationType.REGISTER,
                        actor_id=actor_id,
                        message=f"Error in discovery callback",
                        details={
                            "callback": str(callback)
                        },
                        error=e,
                        stack_trace=True
                    )

        # Calculate operation duration
        end_time = time.time()
        duration = end_time - start_time

        # Log the operation completion
        logger.info(f"Registered actor {actor_id} of type {actor_type}")

        if is_registry_debugging_enabled():
            # Log the operation completion
            log_registry_operation(
                operation_type=RegistryOperationType.REGISTER,
                actor_id=actor_id,
                message=f"Completed registration of actor {actor_id}",
                details={
                    "actor_type": actor_type,
                    "duration": duration,
                    "already_registered": already_registered,
                    "callback_errors": callback_errors
                }
            )

            # Take a snapshot of the registry state
            self._log_registry_state(f"After registering actor {actor_id}")

            # End the correlation context
            end_correlation()

    def unregister_actor(self, actor_id: str) -> None:
        """
        Unregister an actor from the registry.

        Args:
            actor_id: ID of the actor to unregister
        """
        if actor_id not in self._actors:
            logger.warning(f"Actor {actor_id} not registered, cannot unregister")
            return

        # Remove from actors dict
        actor = self._actors.pop(actor_id)

        # Remove from actor types
        for actor_type, actors in list(self._actor_types.items()):
            if actor_id in actors:
                actors.remove(actor_id)
                if not actors:
                    self._actor_types.pop(actor_type)

        # Remove from tags
        if actor_id in self._actor_tags:
            self._actor_tags.pop(actor_id)

        # Remove from capabilities
        if actor_id in self._actor_capabilities:
            self._actor_capabilities.pop(actor_id)

        # Remove from patterns
        for pattern, actors in list(self._actor_patterns.items()):
            if actor_id in actors:
                actors.remove(actor_id)
                if not actors:
                    self._actor_patterns.pop(pattern)

        # Remove from subscriptions
        for stream_id, subscribers in list(self._subscriptions.items()):
            if actor_id in subscribers:
                subscribers.remove(actor_id)
                if not subscribers:
                    self._subscriptions.pop(stream_id)

        # Remove from heartbeats
        if actor_id in self._last_heartbeat:
            self._last_heartbeat.pop(actor_id)

        logger.info(f"Unregistered actor {actor_id}")

    def get_actor(self, actor_id: str, retry_count: int = 0, retry_delay: float = 0.1) -> Optional[Actor]:
        """
        Get an actor by ID.

        This method supports retry mechanisms to handle race conditions during initialization.
        If retry_count > 0, the method will retry the lookup if the actor is not found.

        Args:
            actor_id: ID of the actor to get
            retry_count: Number of times to retry if actor not found (default: 0)
            retry_delay: Delay between retries in seconds (default: 0.1)

        Returns:
            The actor instance or None if not found
        """
        # Start timing the operation
        start_time = time.time()

        # Start a correlation context for this operation
        if is_registry_debugging_enabled():
            correlation_id = f"lookup_{actor_id}_{int(start_time * 1000)}"
            start_correlation(correlation_id)

            # Log the operation start
            log_registry_operation(
                operation_type=RegistryOperationType.LOOKUP,
                actor_id=actor_id,
                message=f"Looking up actor {actor_id}",
                details={
                    "retry_count": retry_count,
                    "retry_delay": retry_delay,
                    "start_time": start_time
                }
            )

        # Attempt to get the actor
        actor = self._actors.get(actor_id)

        # If actor not found and retries are enabled, retry the lookup
        current_retry = 0
        while actor is None and current_retry < retry_count:
            # Log the retry attempt
            if is_registry_debugging_enabled():
                log_lookup_retry(
                    actor_id=actor_id,
                    attempt=current_retry + 1,
                    reason="Actor not found in registry",
                    details={
                        "retry_delay": retry_delay,
                        "max_retries": retry_count
                    }
                )

            # Wait before retrying
            time.sleep(retry_delay)

            # Retry the lookup
            actor = self._actors.get(actor_id)
            current_retry += 1

            # Log the retry result
            if is_registry_debugging_enabled():
                update_lookup_retry_result(
                    actor_id=actor_id,
                    retry_index=current_retry - 1,
                    success=actor is not None,
                    details={
                        "attempt": current_retry,
                        "max_retries": retry_count,
                        "found": actor is not None
                    }
                )

        # Calculate operation duration
        end_time = time.time()
        duration = end_time - start_time

        # Log the operation completion
        if is_registry_debugging_enabled():
            operation_type = RegistryOperationType.LOOKUP_SUCCEEDED if actor else RegistryOperationType.LOOKUP_FAILED
            log_registry_operation(
                operation_type=operation_type,
                actor_id=actor_id,
                message=f"Actor lookup {'succeeded' if actor else 'failed'} for {actor_id}",
                details={
                    "duration": duration,
                    "retries": current_retry,
                    "max_retries": retry_count,
                    "found": actor is not None
                }
            )

            # End the correlation context
            end_correlation()

        return actor

    def get_actors_by_type(self, actor_type: str) -> List[Actor]:
        """
        Get all actors of a specific type.

        Args:
            actor_type: Type of actors to get

        Returns:
            List of actor instances
        """
        actor_ids = self._actor_types.get(actor_type, set())
        return [self._actors[actor_id] for actor_id in actor_ids if actor_id in self._actors]

    def get_actors_by_tag(self, tag: str) -> List[Actor]:
        """
        Get all actors with a specific tag.

        Args:
            tag: Tag to filter by

        Returns:
            List of actor instances
        """
        return [
            self._actors[actor_id]
            for actor_id, tags in self._actor_tags.items()
            if tag in tags and actor_id in self._actors
        ]

    def get_actors_by_capability(self, capability: str) -> List[Actor]:
        """
        Get all actors with a specific capability.

        Args:
            capability: Capability to filter by

        Returns:
            List of actor instances
        """
        return [
            self._actors[actor_id]
            for actor_id, capabilities in self._actor_capabilities.items()
            if capability in capabilities and actor_id in self._actors
        ]

    def get_actors_by_pattern(self, pattern: str) -> List[Actor]:
        """
        Get all actors with IDs matching a regex pattern.

        Args:
            pattern: Regex pattern to match against actor IDs

        Returns:
            List of actor instances
        """
        compiled_pattern = re.compile(pattern)
        matching_actors = []

        # Check existing patterns
        for existing_pattern, actor_ids in self._actor_patterns.items():
            if existing_pattern.pattern == compiled_pattern.pattern:
                matching_actors.extend([
                    self._actors[actor_id]
                    for actor_id in actor_ids
                    if actor_id in self._actors
                ])
                return matching_actors

        # If pattern not found, search all actors
        for actor_id, actor in self._actors.items():
            if compiled_pattern.match(actor_id):
                matching_actors.append(actor)

        return matching_actors

    def _update_patterns(self, actor_id: str) -> None:
        """
        Update pattern matching for an actor ID.

        Args:
            actor_id: Actor ID to update patterns for
        """
        # Check all existing patterns
        for pattern, actor_ids in self._actor_patterns.items():
            if pattern.match(actor_id):
                actor_ids.add(actor_id)

    def register_pattern(self, pattern: str) -> None:
        """
        Register a new pattern for actor discovery.

        Args:
            pattern: Regex pattern to register
        """
        compiled_pattern = re.compile(pattern)

        # Check if pattern already exists
        for existing_pattern in self._actor_patterns:
            if existing_pattern.pattern == compiled_pattern.pattern:
                logger.warning(f"Pattern {pattern} already registered")
                return

        # Register new pattern
        matching_actors = set()
        for actor_id in self._actors:
            if compiled_pattern.match(actor_id):
                matching_actors.add(actor_id)

        self._actor_patterns[compiled_pattern] = matching_actors
        logger.info(f"Registered pattern {pattern} matching {len(matching_actors)} actors")

    def subscribe(self, stream_id: str, actor_id: str) -> None:
        """
        Subscribe an actor to a stream.

        Args:
            stream_id: ID of the stream to subscribe to
            actor_id: ID of the actor subscribing
        """
        if actor_id not in self._actors:
            logger.warning(f"Actor {actor_id} not registered, cannot subscribe")
            return

        if stream_id not in self._subscriptions:
            self._subscriptions[stream_id] = set()

        self._subscriptions[stream_id].add(actor_id)
        logger.info(f"Actor {actor_id} subscribed to stream {stream_id}")

    def unsubscribe(self, stream_id: str, actor_id: str) -> None:
        """
        Unsubscribe an actor from a stream.

        Args:
            stream_id: ID of the stream to unsubscribe from
            actor_id: ID of the actor unsubscribing
        """
        if stream_id not in self._subscriptions:
            logger.warning(f"Stream {stream_id} has no subscribers")
            return

        if actor_id not in self._subscriptions[stream_id]:
            logger.warning(f"Actor {actor_id} not subscribed to stream {stream_id}")
            return

        self._subscriptions[stream_id].remove(actor_id)

        if not self._subscriptions[stream_id]:
            self._subscriptions.pop(stream_id)

        logger.info(f"Actor {actor_id} unsubscribed from stream {stream_id}")

    async def publish(self, stream_id: str, message_type: MessageType,
                     payload: Dict[str, Any], sender_id: Optional[str] = None,
                     priority: int = 0, ttl: Optional[int] = None) -> None:
        """
        Publish a message to a stream.

        This method sends a message to all subscribers of a stream, handling errors
        gracefully and ensuring that the message is delivered to all available subscribers.

        Args:
            stream_id: ID of the stream to publish to
            message_type: Type of message to publish
            payload: Message payload
            sender_id: Optional ID of the sender
            priority: Optional priority for the message (0-9, higher is more important)
            ttl: Optional time-to-live in seconds

        Returns:
            None
        """
        # Validate stream_id
        if not stream_id:
            logger.error("Cannot publish to stream with empty stream_id")
            return

        # Check if stream has subscribers
        if stream_id not in self._subscriptions:
            logger.warning(f"Stream {stream_id} has no subscribers")
            return

        subscribers = self._subscriptions[stream_id]
        if not subscribers:
            logger.warning(f"Stream {stream_id} has no subscribers")
            return

        # Create message context
        from .context_wave import ContextWave
        context = ContextWave()
        context.metadata["stream_id"] = stream_id
        context.metadata["publish_time"] = time.time()
        if sender_id:
            context.metadata["sender_id"] = sender_id

        # Create a copy of the payload to avoid shared references
        safe_payload = payload.copy() if payload else {}

        # Add stream metadata to payload
        safe_payload["_stream_metadata"] = {
            "stream_id": stream_id,
            "publish_time": time.time(),
            "subscriber_count": len(subscribers)
        }

        # Send to all subscribers
        tasks = []
        successful_deliveries = 0

        for subscriber_id in list(subscribers):  # Use a copy of the set to avoid modification during iteration
            if subscriber_id not in self._actors:
                logger.warning(f"Subscriber {subscriber_id} not registered, removing from stream {stream_id}")
                subscribers.remove(subscriber_id)
                continue

            subscriber = self._actors[subscriber_id]

            try:
                # Create message for this subscriber
                message = Message(
                    type=message_type,
                    payload=safe_payload,
                    context=context.propagate(),  # Create a new context for each subscriber
                    recipient_id=subscriber_id,
                    sender_id=sender_id,
                    stream_id=stream_id,
                    priority=priority,
                    ttl=ttl
                )

                # Add to tasks
                tasks.append(subscriber.receive(message))
                successful_deliveries += 1
            except Exception as e:
                logger.error(f"Error creating message for subscriber {subscriber_id}: {e}")

        # Wait for all messages to be delivered
        if tasks:
            try:
                # Use gather with return_exceptions to prevent one failure from affecting others
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # Check for exceptions
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.error(f"Error delivering message to subscriber: {result}")
                        successful_deliveries -= 1

                logger.info(f"Published message to {successful_deliveries}/{len(tasks)} subscribers of stream {stream_id}")
            except Exception as e:
                logger.error(f"Error publishing to stream {stream_id}: {e}")
        else:
            logger.warning(f"No messages sent to stream {stream_id} (no valid subscribers)")

    def record_heartbeat(self, actor_id: str) -> None:
        """
        Record a heartbeat from an actor.

        Args:
            actor_id: ID of the actor sending the heartbeat
        """
        if actor_id not in self._actors:
            logger.warning(f"Actor {actor_id} not registered, ignoring heartbeat")
            return

        self._last_heartbeat[actor_id] = time.time()

    def get_inactive_actors(self, timeout: float = 60.0) -> List[str]:
        """
        Get actors that haven't sent a heartbeat within the timeout period.

        Args:
            timeout: Timeout period in seconds

        Returns:
            List of inactive actor IDs
        """
        now = time.time()
        return [
            actor_id
            for actor_id, last_heartbeat in self._last_heartbeat.items()
            if now - last_heartbeat > timeout
        ]

    def add_discovery_callback(self, callback: Callable[[str, Actor], None]) -> None:
        """
        Add a callback to be called when a new actor is discovered.

        Args:
            callback: Function to call with actor_id and actor instance
        """
        self._discovery_callbacks.append(callback)

    def remove_discovery_callback(self, callback: Callable[[str, Actor], None]) -> None:
        """
        Remove a discovery callback.

        Args:
            callback: Callback to remove
        """
        if callback in self._discovery_callbacks:
            self._discovery_callbacks.remove(callback)

    def get_all_actors(self) -> List[Actor]:
        """
        Get all registered actors.

        Returns:
            List of all actor instances
        """
        return list(self._actors.values())

    def clear(self) -> None:
        """Clear the registry."""
        self._actors.clear()
        self._actor_types.clear()
        self._actor_tags.clear()
        self._actor_capabilities.clear()
        self._actor_patterns.clear()
        self._subscriptions.clear()
        self._last_heartbeat.clear()
        logger.info("Actor registry cleared")

        if is_registry_debugging_enabled():
            log_registry_operation(
                operation_type=RegistryOperationType.REGISTER,
                message="Registry cleared",
                details={
                    "timestamp": time.time()
                }
            )

            # Take a snapshot of the empty registry state
            self._log_registry_state("After clearing registry")

    def check_consistency(self) -> Dict[str, Any]:
        """
        Check the consistency of the registry.

        This method checks for inconsistencies in the registry data structures,
        such as actors referenced in type mappings but not in the actors dictionary.

        Returns:
            Dictionary with consistency check results
        """
        # Start timing the operation
        start_time = time.time()

        # Start a correlation context for this operation
        if is_registry_debugging_enabled():
            correlation_id = f"consistency_check_{int(start_time * 1000)}"
            start_correlation(correlation_id)

            # Log the operation start
            log_registry_operation(
                operation_type=RegistryOperationType.CONSISTENCY_CHECK,
                message="Starting registry consistency check",
                details={
                    "start_time": start_time,
                    "actors_count": len(self._actors),
                    "actor_types_count": len(self._actor_types)
                }
            )

        # Initialize results with explicit typing
        results: Dict[str, Any] = {
            "consistent": True,
            "inconsistencies": [],
            "orphaned_references": {
                "actor_types": [],
                "actor_tags": [],
                "actor_capabilities": [],
                "actor_patterns": [],
                "subscriptions": [],
                "heartbeats": []
            },
            "missing_references": {
                "actor_types": [],
                "actor_tags": [],
                "actor_capabilities": [],
                "actor_patterns": [],
                "heartbeats": []
            },
            "duration": 0
        }

        # Cast to specific types to help type checker
        orphaned_refs = cast(Dict[str, List[Dict[str, Any]]], results["orphaned_references"])
        missing_refs = cast(Dict[str, List[Dict[str, Any]]], results["missing_references"])

        # Check actor types
        for actor_type, actor_ids in self._actor_types.items():
            for actor_id in actor_ids:
                if actor_id not in self._actors:
                    results["consistent"] = False
                    orphaned_refs["actor_types"].append({
                        "actor_type": actor_type,
                        "actor_id": actor_id
                    })

                    if is_registry_debugging_enabled():
                        log_registry_inconsistency(
                            inconsistency_type="orphaned_reference",
                            message=f"Actor {actor_id} referenced in actor_types but not in actors",
                            details={
                                "actor_type": actor_type,
                                "actor_id": actor_id
                            },
                            actor_id=actor_id,
                            resolution_suggestion="Remove the orphaned reference from actor_types"
                        )

        # Check actor tags
        for actor_id, tags in self._actor_tags.items():
            if actor_id not in self._actors:
                results["consistent"] = False
                orphaned_refs["actor_tags"].append({
                    "actor_id": actor_id,
                    "tags": list(tags)
                })

                if is_registry_debugging_enabled():
                    log_registry_inconsistency(
                        inconsistency_type="orphaned_reference",
                        message=f"Actor {actor_id} referenced in actor_tags but not in actors",
                        details={
                            "actor_id": actor_id,
                            "tags": list(tags)
                        },
                        actor_id=actor_id,
                        resolution_suggestion="Remove the orphaned reference from actor_tags"
                    )

        # Check actor capabilities
        for actor_id, capabilities in self._actor_capabilities.items():
            if actor_id not in self._actors:
                results["consistent"] = False
                orphaned_refs["actor_capabilities"].append({
                    "actor_id": actor_id,
                    "capabilities": list(capabilities)
                })

                if is_registry_debugging_enabled():
                    log_registry_inconsistency(
                        inconsistency_type="orphaned_reference",
                        message=f"Actor {actor_id} referenced in actor_capabilities but not in actors",
                        details={
                            "actor_id": actor_id,
                            "capabilities": list(capabilities)
                        },
                        actor_id=actor_id,
                        resolution_suggestion="Remove the orphaned reference from actor_capabilities"
                    )

        # Check actor patterns
        for pattern, actor_ids in self._actor_patterns.items():
            for actor_id in actor_ids:
                if actor_id not in self._actors:
                    results["consistent"] = False
                    orphaned_refs["actor_patterns"].append({
                        "pattern": pattern.pattern,
                        "actor_id": actor_id
                    })

                    if is_registry_debugging_enabled():
                        log_registry_inconsistency(
                            inconsistency_type="orphaned_reference",
                            message=f"Actor {actor_id} referenced in actor_patterns but not in actors",
                            details={
                                "pattern": pattern.pattern,
                                "actor_id": actor_id
                            },
                            actor_id=actor_id,
                            resolution_suggestion="Remove the orphaned reference from actor_patterns"
                        )

        # Check subscriptions
        for stream_id, actor_ids in self._subscriptions.items():
            for actor_id in actor_ids:
                if actor_id not in self._actors:
                    results["consistent"] = False
                    orphaned_refs["subscriptions"].append({
                        "stream_id": stream_id,
                        "actor_id": actor_id
                    })

                    if is_registry_debugging_enabled():
                        log_registry_inconsistency(
                            inconsistency_type="orphaned_reference",
                            message=f"Actor {actor_id} referenced in subscriptions but not in actors",
                            details={
                                "stream_id": stream_id,
                                "actor_id": actor_id
                            },
                            actor_id=actor_id,
                            resolution_suggestion="Remove the orphaned reference from subscriptions"
                        )

        # Check heartbeats
        for actor_id in self._last_heartbeat:
            if actor_id not in self._actors:
                results["consistent"] = False
                orphaned_refs["heartbeats"].append({
                    "actor_id": actor_id,
                    "last_heartbeat": self._last_heartbeat[actor_id]
                })

                if is_registry_debugging_enabled():
                    log_registry_inconsistency(
                        inconsistency_type="orphaned_reference",
                        message=f"Actor {actor_id} referenced in last_heartbeat but not in actors",
                        details={
                            "actor_id": actor_id,
                            "last_heartbeat": self._last_heartbeat[actor_id]
                        },
                        actor_id=actor_id,
                        resolution_suggestion="Remove the orphaned reference from last_heartbeat"
                    )

        # Check for actors missing from secondary data structures
        for actor_id in self._actors:
            # Check if actor has a heartbeat
            if actor_id not in self._last_heartbeat:
                results["consistent"] = False
                missing_refs["heartbeats"].append({
                    "actor_id": actor_id
                })

                if is_registry_debugging_enabled():
                    log_registry_inconsistency(
                        inconsistency_type="missing_reference",
                        message=f"Actor {actor_id} missing from last_heartbeat",
                        details={
                            "actor_id": actor_id
                        },
                        actor_id=actor_id,
                        resolution_suggestion="Add the actor to last_heartbeat"
                    )

        # Calculate operation duration
        end_time = time.time()
        duration = end_time - start_time
        results["duration"] = duration

        # Log the operation completion
        if is_registry_debugging_enabled():
            log_registry_operation(
                operation_type=RegistryOperationType.CONSISTENCY_CHECK,
                message=f"Registry consistency check {'passed' if results['consistent'] else 'failed'}",
                details={
                    "duration": duration,
                    "consistent": results["consistent"],
                    "inconsistencies_count": len(results["inconsistencies"]),
                    "orphaned_references_count": sum(len(refs) for refs in orphaned_refs.values()),
                    "missing_references_count": sum(len(refs) for refs in missing_refs.values())
                }
            )

            # End the correlation context
            end_correlation()

        return results


# Singleton instance with lock for thread safety
_registry = None
_registry_lock = asyncio.Lock()

async def get_registry_async() -> ActorRegistry:
    """
    Get the singleton registry instance asynchronously.

    This method is thread-safe and should be used in async contexts.

    Returns:
        The actor registry instance
    """
    global _registry, _registry_lock

    async with _registry_lock:
        if _registry is None:
            _registry = ActorRegistry()
            logger.info("Created new actor registry instance")

    return _registry

def get_registry() -> ActorRegistry:
    """
    Get the singleton registry instance synchronously.

    This method is not thread-safe and should only be used in synchronous contexts
    or when you know there won't be concurrent access.

    Returns:
        The actor registry instance
    """
    global _registry

    if _registry is None:
        _registry = ActorRegistry()
        logger.info("Created new actor registry instance")

    return _registry

async def reset_registry_async() -> None:
    """
    Reset the singleton registry instance asynchronously.

    This method is thread-safe and should be used in async contexts.
    """
    global _registry, _registry_lock

    async with _registry_lock:
        if _registry is not None:
            logger.info("Clearing actor registry")
            _registry.clear()

        _registry = None
        logger.info("Reset actor registry to None")

def reset_registry() -> None:
    """
    Reset the singleton registry instance synchronously.

    This method is not thread-safe and should only be used in synchronous contexts
    or when you know there won't be concurrent access.
    """
    global _registry

    if _registry is not None:
        logger.info("Clearing actor registry")
        _registry.clear()

    _registry = None
    logger.info("Reset actor registry to None")
