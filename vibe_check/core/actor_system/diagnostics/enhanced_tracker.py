"""
Enhanced Initialization Tracker Module
================================

This module provides an enhanced version of the initialization tracker
with more detailed diagnostics, visualization capabilities, and integration
with the dependency injection framework.
"""

import asyncio
import logging
import time
import json
import os
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

from ..actor_state import ActorState
from ..dependency_injection import Injectable, Component, ComponentScope

logger = logging.getLogger("vibe_check_enhanced_tracker")


class DiagnosticLevel(Enum):
    """Enum representing the level of diagnostic information."""
    MINIMAL = "minimal"
    BASIC = "basic"
    DETAILED = "detailed"
    VERBOSE = "verbose"


class DiagnosticCategory(Enum):
    """Enum representing the category of diagnostic information."""
    INITIALIZATION = "initialization"
    MESSAGING = "messaging"
    ERROR = "error"
    PERFORMANCE = "performance"
    DEPENDENCY = "dependency"
    STATE = "state"


class DiagnosticEvent:
    """Class representing a diagnostic event."""

    def __init__(
        self,
        category: DiagnosticCategory,
        level: DiagnosticLevel,
        actor_id: str,
        event_type: str,
        timestamp: float,
        details: Optional[Dict[str, Any]] = None,
        error: Optional[Exception] = None,
        parent_event_id: Optional[str] = None
    ):
        """
        Initialize the event.

        Args:
            category: Category of the event
            level: Level of detail for the event
            actor_id: ID of the actor
            event_type: Type of event
            timestamp: Time when the event occurred
            details: Optional details about the event
            error: Optional error that occurred
            parent_event_id: Optional ID of the parent event
        """
        self.event_id = f"{actor_id}_{event_type}_{timestamp}"
        self.category = category
        self.level = level
        self.actor_id = actor_id
        self.event_type = event_type
        self.timestamp = timestamp
        self.details = details or {}
        self.error = error
        self.error_str = str(error) if error else None
        self.parent_event_id = parent_event_id

    def to_dict(self) -> Dict[str, Any]:
        """Convert the event to a dictionary for serialization."""
        return {
            "event_id": self.event_id,
            "category": self.category.value,
            "level": self.level.value,
            "actor_id": self.actor_id,
            "event_type": self.event_type,
            "timestamp": self.timestamp,
            "details": self.details,
            "error": self.error_str,
            "parent_event_id": self.parent_event_id
        }


@Component(scope=ComponentScope.SINGLETON)
class EnhancedTracker:
    """
    Enhanced tracker for the actor system.

    This class provides detailed diagnostics for the actor system,
    including initialization tracking, message flow visualization,
    error reporting, and performance metrics.
    """

    def __init__(self, output_dir: Optional[str] = None, min_level: DiagnosticLevel = DiagnosticLevel.BASIC):
        """
        Initialize the tracker.

        Args:
            output_dir: Optional directory to save diagnostics data
            min_level: Minimum level of diagnostic information to record
        """
        self._events: List[DiagnosticEvent] = []
        self._actor_dependencies: Dict[str, Set[str]] = {}
        self._actor_states: Dict[str, Dict[str, Any]] = {}
        self._actor_metrics: Dict[str, Dict[str, Any]] = {}
        self._actor_errors: Dict[str, List[Dict[str, Any]]] = {}
        self._start_time = time.time()
        self._lock = asyncio.Lock()
        self._output_dir = output_dir
        self._min_level = min_level

        # Create output directory if specified
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        logger.info(f"Enhanced tracker initialized with min_level={min_level.value}")

    async def record_event(
        self,
        category: DiagnosticCategory,
        level: DiagnosticLevel,
        actor_id: str,
        event_type: str,
        details: Optional[Dict[str, Any]] = None,
        error: Optional[Exception] = None,
        parent_event_id: Optional[str] = None
    ) -> str:
        """
        Record a diagnostic event.

        Args:
            category: Category of the event
            level: Level of detail for the event
            actor_id: ID of the actor
            event_type: Type of event
            details: Optional details about the event
            error: Optional error that occurred
            parent_event_id: Optional ID of the parent event

        Returns:
            The ID of the recorded event
        """
        # Skip events below the minimum level
        if level.value < self._min_level.value:
            return ""

        async with self._lock:
            timestamp = time.time()
            event = DiagnosticEvent(
                category=category,
                level=level,
                actor_id=actor_id,
                event_type=event_type,
                timestamp=timestamp,
                details=details,
                error=error,
                parent_event_id=parent_event_id
            )
            self._events.append(event)

            # Update actor state if this is a state change event
            if category == DiagnosticCategory.STATE:
                if actor_id not in self._actor_states:
                    self._actor_states[actor_id] = {}
                self._actor_states[actor_id]["state"] = event_type
                self._actor_states[actor_id]["timestamp"] = timestamp
                self._actor_states[actor_id].update(details or {})

            # Update actor metrics if this is a performance event
            if category == DiagnosticCategory.PERFORMANCE:
                if actor_id not in self._actor_metrics:
                    self._actor_metrics[actor_id] = {}
                self._actor_metrics[actor_id][event_type] = details or {}
                self._actor_metrics[actor_id][event_type]["timestamp"] = timestamp

            # Update actor errors if this is an error event
            if category == DiagnosticCategory.ERROR:
                if actor_id not in self._actor_errors:
                    self._actor_errors[actor_id] = []
                error_details = {
                    "event_type": event_type,
                    "timestamp": timestamp,
                    "error": str(error) if error else None,
                    "details": details or {}
                }
                self._actor_errors[actor_id].append(error_details)

            # Log the event
            if error:
                logger.error(f"Actor {actor_id} {event_type}: {error}")
            elif level == DiagnosticLevel.VERBOSE:
                logger.debug(f"Actor {actor_id} {event_type}")
            else:
                logger.info(f"Actor {actor_id} {event_type}")

            return event.event_id

    async def record_dependency(self, actor_id: str, dependency_id: str) -> None:
        """
        Record a dependency between actors.

        Args:
            actor_id: ID of the dependent actor
            dependency_id: ID of the actor that must be ready first
        """
        async with self._lock:
            if actor_id not in self._actor_dependencies:
                self._actor_dependencies[actor_id] = set()
            self._actor_dependencies[actor_id].add(dependency_id)
            logger.debug(f"Recorded dependency: {actor_id} depends on {dependency_id}")

    async def get_actor_state(self, actor_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the current state of an actor.

        Args:
            actor_id: ID of the actor

        Returns:
            The current state of the actor, or None if not found
        """
        async with self._lock:
            return self._actor_states.get(actor_id)

    async def get_actor_metrics(self, actor_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the metrics for an actor.

        Args:
            actor_id: ID of the actor

        Returns:
            The metrics for the actor, or None if not found
        """
        async with self._lock:
            return self._actor_metrics.get(actor_id)

    async def get_actor_errors(self, actor_id: str) -> List[Dict[str, Any]]:
        """
        Get the errors for an actor.

        Args:
            actor_id: ID of the actor

        Returns:
            The errors for the actor
        """
        async with self._lock:
            return self._actor_errors.get(actor_id, [])

    async def get_diagnostic_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the diagnostic information.

        Returns:
            Dictionary with diagnostic summary
        """
        async with self._lock:
            total_actors = len(self._actor_states)
            total_events = len(self._events)
            total_errors = sum(len(errors) for errors in self._actor_errors.values())

            # Calculate event counts by category
            event_counts = {}
            for category in DiagnosticCategory:
                event_counts[category.value] = sum(1 for e in self._events if e.category == category)

            # Calculate event counts by level
            level_counts = {}
            for level in DiagnosticLevel:
                level_counts[level.value] = sum(1 for e in self._events if e.level == level)

            # Calculate actor state counts
            state_counts: Dict[str, int] = {}
            for actor_state in self._actor_states.values():
                state = actor_state.get("state", "unknown")
                state_counts[state] = state_counts.get(state, 0) + 1

            return {
                "total_actors": total_actors,
                "total_events": total_events,
                "total_errors": total_errors,
                "event_counts_by_category": event_counts,
                "event_counts_by_level": level_counts,
                "actor_state_counts": state_counts,
                "runtime": time.time() - self._start_time
            }

    async def save_diagnostics(self, filename: Optional[str] = None) -> Optional[str]:
        """
        Save diagnostics data to a file.

        Args:
            filename: Optional filename to save to

        Returns:
            Path to the saved file, or None if saving failed
        """
        if not self._output_dir:
            logger.warning("Cannot save diagnostics: no output directory specified")
            return None

        try:
            # Create a default filename if none provided
            if not filename:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"enhanced_diagnostics_{timestamp}.json"

            filepath = Path(self._output_dir) / filename

            # Prepare data for serialization
            data = {
                "events": [event.to_dict() for event in self._events],
                "actor_dependencies": {
                    actor_id: list(dependencies)
                    for actor_id, dependencies in self._actor_dependencies.items()
                },
                "actor_states": self._actor_states,
                "actor_metrics": self._actor_metrics,
                "actor_errors": self._actor_errors,
                "summary": await self.get_diagnostic_summary()
            }

            # Save to file
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Saved enhanced diagnostics to {filepath}")
            return str(filepath)

        except Exception as e:
            logger.error(f"Failed to save diagnostics: {e}")
            return None

    async def visualize_initialization_sequence(self, filename: Optional[str] = None) -> Optional[str]:
        """
        Generate a visualization of the initialization sequence.

        Args:
            filename: Optional filename to save to

        Returns:
            Path to the saved file, or None if visualization failed
        """
        if not self._output_dir:
            logger.warning("Cannot visualize initialization: no output directory specified")
            return None

        try:
            # Create a default filename if none provided
            if not filename:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"initialization_sequence_{timestamp}.html"

            filepath = Path(self._output_dir) / filename

            # Filter initialization events
            init_events = [e for e in self._events if e.category == DiagnosticCategory.INITIALIZATION]

            # Sort events by timestamp
            init_events.sort(key=lambda e: e.timestamp)

            # Generate HTML visualization
            html = self._generate_sequence_html(init_events)

            # Save to file
            with open(filepath, 'w') as f:
                f.write(html)

            logger.info(f"Saved initialization sequence visualization to {filepath}")
            return str(filepath)

        except Exception as e:
            logger.error(f"Failed to visualize initialization sequence: {e}")
            return None

    def _generate_sequence_html(self, events: List[DiagnosticEvent]) -> str:
        """
        Generate HTML for sequence visualization.

        Args:
            events: List of events to visualize

        Returns:
            HTML string
        """
        # Simple HTML template for sequence diagram
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Actor Initialization Sequence</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .timeline { position: relative; margin: 20px 0; padding: 20px 0; }
                .timeline::before { content: ''; position: absolute; top: 0; bottom: 0; left: 50px; width: 2px; background: #ccc; }
                .event { position: relative; margin-bottom: 20px; padding-left: 70px; }
                .event-dot { position: absolute; left: 44px; width: 16px; height: 16px; border-radius: 50%; background: #4CAF50; }
                .event-dot.error { background: #F44336; }
                .event-time { position: absolute; left: 0; width: 40px; text-align: right; color: #666; }
                .event-content { padding: 10px; background: #f9f9f9; border-radius: 5px; }
                .event-actor { font-weight: bold; }
                .event-type { color: #666; }
                .event-details { margin-top: 5px; font-size: 0.9em; }
                .error-message { color: #F44336; }
            </style>
        </head>
        <body>
            <h1>Actor Initialization Sequence</h1>
            <div class="timeline">
        """

        # Add events to timeline
        start_time = events[0].timestamp if events else time.time()
        for event in events:
            # Calculate relative time
            relative_time = event.timestamp - start_time

            # Determine if this is an error event
            is_error = event.error is not None

            # Add event to timeline
            html += f"""
                <div class="event">
                    <div class="event-time">{relative_time:.2f}s</div>
                    <div class="event-dot{'error' if is_error else ''}"></div>
                    <div class="event-content">
                        <div class="event-actor">{event.actor_id}</div>
                        <div class="event-type">{event.event_type}</div>
            """

            # Add error message if present
            if is_error:
                html += f"""
                        <div class="error-message">{event.error_str}</div>
                """

            # Add event details if present
            if event.details:
                html += f"""
                        <div class="event-details">{json.dumps(event.details)}</div>
                """

            html += """
                    </div>
                </div>
            """

        # Close HTML
        html += """
            </div>
        </body>
        </html>
        """

        return html
