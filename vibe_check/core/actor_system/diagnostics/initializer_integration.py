"""
Initializer Integration Module
=========================

This module integrates the InitializationTracker with the ConsolidatedActorInitializer
to provide detailed diagnostics during the actor initialization process.

It connects the tracker to the initializer, adds detailed step-by-step tracking,
visualizes the initialization sequence, and helps identify bottlenecks and deadlocks.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Set, Tuple, Any, Callable
import json
from pathlib import Path
import os
import warnings

# Import from consolidated_initializer instead of actor_initializer
from ..consolidated_initializer import ConsolidatedActorInitializer, ActorState, ActorInitializationError, get_initializer
from .initialization_tracker import InitializationTracker, InitializationStep, get_tracker

# Issue deprecation warning for this module
warnings.warn(
    "The InitializerIntegration class is being updated to work with ConsolidatedActorInitializer. "
    "Some functionality may change in future versions.",
    DeprecationWarning,
    stacklevel=2
)

logger = logging.getLogger("vibe_check_initializer_integration")


class InitializerIntegration:
    """
    Integrates the InitializationTracker with the ConsolidatedActorInitializer.

    This class connects the tracker to the initializer, providing detailed
    diagnostics during the actor initialization process.
    """

    def __init__(self, output_dir: Optional[str] = None):
        """
        Initialize the integration.

        Args:
            output_dir: Optional directory to save diagnostics data
        """
        self._output_dir = output_dir
        self._tracker = get_tracker(output_dir)
        self._initializer = get_initializer()
        self._lock = asyncio.Lock()
        self._initialized = False
        self._state_callbacks_registered = False

        # Create output directory if specified
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

    async def initialize(self) -> None:
        """
        Initialize the integration.

        This method connects the tracker to the initializer by registering
        callbacks for state changes and other events.
        """
        async with self._lock:
            if self._initialized:
                logger.warning("InitializerIntegration already initialized")
                return

            # Register state change callback with the initializer
            if not self._state_callbacks_registered:
                await self._register_state_callbacks()
                self._state_callbacks_registered = True

            self._initialized = True
            logger.info("InitializerIntegration initialized")

    async def _register_state_callbacks(self) -> None:
        """
        Register callbacks for state changes with the initializer.

        This method uses the event bus to track state changes instead of patching methods,
        which is more compatible with the ConsolidatedActorInitializer.
        """
        # Subscribe to state change events
        event_bus = self._initializer.event_bus

        # Subscribe to actor state change events
        await event_bus.subscribe(
            event_type="actor.state_change.*",
            callback=self._handle_state_change_event
        )

        # Subscribe to dependency registration events
        await event_bus.subscribe(
            event_type="actor.dependency_registration.*",
            callback=self._handle_dependency_registration_event
        )

        logger.info("Registered event callbacks with initializer")

    async def _handle_state_change_event(self, event: Any) -> None:
        """
        Handle a state change event from the event bus.

        Args:
            event: The event object from the event bus
        """
        # Extract event data
        event_type = getattr(event, "event_type", "")
        data = getattr(event, "data", {})
        source = getattr(event, "source", "")
        # Extract actor ID from event type
        actor_id = event_type.split(".")[-1]

        # Extract state information
        state = data.get("new_state")
        old_state = data.get("old_state")
        error = data.get("error")

        if not state or not isinstance(state, ActorState):
            logger.warning(f"Invalid state in event: {event_type}")
            return

        # Map ActorState to InitializationStep
        step = self._map_state_to_step(state)

        # Record the event
        details = {
            "phase": data.get("phase", "unknown"),
            "old_state": old_state.value if old_state else None,
            "new_state": state.value,
            "timestamp": data.get("timestamp", time.time())
        }

        # Create an exception object if error data is present
        exception = None
        if error:
            exception = Exception(error.get("message", "Unknown error"))

        # Record the event
        await self._tracker.record_event(actor_id, step, details, exception)

    async def _handle_dependency_registration_event(self, event: Any) -> None:
        """
        Handle a dependency registration event from the event bus.

        Args:
            event: The event object from the event bus
        """
        # Extract event data
        event_type = getattr(event, "event_type", "")
        data = getattr(event, "data", {})
        source = getattr(event, "source", "")
        # Extract actor ID and dependency ID
        actor_id = data.get("actor_id")
        dependency_id = data.get("dependency_id")

        if not actor_id or not dependency_id:
            logger.warning(f"Invalid dependency registration event: {event_type}")
            return

        # Record the dependency
        await self._tracker.record_dependency(actor_id, dependency_id)

        logger.debug(f"Recorded dependency: {actor_id} -> {dependency_id}")

    def _map_state_to_step(self, state: ActorState) -> InitializationStep:
        """
        Map an ActorState to an InitializationStep.

        Args:
            state: Actor state

        Returns:
            Corresponding initialization step
        """
        mapping = {
            ActorState.CREATED: InitializationStep.REGISTRATION,
            ActorState.INITIALIZING: InitializationStep.INITIALIZATION_START,
            ActorState.INITIALIZED: InitializationStep.INITIALIZATION_COMPLETE,
            ActorState.STARTING: InitializationStep.START_BEGIN,
            ActorState.READY: InitializationStep.READY,
            ActorState.FAILED: InitializationStep.FAILED,
            ActorState.STOPPING: InitializationStep.DEPENDENCY_CHECK,  # Reusing this step for stopping
            ActorState.STOPPED: InitializationStep.DEPENDENCY_CHECK,   # Reusing this step for stopped
            ActorState.ROLLBACK: InitializationStep.DEPENDENCY_CHECK   # Reusing this step for rollback
        }
        return mapping.get(state, InitializationStep.DEPENDENCY_CHECK)

    async def generate_initialization_report(self, filename: Optional[str] = None) -> Optional[str]:
        """
        Generate a detailed initialization report.

        Args:
            filename: Optional filename to save to

        Returns:
            Path to the saved file, or None if saving failed
        """
        # Get initialization summary from tracker
        summary = self._tracker.get_initialization_summary()

        # Add additional information
        summary["initialization_sequence"] = await self._get_initialization_sequence()
        summary["dependency_graph"] = await self._get_dependency_graph()
        summary["bottlenecks"] = self._identify_bottlenecks()
        summary["deadlocks"] = await self._identify_deadlocks()

        # Save the report
        return await self._tracker.save_diagnostics(filename)

    async def _get_initialization_sequence(self) -> List[Dict[str, Any]]:
        """
        Get the initialization sequence for all actors.

        Returns:
            List of initialization events in sequence
        """
        # This is already tracked by the tracker
        return [event.to_dict() for event in self._tracker._events]

    async def _get_dependency_graph(self) -> Dict[str, List[str]]:
        """
        Get the dependency graph for all actors.

        Returns:
            Dictionary mapping actor IDs to lists of dependency IDs
        """
        # This is already tracked by the tracker
        return {
            actor_id: list(dependencies)
            for actor_id, dependencies in self._tracker._actor_dependencies.items()
        }

    def _identify_bottlenecks(self) -> List[Dict[str, Any]]:
        """
        Identify bottlenecks in the initialization process.

        Returns:
            List of bottleneck information
        """
        # Get the slowest actors
        slowest_actors = self._tracker.get_slowest_actors(10)

        # Format the results
        return [
            {
                "actor_id": actor_id,
                "initialization_time": time_taken,
                "percentage_of_total": time_taken / (time.time() - self._tracker._start_time) * 100
            }
            for actor_id, time_taken in slowest_actors
        ]

    async def _identify_deadlocks(self) -> List[Dict[str, Any]]:
        """
        Identify potential deadlocks in the initialization process.

        Returns:
            List of deadlock information
        """
        # Look for circular dependencies
        deadlocks = []

        # Build a dependency graph
        graph = {}
        for actor_id, dependencies in self._tracker._actor_dependencies.items():
            graph[actor_id] = list(dependencies)

        # Check for cycles using DFS
        visited: Set[str] = set()
        path: List[str] = []
        path_set: Set[str] = set()

        async def dfs(node: str) -> None:
            if node in path_set:
                # Found a cycle
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                deadlocks.append({
                    "cycle": cycle,
                    "actors": [
                        {
                            "actor_id": actor_id,
                            "state": await self._get_actor_state(actor_id)
                        }
                        for actor_id in cycle
                    ]
                })
                return

            if node in visited:
                return

            visited.add(node)
            path.append(node)
            path_set.add(node)

            for neighbor in graph.get(node, []):
                await dfs(neighbor)

            path.pop()
            path_set.remove(node)

        # Check from each node
        for node in graph:
            if node not in visited:
                await dfs(node)

        return deadlocks

    async def _get_actor_state(self, actor_id: str) -> Optional[str]:
        """
        Get the current state of an actor.

        Args:
            actor_id: ID of the actor

        Returns:
            Current state as a string, or None if not found
        """
        try:
            # Use the actor_states dictionary directly instead of get_actor_state method
            with self._initializer._state_lock:
                state = self._initializer._actor_states.get(actor_id)
                if state:
                    return state.value
            return None
        except Exception:
            return None


# Singleton instance
_integration = None


def get_integration(output_dir: Optional[str] = None) -> InitializerIntegration:
    """
    Get the singleton integration instance.

    Args:
        output_dir: Optional directory to save diagnostics data

    Returns:
        The initializer integration instance
    """
    global _integration

    if _integration is None:
        _integration = InitializerIntegration(output_dir)
        logger.info("Created new initializer integration")

    return _integration


def reset_integration() -> None:
    """Reset the singleton integration instance."""
    global _integration

    _integration = None
    logger.info("Reset initializer integration")
