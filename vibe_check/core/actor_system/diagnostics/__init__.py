"""
Actor System Diagnostics Package
============================

This package provides diagnostic tools for the actor system, including:

- Initialization tracking: Detailed tracking of the actor initialization process
- Dependency visualization: Visualization of actor dependencies
- Performance monitoring: Monitoring of actor performance metrics
- Error tracking: Tracking and analysis of errors in the actor system

These tools help identify issues, optimize performance, and improve the
reliability of the actor system.
"""

from .initialization_tracker import (
    InitializationTracker,
    InitializationStep,
    InitializationEvent,
    get_tracker,
    reset_tracker
)

# Add an alias for get_tracker for backward compatibility
initialize_tracker = get_tracker

__all__ = [
    'InitializationTracker',
    'InitializationStep',
    'InitializationEvent',
    'get_tracker',
    'reset_tracker',
    'initialize_tracker'
]
