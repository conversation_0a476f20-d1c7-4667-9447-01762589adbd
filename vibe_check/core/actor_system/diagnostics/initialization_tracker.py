"""
Initialization Tracker Module
=========================

This module provides detailed diagnostics for the actor system initialization process.
It tracks the initialization steps for each actor, records timing information,
and provides visualization of the initialization sequence.

This helps identify bottlenecks, deadlocks, and other issues during the
initialization process, making it easier to debug and optimize the system.
"""

import asyncio
import logging
import time
from enum import Enum
from typing import Dict, List, Optional, Set, Tuple, Any
import json
from pathlib import Path
import os

from ..actor_state import ActorState

logger = logging.getLogger("vibe_check_initialization_tracker")


class InitializationStep(Enum):
    """Enum representing the steps in the initialization process."""
    REGISTRATION = "registration"
    DEPENDENCY_CHECK = "dependency_check"
    INITIALIZATION_START = "initialization_start"
    INITIALIZATION_COMPLETE = "initialization_complete"
    START_BEGIN = "start_begin"
    START_COMPLETE = "start_complete"
    READY = "ready"
    FAILED = "failed"


class InitializationEvent:
    """Class representing an event during the initialization process."""

    def __init__(
        self,
        actor_id: str,
        step: InitializationStep,
        timestamp: float,
        details: Optional[Dict[str, Any]] = None,
        error: Optional[Exception] = None
    ):
        """
        Initialize the event.

        Args:
            actor_id: ID of the actor
            step: Initialization step
            timestamp: Time when the event occurred
            details: Optional details about the event
            error: Optional error that occurred during this step
        """
        self.actor_id = actor_id
        self.step = step
        self.timestamp = timestamp
        self.details = details or {}
        self.error = error
        self.error_str = str(error) if error else None

    def to_dict(self) -> Dict[str, Any]:
        """Convert the event to a dictionary for serialization."""
        return {
            "actor_id": self.actor_id,
            "step": self.step.value,
            "timestamp": self.timestamp,
            "details": self.details,
            "error": self.error_str
        }


class InitializationTracker:
    """
    Tracks the initialization process for all actors in the system.

    This class records events during the initialization process, providing
    detailed diagnostics to help identify issues and optimize the system.
    """

    def __init__(self, output_dir: Optional[str] = None):
        """
        Initialize the tracker.

        Args:
            output_dir: Optional directory to save diagnostics data
        """
        self._events: List[InitializationEvent] = []
        self._actor_steps: Dict[str, Dict[InitializationStep, float]] = {}
        self._actor_errors: Dict[str, List[Tuple[InitializationStep, Exception]]] = {}
        self._actor_dependencies: Dict[str, Set[str]] = {}
        self._start_time = time.time()
        self._lock = asyncio.Lock()
        self._output_dir = output_dir

        # Create output directory if specified
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

    async def record_event(
        self,
        actor_id: str,
        step: InitializationStep,
        details: Optional[Dict[str, Any]] = None,
        error: Optional[Exception] = None
    ) -> None:
        """
        Record an initialization event.

        Args:
            actor_id: ID of the actor
            step: Initialization step
            details: Optional details about the event
            error: Optional error that occurred during this step
        """
        async with self._lock:
            timestamp = time.time()
            event = InitializationEvent(actor_id, step, timestamp, details, error)
            self._events.append(event)

            # Update actor steps
            if actor_id not in self._actor_steps:
                self._actor_steps[actor_id] = {}
            self._actor_steps[actor_id][step] = timestamp

            # Record error if any
            if error:
                if actor_id not in self._actor_errors:
                    self._actor_errors[actor_id] = []
                self._actor_errors[actor_id].append((step, error))

            # Log the event
            if error:
                logger.error(f"Actor {actor_id} {step.value}: {error}")
            else:
                logger.info(f"Actor {actor_id} {step.value}")

    async def record_dependency(self, actor_id: str, dependency_id: str) -> None:
        """
        Record a dependency between actors.

        Args:
            actor_id: ID of the dependent actor
            dependency_id: ID of the actor that must be ready first
        """
        async with self._lock:
            if actor_id not in self._actor_dependencies:
                self._actor_dependencies[actor_id] = set()
            self._actor_dependencies[actor_id].add(dependency_id)
            logger.debug(f"Recorded dependency: {actor_id} depends on {dependency_id}")

    def get_actor_initialization_time(self, actor_id: str) -> Optional[float]:
        """
        Get the total initialization time for an actor.

        Args:
            actor_id: ID of the actor

        Returns:
            Total initialization time in seconds, or None if not complete
        """
        if actor_id not in self._actor_steps:
            return None

        steps = self._actor_steps[actor_id]
        if InitializationStep.REGISTRATION not in steps or InitializationStep.READY not in steps:
            return None

        return steps[InitializationStep.READY] - steps[InitializationStep.REGISTRATION]

    def get_slowest_actors(self, limit: int = 5) -> List[Tuple[str, float]]:
        """
        Get the slowest actors during initialization.

        Args:
            limit: Maximum number of actors to return

        Returns:
            List of (actor_id, time) tuples, sorted by time (descending)
        """
        times = []
        for actor_id in self._actor_steps:
            time_taken = self.get_actor_initialization_time(actor_id)
            if time_taken is not None:
                times.append((actor_id, time_taken))

        return sorted(times, key=lambda x: x[1], reverse=True)[:limit]

    def get_failed_actors(self) -> Dict[str, List[Tuple[InitializationStep, str]]]:
        """
        Get all actors that failed during initialization.

        Returns:
            Dictionary mapping actor IDs to lists of (step, error) tuples
        """
        result = {}
        for actor_id, errors in self._actor_errors.items():
            result[actor_id] = [(step, str(error)) for step, error in errors]
        return result

    def get_initialization_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the initialization process.

        Returns:
            Dictionary with initialization summary
        """
        total_actors = len(self._actor_steps)
        # Count actors that have reached the READY step or have any step recorded
        completed_actors = sum(1 for actor_id in self._actor_steps
                              if self._actor_steps[actor_id])
        failed_actors = len(self._actor_errors)

        total_time = time.time() - self._start_time

        return {
            "total_actors": total_actors,
            "completed_actors": completed_actors,
            "failed_actors": failed_actors,
            "total_time": total_time,
            "slowest_actors": self.get_slowest_actors(),
            "failed_actor_details": self.get_failed_actors()
        }

    async def save_diagnostics(self, filename: Optional[str] = None) -> Optional[str]:
        """
        Save diagnostics data to a file.

        Args:
            filename: Optional filename to save to

        Returns:
            Path to the saved file, or None if saving failed
        """
        if not self._output_dir:
            logger.warning("Cannot save diagnostics: no output directory specified")
            return None

        try:
            # Create a default filename if none provided
            if not filename:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"initialization_diagnostics_{timestamp}.json"

            filepath = Path(self._output_dir) / filename

            # Prepare data for serialization
            data = {
                "events": [event.to_dict() for event in self._events],
                "actor_steps": {
                    actor_id: {step.value: timestamp for step, timestamp in steps.items()}
                    for actor_id, steps in self._actor_steps.items()
                },
                "actor_dependencies": {
                    actor_id: list(dependencies)
                    for actor_id, dependencies in self._actor_dependencies.items()
                },
                "summary": self.get_initialization_summary()
            }

            # Save to file
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Saved initialization diagnostics to {filepath}")
            return str(filepath)

        except Exception as e:
            logger.error(f"Failed to save diagnostics: {e}")
            return None


# Singleton instance
_tracker = None


def get_tracker(output_dir: Optional[str] = None) -> InitializationTracker:
    """
    Get the singleton tracker instance.

    Args:
        output_dir: Optional directory to save diagnostics data

    Returns:
        The initialization tracker instance
    """
    global _tracker

    if _tracker is None:
        _tracker = InitializationTracker(output_dir)
        logger.info("Created new initialization tracker")

    return _tracker


def reset_tracker() -> None:
    """Reset the singleton tracker instance."""
    global _tracker

    _tracker = None
    logger.info("Reset initialization tracker")
