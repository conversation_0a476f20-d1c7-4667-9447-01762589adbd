"""
Synchronization Module
=====================

This module provides synchronization primitives for the actor system,
including synchronization points and dummy implementations for testing.
"""

import asyncio
import logging
import time
from typing import Any, Optional, Set, Type, Union, cast

logger = logging.getLogger("vibe_check_synchronization")


class SynchronizationPoint:
    """
    Represents a synchronization point in the actor initialization process.

    A synchronization point is a barrier that actors must wait for before
    proceeding to the next phase of initialization.

    This implementation uses lazy initialization for asyncio objects to avoid
    event loop dependencies in constructors.
    """

    def __init__(self, name: str, auto_complete: bool = False):
        """
        Initialize the synchronization point.

        Args:
            name: Name of the synchronization point
            auto_complete: If True, immediately complete the synchronization point
                          (useful for testing)
        """
        self.name = name
        self.reached_actors: Set[str] = set()
        self._event: Optional[Union[asyncio.Event, 'DummyEvent']] = None  # Lazy initialization
        self._lock: Optional[Union[asyncio.Lock, 'DummyLock']] = None   # Lazy initialization
        self.creation_time = time.time()
        self.completion_time: Optional[float] = None
        self._is_complete = False  # Track completion state without requiring an event

        # For testing purposes, immediately complete the synchronization point if requested
        if auto_complete:
            self.complete()

    @property
    def event(self) -> Union[asyncio.Event, 'DummyEvent']:
        """
        Get the event for this synchronization point.

        Lazily initializes the event if it doesn't exist yet.

        Returns:
            The event for this synchronization point
        """
        if self._event is None:
            try:
                event = asyncio.Event()
                # If we're already complete, set the event
                if self._is_complete:
                    event.set()
                self._event = event
            except RuntimeError:
                # No running event loop, create a dummy event that's always set
                logger.warning(f"No running event loop when creating event for {self.name}")
                self._event = DummyEvent(self._is_complete)
        # Cast to the expected return type for type checking
        return cast(Union[asyncio.Event, DummyEvent], self._event)

    @property
    def lock(self) -> Union[asyncio.Lock, 'DummyLock']:
        """
        Get the lock for this synchronization point.

        Lazily initializes the lock if it doesn't exist yet.

        Returns:
            The lock for this synchronization point
        """
        if self._lock is None:
            try:
                self._lock = asyncio.Lock()
            except RuntimeError:
                # No running event loop, create a dummy lock
                logger.warning(f"No running event loop when creating lock for {self.name}")
                self._lock = DummyLock()
        return cast(Union[asyncio.Lock, DummyLock], self._lock)

    async def reach(self, actor_id: str) -> None:
        """
        Mark that an actor has reached this synchronization point.

        Args:
            actor_id: ID of the actor
        """
        async with self.lock:
            # Add the actor to the reached set
            self.reached_actors.add(actor_id)
            logger.debug(f"Actor {actor_id} reached synchronization point {self.name}")

    async def wait(self, timeout: Optional[float] = None) -> bool:
        """
        Wait for the synchronization point to be completed.

        Args:
            timeout: Optional timeout in seconds

        Returns:
            True if the point was completed, False if timed out
        """
        # If we're already complete, return immediately
        if self._is_complete:
            return True

        try:
            # Get the event (lazy initialization)
            event = self.event

            if event.is_set():
                return True

            if timeout is not None:
                await asyncio.wait_for(event.wait(), timeout=timeout)
            else:
                await event.wait()

            return True
        except asyncio.TimeoutError:
            logger.warning(f"Timeout waiting for synchronization point {self.name}")
            return False

    def complete(self) -> None:
        """Mark this synchronization point as complete."""
        if not self._is_complete:
            self.completion_time = time.time()
            self._is_complete = True

            # Set the event if it exists
            if self._event is not None:
                self._event.set()

            logger.info(f"Synchronization point {self.name} completed")

    def is_complete(self) -> bool:
        """
        Check if this synchronization point is complete.

        Returns:
            True if complete, False otherwise
        """
        return self._is_complete


class DummyEvent:
    """
    A dummy event that can be used when no event loop is available.

    This class mimics the asyncio.Event interface but doesn't require an event loop.
    """

    def __init__(self, is_set: bool = False):
        """
        Initialize the dummy event.

        Args:
            is_set: Whether the event is initially set
        """
        self._is_set = is_set

    def is_set(self) -> bool:
        """
        Check if the event is set.

        Returns:
            True if the event is set, False otherwise
        """
        return self._is_set

    def set(self) -> None:
        """Set the event."""
        self._is_set = True

    def clear(self) -> None:
        """Clear the event."""
        self._is_set = False

    async def wait(self) -> bool:
        """
        Wait for the event to be set.

        Since this is a dummy event, it returns immediately.

        Returns:
            True if the event is set, False otherwise
        """
        return self._is_set


class DummyLock:
    """
    A dummy lock that can be used when no event loop is available.

    This class mimics the asyncio.Lock interface but doesn't require an event loop.
    """

    def __init__(self) -> None:
        """Initialize the dummy lock."""
        self._locked = False

    async def __aenter__(self) -> 'DummyLock':
        """
        Acquire the lock.

        Returns:
            The lock itself
        """
        self._locked = True
        return self

    async def __aexit__(self, exc_type: Optional[Type[BaseException]],
                      exc_val: Optional[BaseException],
                      exc_tb: Optional[Any]) -> None:
        """Release the lock."""
        self._locked = False

    async def acquire(self) -> bool:
        """
        Acquire the lock.

        Returns:
            True if the lock was acquired, False otherwise
        """
        self._locked = True
        return True

    def release(self) -> None:
        """Release the lock."""
        self._locked = False

    def locked(self) -> bool:
        """
        Check if the lock is locked.

        Returns:
            True if the lock is locked, False otherwise
        """
        return self._locked
