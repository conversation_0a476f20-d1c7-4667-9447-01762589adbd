"""
Actor Starter Module
==================

This module defines the ActorStarter class, which is responsible for
starting an actor in the system.

The ActorStarter implements the second phase of the two-phase initialization
process, ensuring that actors are properly started and ready to process messages.
It handles dependency waiting, task creation, and supervisor notification.
"""

import asyncio
import logging
import time
import traceback
from typing import Any, Dict, List, Optional, Set, TYPE_CHECKING, Callable

# Avoid circular imports
if TYPE_CHECKING:
    from ..actor import Actor
    from ..actor_initializer import ActorInitializer

from ..consolidated_initializer import ActorState, get_initializer
from ..message import MessageType
from ..exceptions import (
    ActorSystemError,
    ActorInitializationError,
    ActorDependencyError,
    ActorTimeoutError
)

logger = logging.getLogger("vibe_check_actor_system.starter")


class ActorStarter:
    """
    Handles the starting of an actor.

    This class encapsulates the actor starting logic that was previously
    part of the Actor class. It implements the second phase of the two-phase
    initialization process, ensuring that actors are properly started and
    ready to process messages.

    Attributes:
        actor: The actor that owns this starter
    """

    def __init__(self, actor: 'Actor'):
        """
        Initialize the actor starter.

        Args:
            actor: The actor that owns this starter
        """
        self.actor = actor

    async def start(self, timeout: float = 30.0) -> None:
        """
        Start the actor (second phase of two-phase initialization).

        This method handles the starting of an actor, including:
        - Updating the initializer state
        - Verifying the actor can be started
        - Waiting for dependencies
        - Starting actor tasks
        - Notifying the supervisor
        - Processing pending messages

        Args:
            timeout: Maximum time in seconds to wait for the actor to start

        Raises:
            ActorInitializationError: If the actor fails to start
            ActorTimeoutError: If the actor fails to start within the timeout
            ActorDependencyError: If a dependency fails to start
        """
        # Get initializer
        initializer = get_initializer()

        # Create a timeout for the entire start process
        try:
            # Use wait_for instead of asyncio.timeout for compatibility with older Python versions
            async def _start_with_timeout() -> None:
                try:
                    # Update initializer state
                    await self._update_initializer_state(initializer, ActorState.STARTING)

                    # Verify actor can be started
                    await self._verify_can_start()

                    # Wait for dependencies to be ready (not just initialized)
                    await self._wait_for_dependencies(initializer, timeout=timeout/2)

                    # Start actor tasks
                    await self._start_actor_tasks()

                    # Notify supervisor
                    await self._notify_supervisor_of_start()

                    # Process pending messages
                    await self._process_pending_messages()

                    # Update final state
                    await self._update_initializer_state(initializer, ActorState.READY)

                    logger.info(f"Actor {self.actor.actor_id} started successfully")

                except ActorSystemError as e:
                    # These are our custom exceptions, so just re-raise them
                    logger.error(f"Actor system error during start of {self.actor.actor_id}: {e}")
                    raise

                except Exception as e:
                    logger.error(f"Error starting actor {self.actor.actor_id}: {e}")
                    logger.error(traceback.format_exc())

                    # Wrap the exception in our custom exception
                    raise ActorInitializationError(
                        message=str(e),
                        actor_id=self.actor.actor_id,
                        phase="start",
                        state=ActorState.STARTING,
                        original_error=e
                    ) from e

            # Execute the start process with a timeout
            try:
                await asyncio.wait_for(_start_with_timeout(), timeout=timeout)
            except asyncio.TimeoutError as e:
                logger.error(f"Timeout starting actor {self.actor.actor_id} after {timeout} seconds")

            # Create a timeout exception
            timeout_error = ActorTimeoutError(
                message=f"Start timed out after {timeout} seconds",
                actor_id=self.actor.actor_id,
                operation="start",
                timeout=timeout
            )

            # Handle the error
            await self._handle_start_error(timeout_error, initializer)

            # Raise the timeout exception
            raise timeout_error

        except Exception as e:
            # This catches any exceptions from the timeout context
            await self._handle_start_error(e, initializer)

            # Re-raise the exception
            raise

    async def _verify_can_start(self) -> None:
        """
        Verify the actor can be started.

        This method checks if the actor is already running and if it has been
        properly initialized.

        Raises:
            ActorInitializationError: If the actor is already running or has not been initialized
        """
        if self.actor.is_running:
            logger.warning(f"Actor {self.actor.actor_id} is already running")
            return

        # Enforce two-phase initialization
        if not self.actor._initialization_complete:
            logger.warning(f"Actor {self.actor.actor_id} not initialized, initializing now")
            logger.warning("Two-phase initialization is recommended: call initialize() before start()")
            await self.actor.initialize()

        # Verify initialization was successful
        if not self.actor._initialization_complete:
            error_msg = f"Actor {self.actor.actor_id} failed to initialize properly"
            logger.error(error_msg)
            raise ActorInitializationError(
                actor_id=self.actor.actor_id,
                phase="start",
                state=ActorState.STARTING,
                message=error_msg
            )

    async def _wait_for_dependencies(self, initializer: Optional['ActorInitializer'],
                                 timeout: float = 30.0) -> None:
        """
        Wait for dependencies to be ready.

        This method waits for all dependencies to be in the READY state before proceeding.
        If any dependency fails, it raises an ActorDependencyError.

        Args:
            initializer: The actor initializer
            timeout: Maximum time in seconds to wait for dependencies

        Raises:
            ActorDependencyError: If a dependency fails to start
            ActorTimeoutError: If waiting for dependencies times out
        """
        if not initializer:
            logger.warning(f"Actor {self.actor.actor_id} cannot wait for dependencies: initializer not available")
            return

        logger.info(f"Actor {self.actor.actor_id} waiting for dependencies to be ready")

        try:
            # Wait for dependencies to be ready (not just initialized)
            dependencies_ready = await self.actor.wait_for_dependencies(timeout=timeout)

            if not dependencies_ready:
                # Check if any dependencies failed
                dependency_errors = {}

                # Get dependencies from the initializer
                dependencies = await initializer.get_actor_dependencies(self.actor.actor_id)

                # Check each dependency's state
                for dep_id in dependencies:
                    try:
                        state = await initializer.get_actor_state(dep_id)
                        if state == ActorState.FAILED:
                            error_msg = f"Dependency {dep_id} failed to start"
                            logger.error(error_msg)
                            dependency_errors[dep_id] = error_msg
                    except Exception as e:
                        error_msg = f"Error checking dependency {dep_id}: {str(e)}"
                        logger.error(error_msg)
                        dependency_errors[dep_id] = error_msg

                # If any dependencies failed, raise an error
                if dependency_errors:
                    raise ActorDependencyError(
                        message=f"Failed to start due to dependency errors",
                        actor_id=self.actor.actor_id,
                        dependencies=dependency_errors
                    )

                # Otherwise, it's just a timeout
                logger.warning(f"Actor {self.actor.actor_id} timed out waiting for dependencies, starting anyway")

        except ActorSystemError:
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            logger.error(f"Error waiting for dependencies: {e}")
            logger.error(traceback.format_exc())

            # Wrap in a dependency error
            raise ActorDependencyError(
                message=f"Error waiting for dependencies: {str(e)}",
                actor_id=self.actor.actor_id,
                dependencies={"unknown": str(e)}
            ) from e

    async def _start_actor_tasks(self) -> None:
        """
        Start the actor's tasks.

        This method starts the message processing task, heartbeat task, and metrics task.
        """
        # Set the running flag to true
        self.actor.is_running = True

        # Start the message processing task
        process_coro = self.actor.process_messages()
        self.actor._process_task = await self._create_task(
            process_coro,
            "process task",
            self._create_task_callback("process")
        )

        # Start the heartbeat task if we have a supervisor
        if self.actor.supervisor_id:
            heartbeat_coro = self.actor._send_heartbeats()
            self.actor._heartbeat_task = await self._create_task(
                heartbeat_coro,
                "heartbeat task",
                self._create_task_callback("heartbeat")
            )

        # Start the metrics task
        metrics_coro = self.actor._collect_metrics()
        self.actor._metrics_task = await self._create_task(
            metrics_coro,
            "metrics task",
            self._create_task_callback("metrics")
        )

    def _create_task_callback(self, task_name: str) -> Callable[[asyncio.Task], None]:
        """
        Create a callback function for a task.

        Args:
            task_name: The name of the task

        Returns:
            A callback function for the task
        """
        def callback(task: asyncio.Task) -> None:
            if task.cancelled():
                logger.info(f"Actor {self.actor.actor_id} {task_name} task was cancelled")
            else:
                logger.info(f"Actor {self.actor.actor_id} {task_name} task completed")

        return callback

    async def _create_task(self, coro: Any, task_name: str, callback: Any) -> asyncio.Task:
        """
        Create a task with proper callback.

        Args:
            coro: The coroutine to run in the task
            task_name: The name of the task
            callback: The callback function for the task

        Returns:
            The created task
        """
        task = asyncio.create_task(coro)
        task.add_done_callback(callback)
        return task

    async def _notify_supervisor_of_start(self) -> None:
        """
        Notify supervisor that the actor has started.
        """
        if self.actor.supervisor_id and self.actor.supervisor_id in self.actor._known_actors:
            try:
                start_payload = {
                    "actor_id": self.actor.actor_id,
                    "timestamp": time.time(),
                    "actor_type": self.actor.actor_type,
                    "tags": list(self.actor.tags),
                    "capabilities": list(self.actor.capabilities)
                }
                await self.actor.send(self.actor.supervisor_id, MessageType.START, start_payload)
            except Exception as e:
                logger.warning(f"Failed to notify supervisor of start: {e}")

    async def _process_pending_messages(self) -> None:
        """
        Process any pending messages.

        This method sets the actor as ready and processes any messages that were
        received before the actor was ready.
        """
        # Set the ready flag to true
        self.actor._ready = True
        self.actor._ready_event.set()

        # Process any pending messages using the MessageProcessor
        if self.actor._pending_messages:
            # Log the count of pending messages
            pending_count = len(self.actor._pending_messages)
            logger.info(f"Actor {self.actor.actor_id} has {pending_count} pending messages to process")

            # Use the MessageProcessor to process pending messages
            try:
                await self.actor._message_processor._process_pending_messages()
            except Exception as e:
                logger.error(f"Error processing pending messages for actor {self.actor.actor_id}: {e}")
                import traceback
                logger.error(traceback.format_exc())

    async def _update_initializer_state(self, initializer: Optional['ActorInitializer'],
                                       state: ActorState) -> None:
        """
        Update the actor's state in the initializer.

        Args:
            initializer: The actor initializer
            state: The new state
        """
        if initializer:
            await initializer.set_actor_state(self.actor.actor_id, state, phase="start")

    async def _handle_start_error(self, error: Exception,
                                 initializer: Optional['ActorInitializer']) -> None:
        """
        Handle errors during start.

        Args:
            error: The error that occurred
            initializer: The actor initializer

        Raises:
            ActorInitializationError: Always raised with the original error
        """
        logger.error(f"Error starting actor {self.actor.actor_id}: {error}")
        logger.error(traceback.format_exc())

        # Reset running state
        self.actor.is_running = False
        self.actor._ready = False
        self.actor._ready_event.clear()

        # Cancel any tasks that were started
        for task_name in ['_process_task', '_heartbeat_task', '_metrics_task']:
            task = getattr(self.actor, task_name, None)
            if task and not task.done():
                try:
                    task.cancel()
                    logger.info(f"Cancelled {task_name} for actor {self.actor.actor_id} due to start failure")
                except Exception as cancel_error:
                    logger.error(f"Error cancelling {task_name} for actor {self.actor.actor_id}: {cancel_error}")

        # Update initializer state to FAILED
        if initializer:
            try:
                await initializer.set_actor_state(
                    self.actor.actor_id,
                    ActorState.FAILED,
                    error=error,
                    phase="start"
                )

                # Roll back initialization
                await initializer.rollback_actor(self.actor.actor_id)
            except Exception as rollback_error:
                logger.error(f"Error rolling back actor {self.actor.actor_id}: {rollback_error}")

        # Re-raise as ActorInitializationError
        raise ActorInitializationError(
            actor_id=self.actor.actor_id,
            phase="start",
            state=ActorState.STARTING,
            message=str(error),
            original_error=error
        ) from error
