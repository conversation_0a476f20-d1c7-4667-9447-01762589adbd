"""
Actor Terminator Module
=====================

This module defines the ActorTerminator class, which is responsible for
stopping an actor in the system.

The ActorTerminator handles the graceful shutdown of an actor, including
state saving, task cancellation, and registry unregistration.
"""

import logging
import time
import traceback
from typing import Optional, TYPE_CHECKING

# Avoid circular imports
if TYPE_CHECKING:
    from ..actor import Actor
    from ..consolidated_initializer import ConsolidatedActorInitializer

from ..consolidated_initializer import ActorState, get_initializer
from ..message import MessageType

logger = logging.getLogger("vibe_check_actor_system.terminator")


class ActorTerminator:
    """
    Handles the stopping of an actor.

    This class encapsulates the actor stopping logic that was previously
    part of the Actor class. It handles the graceful shutdown of an actor,
    including state saving, task cancellation, and registry unregistration.

    Attributes:
        actor: The actor that owns this terminator
    """

    def __init__(self, actor: 'Actor'):
        """
        Initialize the actor terminator.

        Args:
            actor: The actor that owns this terminator
        """
        self.actor = actor

    async def stop(self) -> None:
        """
        Stop the actor.

        This method handles the stopping of an actor, including:
        - Updating the initializer state
        - Notifying the supervisor
        - Saving state
        - Unregistering from the registry
        - Cancelling tasks
        - Processing remaining messages
        """
        # Get initializer
        initializer = get_initializer()

        try:
            # Update initializer state
            await self._update_initializer_state(initializer, ActorState.STOPPING)

            # Check if already stopped
            if not self.actor.is_running:
                logger.info(f"Actor {self.actor.actor_id} is already stopped")
                await self._update_initializer_state(initializer, ActorState.STOPPED)
                return

            logger.info(f"Stopping actor {self.actor.actor_id}")

            # Notify supervisor
            await self._notify_supervisor_of_stop()

            # Set flags to stop tasks
            self._set_stop_flags()

            # Save state
            await self._save_state()

            # Unregister from registry
            await self._unregister_from_registry()

            # Cancel tasks
            await self.actor._cancel_all_tasks()

            # Process remaining messages
            await self.actor._process_remaining_messages()

            # Update final state
            await self._update_initializer_state(initializer, ActorState.STOPPED)

            logger.info(f"Actor {self.actor.actor_id} stopped")

        except Exception as e:
            await self._handle_stop_error(e, initializer)

    async def _notify_supervisor_of_stop(self) -> None:
        """
        Notify supervisor that the actor is stopping.
        """
        if self.actor.supervisor_id and self.actor.supervisor_id in self.actor._known_actors:
            try:
                stop_payload = {
                    "actor_id": self.actor.actor_id,
                    "timestamp": time.time(),
                    "reason": "normal_shutdown"
                }
                await self.actor.send(self.actor.supervisor_id, MessageType.STOP, stop_payload)
            except Exception as e:
                logger.warning(f"Failed to notify supervisor of stop: {e}")

    def _set_stop_flags(self) -> None:
        """
        Set flags to stop all tasks.
        """
        self.actor.is_running = False
        self.actor._ready = False
        self.actor._ready_event.clear()

    async def _save_state(self) -> None:
        """
        Save actor state before stopping.
        """
        try:
            await self.actor._save_state()
        except Exception as state_error:
            logger.error(f"Error saving state for actor {self.actor.actor_id}: {state_error}")
            # Continue with shutdown despite state saving error

    async def _unregister_from_registry(self) -> None:
        """
        Unregister from the actor registry.
        """
        try:
            from ..actor_registry import get_registry
            registry = get_registry()
            registry.unregister_actor(self.actor.actor_id)
            logger.info(f"Actor {self.actor.actor_id} unregistered from registry")
        except (ImportError, AttributeError, Exception) as e:
            logger.warning(f"Could not unregister actor {self.actor.actor_id} from registry: {e}")

    async def _update_initializer_state(self, initializer: Optional['ConsolidatedActorInitializer'],
                                       state: ActorState) -> None:
        """
        Update the actor's state in the initializer.

        Args:
            initializer: The actor initializer
            state: The new state
        """
        if initializer:
            await initializer.set_actor_state(
                actor_id=self.actor.actor_id,
                state=state,
                error=None,
                phase="stop"
            )

    async def _handle_stop_error(self, error: Exception,
                                initializer: Optional['ConsolidatedActorInitializer']) -> None:
        """
        Handle errors during stop.

        Args:
            error: The error that occurred
            initializer: The actor initializer
        """
        logger.error(f"Error stopping actor {self.actor.actor_id}: {error}")
        logger.error(traceback.format_exc())

        # Ensure the actor is marked as not running
        self.actor.is_running = False
        self.actor._ready = False
        self.actor._ready_event.clear()

        # Try to cancel tasks anyway
        try:
            await self.actor._cancel_all_tasks()
        except Exception as cancel_error:
            logger.error(f"Error cancelling tasks during error recovery: {cancel_error}")

        # Update initializer state to FAILED if possible
        if initializer:
            try:
                await initializer.set_actor_state(
                    actor_id=self.actor.actor_id,
                    state=ActorState.FAILED,
                    error=error,
                    phase="stop"
                )
            except Exception as state_error:
                logger.error(f"Error updating actor state during error recovery: {state_error}")

        # Don't re-raise the exception - stopping should be best-effort
