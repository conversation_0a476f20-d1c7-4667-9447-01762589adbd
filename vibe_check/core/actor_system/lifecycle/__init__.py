"""
Lifecycle package for the actor system.

This package contains components related to actor lifecycle management,
including initialization, starting, and stopping actors.

Classes:
    ActorStarter: Handles the starting of an actor
    ActorTerminator: Handles the stopping of an actor
    TaskManager: Manages actor tasks
    MessageProcessor: Handles message processing for an actor
"""

from .message_processor import MessageProcessor
from .starter import ActorStarter
from .task_manager import TaskManager
from .terminator import ActorTerminator

__all__ = ["ActorStarter", "ActorTerminator", "TaskManager", "MessageProcessor"]
