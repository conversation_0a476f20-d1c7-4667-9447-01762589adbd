"""
Task Manager Module
================

This module defines the TaskManager class, which is responsible for
managing tasks associated with an actor.

The TaskManager handles task creation, cancellation, and monitoring,
providing a standardized way to manage asynchronous tasks.
"""

import asyncio
import logging
from typing import Any, Callable, Optional, TYPE_CHECKING

# Avoid circular imports
if TYPE_CHECKING:
    from ..actor import Actor

logger = logging.getLogger("vibe_check_actor_system.task_manager")


class TaskManager:
    """
    Manages actor tasks.
    
    This class encapsulates the task management logic that was previously
    part of the Actor class. It provides methods for creating, cancelling,
    and monitoring tasks.
    
    Attributes:
        actor: The actor that owns this task manager
    """
    
    def __init__(self, actor: 'Actor'):
        """
        Initialize the task manager.
        
        Args:
            actor: The actor that owns this task manager
        """
        self.actor = actor
    
    async def create_task(self, coro: Any, task_name: str, 
                         callback: Optional[Callable[[asyncio.Task], None]] = None) -> asyncio.Task:
        """
        Create a task with proper callback.
        
        Args:
            coro: The coroutine to run in the task
            task_name: The name of the task
            callback: Optional callback function for the task
            
        Returns:
            The created task
        """
        task = asyncio.create_task(coro)
        
        if callback:
            task.add_done_callback(callback)
        else:
            # Use default callback if none provided
            def default_callback(task: asyncio.Task) -> None:
                if task.cancelled():
                    logger.info(f"Actor {self.actor.actor_id} {task_name} was cancelled")
                else:
                    logger.info(f"Actor {self.actor.actor_id} {task_name} completed")
            
            task.add_done_callback(default_callback)
        
        return task
    
    async def cancel_all_tasks(self) -> None:
        """
        Cancel all tasks associated with the actor.
        
        This method cancels the heartbeat task, metrics task, and process task.
        """
        await self._cancel_heartbeat_task()
        await self._cancel_metrics_task()
        await self._cancel_process_task()
    
    async def _cancel_heartbeat_task(self) -> None:
        """
        Cancel the heartbeat task.
        """
        await self._cancel_task(
            self.actor._heartbeat_task,
            "heartbeat task",
            self.actor.actor_id
        )
    
    async def _cancel_metrics_task(self) -> None:
        """
        Cancel the metrics task.
        """
        await self._cancel_task(
            self.actor._metrics_task,
            "metrics task",
            self.actor.actor_id
        )
    
    async def _cancel_process_task(self) -> None:
        """
        Cancel the process task.
        """
        await self._cancel_task(
            self.actor._process_task,
            "process task",
            self.actor.actor_id
        )
    
    async def _cancel_task(self, task: Optional[asyncio.Task], 
                          task_name: str, actor_id: str) -> None:
        """
        Cancel a task with proper error handling.
        
        Args:
            task: The task to cancel
            task_name: The name of the task
            actor_id: The ID of the actor that owns the task
        """
        if task is not None and not task.done():
            logger.info(f"Cancelling {task_name} for actor {actor_id}")
            task.cancel()
            try:
                # Use shield to prevent cancellation of the wait_for itself
                await asyncio.wait_for(asyncio.shield(task), timeout=0.5)
            except (asyncio.TimeoutError, asyncio.CancelledError):
                # These exceptions are expected during cancellation
                pass
            except Exception as e:
                logger.error(f"Error cancelling {task_name} for actor {actor_id}: {e}")
    
    def create_task_callback(self, task_name: str) -> Callable[[asyncio.Task], None]:
        """
        Create a callback function for a task.
        
        Args:
            task_name: The name of the task
            
        Returns:
            A callback function for the task
        """
        def callback(task: asyncio.Task) -> None:
            if task.cancelled():
                logger.info(f"Actor {self.actor.actor_id} {task_name} task was cancelled")
            else:
                logger.info(f"Actor {self.actor.actor_id} {task_name} task completed")
                
                # Check for exceptions
                if task.done() and not task.cancelled():
                    exception = task.exception()
                    if exception:
                        logger.error(f"Actor {self.actor.actor_id} {task_name} task failed with exception: {exception}")
        
        return callback
