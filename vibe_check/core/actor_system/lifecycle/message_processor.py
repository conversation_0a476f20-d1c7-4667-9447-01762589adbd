"""
Message Processor Module
====================

This module defines the MessageProcessor class, which is responsible for
processing messages in the actor system.

The MessageProcessor handles message processing, including pending messages
and mailbox management.
"""

import asyncio
import logging
from typing import List, Optional, TYPE_CHECKING

# Avoid circular imports
if TYPE_CHECKING:
    from ..actor import Actor
    from ..message import Message

logger = logging.getLogger("vibe_check_actor_system.message_processor")


class MessageProcessor:
    """
    Handles message processing for an actor.
    
    This class encapsulates the message processing logic that was previously
    part of the Actor class. It provides methods for processing messages,
    handling pending messages, and managing the mailbox.
    
    Attributes:
        actor: The actor that owns this message processor
    """
    
    def __init__(self, actor: 'Actor'):
        """
        Initialize the message processor.
        
        Args:
            actor: The actor that owns this message processor
        """
        self.actor = actor
    
    async def process_remaining_messages(self) -> None:
        """
        Process remaining messages in the mailbox before stopping.
        
        This method is called during actor shutdown to ensure that all
        messages in the mailbox are processed before the actor stops.
        It logs information about unprocessed messages and attempts to
        process them with a timeout.
        """
        if not self.actor.mailbox.empty():
            try:
                # Process any remaining messages in the mailbox
                logger.info(f"Actor {self.actor.actor_id} has {self.actor.mailbox.qsize()} messages in mailbox")
                
                # Process remaining messages with a timeout
                remaining_messages: List['Message'] = []
                while not self.actor.mailbox.empty():
                    try:
                        message = self.actor.mailbox.get_nowait()
                        remaining_messages.append(message)
                        self.actor.mailbox.task_done()
                    except asyncio.QueueEmpty:
                        break
                
                # Log information about unprocessed messages
                if remaining_messages:
                    logger.warning(f"Actor {self.actor.actor_id} has {len(remaining_messages)} unprocessed messages")
                    for msg in remaining_messages[:5]:  # Log first 5 messages
                        logger.warning(f"Unprocessed message: {msg.type.name if hasattr(msg, 'type') else 'unknown'}")
                    
                    if len(remaining_messages) > 5:
                        logger.warning(f"... and {len(remaining_messages) - 5} more unprocessed messages")
                
                # Wait for the mailbox to be empty
                await asyncio.wait_for(self.actor.mailbox.join(), timeout=0.5)
            except asyncio.TimeoutError:
                logger.warning(f"Timeout waiting for actor {self.actor.actor_id} mailbox to empty")
            except Exception as e:
                logger.error(f"Error during actor {self.actor.actor_id} shutdown: {e}")
                import traceback
                logger.error(traceback.format_exc())
    
    async def process_pending_messages(self) -> None:
        """
        Process any pending messages.
        
        This method is called after the actor is ready to process messages.
        It moves any pending messages to the mailbox for processing.
        """
        if self.actor._pending_messages:
            logger.info(f"Actor {self.actor.actor_id} processing {len(self.actor._pending_messages)} pending messages")
            for message in self.actor._pending_messages:
                await self.actor.mailbox.put(message)
            self.actor._pending_messages.clear()
