"""
Actor System Debug Utilities
========================

This module provides utilities for debugging the actor system, including:
- Initialization debugging
- Dependency resolution debugging
- Message flow debugging
- State transition debugging
- Timeout debugging and analysis

These utilities help diagnose and resolve issues with the actor system.

DEPRECATED: This module is deprecated and will be removed in a future version.
Please use the `vibe_check.core.actor_system.debug` package instead.
"""

import warnings
from typing import Any, Dict, List, Optional, Set, Tuple, Union, cast

from .debug.debugger import ActorSystemDebugger, get_debugger, reset_debugger
from .debug.initialization_analyzer import InitializationAnalyzer
from .debug.registry_analyzer import RegistryAnalyzer
from .debug.visualizer import DebugVisualizer
from .debug.report_generator import DebugReportGenerator

# Issue deprecation warning
warnings.warn(
    "The debug_utils module is deprecated and will be removed in a future version. "
    "Please use the vibe_check.core.actor_system.debug package instead.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export the classes and functions from the debug package
__all__ = [
    'ActorSystemDebugger',
    'get_debugger',
    'reset_debugger',
    'InitializationAnalyzer',
    'RegistryAnalyzer',
    'DebugVisualizer',
    'DebugReportGenerator',
]
