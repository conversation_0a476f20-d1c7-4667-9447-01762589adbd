"""
Actor Module
===========

This module defines the Actor base class for the actor system.
Actors are autonomous components that communicate via messages
and implement the CAW principle of choreographed interactions.

The actor system replaces the previous pipeline architecture with
a more flexible, resilient approach that allows for parallel execution
and contextual adaptation.

Enhanced with features for dynamic discovery, supervision, state persistence,
metrics collection, and distributed execution.

The Actor class has been refactored to use a modular architecture, with components
extracted into separate modules for better maintainability and testability:
- MessageProcessor: Handles message processing logic (messaging/processor.py)
- ActorStarter: Handles actor starting logic (lifecycle/starter.py)
- ActorTerminator: Handles actor stopping logic (lifecycle/terminator.py)
- TaskManager: Handles task management logic (lifecycle/task_manager.py)

This modular architecture reduces the complexity of the Actor class and makes
it easier to test and maintain. Each component is responsible for a specific
aspect of the actor's lifecycle, and the Actor class delegates to these components
as needed.
"""

import asyncio
import json
import logging
import os
import time
from typing import Any, Awaitable, Callable, Dict, List, Optional, Set, Union

from .actor_base import ActorBase
from .context_wave import ContextWave
from .lifecycle.starter import ActorStarter
from .lifecycle.task_manager import TaskManager
from .lifecycle.terminator import ActorTerminator
from .message import Message, MessageType
from .messaging.processor import MessageProcessor
from .messaging.router import MessageRouter
from .state.state_manager import ActorStateManager
from .streaming.stream_manager import ActorStreamManager

# Import actor state
from .actor_state import ActorState

# Import consolidated initializer (preferred)
from .consolidated_initializer import (
    ConsolidatedActorInitializer,
    get_initializer,
    reset_initializer
)



# Import exceptions
from .exceptions import (
    ActorSystemError,
    ActorInitializationError,
    ActorDependencyError,
    ActorMessageError,
    ActorTimeoutError,
    ActorNotFoundError
)

# Import diagnostics
from .diagnostics import get_tracker, InitializationStep

# Configure logging
logger = logging.getLogger("vibe_check_actor_system")


class Actor(ActorBase):
    """
    Base actor class for the CAW choreography system.

    Implements the CAW principle of adaptive actors that communicate
    via messages with propagating context.

    Enhanced with features for:
    - Dynamic discovery via the actor registry
    - Supervision and monitoring
    - State persistence
    - Metrics collection
    - Distributed execution
    - Reactive streams

    This class has been refactored to use a modular architecture, with components
    extracted into separate modules for better maintainability and testability:
    - MessageProcessor: Handles message processing logic

    Implementation:
        The Actor class now delegates message processing to the MessageProcessor
        component, which encapsulates the message processing loop, error handling,
        and heartbeat sending logic. This reduces the complexity of the Actor class
        and makes it easier to test and maintain.
    """

    def __init__(self, actor_id: str, actor_type: Optional[str] = None,
                tags: Optional[Set[str]] = None, capabilities: Optional[Set[str]] = None,
                supervisor_id: Optional[str] = None, state_dir: Optional[str] = None):
        """
        Initialize the actor.

        Args:
            actor_id: Unique ID for this actor
            actor_type: Optional type of the actor for discovery
            tags: Optional set of tags for discovery
            capabilities: Optional set of capabilities for discovery
            supervisor_id: Optional ID of the supervisor actor
            state_dir: Optional directory for state persistence
        """
        self._actor_id = actor_id
        self._actor_type = actor_type or self.__class__.__name__
        self._tags = tags or set()
        self._capabilities = capabilities or set()
        self._supervisor_id = supervisor_id
        self.state_dir = state_dir

        # Core actor state
        self.mailbox: asyncio.Queue[Message] = asyncio.Queue()
        self.context_wave: ContextWave = ContextWave()
        self._is_running: bool = False
        self._known_actors: Dict[str, 'Actor'] = {}
        self._message_handlers: Dict[MessageType, Callable[[Dict[str, Any], ContextWave], Awaitable[Any]]] = {}
        self._process_task: Optional[asyncio.Task[None]] = None  # Task for the message processing loop
        self._initialization_complete: bool = False
        self._ready: bool = False
        self._pending_messages: List[Union[Message, Dict[str, Any]]] = []  # Messages received before actor is ready
        self._ready_event: asyncio.Event = asyncio.Event()  # Event to signal when actor is ready

        # Supervision state
        self._supervised_actors: Set[str] = set()
        self._last_heartbeat: float = time.time()
        self._heartbeat_interval: float = 10.0  # seconds
        self._heartbeat_task: Optional[asyncio.Task[None]] = None
        self._restart_count: int = 0
        self._max_restarts: int = 3
        self._restart_window: float = 60.0  # seconds
        self._last_restart_time: float = 0.0

        # Metrics state
        self._metrics: Dict[str, Any] = {
            "messages_received": 0,
            "messages_sent": 0,
            "messages_processed": 0,
            "errors": 0,
            "processing_time": 0.0,
            "avg_processing_time": 0.0,
            "last_activity": time.time(),
            "uptime": 0.0,
            "start_time": time.time(),
            "restarts": 0
        }
        self._metrics_task: Optional[asyncio.Task[None]] = None
        self._metrics_interval: float = 30.0  # seconds

        # Stream state
        self._subscriptions: Set[str] = set()

        # Component managers
        self._message_processor = MessageProcessor(self)
        self._message_router = MessageRouter(self)
        self._starter = ActorStarter(self)
        self._terminator = ActorTerminator(self)
        self._task_manager = TaskManager(self)
        self._state_manager = ActorStateManager(self)
        self._stream_manager = ActorStreamManager(self)

        # Initialize handlers
        self._initialize_handlers()

        # Register with registry if available
        try:
            from .actor_registry import get_registry
            registry = get_registry()
            registry.register_actor(
                actor_id=self.actor_id,
                actor=self,
                actor_type=self.actor_type,
                tags=self.tags,
                capabilities=self.capabilities
            )
            logger.info(f"Actor {self.actor_id} registered with registry")
        except (ImportError, AttributeError) as e:
            logger.warning(f"Could not register actor {self.actor_id} with registry: {e}")

        # Registration with initializers will be handled in async methods
        # to ensure proper synchronization when the actor is initialized

        # Store coroutines for later execution
        self._initialization_coroutine = self._register_with_initializer

        # These will be set when the coroutines are executed
        self._initialization_task = None

    async def _register_with_initializer(self) -> None:
        """
        Register this actor with the ConsolidatedActorInitializer.

        This method is called during initialization to ensure proper
        synchronization with the initializer.
        """
        try:
            # Get the ConsolidatedActorInitializer
            initializer = get_initializer()
            if not initializer:
                logger.warning(f"ConsolidatedActorInitializer not available for actor {self.actor_id}")
                return

            # Check if already registered to avoid duplicate registration
            try:
                with initializer._state_lock:
                    if self.actor_id in initializer._actor_states:
                        logger.info(f"Actor {self.actor_id} is already registered with initializer")
                        return
            except Exception as check_error:
                logger.warning(f"Error checking if actor {self.actor_id} is registered with initializer: {check_error}")
                # Continue with registration attempt

            # Register with initializer
            try:
                await initializer.register_actor(
                    actor_id=self.actor_id,
                    actor_type=self.actor_type,
                    tags=self.tags
                )
                logger.info(f"Actor {self.actor_id} registered with initializer")
            except Exception as reg_error:
                logger.error(f"Error registering actor {self.actor_id} with initializer: {reg_error}")
                import traceback
                logger.error(traceback.format_exc())
                # Re-raise to ensure the caller knows registration failed
                raise

            # Register cleanup resources
            try:
                initializer.register_resource(
                    actor_id=self.actor_id,
                    cleanup_func=self._cleanup_resources,
                    args=[],
                    kwargs={}
                )
            except Exception as resource_error:
                logger.warning(f"Error registering cleanup resources for actor {self.actor_id} with initializer: {resource_error}")
                # Continue with other operations

            # Record registration with the tracker
            try:
                tracker = get_tracker()
                if tracker:
                    await tracker.record_event(
                        actor_id=self.actor_id,
                        step=InitializationStep.REGISTRATION,
                        details={"state": ActorState.CREATED.value}
                    )
            except Exception as tracker_error:
                logger.warning(f"Error recording registration event for actor {self.actor_id}: {tracker_error}")
                # Continue with other operations

        except Exception as e:
            logger.error(f"Error registering actor {self.actor_id} with initializer: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # Re-raise the exception to ensure the caller knows registration failed
            raise

    async def _ensure_registered_with_initializer(self) -> None:
        """
        Ensure the actor is registered with the ConsolidatedActorInitializer.

        This method ensures that the actor is properly registered with the
        ConsolidatedActorInitializer before proceeding with initialization.
        It waits for the registration tasks to complete if they're still running,
        or registers the actor directly if needed.

        Raises:
            Exception: If registration with the initializer fails after multiple attempts
        """
        max_retries = 3
        retry_count = 0

        # Ensure registration with the ConsolidatedActorInitializer
        while retry_count < max_retries:
            try:
                # Check if we have a pending registration task
                if self._initialization_task is not None and not self._initialization_task.done():
                    try:
                        # Wait for the task to complete
                        await self._initialization_task
                        logger.info(f"Actor {self.actor_id} registration task completed")
                        # If we get here, registration was successful
                        break
                    except Exception as e:
                        logger.error(f"Error in registration task for actor {self.actor_id}: {e}")
                        import traceback
                        logger.error(traceback.format_exc())
                        # We'll try to register again below
                elif self._initialization_task is None:
                    # Create and execute the task now
                    self._initialization_task = asyncio.create_task(self._initialization_coroutine())
                    try:
                        await self._initialization_task
                        logger.info(f"Actor {self.actor_id} registration task completed")
                        # If we get here, registration was successful
                        break
                    except Exception as e:
                        logger.error(f"Error in registration task for actor {self.actor_id}: {e}")
                        import traceback
                        logger.error(traceback.format_exc())
                        # We'll try to register again directly

                # Get the ConsolidatedActorInitializer
                initializer = get_initializer()
                if not initializer:
                    logger.warning(f"ConsolidatedActorInitializer not available for actor {self.actor_id}")
                    # Sleep before retry
                    await asyncio.sleep(0.1 * (retry_count + 1))
                    retry_count += 1
                    continue

                # Check if we're already registered
                try:
                    with initializer._state_lock:
                        if self.actor_id in initializer._actor_states:
                            logger.info(f"Actor {self.actor_id} is already registered with initializer")
                            break
                except Exception as lock_error:
                    logger.warning(f"Error checking if actor {self.actor_id} is registered with initializer: {lock_error}")
                    # Continue with registration attempt

                # Register with the initializer
                try:
                    await initializer.register_actor(
                        actor_id=self.actor_id,
                        actor_type=self.actor_type,
                        tags=self.tags
                    )
                    logger.info(f"Actor {self.actor_id} registered with initializer")

                    # Register cleanup resources
                    initializer.register_resource(
                        actor_id=self.actor_id,
                        cleanup_func=self._cleanup_resources,
                        args=[],
                        kwargs={}
                    )

                    # If we get here, registration was successful
                    break
                except Exception as reg_error:
                    logger.error(f"Error registering actor {self.actor_id} with initializer: {reg_error}")
                    # Increment retry count and continue with the loop
                    retry_count += 1
                    await asyncio.sleep(0.1 * retry_count)
                    continue

            except Exception as e:
                logger.error(f"Error ensuring actor {self.actor_id} is registered with initializer (attempt {retry_count+1}/{max_retries}): {e}")
                import traceback
                logger.error(traceback.format_exc())

                # Sleep before retry
                await asyncio.sleep(0.1 * (retry_count + 1))
                retry_count += 1

        # If we get here and retry_count >= max_retries, all retries failed
        if retry_count >= max_retries:
            error_msg = f"Failed to register actor {self.actor_id} with initializer after {max_retries} attempts"
            logger.error(error_msg)
            raise RuntimeError(error_msg)

    def _initialize_handlers(self) -> None:
        """
        Initialize message handlers for this actor.

        This maps message types to handler methods, allowing for dynamic dispatch
        based on the received message type.
        """
        # Find all handler methods in the class
        for attr_name in dir(self):
            if attr_name.startswith("handle_") and callable(getattr(self, attr_name)):
                # Extract the message type from the handler name
                message_type_name = attr_name[len("handle_"):].upper()
                try:
                    # Try to get the MessageType enum value
                    message_type = MessageType[message_type_name]
                    # Register the handler
                    self._message_handlers[message_type] = getattr(self, attr_name)
                except (KeyError, AttributeError):
                    # If the message type doesn't exist, skip it
                    pass

    def register_actor(self, actor_id: str, actor: 'Actor') -> None:
        """
        Register another actor that this actor can send messages to.

        Args:
            actor_id: ID of the actor to register
            actor: The actor instance
        """
        self._known_actors[actor_id] = actor

    async def send(self, recipient_id: str, msg_type: MessageType,
                  payload: Dict[str, Any], context: Optional[ContextWave] = None,
                  priority: int = 0, ttl: Optional[int] = None) -> None:
        """
        Send a message to another actor.

        Enhanced to support dynamic discovery via the registry if the recipient
        is not directly known to this actor.

        This method now delegates to the MessageRouter component, which encapsulates
        the message routing logic, error handling, and supervisor notification.

        Args:
            recipient_id: ID of the recipient actor
            msg_type: Type of the message
            payload: Message payload
            context: Optional context to propagate
            priority: Optional priority for the message (0-9, higher is more important)
            ttl: Optional time-to-live in seconds
        """
        try:
            await self._message_router.send(
                recipient_id=recipient_id,
                msg_type=msg_type,
                payload=payload,
                context=context,
                priority=priority,
                ttl=ttl
            )
        except (ValueError, RuntimeError) as e:
            # Log the error but don't re-raise it to maintain backward compatibility
            logger.error(f"Error in send method: {e}")
            # The MessageRouter component will have already updated metrics and notified the supervisor

    async def receive(self, message: Message) -> None:
        """
        Receive a message from another actor.

        Enhanced to handle message priorities, TTL, and metrics.
        Modified to queue messages until the actor is ready.
        Improved with better logging and error handling.

        Args:
            message: The message to receive
        """
        try:
            # Check if message has expired
            if message.is_expired():
                logger.warning(f"Actor {self.actor_id} received expired message of type {message.type.name}")
                return

            # Update metrics
            self._metrics["messages_received"] += 1
            self._metrics["last_activity"] = time.time()

            # Log receipt with more context
            sender_id = message.sender_id or 'unknown'
            message_type = message.type.name if hasattr(message, 'type') else 'unknown'
            logger.debug(f"Actor {self.actor_id} received {message_type} from {sender_id}")

            # Record sender if not already known
            if message.sender_id and message.sender_id not in self._known_actors:
                try:
                    from .actor_registry import get_registry
                    registry = get_registry()
                    sender = registry.get_actor(message.sender_id)
                    if sender:
                        self._known_actors[message.sender_id] = sender
                        logger.debug(f"Actor {self.actor_id} discovered sender {message.sender_id} via registry")
                except (ImportError, AttributeError) as e:
                    logger.debug(f"Could not discover sender {message.sender_id}: {e}")

            # Special handling for initialization and start messages
            if message.type in [MessageType.INITIALIZE, MessageType.START]:
                # These messages are always processed immediately
                logger.debug(f"Actor {self.actor_id} processing {message.type.name} message immediately")
                await self.mailbox.put(message)

                # If this is a START message, make sure we have a message processing task
                if message.type == MessageType.START and (self._process_task is None or self._process_task.done()):
                    logger.debug(f"Actor {self.actor_id} starting message processing task for START message")
                    self._process_task = asyncio.create_task(self.process_messages())

                return

            # Check if the actor is ready to process messages
            if not self._ready:
                # Queue the message for later processing
                logger.debug(f"Actor {self.actor_id} queuing message of type {message.type.name} until ready")

                # Add timestamp to track how long messages are queued
                # Use a safer approach that doesn't modify the message object
                queued_message = {
                    "message": message,
                    "queued_at": time.time()
                }

                # Add to pending messages
                self._pending_messages.append(queued_message)

                # Log if we have many pending messages
                if len(self._pending_messages) % 10 == 0:
                    logger.info(f"Actor {self.actor_id} has {len(self._pending_messages)} pending messages")

                # Check if we need to start the actor
                if not self._initialization_complete:
                    logger.warning(f"Actor {self.actor_id} received message of type {message.type.name} but is not initialized")

                    # Try to initialize and start the actor
                    try:
                        from .consolidated_initializer import get_initializer
                        initializer = get_initializer()

                        # Check current state
                        with initializer._state_lock:
                            current_state = initializer._actor_states.get(self.actor_id)
                        logger.debug(f"Actor {self.actor_id} current state: {current_state}")

                        if current_state == ActorState.CREATED:
                            # Initialize and start the actor
                            logger.info(f"Actor {self.actor_id} auto-initializing due to received message")
                            await initializer.initialize_actor(self)
                            await initializer.start_actor(self)
                        elif current_state == ActorState.INITIALIZED:
                            # Just start the actor
                            logger.info(f"Actor {self.actor_id} auto-starting due to received message")
                            await initializer.start_actor(self)
                    except Exception as e:
                        logger.error(f"Error auto-initializing actor {self.actor_id}: {e}")
                        import traceback
                        logger.error(traceback.format_exc())

                return

            # Actor is ready, add to mailbox
            await self.mailbox.put(message)

            # Ensure we have a message processing task
            if self._process_task is None or self._process_task.done():
                logger.debug(f"Actor {self.actor_id} starting message processing task")
                self._process_task = asyncio.create_task(self.process_messages())

        except Exception as e:
            # Log any errors during message receipt
            logger.error(f"Error receiving message in actor {self.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Update error metrics
            self._metrics["errors"] += 1

            # Try to notify sender of error if possible
            try:
                if message.sender_id:
                    error_payload = {
                        "error": f"Error receiving message: {str(e)}",
                        "actor_id": self.actor_id,
                        "original_message_type": message.type.name if hasattr(message, 'type') else 'unknown'
                    }
                    await self.send(message.sender_id, MessageType.ERROR, error_payload)
            except Exception as notify_error:
                logger.error(f"Error notifying sender of receive error: {notify_error}")

    async def process_messages(self) -> None:
        """
        Process messages from the mailbox.

        Enhanced to handle message priorities, metrics, and heartbeats.
        Implements the CAW principle of adaptive processing based on context.

        This method now delegates to the MessageProcessor component, which encapsulates
        the message processing loop, error handling, and heartbeat sending logic.
        This reduces the complexity of this method and makes it easier to test and maintain.

        Returns:
            None

        Implementation:
            This method delegates to the MessageProcessor.process_messages() method,
            which handles the actual message processing logic. This is part of the
            refactoring effort to reduce the complexity of the Actor class.
        """
        await self._message_processor.process_messages()

    async def _handle_message(self, message: Message) -> None:
        """
        Handle an incoming message.

        Enhanced with better error handling for unknown message types and
        improved context handling.

        Args:
            message: The message to handle
        """
        # Use the message handler component
        from .message_handling.handler import MessageHandler

        # Create the handler if it doesn't exist
        if not hasattr(self, '_message_handler'):
            self._message_handler = MessageHandler(self)

        # Delegate to the handler
        await self._message_handler.handle_message(message)

    async def handle_unknown(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle an unknown message type.

        Args:
            payload: Message payload
            context: Message context
        """
        logger.warning(f"Actor {self.actor_id} received unknown message type")
        # You might want to send an error response back to the sender
        sender_id = context.metadata.get("sender_id")
        if sender_id and sender_id in self._known_actors:
            error_payload = {
                "error": "Unknown message type",
                "actor_id": self.actor_id
            }
            await self.send(sender_id, MessageType.ERROR, error_payload, context)

    async def _cleanup_resources(self) -> None:
        """
        Clean up resources associated with this actor.

        This method is called when the actor fails to initialize or is being
        rolled back. It should release any resources that were acquired during
        initialization.
        """
        logger.info(f"Cleaning up resources for actor {self.actor_id}")

        # Cancel any running tasks
        for task_name in ['_process_task', '_heartbeat_task', '_metrics_task']:
            task = getattr(self, task_name, None)
            if task and not task.done():
                try:
                    task.cancel()
                    logger.info(f"Cancelled {task_name} for actor {self.actor_id}")
                except Exception as e:
                    logger.error(f"Error cancelling {task_name} for actor {self.actor_id}: {e}")

        # Clear the mailbox
        try:
            while not self.mailbox.empty():
                try:
                    self.mailbox.get_nowait()
                    self.mailbox.task_done()
                except asyncio.QueueEmpty:
                    break
            logger.info(f"Cleared mailbox for actor {self.actor_id}")
        except Exception as e:
            logger.error(f"Error clearing mailbox for actor {self.actor_id}: {e}")

        # Unregister from registry
        try:
            from .actor_registry import get_registry
            registry = get_registry()
            registry.unregister_actor(self.actor_id)
            logger.info(f"Unregistered actor {self.actor_id} from registry during cleanup")
        except (ImportError, AttributeError, Exception) as e:
            logger.warning(f"Could not unregister actor {self.actor_id} from registry during cleanup: {e}")

        # Reset state flags
        self._is_running = False
        self._ready = False
        self._initialization_complete = False
        self._ready_event.clear()

        logger.info(f"Cleaned up resources for actor {self.actor_id}")

    async def initialize(self, config: Optional[Dict[str, Any]] = None,
                       timeout: float = 60.0,
                       dependencies: Optional[List[str]] = None,
                       optional_dependencies: Optional[List[str]] = None) -> None:
        """
        Initialize the actor (first phase of two-phase initialization).

        This method prepares the actor for starting but doesn't begin processing messages.
        It's part of the two-phase initialization process to ensure all actors are
        properly initialized before they start communicating.

        Args:
            config: Optional configuration dictionary for the actor
            timeout: Maximum time in seconds to wait for initialization to complete
            dependencies: Optional list of actor IDs that this actor depends on
            optional_dependencies: Optional list of actor IDs that this actor can
                                 function without

        Raises:
            ActorInitializationError: If initialization fails
            ActorTimeoutError: If initialization times out
            ActorDependencyError: If a dependency fails to initialize

        Enhanced with robust error handling, dependency management, timeout handling,
        and cleanup on failure. Now uses the new ActorInitializationManager.
        """
        # Import the implementation from the separate module
        from .actor_initialize import initialize_actor

        # Call the implementation
        await initialize_actor(
            self,
            config=config,
            timeout=timeout,
            dependencies=dependencies,
            optional_dependencies=optional_dependencies
        )
        # First, ensure we're registered with the initializer
        try:
            await self._ensure_registered_with_initializer()
        except Exception as e:
            logger.error(f"Error ensuring actor {self.actor_id} is registered with initializer: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise ActorInitializationError(
                message=f"Failed to register with initializer: {str(e)}",
                actor_id=self.actor_id,
                phase="registration",
                state=ActorState.CREATED,
                original_error=e
            )

        # Get initializer
        initializer = get_initializer()
        if not initializer:
            logger.warning(f"Actor {self.actor_id} initializing without initializer")

        # Create a timeout for the entire initialization process
        try:
            # Use wait_for instead of asyncio.timeout for compatibility with older Python versions
            async def _initialize_with_timeout() -> None:
                try:
                    # Update initializer state
                    if initializer:
                        await initializer.set_actor_state(
                            actor_id=self.actor_id,
                            state=ActorState.INITIALIZING,
                            error=None,
                            phase="initialize"
                        )

                    # Register and wait for dependencies to be initialized if specified
                    if dependencies and initializer:
                        logger.info(f"Actor {self.actor_id} registering and waiting for dependencies: {dependencies}")
                        dependency_errors = {}

                        # Register all dependencies first
                        for dep_id in dependencies:
                            try:
                                # Check if dependency is registered
                                if not await initializer.is_actor_registered(dep_id):
                                    error_msg = f"Dependency {dep_id} is not registered"
                                    logger.error(error_msg)
                                    dependency_errors[dep_id] = error_msg
                                    continue

                                # Register the dependency with the initializer
                                logger.info(f"Registering dependency: {self.actor_id} depends on {dep_id}")
                                await initializer.register_dependency(self.actor_id, dep_id)
                            except Exception as e:
                                error_msg = f"Error registering dependency {dep_id}: {str(e)}"
                                logger.error(error_msg)
                                dependency_errors[dep_id] = error_msg

                        # Wait for all dependencies to be initialized
                        for dep_id in dependencies:
                            try:
                                # Skip dependencies that failed registration
                                if dep_id in dependency_errors:
                                    continue

                                # Check dependency state
                                with initializer._state_lock:
                                    state = initializer._actor_states.get(dep_id)

                                # Check if the state is a failed state
                                if state == ActorState.FAILED:
                                    error_msg = f"Dependency {dep_id} failed to initialize"
                                    logger.error(error_msg)
                                    dependency_errors[dep_id] = error_msg
                                    continue

                                # Wait for the dependency to be at least initialized
                                await initializer.wait_for_actor_initialized(
                                    actor_id=dep_id,
                                    timeout=timeout
                                )
                                logger.info(f"Dependency {dep_id} is initialized")
                            except Exception as e:
                                error_msg = f"Error waiting for dependency {dep_id}: {str(e)}"
                                logger.error(error_msg)
                                dependency_errors[dep_id] = error_msg

                        # If any dependencies failed, raise an error
                        if dependency_errors:
                            raise ActorDependencyError(
                                message=f"Failed to initialize due to dependency errors",
                                actor_id=self.actor_id,
                                dependencies=dependency_errors
                            )

                    # Reset metrics for a clean start
                    self._metrics["start_time"] = time.time()
                    self._metrics["uptime"] = 0.0
                    self._metrics["messages_received"] = 0
                    self._metrics["messages_sent"] = 0
                    self._metrics["messages_processed"] = 0
                    self._metrics["errors"] = 0
                    self._metrics["processing_time"] = 0.0
                    self._metrics["avg_processing_time"] = 0.0
                    self._metrics["last_activity"] = time.time()

                    # Load state if available
                    try:
                        await self._load_state()
                    except Exception as state_error:
                        logger.error(f"Error loading state for actor {self.actor_id}: {state_error}")
                        import traceback
                        logger.error(traceback.format_exc())
                        # Continue with initialization despite state loading error

                    # Register with registry again in case we were unregistered
                    try:
                        from .actor_registry import get_registry
                        registry = get_registry()
                        registry.register_actor(
                            actor_id=self.actor_id,
                            actor=self,
                            actor_type=self.actor_type,
                            tags=self.tags,
                            capabilities=self.capabilities
                        )
                    except (ImportError, AttributeError) as e:
                        logger.warning(f"Could not register actor {self.actor_id} with registry: {e}")

                    # Call the actor-specific initialization
                    # This is a hook for subclasses to implement their own initialization logic
                    # The method doesn't exist in the base class, so we'll check if it exists
                    if hasattr(self, '_initialize') and callable(getattr(self, '_initialize')):
                        await self._initialize(config or {})
                    else:
                        # If the method doesn't exist, log a debug message
                        logger.debug(f"Actor {self.actor_id} has no _initialize method")

                    # Mark initialization as complete
                    self._initialization_complete = True

                    # Update initializer state
                    if initializer:
                        await initializer.set_actor_state(
                            actor_id=self.actor_id,
                            state=ActorState.INITIALIZED,
                            error=None,
                            phase="initialize"
                        )

                    logger.info(f"Actor {self.actor_id} initialized successfully")
                except ActorSystemError as e:
                    # These are our custom exceptions, so just re-raise them
                    logger.error(f"Actor system error during initialization of {self.actor_id}: {e}")

                    # Mark initialization as failed
                    self._initialization_complete = False

                    # Update initializer state to FAILED
                    if initializer:
                        try:
                            await initializer.set_actor_state(
                                actor_id=self.actor_id,
                                state=ActorState.FAILED,
                                error=e,
                                phase="initialize"
                            )

                            # Clean up resources
                            await initializer.cleanup_actor(self.actor_id)
                        except Exception as rollback_error:
                            logger.error(f"Error rolling back actor {self.actor_id}: {rollback_error}")

                    raise
                except Exception as e:
                    logger.error(f"Error initializing actor {self.actor_id}: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

                    # Mark initialization as failed
                    self._initialization_complete = False

                    # Update initializer state to FAILED
                    if initializer:
                        try:
                            await initializer.set_actor_state(
                                actor_id=self.actor_id,
                                state=ActorState.FAILED,
                                error=e,
                                phase="initialize"
                            )

                            # Clean up resources
                            await initializer.cleanup_actor(self.actor_id)
                        except Exception as rollback_error:
                            logger.error(f"Error rolling back actor {self.actor_id}: {rollback_error}")

                    # Wrap the exception in our custom exception
                    raise ActorInitializationError(
                        message=str(e),
                        actor_id=self.actor_id,
                        phase="initialize",
                        state=ActorState.INITIALIZING,
                        original_error=e
                    ) from e

            # Execute the initialization with a timeout
            try:
                await asyncio.wait_for(_initialize_with_timeout(), timeout=timeout)
            except asyncio.TimeoutError as e:
                logger.error(f"Timeout initializing actor {self.actor_id} after {timeout} seconds")

                # Mark initialization as failed
                self._initialization_complete = False

                # Create a timeout exception
                timeout_error = ActorTimeoutError(
                    message=f"Initialization timed out after {timeout} seconds",
                    actor_id=self.actor_id,
                    operation="initialize",
                    timeout=timeout
                )

                # Update initializer state
                if initializer:
                    try:
                        await initializer.set_actor_state(
                            actor_id=self.actor_id,
                            state=ActorState.FAILED,
                            error=timeout_error,
                            phase="initialize"
                        )

                        # Clean up resources
                        await initializer.cleanup_actor(self.actor_id)
                    except Exception as rollback_error:
                        logger.error(f"Error rolling back actor {self.actor_id}: {rollback_error}")

                # Clean up resources
                await self._cleanup_resources()

                # Raise the timeout exception
                raise timeout_error
        except Exception as e:
            # This catches any exceptions from the timeout context
            logger.error(f"Error in initialization process for actor {self.actor_id}: {e}")

            # Mark initialization as failed
            self._initialization_complete = False

            # Update initializer state
            if initializer:
                try:
                    await initializer.set_actor_state(
                        actor_id=self.actor_id,
                        state=ActorState.FAILED,
                        error=e,
                        phase="initialize"
                    )

                    # Clean up resources
                    await initializer.cleanup_actor(self.actor_id)
                except Exception as rollback_error:
                    logger.error(f"Error rolling back actor {self.actor_id}: {rollback_error}")

            # Clean up resources
            await self._cleanup_resources()

            # Re-raise the exception
            raise

    async def start(self, timeout: float = 60.0) -> None:
        """
        Start the actor (second phase of two-phase initialization).

        This method starts the actor and begins processing messages. It's the second
        phase of the two-phase initialization process, following the initialize method.

        Args:
            timeout: Maximum time in seconds to wait for the actor to start

        Raises:
            ActorInitializationError: If the actor fails to start
            ActorTimeoutError: If starting times out

        Enhanced with robust error handling, explicit synchronization points, and
        improved diagnostics. Now uses the new ActorInitializationManager.
        """
        # Import the implementation from the separate module
        from .actor_start import start_actor

        # Call the implementation
        await start_actor(self, timeout=timeout)

    async def stop(self) -> None:
        """
        Stop the actor.

        Enhanced to stop additional tasks, save state, and unregister from registry.
        Updated to update the actor state in the initializer.
        Enhanced with robust error handling and cleanup.

        This method now delegates to the ActorTerminator component.

        Returns:
            None

        Implementation:
            This method delegates to the ActorTerminator.stop() method,
            which handles the actual actor stopping logic. This is part of the
            refactoring effort to reduce the complexity of the Actor class.
        """
        await self._terminator.stop()

    async def _cancel_all_tasks(self) -> None:
        """
        Cancel all tasks associated with this actor.

        This is a helper method for the stop() method to make it less complex.

        This method now delegates to the TaskManager component.

        Returns:
            None

        Implementation:
            This method delegates to the TaskManager.cancel_all_tasks() method,
            which handles the actual task cancellation logic. This is part of the
            refactoring effort to reduce the complexity of the Actor class.
        """
        await self._task_manager.cancel_all_tasks()

    async def _process_remaining_messages(self) -> None:
        """Process remaining messages in the mailbox before stopping."""
        if not self.mailbox.empty():
            try:
                # Process any remaining messages in the mailbox
                logger.info(f"Actor {self.actor_id} has {self.mailbox.qsize()} messages in mailbox")

                # Process remaining messages with a timeout
                remaining_messages = []
                while not self.mailbox.empty():
                    try:
                        message = self.mailbox.get_nowait()
                        remaining_messages.append(message)
                        self.mailbox.task_done()
                    except asyncio.QueueEmpty:
                        break

                # Log information about unprocessed messages
                if remaining_messages:
                    logger.warning(f"Actor {self.actor_id} has {len(remaining_messages)} unprocessed messages")
                    for msg in remaining_messages[:5]:  # Log first 5 messages
                        logger.warning(f"Unprocessed message: {msg.type.name if hasattr(msg, 'type') else 'unknown'}")

                    if len(remaining_messages) > 5:
                        logger.warning(f"... and {len(remaining_messages) - 5} more unprocessed messages")

                # Wait for the mailbox to be empty
                await asyncio.wait_for(self.mailbox.join(), timeout=0.5)
            except asyncio.TimeoutError:
                logger.warning(f"Timeout waiting for actor {self.actor_id} mailbox to empty")
            except Exception as e:
                logger.error(f"Error during actor {self.actor_id} shutdown: {e}")
                import traceback
                logger.error(traceback.format_exc())

    async def _send_heartbeats(self) -> None:
        """Send periodic heartbeats to the supervisor."""
        try:
            while self.is_running:
                if self.supervisor_id and self.supervisor_id in self._known_actors:
                    try:
                        # Update metrics
                        self._metrics["uptime"] = time.time() - self._metrics["start_time"]

                        # Create heartbeat payload
                        heartbeat_payload = {
                            "actor_id": self.actor_id,
                            "timestamp": time.time(),
                            "metrics": self._metrics
                        }

                        # Send heartbeat
                        await self.send(
                            self.supervisor_id,
                            MessageType.HEARTBEAT,
                            heartbeat_payload
                        )

                        # Update last heartbeat time
                        self._last_heartbeat = time.time()

                        logger.debug(f"Actor {self.actor_id} sent heartbeat to supervisor {self.supervisor_id}")
                    except Exception as e:
                        logger.warning(f"Failed to send heartbeat to supervisor: {e}")

                # Wait for next heartbeat interval
                await asyncio.sleep(self._heartbeat_interval)
        except asyncio.CancelledError:
            logger.info(f"Actor {self.actor_id} heartbeat task cancelled")
        except Exception as e:
            logger.error(f"Error in heartbeat task for actor {self.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def _collect_metrics(self) -> None:
        """Collect and report metrics periodically."""
        try:
            while self.is_running:
                try:
                    # Update metrics
                    self._metrics["uptime"] = time.time() - self._metrics["start_time"]

                    # Publish metrics to registry if available
                    try:
                        from .actor_registry import get_registry
                        registry = get_registry()
                        await registry.publish(
                            stream_id="metrics",
                            message_type=MessageType.METRICS,
                            payload={
                                "actor_id": self.actor_id,
                                "timestamp": time.time(),
                                "metrics": self._metrics
                            },
                            sender_id=self.actor_id
                        )
                    except (ImportError, AttributeError):
                        pass

                    logger.debug(f"Actor {self.actor_id} collected metrics: {self._metrics}")
                except Exception as e:
                    logger.warning(f"Failed to collect metrics: {e}")

                # Wait for next metrics interval
                await asyncio.sleep(self._metrics_interval)
        except asyncio.CancelledError:
            logger.info(f"Actor {self.actor_id} metrics task cancelled")
        except Exception as e:
            logger.error(f"Error in metrics task for actor {self.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def _save_state(self) -> None:
        """Save actor state to disk."""
        if not self.state_dir:
            return

        try:
            # Create state directory if it doesn't exist
            os.makedirs(self.state_dir, exist_ok=True)

            # Create state file path
            state_file = os.path.join(self.state_dir, f"{self.actor_id}.json")

            # Get state to save
            state = self._get_state()

            # Save state to file
            with open(state_file, "w") as f:
                json.dump(state, f, indent=2)

            logger.info(f"Actor {self.actor_id} saved state to {state_file}")
        except Exception as e:
            logger.error(f"Failed to save state for actor {self.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def _load_state(self) -> None:
        """Load actor state from disk."""
        if not self.state_dir:
            return

        try:
            # Create state file path
            state_file = os.path.join(self.state_dir, f"{self.actor_id}.json")

            # Check if state file exists
            if not os.path.exists(state_file):
                logger.info(f"No state file found for actor {self.actor_id}")
                return

            # Load state from file
            with open(state_file, "r") as f:
                state = json.load(f)

            # Restore state
            self._set_state(state)

            logger.info(f"Actor {self.actor_id} loaded state from {state_file}")
        except Exception as e:
            logger.error(f"Failed to load state for actor {self.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _get_state(self) -> Dict[str, Any]:
        """
        Get actor state for persistence.

        Override this method in subclasses to customize state persistence.

        Returns:
            Dictionary of state to save
        """
        return {
            "actor_id": self._actor_id,
            "actor_type": self._actor_type,
            "tags": list(self._tags),
            "capabilities": list(self._capabilities),
            "metrics": self._metrics,
            "timestamp": time.time()
        }

    def _set_state(self, state: Dict[str, Any]) -> None:
        """
        Set actor state from persistence.

        Override this method in subclasses to customize state restoration.

        Args:
            state: Dictionary of state to restore
        """
        # Restore metrics
        if "metrics" in state:
            self._metrics.update(state["metrics"])

        # Restore tags
        if "tags" in state:
            self._tags = set(state["tags"])

        # Restore capabilities
        if "capabilities" in state:
            self._capabilities = set(state["capabilities"])

    @property
    def actor_id(self) -> str:
        """Get the actor's unique identifier."""
        return self._actor_id

    @property
    def actor_type(self) -> str:
        """Get the actor's type."""
        return self._actor_type

    @property
    def tags(self) -> Set[str]:
        """Get the actor's tags."""
        return self._tags

    @property
    def capabilities(self) -> Set[str]:
        """Get the actor's capabilities."""
        return self._capabilities

    @property
    def supervisor_id(self) -> Optional[str]:
        """Get the actor's supervisor ID (if any)."""
        return self._supervisor_id

    @property
    def is_running(self) -> bool:
        """Check if the actor is running."""
        return self._is_running

    @is_running.setter
    def is_running(self, value: bool) -> None:
        """Set the actor's running state."""
        self._is_running = value

    @property
    def is_ready(self) -> bool:
        """Check if the actor is ready to process messages."""
        return self._ready

    async def wait_until_ready(self, timeout: Optional[float] = None) -> bool:
        """
        Wait until the actor is ready to process messages.

        Args:
            timeout: Optional timeout in seconds

        Returns:
            True if the actor is ready, False if timed out

        Raises:
            ActorTimeoutError: If waiting times out and raise_on_timeout is True
        """
        if self._ready:
            return True

        try:
            if timeout is not None:
                # Wait with timeout
                await asyncio.wait_for(self._ready_event.wait(), timeout=timeout)
            else:
                # Wait indefinitely
                await self._ready_event.wait()

            return True
        except asyncio.TimeoutError:
            logger.warning(f"Timeout waiting for actor {self.actor_id} to be ready")
            return False

    async def supervise(self, actor_id: str) -> None:
        """
        Start supervising another actor.

        Args:
            actor_id: ID of the actor to supervise
        """
        if actor_id in self._supervised_actors:
            logger.warning(f"Actor {self.actor_id} is already supervising {actor_id}")
            return

        # Add to supervised actors
        self._supervised_actors.add(actor_id)

        # Send supervise message to the actor
        try:
            supervise_payload = {
                "supervisor_id": self.actor_id,
                "timestamp": time.time()
            }
            await self.send(actor_id, MessageType.SUPERVISE, supervise_payload)
            logger.info(f"Actor {self.actor_id} started supervising {actor_id}")
        except Exception as e:
            logger.error(f"Failed to start supervising actor {actor_id}: {e}")
            self._supervised_actors.remove(actor_id)

    async def handle_heartbeat(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a heartbeat message from a supervised actor.

        Args:
            payload: Heartbeat payload
            context: Message context
        """
        actor_id = payload.get("actor_id")
        timestamp = payload.get("timestamp")
        metrics = payload.get("metrics", {})

        if not actor_id:
            logger.warning("Received heartbeat without actor_id")
            return

        if actor_id not in self._supervised_actors:
            logger.warning(f"Received heartbeat from unsupervised actor {actor_id}")
            return

        logger.debug(f"Actor {self.actor_id} received heartbeat from {actor_id} at {timestamp}")

        # Process metrics if needed
        if metrics:
            logger.debug(f"Actor {actor_id} metrics: {metrics}")

    async def handle_error(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle an error message.

        Args:
            payload: Error payload
            context: Message context
        """
        error = payload.get("error", "Unknown error")
        original_type = payload.get("original_message_type", "Unknown")
        actor_id = payload.get("actor_id")
        fatal = payload.get("fatal", False)

        if actor_id and actor_id in self._supervised_actors:
            # This is an error from a supervised actor
            logger.error(f"Supervised actor {actor_id} reported error: {error}")

            if fatal:
                # Try to restart the actor if it's a fatal error
                await self._restart_supervised_actor(actor_id)
        else:
            # This is an error for this actor
            logger.error(f"Actor {self.actor_id} received error: {error} (from message type: {original_type})")

    async def _restart_supervised_actor(self, actor_id: str) -> None:
        """
        Restart a supervised actor.

        Args:
            actor_id: ID of the actor to restart
        """
        # Check if we've restarted too many times
        current_time = time.time()
        if current_time - self._last_restart_time < self._restart_window:
            self._restart_count += 1
        else:
            self._restart_count = 1
            self._last_restart_time = current_time

        if self._restart_count > self._max_restarts:
            logger.error(f"Actor {actor_id} has been restarted {self._restart_count} times in {self._restart_window}s, giving up")
            self._supervised_actors.remove(actor_id)
            return

        logger.info(f"Restarting actor {actor_id} (restart {self._restart_count} of {self._max_restarts})")

        try:
            # Send restart message to the actor
            restart_payload = {
                "supervisor_id": self.actor_id,
                "timestamp": time.time(),
                "restart_count": self._restart_count
            }
            await self.send(actor_id, MessageType.RESTART, restart_payload)
            logger.info(f"Sent restart message to actor {actor_id}")
        except Exception as e:
            logger.error(f"Failed to restart actor {actor_id}: {e}")

    async def handle_restart(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a restart message from the supervisor.

        Args:
            payload: Restart payload
            context: Message context
        """
        supervisor_id = payload.get("supervisor_id")
        restart_count = payload.get("restart_count", 1)

        if supervisor_id != self.supervisor_id:
            logger.warning(f"Received restart message from non-supervisor {supervisor_id}")
            return

        logger.info(f"Actor {self.actor_id} received restart message from supervisor {supervisor_id} (restart {restart_count})")

        # Update metrics
        self._metrics["restarts"] += 1

        # Stop and start the actor
        await self.stop()
        await self.start()

    async def subscribe(self, stream_id: str) -> None:
        """
        Subscribe to a stream.

        Args:
            stream_id: ID of the stream to subscribe to
        """
        if stream_id in self._subscriptions:
            logger.warning(f"Actor {self.actor_id} is already subscribed to stream {stream_id}")
            return

        try:
            from .actor_registry import get_registry
            registry = get_registry()
            registry.subscribe(stream_id, self.actor_id)
            self._subscriptions.add(stream_id)
            logger.info(f"Actor {self.actor_id} subscribed to stream {stream_id}")
        except (ImportError, AttributeError) as e:
            logger.error(f"Failed to subscribe to stream {stream_id}: {e}")

    async def unsubscribe(self, stream_id: str) -> None:
        """
        Unsubscribe from a stream.

        Args:
            stream_id: ID of the stream to unsubscribe from
        """
        if stream_id not in self._subscriptions:
            logger.warning(f"Actor {self.actor_id} is not subscribed to stream {stream_id}")
            return

        try:
            from .actor_registry import get_registry
            registry = get_registry()
            registry.unsubscribe(stream_id, self.actor_id)
            self._subscriptions.remove(stream_id)
            logger.info(f"Actor {self.actor_id} unsubscribed from stream {stream_id}")
        except (ImportError, AttributeError) as e:
            logger.error(f"Failed to unsubscribe from stream {stream_id}: {e}")

    async def publish(self, stream_id: str, message_type: MessageType, payload: Optional[Dict[str, Any]] = None) -> None:
        """
        Publish a message to a stream.

        Args:
            stream_id: ID of the stream to publish to
            message_type: Type of the message
            payload: Message payload
        """
        try:
            # Create a copy of the payload to avoid modifying the original
            safe_payload = payload.copy() if payload else {}

            # Ensure stream_id is in the payload
            if "stream_id" not in safe_payload:
                safe_payload["stream_id"] = stream_id

            # Add publisher info to payload
            if "from" not in safe_payload:
                safe_payload["from"] = self.actor_id

            from .actor_registry import get_registry
            registry = get_registry()
            await registry.publish(stream_id, message_type, safe_payload, self.actor_id)
            logger.info(f"Actor {self.actor_id} published message of type {message_type.name} to stream {stream_id}")
        except (ImportError, AttributeError) as e:
            logger.error(f"Failed to publish to stream {stream_id}: {e}")
        except Exception as e:
            logger.error(f"Unexpected error publishing to stream {stream_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def handle_stream_data(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle data from a stream.

        Args:
            payload: Stream data payload
            context: Message context
        """
        # Try to get stream_id from context metadata
        stream_id = context.metadata.get("stream_id")

        # If not found in context, try to get from payload
        if not stream_id and isinstance(payload, dict):
            # Try direct stream_id field
            if "stream_id" in payload:
                stream_id = payload["stream_id"]
            # Try stream metadata
            elif "_stream_metadata" in payload and isinstance(payload["_stream_metadata"], dict):
                stream_id = payload["_stream_metadata"].get("stream_id")

        if not stream_id:
            logger.warning(f"Actor {self.actor_id} received stream data without stream_id in context or payload")
            logger.debug(f"Context metadata: {context.metadata}")
            logger.debug(f"Payload: {payload}")
            return

        logger.debug(f"Actor {self.actor_id} received data from stream {stream_id}")

        # Process stream data based on stream_id
        # Override this method in subclasses to handle specific streams

    async def handle_subscribe(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a subscription request.

        Args:
            payload: Subscription payload
            context: Message context
        """
        stream_id = payload.get("stream_id")
        if not stream_id:
            logger.warning("Received subscribe message without stream_id")
            return

        await self.subscribe(stream_id)

    async def handle_unsubscribe(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle an unsubscription request.

        Args:
            payload: Unsubscription payload
            context: Message context
        """
        stream_id = payload.get("stream_id")
        if not stream_id:
            logger.warning("Received unsubscribe message without stream_id")
            return

        await self.unsubscribe(stream_id)

    async def handle_publish(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a publish request.

        Args:
            payload: Publish payload
            context: Message context
        """
        stream_id = payload.get("stream_id")
        message_type_name = payload.get("message_type")
        stream_payload = payload.get("payload", {})

        if not stream_id:
            logger.warning("Received publish message without stream_id")
            return

        if not message_type_name:
            logger.warning("Received publish message without message_type")
            return

        try:
            message_type = MessageType[message_type_name]
        except KeyError:
            logger.warning(f"Unknown message type: {message_type_name}")
            return

        await self.publish(stream_id, message_type, stream_payload)

    async def handle_supervise(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a supervision request from another actor.

        This method is called when another actor wants to supervise this actor.
        It updates the supervisor_id and sends a heartbeat to confirm the supervision.

        Args:
            payload: Supervision payload
            context: Message context
        """
        supervisor_id = payload.get("supervisor_id")

        if not supervisor_id:
            logger.warning(f"Actor {self.actor_id} received supervise message without supervisor_id")
            return

        # Update supervisor ID
        old_supervisor = self._supervisor_id
        self._supervisor_id = supervisor_id

        logger.info(f"Actor {self.actor_id} is now supervised by {supervisor_id} (was {old_supervisor})")

        # Send a heartbeat to confirm supervision
        try:
            # Create heartbeat payload
            heartbeat_payload = {
                "actor_id": self.actor_id,
                "timestamp": time.time(),
                "metrics": self._metrics,
                "supervision_confirmed": True
            }

            # Send heartbeat
            await self.send(
                supervisor_id,
                MessageType.HEARTBEAT,
                heartbeat_payload,
                context.propagate()
            )

            # Update last heartbeat time
            self._last_heartbeat = time.time()

            logger.info(f"Actor {self.actor_id} sent confirmation heartbeat to new supervisor {supervisor_id}")
        except Exception as e:
            logger.error(f"Failed to send confirmation heartbeat to supervisor {supervisor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
