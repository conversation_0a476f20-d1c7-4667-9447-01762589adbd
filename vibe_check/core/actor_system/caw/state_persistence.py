"""
CAW Actor State Persistence Module
=================================

This module provides state persistence functionality for CAW actors,
including loading and saving state to disk.
"""

import json
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict, TYPE_CHECKING

if TYPE_CHECKING:
    from ..caw_actor import CAWActor

logger = logging.getLogger("vibe_check_caw_state_persistence")


class CAWStatePersistenceManager:
    """
    Manages state persistence for CAW actors.
    
    This class handles loading and saving actor state to/from disk,
    providing a way to restore actor state across restarts.
    """

    def __init__(self, actor: 'CAWActor'):
        """
        Initialize the state persistence manager.

        Args:
            actor: The CAW actor instance
        """
        self.actor = actor

    async def load_state_from_disk(self) -> None:
        """
        Load actor state from disk.

        This method loads the actor's state from a JSON file in the state directory.
        It's called during initialization to restore the actor's state.
        """
        if not self.actor.state_dir:
            logger.debug(f"No state directory configured for actor {self.actor.actor_id}")
            return

        try:
            state_file = Path(self.actor.state_dir) / f"{self.actor.actor_id}_state.json"
            if not state_file.exists():
                logger.debug(f"No state file found for actor {self.actor.actor_id}")
                return

            # Load state data
            with open(state_file, 'r') as f:
                state_data = json.load(f)

            # Restore state
            self._set_state(state_data)

            logger.info(f"Loaded state for actor {self.actor.actor_id} from {state_file}")

        except Exception as e:
            logger.error(f"Failed to load state for actor {self.actor.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise

    async def save_state_to_disk(self) -> None:
        """
        Save actor state to disk.

        This method saves the actor's state to a JSON file in the state directory.
        It's called during shutdown to persist the actor's state.
        """
        if not self.actor.state_dir:
            logger.debug(f"No state directory configured for actor {self.actor.actor_id}")
            return

        try:
            # Create state directory if it doesn't exist
            os.makedirs(self.actor.state_dir, exist_ok=True)

            # Get state data
            state_data = self._get_state()

            # Save to file
            state_file = Path(self.actor.state_dir) / f"{self.actor.actor_id}_state.json"
            with open(state_file, 'w') as f:
                json.dump(state_data, f, indent=2)

            logger.info(f"Saved state for actor {self.actor.actor_id} to {state_file}")

        except Exception as e:
            logger.error(f"Failed to save state for actor {self.actor.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise

    def _get_state(self) -> Dict[str, Any]:
        """
        Get actor state for persistence.

        Returns:
            Dictionary containing the actor's state
        """
        return {
            "actor_id": self.actor.actor_id,
            "actor_type": self.actor.actor_type,
            "tags": list(self.actor.tags),
            "capabilities": list(self.actor.capabilities),
            "metrics": self.actor._metrics,
            "timestamp": time.time()
        }

    def _set_state(self, state: Dict[str, Any]) -> None:
        """
        Set actor state from persistence.

        Args:
            state: State dictionary to restore
        """
        # Restore metrics
        if "metrics" in state:
            self.actor._metrics.update(state["metrics"])

        # Restore tags
        if "tags" in state:
            self.actor._tags = set(state["tags"])

        # Restore capabilities
        if "capabilities" in state:
            self.actor._capabilities = set(state["capabilities"])

    def get_state_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current actor state.

        Returns:
            Dictionary containing a summary of the actor's state
        """
        return {
            "actor_id": self.actor.actor_id,
            "actor_type": self.actor.actor_type,
            "current_state": self.actor.state_machine.current_state.value,
            "is_running": self.actor._is_running,
            "tags_count": len(self.actor.tags),
            "capabilities_count": len(self.actor.capabilities),
            "subscriptions_count": len(self.actor._subscriptions),
            "supervised_actors_count": len(self.actor._supervised_actors),
            "pending_messages_count": len(self.actor._pending_messages),
            "metrics": {
                "messages_received": self.actor._metrics.get("messages_received", 0),
                "messages_sent": self.actor._metrics.get("messages_sent", 0),
                "messages_processed": self.actor._metrics.get("messages_processed", 0),
                "errors": self.actor._metrics.get("errors", 0),
                "uptime": self.actor._metrics.get("uptime", 0.0),
                "restarts": self.actor._metrics.get("restarts", 0)
            },
            "circuit_breakers": {
                name: {
                    "is_open": cb.is_open(),
                    "failure_count": cb.failure_count,
                    "last_failure_time": cb.last_failure_time
                }
                for name, cb in self.actor._circuit_breakers.items()
            },
            "timestamp": time.time()
        }

    def reset_state(self) -> None:
        """Reset the actor's state to initial values."""
        # Reset metrics
        start_time = self.actor._metrics.get("start_time", time.time())
        self.actor._metrics = {
            "messages_received": 0,
            "messages_sent": 0,
            "messages_processed": 0,
            "errors": 0,
            "processing_time": 0.0,
            "avg_processing_time": 0.0,
            "last_activity": time.time(),
            "uptime": 0.0,
            "start_time": start_time,
            "restarts": 0
        }

        # Clear collections
        self.actor._known_actors.clear()
        self.actor._message_handlers.clear()
        self.actor._subscriptions.clear()
        self.actor._supervised_actors.clear()
        self.actor._pending_messages.clear()

        # Reset flags
        self.actor._is_running = False
        self.actor._ready_event.clear()

        # Reset circuit breakers
        for circuit_breaker in self.actor._circuit_breakers.values():
            circuit_breaker.reset()

        logger.info(f"Reset state for actor {self.actor.actor_id}")

    async def backup_state(self, backup_suffix: str = None) -> Path:
        """
        Create a backup of the current state.

        Args:
            backup_suffix: Optional suffix for the backup file

        Returns:
            Path to the backup file
        """
        if not self.actor.state_dir:
            raise ValueError(f"No state directory configured for actor {self.actor.actor_id}")

        try:
            # Create backup directory if it doesn't exist
            backup_dir = Path(self.actor.state_dir) / "backups"
            os.makedirs(backup_dir, exist_ok=True)

            # Generate backup filename
            timestamp = int(time.time())
            suffix = f"_{backup_suffix}" if backup_suffix else ""
            backup_filename = f"{self.actor.actor_id}_state_backup_{timestamp}{suffix}.json"
            backup_file = backup_dir / backup_filename

            # Get state data
            state_data = self._get_state()
            state_data["backup_timestamp"] = timestamp
            state_data["backup_suffix"] = backup_suffix

            # Save backup
            with open(backup_file, 'w') as f:
                json.dump(state_data, f, indent=2)

            logger.info(f"Created state backup for actor {self.actor.actor_id} at {backup_file}")
            return backup_file

        except Exception as e:
            logger.error(f"Failed to create state backup for actor {self.actor.actor_id}: {e}")
            raise
