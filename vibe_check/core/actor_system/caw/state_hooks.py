"""
CAW Actor State Hooks Module
===========================

This module provides state machine hooks and event publishing functionality
for CAW actors, including state transition handling and event management.
"""

import asyncio
import logging
import time
from typing import TYPE_CHECKING

from ..actor_state import ActorState
from ..actor_state_machine import StateTransitionContext

if TYPE_CHECKING:
    from ..caw_actor import CAWActor

logger = logging.getLogger("vibe_check_caw_state_hooks")


class CAWStateHookManager:
    """
    Manages state machine hooks and event publishing for CAW actors.
    
    This class handles the setup of state machine hooks and the publishing
    of state change events for better observability and debugging.
    """

    def __init__(self, actor: 'CAWActor'):
        """
        Initialize the state hook manager.

        Args:
            actor: The CAW actor instance
        """
        self.actor = actor

    def setup_state_machine_hooks(self) -> None:
        """Set up hooks for the actor state machine."""
        # Add state entry hooks
        self.actor.state_machine.add_state_entry_hook(
            ActorState.INITIALIZING,
            lambda ctx: logger.info(f"Actor {self.actor.actor_id} is initializing")
        )
        self.actor.state_machine.add_state_entry_hook(
            ActorState.INITIALIZED,
            lambda ctx: logger.info(f"Actor {self.actor.actor_id} is initialized")
        )
        self.actor.state_machine.add_state_entry_hook(
            ActorState.STARTING,
            lambda ctx: logger.info(f"Actor {self.actor.actor_id} is starting")
        )
        self.actor.state_machine.add_state_entry_hook(
            ActorState.READY,
            lambda ctx: self.actor._ready_event.set()
        )
        self.actor.state_machine.add_state_entry_hook(
            ActorState.FAILED,
            lambda ctx: logger.error(f"Actor {self.actor.actor_id} failed: {ctx.error}")
        )

        # Add state exit hooks
        self.actor.state_machine.add_state_exit_hook(
            ActorState.READY,
            lambda ctx: self.actor._ready_event.clear()
        )

        # Add transition hooks with safe task creation
        def safe_create_task(ctx: StateTransitionContext) -> None:
            try:
                asyncio.create_task(self.publish_state_change_event(ctx))
            except RuntimeError:
                # No running event loop, just log the state change
                logger.info(f"Actor {self.actor.actor_id} state changed from {ctx.old_state.value} to {ctx.new_state.value}")

        # Add hooks for all transitions
        self.actor.state_machine.add_transition_hook(
            ActorState.CREATED,
            ActorState.INITIALIZING,
            safe_create_task
        )
        self.actor.state_machine.add_transition_hook(
            ActorState.INITIALIZING,
            ActorState.INITIALIZED,
            safe_create_task
        )
        self.actor.state_machine.add_transition_hook(
            ActorState.INITIALIZED,
            ActorState.STARTING,
            safe_create_task
        )
        self.actor.state_machine.add_transition_hook(
            ActorState.STARTING,
            ActorState.READY,
            safe_create_task
        )
        self.actor.state_machine.add_transition_hook(
            ActorState.READY,
            ActorState.STOPPING,
            safe_create_task
        )
        self.actor.state_machine.add_transition_hook(
            ActorState.STOPPING,
            ActorState.STOPPED,
            safe_create_task
        )

    async def publish_state_change_event(self, context: StateTransitionContext) -> None:
        """
        Publish an event for a state transition.

        Args:
            context: The state transition context
        """
        try:
            await self.actor._event_bus.publish_simple(
                event_type=f"actor.state_change.{self.actor.actor_id}",
                data={
                    "actor_id": self.actor.actor_id,
                    "old_state": context.old_state.value,
                    "new_state": context.new_state.value,
                    "phase": context.phase,
                    "timestamp": time.time()
                },
                source=self.actor.actor_id
            )
        except Exception as e:
            logger.error(f"Error publishing state change event: {e}")

    async def publish_actor_created_event(self) -> None:
        """Publish an event indicating that the actor was created."""
        try:
            await self.actor._event_bus.publish_simple(
                event_type=f"actor.created.{self.actor.actor_id}",
                data={
                    "actor_id": self.actor.actor_id,
                    "actor_type": self.actor.actor_type,
                    "tags": list(self.actor.tags),
                    "capabilities": list(self.actor.capabilities),
                    "timestamp": time.time()
                },
                source=self.actor.actor_id
            )
        except Exception as e:
            logger.error(f"Error publishing actor created event: {e}")

    async def publish_cleanup_event(self) -> None:
        """Publish an event indicating that the actor is being cleaned up."""
        try:
            await self.actor._event_bus.publish_simple(
                event_type=f"actor.cleanup.{self.actor.actor_id}",
                data={
                    "actor_id": self.actor.actor_id,
                    "timestamp": time.time()
                },
                source=self.actor.actor_id
            )
        except Exception as e:
            logger.error(f"Error publishing cleanup event: {e}")

    async def publish_stop_event(self) -> None:
        """Publish an event indicating that the actor is stopping."""
        try:
            await self.actor._event_bus.publish_simple(
                event_type=f"actor.stop.{self.actor.actor_id}",
                data={
                    "actor_id": self.actor.actor_id,
                    "timestamp": time.time()
                },
                source=self.actor.actor_id
            )
        except Exception as e:
            logger.error(f"Error publishing stop event: {e}")

    async def publish_subscription_event(self, stream_id: str, action: str) -> None:
        """
        Publish a subscription/unsubscription event.

        Args:
            stream_id: ID of the stream
            action: Action performed ("subscribe" or "unsubscribe")
        """
        try:
            await self.actor._event_bus.publish_simple(
                event_type=f"actor.{action}.{self.actor.actor_id}",
                data={
                    "actor_id": self.actor.actor_id,
                    "stream_id": stream_id,
                    "timestamp": time.time()
                },
                source=self.actor.actor_id
            )
        except Exception as e:
            logger.error(f"Error publishing {action} event: {e}")

    async def publish_stream_message(self, stream_id: str, message_type: str, payload: dict) -> None:
        """
        Publish a message to a stream.

        Args:
            stream_id: ID of the stream
            message_type: Type of the message
            payload: Message payload
        """
        try:
            await self.actor._event_bus.publish_simple(
                event_type=f"stream.{stream_id}",
                data={
                    "type": message_type,
                    "actor_id": self.actor.actor_id,
                    "stream_id": stream_id,
                    "payload": payload,
                    "timestamp": time.time()
                },
                source=self.actor.actor_id
            )
        except Exception as e:
            logger.error(f"Error publishing stream message: {e}")
