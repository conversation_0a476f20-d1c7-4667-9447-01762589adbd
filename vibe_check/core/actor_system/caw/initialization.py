"""
CAW Actor Initialization Module
==============================

This module provides initialization functionality for CAW actors,
including registration with initializers and registry management.
"""

import logging
from typing import TYPE_CHECKING, cast

if TYPE_CHECKING:
    from ..caw_actor import CAWActor

logger = logging.getLogger("vibe_check_caw_initialization")


class CAWInitializationManager:
    """
    Manages initialization processes for CAW actors.
    
    This class handles registration with the actor registry and initializer,
    as well as managing the initialization lifecycle.
    """

    def __init__(self, actor: 'CAWActor'):
        """
        Initialize the initialization manager.

        Args:
            actor: The CAW actor instance
        """
        self.actor = actor

    def register_with_registry(self) -> None:
        """Register this actor with the registry."""
        try:
            from ..actor_registry import get_registry
            registry = get_registry()
            # Cast self to Actor to satisfy type checking
            from ..actor import Actor
            registry.register_actor(
                actor_id=self.actor.actor_id,
                actor=cast(Actor, self.actor),
                actor_type=self.actor.actor_type,
                tags=self.actor.tags,
                capabilities=self.actor.capabilities
            )
            logger.info(f"Actor {self.actor.actor_id} registered with registry")
        except (ImportError, AttributeError) as e:
            logger.warning(f"Could not register actor {self.actor.actor_id} with registry: {e}")

    async def register_with_initializer(self) -> None:
        """
        Register this actor with the initializer.

        This method is called during initialization to ensure proper
        synchronization with the initializer.
        """
        try:
            # Get the initializer
            from ..actor_initializer import get_initializer
            initializer = get_initializer()
            if not initializer:
                logger.warning(f"Initializer not available for actor {self.actor.actor_id}")
                return

            # Register with initializer
            await initializer.register_actor(
                actor_id=self.actor.actor_id,
                actor_type=self.actor.actor_type,
                tags=self.actor.tags
            )
            logger.info(f"Actor {self.actor.actor_id} registered with initializer")

            # Register cleanup resources
            initializer.register_resource(
                actor_id=self.actor.actor_id,
                cleanup_func=self.actor._cleanup_resources,
                args=[],
                kwargs={}
            )

        except Exception as e:
            logger.error(f"Error registering actor {self.actor.actor_id} with initializer: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # Re-raise the exception to ensure the caller knows registration failed
            raise

    def initialize_handlers(self) -> None:
        """
        Initialize message handlers for this actor.

        This maps message types to handler methods, allowing for dynamic dispatch
        based on the received message type.
        """
        # Find all handler methods in the class
        for attr_name in dir(self.actor):
            if attr_name.startswith("handle_") and callable(getattr(self.actor, attr_name)):
                # Extract the message type from the handler name
                message_type_name = attr_name[len("handle_"):].upper()
                try:
                    # Try to get the MessageType enum value
                    from ..message import MessageType
                    message_type = MessageType[message_type_name]
                    # Register the handler
                    self.actor._message_handlers[message_type] = getattr(self.actor, attr_name)
                except (KeyError, AttributeError):
                    # If the message type doesn't exist, skip it
                    pass

    async def cleanup_resources(self) -> None:
        """
        Clean up resources associated with this actor.

        This method is called when the actor fails to initialize or is being
        rolled back. It should release any resources that were acquired during
        initialization.
        """
        try:
            # Cancel any running tasks
            for task_name in ['_process_task', '_heartbeat_task', '_metrics_task']:
                task = getattr(self.actor, task_name, None)
                if task and not task.done():
                    try:
                        task.cancel()
                        logger.debug(f"Cancelled {task_name} for actor {self.actor.actor_id}")
                    except Exception as e:
                        logger.error(f"Error cancelling {task_name} for actor {self.actor.actor_id}: {e}")

            # Clear known actors
            self.actor._known_actors.clear()

            # Clear message handlers
            self.actor._message_handlers.clear()

            # Clear subscriptions
            self.actor._subscriptions.clear()

            # Clear supervised actors
            self.actor._supervised_actors.clear()

            # Clear pending messages
            self.actor._pending_messages.clear()

            # Reset metrics
            self.actor._metrics = {
                "messages_received": 0,
                "messages_sent": 0,
                "messages_processed": 0,
                "errors": 0,
                "processing_time": 0.0,
                "avg_processing_time": 0.0,
                "last_activity": 0.0,
                "uptime": 0.0,
                "start_time": 0.0,
                "restarts": 0
            }

            # Reset state flags
            self.actor._is_running = False
            self.actor._ready_event.clear()

            # Unregister from registry if available
            try:
                from ..actor_registry import get_registry
                registry = get_registry()
                registry.unregister_actor(self.actor.actor_id)
                logger.debug(f"Unregistered actor {self.actor.actor_id} from registry")
            except (ImportError, AttributeError) as e:
                logger.debug(f"Could not unregister actor {self.actor.actor_id} from registry: {e}")

            # Publish cleanup event
            from .state_hooks import CAWStateHookManager
            hook_manager = CAWStateHookManager(self.actor)
            await hook_manager.publish_cleanup_event()

        except Exception as e:
            logger.error(f"Error during cleanup for actor {self.actor.actor_id}: {e}")

        logger.info(f"Cleaned up resources for actor {self.actor.actor_id}")

    async def check_readiness(self) -> bool:
        """
        Check if the actor is ready to process messages.

        This method is called after the start method completes to verify
        that the actor is fully operational. Subclasses can override this
        method to add custom readiness checks.

        Returns:
            True if the actor is ready, False otherwise
        """
        try:
            # Check if the actor is in the READY state
            if self.actor.state_machine.current_state != self.actor.state_machine.ActorState.READY:
                logger.warning(f"Actor {self.actor.actor_id} is not in READY state")
                return False

            # Check if the message processing task is running
            if not self.actor._process_task or self.actor._process_task.done():
                logger.warning(f"Actor {self.actor.actor_id} message processing task is not running")
                return False

            # Check if the actor is marked as running
            if not self.actor._is_running:
                logger.warning(f"Actor {self.actor.actor_id} is not marked as running")
                return False

            # Check circuit breakers
            for name, circuit_breaker in self.actor._circuit_breakers.items():
                if circuit_breaker.is_open():
                    logger.warning(f"Actor {self.actor.actor_id} circuit breaker '{name}' is open")
                    return False

        except Exception as e:
            logger.error(f"Error checking readiness for actor {self.actor.actor_id}: {e}")
            return False

        # All checks passed
        return True
