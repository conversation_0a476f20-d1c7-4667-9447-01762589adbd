"""
Circuit Breaker for the Vibe Check Actor System.

This module provides a circuit breaker implementation for preventing cascading
failures in the actor system. It monitors for failures and temporarily disables
operations when failure thresholds are exceeded.

The circuit breaker is a core component of the improved actor system, addressing
issues with error handling and providing more robust failure recovery mechanisms.
"""

import asyncio
import enum
import logging
import time
from dataclasses import dataclass
from enum import Enum
from typing import Any, Awaitable, Callable, Dict, Generic, List, Optional, TypeVar, Union, cast

# Configure logging
logger = logging.getLogger(__name__)

# Type variables for the circuit breaker
T = TypeVar('T')
R = TypeVar('R')


class CircuitBreakerState(Enum):
    """Possible states of a circuit breaker."""
    CLOSED = "CLOSED"  # Normal operation, requests are allowed
    OPEN = "OPEN"  # Circuit is open, requests are blocked
    HALF_OPEN = "HALF_OPEN"  # Testing if the circuit can be closed again


class CircuitBreakerOpenError(Exception):
    """Exception raised when a circuit breaker is open."""

    def __init__(self, name: str, opened_at: float, reset_timeout: float):
        """
        Initialize the exception.

        Args:
            name: Name of the circuit breaker
            opened_at: Timestamp when the circuit was opened
            reset_timeout: Timeout before the circuit will be half-open
        """
        self.name = name
        self.opened_at = opened_at
        self.reset_timeout = reset_timeout
        self.retry_after = max(0, opened_at + reset_timeout - time.time())
        message = (
            f"Circuit breaker '{name}' is open. "
            f"Opened at: {opened_at}, retry after: {self.retry_after:.2f}s"
        )
        super().__init__(message)


@dataclass
class CircuitBreakerMetrics:
    """Metrics for a circuit breaker."""
    success_count: int = 0
    failure_count: int = 0
    timeout_count: int = 0
    rejection_count: int = 0
    last_failure_time: Optional[float] = None
    last_success_time: Optional[float] = None
    consecutive_failures: int = 0
    consecutive_successes: int = 0


class CircuitBreaker(Generic[T, R]):
    """
    A circuit breaker for preventing cascading failures.

    The circuit breaker monitors for failures and temporarily disables
    operations when failure thresholds are exceeded. It helps prevent
    cascading failures and allows for graceful degradation.

    Attributes:
        name: Name of the circuit breaker
        failure_threshold: Number of failures before opening the circuit
        reset_timeout: Time in seconds before attempting to close the circuit
        half_open_success_threshold: Number of successes needed to close the circuit
        state: Current state of the circuit breaker
        metrics: Metrics for the circuit breaker
        last_state_change: Timestamp of the last state change
        exceptions_to_count: Exception types to count as failures
    """

    def __init__(
        self,
        name: str,
        failure_threshold: int = 5,
        reset_timeout: float = 60.0,
        half_open_success_threshold: int = 1,
        exceptions_to_count: Optional[List[type]] = None,
    ):
        """
        Initialize the circuit breaker.

        Args:
            name: Name of the circuit breaker
            failure_threshold: Number of failures before opening the circuit
            reset_timeout: Time in seconds before attempting to close the circuit
            half_open_success_threshold: Number of successes needed to close the circuit
            exceptions_to_count: Exception types to count as failures (default: all)
        """
        self.name = name
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.half_open_success_threshold = half_open_success_threshold
        self.exceptions_to_count = exceptions_to_count or [Exception]
        
        self.state = CircuitBreakerState.CLOSED
        self.metrics = CircuitBreakerMetrics()
        self.last_state_change = time.time()
        
        self._lock = asyncio.Lock()
        
        logger.info(f"Initialized circuit breaker '{name}'")

    async def execute(
        self,
        func: Callable[..., Union[T, Awaitable[T]]],
        *args: Any,
        **kwargs: Any
    ) -> T:
        """
        Execute a function with circuit breaker protection.

        Args:
            func: The function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function

        Returns:
            The result of the function

        Raises:
            CircuitBreakerOpenError: If the circuit is open
            Exception: Any exception raised by the function
        """
        await self._check_state()
        
        try:
            # Execute the function
            result = func(*args, **kwargs)
            
            # Handle coroutines
            if asyncio.iscoroutine(result):
                result = await result
                
            # Record success
            await self._on_success()
            return cast(T, result)
            
        except asyncio.TimeoutError as e:
            # Record timeout
            await self._on_timeout()
            raise
            
        except Exception as e:
            # Check if this exception type should be counted
            should_count = any(isinstance(e, exc_type) for exc_type in self.exceptions_to_count)
            
            if should_count:
                # Record failure
                await self._on_failure()
                
            raise

    async def _check_state(self) -> None:
        """
        Check the current state of the circuit breaker.

        Raises:
            CircuitBreakerOpenError: If the circuit is open
        """
        async with self._lock:
            current_time = time.time()
            
            # If the circuit is open, check if it's time to try half-open
            if self.state == CircuitBreakerState.OPEN:
                if current_time - self.last_state_change >= self.reset_timeout:
                    logger.info(f"Circuit breaker '{self.name}' transitioning from OPEN to HALF_OPEN")
                    self.state = CircuitBreakerState.HALF_OPEN
                    self.last_state_change = current_time
                    self.metrics.consecutive_successes = 0
                else:
                    # Circuit is still open, reject the request
                    self.metrics.rejection_count += 1
                    raise CircuitBreakerOpenError(
                        self.name,
                        self.last_state_change,
                        self.reset_timeout
                    )

    async def _on_success(self) -> None:
        """Record a successful execution."""
        async with self._lock:
            current_time = time.time()
            self.metrics.success_count += 1
            self.metrics.last_success_time = current_time
            self.metrics.consecutive_successes += 1
            self.metrics.consecutive_failures = 0
            
            # If the circuit is half-open and we've had enough successes, close it
            if (self.state == CircuitBreakerState.HALF_OPEN and
                self.metrics.consecutive_successes >= self.half_open_success_threshold):
                logger.info(f"Circuit breaker '{self.name}' transitioning from HALF_OPEN to CLOSED")
                self.state = CircuitBreakerState.CLOSED
                self.last_state_change = current_time

    async def _on_failure(self) -> None:
        """Record a failed execution."""
        async with self._lock:
            current_time = time.time()
            self.metrics.failure_count += 1
            self.metrics.last_failure_time = current_time
            self.metrics.consecutive_failures += 1
            self.metrics.consecutive_successes = 0
            
            # If we've had too many failures, open the circuit
            if (self.state == CircuitBreakerState.CLOSED and
                self.metrics.consecutive_failures >= self.failure_threshold):
                logger.warning(
                    f"Circuit breaker '{self.name}' transitioning from CLOSED to OPEN "
                    f"after {self.metrics.consecutive_failures} consecutive failures"
                )
                self.state = CircuitBreakerState.OPEN
                self.last_state_change = current_time
            
            # If the circuit is half-open, any failure should open it again
            elif self.state == CircuitBreakerState.HALF_OPEN:
                logger.warning(f"Circuit breaker '{self.name}' transitioning from HALF_OPEN to OPEN after failure")
                self.state = CircuitBreakerState.OPEN
                self.last_state_change = current_time

    async def _on_timeout(self) -> None:
        """Record a timeout."""
        async with self._lock:
            self.metrics.timeout_count += 1
            # Treat timeouts as failures
            await self._on_failure()

    def get_state(self) -> CircuitBreakerState:
        """
        Get the current state of the circuit breaker.

        Returns:
            The current state
        """
        return self.state

    def get_metrics(self) -> CircuitBreakerMetrics:
        """
        Get the metrics for the circuit breaker.

        Returns:
            The current metrics
        """
        return self.metrics

    async def reset(self) -> None:
        """Reset the circuit breaker to its initial state."""
        async with self._lock:
            self.state = CircuitBreakerState.CLOSED
            self.metrics = CircuitBreakerMetrics()
            self.last_state_change = time.time()
            logger.info(f"Reset circuit breaker '{self.name}'")

    async def force_open(self) -> None:
        """Force the circuit breaker to open."""
        async with self._lock:
            if self.state != CircuitBreakerState.OPEN:
                self.state = CircuitBreakerState.OPEN
                self.last_state_change = time.time()
                logger.warning(f"Forced circuit breaker '{self.name}' to OPEN")

    async def force_close(self) -> None:
        """Force the circuit breaker to close."""
        async with self._lock:
            if self.state != CircuitBreakerState.CLOSED:
                self.state = CircuitBreakerState.CLOSED
                self.last_state_change = time.time()
                self.metrics.consecutive_failures = 0
                logger.info(f"Forced circuit breaker '{self.name}' to CLOSED")
