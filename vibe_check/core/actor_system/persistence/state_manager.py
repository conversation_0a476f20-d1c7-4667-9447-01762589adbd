"""
State Manager Module
=================

This module defines the StateManager class, which is responsible for
handling state persistence for actors.

The StateManager implements the StateManagerProtocol and provides methods
for saving and loading actor state.
"""

import json
import logging
import os
import time
import traceback
from typing import Any, Dict, Optional, Set, TYPE_CHECKING

from ..protocols import StateManagerProtocol

# Avoid circular imports
if TYPE_CHECKING:
    from ..actor import Actor


class StateManager(StateManagerProtocol):
    """
    Handles state persistence for actors.

    This component is responsible for saving and loading actor state,
    enabling persistence across restarts. It implements the StateManagerProtocol.

    Attributes:
        actor: The actor that owns this component
        logger: Logger for this component
        state_dir: Directory for state persistence
    """

    def __init__(self, actor: 'Actor', state_dir: Optional[str] = None) -> None:
        """
        Initialize the state manager.

        Args:
            actor: The actor that owns this component
            state_dir: Optional directory for state persistence
        """
        self.actor = actor
        self.logger = logging.getLogger("vibe_check_actor_system.state")
        self.state_dir = state_dir

    async def save_state(self) -> None:
        """
        Save actor state to disk.

        Raises:
            RuntimeError: If the state directory is not set
            IOError: If there is an error writing to the state file
        """
        if not self.state_dir:
            self.logger.debug(f"No state directory set for actor {self.actor.actor_id}, skipping state save")
            return

        try:
            # Create state directory if it doesn't exist
            os.makedirs(self.state_dir, exist_ok=True)

            # Create state file path
            state_file = os.path.join(self.state_dir, f"{self.actor.actor_id}.json")

            # Get state to save
            state = self.get_state()

            # Save state to file
            with open(state_file, "w") as f:
                json.dump(state, f, indent=2)

            self.logger.info(f"Actor {self.actor.actor_id} saved state to {state_file}")
        except Exception as e:
            self.logger.error(f"Failed to save state for actor {self.actor.actor_id}: {e}")
            error_details = traceback.format_exc()
            self.logger.error(error_details)
            raise IOError(f"Failed to save state for actor {self.actor.actor_id}: {e}") from e

    async def load_state(self) -> None:
        """
        Load actor state from disk.

        Raises:
            RuntimeError: If the state directory is not set
            IOError: If there is an error reading from the state file
            ValueError: If the state file contains invalid JSON
        """
        if not self.state_dir:
            self.logger.debug(f"No state directory set for actor {self.actor.actor_id}, skipping state load")
            return

        try:
            # Create state file path
            state_file = os.path.join(self.state_dir, f"{self.actor.actor_id}.json")

            # Check if state file exists
            if not os.path.exists(state_file):
                self.logger.info(f"No state file found for actor {self.actor.actor_id}")
                return

            # Load state from file
            with open(state_file, "r") as f:
                state = json.load(f)

            # Restore state
            self.set_state(state)

            self.logger.info(f"Actor {self.actor.actor_id} loaded state from {state_file}")
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in state file for actor {self.actor.actor_id}: {e}")
            error_details = traceback.format_exc()
            self.logger.error(error_details)
            raise ValueError(f"Invalid JSON in state file for actor {self.actor.actor_id}: {e}") from e
        except Exception as e:
            self.logger.error(f"Failed to load state for actor {self.actor.actor_id}: {e}")
            error_details = traceback.format_exc()
            self.logger.error(error_details)
            raise IOError(f"Failed to load state for actor {self.actor.actor_id}: {e}") from e

    def get_state(self) -> Dict[str, Any]:
        """
        Get actor state for persistence.

        Override this method in subclasses to customize state persistence.

        Returns:
            Dictionary of state to save
        """
        return {
            "actor_id": self.actor.actor_id,
            "actor_type": self.actor.actor_type,
            "tags": list(self.actor.tags),
            "capabilities": list(self.actor.capabilities),
            "metrics": self.actor._metrics,
            "timestamp": time.time()
        }

    def set_state(self, state: Dict[str, Any]) -> None:
        """
        Set actor state from persistence.

        Override this method in subclasses to customize state restoration.

        Args:
            state: Dictionary of state to restore

        Raises:
            ValueError: If the state is invalid
        """
        # Validate state
        if not isinstance(state, dict):
            raise ValueError(f"Invalid state type: {type(state)}, expected dict")

        # Restore metrics
        if "metrics" in state and isinstance(state["metrics"], dict):
            self.actor._metrics.update(state["metrics"])

        # Restore tags
        if "tags" in state and isinstance(state["tags"], list):
            self.actor._tags = set(state["tags"])

        # Restore capabilities
        if "capabilities" in state and isinstance(state["capabilities"], list):
            self.actor._capabilities = set(state["capabilities"])
