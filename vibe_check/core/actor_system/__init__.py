"""
Vibe Check Actor System - Core Components
=================================

This module provides the core components for the actor-based system used in Vibe Check.
It implements the Contextual Adaptive Wave (CAW) paradigm through:

1. Actor base class - Autonomous components that communicate via messages
2. Message class - Message passing with wave-particle duality
3. ContextWave class - Contextual propagation and adaptation
4. MessageType enum - Message type classification
5. ActorSystem class - Manages actors and message passing
6. ActorRegistry - Dynamic actor discovery and communication
7. ActorPool - Pooling of actors for large workloads
8. SupervisorActor - Monitoring and management of actors
9. Distributed actors - Support for actors across machine boundaries
10. ActorInitializer - Two-phase initialization with explicit synchronization
11. Diagnostics - Detailed initialization tracking and visualization
12. Dependency Management - Dependency resolution and circular dependency detection
13. Registry Synchronizer - Consistency between registry and initializer
14. Error Isolation - Circuit breakers and error categorization
15. System Monitoring - Health checks and resource usage tracking

These components enable a choreographed, contextually adaptive system
with enhanced features for resilience, scalability, and distribution.
The two-phase initialization process ensures that all actors are properly
initialized before they start communicating, preventing race conditions
and timing issues during system startup.

The enhanced diagnostics, monitoring, and error isolation features provide
robust tools for debugging, optimizing, and maintaining the actor system.
"""

from .actor import Actor
from .context_wave import ContextWave
from .message import Message, MessageType
from .actor_system import ActorSystem
from .actor_registry import ActorRegistry, get_registry, reset_registry
from .actor_pool import ActorPool
from .supervisor_actor import SupervisorActor
from .distributed import Node, get_node, reset_node, DistributedActor
from .actor_state import ActorState
from .consolidated_initializer import ConsolidatedActorInitializer, get_initializer, reset_initializer, ActorInitializationError
from .dependency_management import DependencyManager, CircularDependencyError, get_dependency_manager, reset_dependency_manager
from .registry_synchronizer import RegistrySynchronizer, RegistryInconsistencyError, get_registry_synchronizer, reset_registry_synchronizer
from .error_isolation import ErrorIsolator, CircuitBreaker, CircuitBreakerOpenError, ErrorCategory, get_error_isolator, reset_error_isolator
from .diagnostics import InitializationTracker, InitializationStep, get_tracker, reset_tracker
from .monitoring import SystemMonitor, HealthStatus, ResourceMetrics, ActorMetrics, get_system_monitor, reset_system_monitor



__all__ = [
    # Core components
    'Actor',
    'ContextWave',
    'Message',
    'MessageType',
    'ActorSystem',
    'ActorRegistry',
    'get_registry',
    'reset_registry',
    'ActorPool',
    'SupervisorActor',
    'Node',
    'get_node',
    'reset_node',
    'DistributedActor',
    'ConsolidatedActorInitializer',
    'ActorState',
    'get_initializer',
    'reset_initializer',
    'ActorInitializationError',

    # Dependency management
    'DependencyManager',
    'CircularDependencyError',
    'get_dependency_manager',
    'reset_dependency_manager',

    # Registry synchronization
    'RegistrySynchronizer',
    'RegistryInconsistencyError',
    'get_registry_synchronizer',
    'reset_registry_synchronizer',

    # Error isolation
    'ErrorIsolator',
    'CircuitBreaker',
    'CircuitBreakerOpenError',
    'ErrorCategory',
    'get_error_isolator',
    'reset_error_isolator',

    # Diagnostics
    'InitializationTracker',
    'InitializationStep',
    'get_tracker',
    'reset_tracker',

    # Monitoring
    'SystemMonitor',
    'HealthStatus',
    'ResourceMetrics',
    'ActorMetrics',
    'get_system_monitor',
    'reset_system_monitor'
]
