"""
Enhanced Actor Module
=================

This module provides an enhanced version of the Actor class that integrates
with the new dependency injection, enhanced diagnostics, and robust initialization
components.

The EnhancedActor class extends the base Actor class with improved reliability,
diagnostics, and error handling.
"""

import asyncio
import logging
import time
import traceback
from typing import Any, Dict, List, Optional, Set, Type, TypeVar, Union, Callable, Awaitable

from .actor import Actor
from .actor_state import ActorState
from .message import Message, MessageType
from .context_wave import ContextWave
from .dependency_injection import (
    DependencyContainer,
    get_container,
    Component,
    ComponentScope,
    Injectable
)
from .diagnostics.enhanced_tracker import (
    EnhancedTracker,
    DiagnosticCategory,
    DiagnosticLevel
)
from .initialization import (
    ActorInitializer,
    get_initializer,
    ActorLifecycleState,
    DependencyResolver,
    get_resolver,
    SynchronizationManager,
    InitializationError,
    DependencyError,
    CircularDependencyError,
    TimeoutError,
    StateTransitionError
)
from .integration import (
    integrate_actor_system,
    integrate_message_processor,
    integrate_actor_starter
)

logger = logging.getLogger("vibe_check_enhanced_actor")


class EnhancedActor(Actor):
    """
    Enhanced version of the Actor class with improved reliability and diagnostics.

    This class extends the base Actor class with integration with the new
    dependency injection, enhanced diagnostics, and robust initialization
    components.

    Attributes:
        All attributes from the base Actor class
        _container: The dependency container
        _tracker: The enhanced diagnostics tracker
        _initializer: The actor initializer
        _resolver: The dependency resolver
        _sync_manager: The synchronization manager
    """

    def __init__(self, actor_id: str, actor_type: Optional[str] = None,
                tags: Optional[Set[str]] = None, capabilities: Optional[Set[str]] = None,
                supervisor_id: Optional[str] = None, state_dir: Optional[str] = None):
        """
        Initialize the enhanced actor.

        Args:
            actor_id: Unique ID for this actor
            actor_type: Optional type of the actor for discovery
            tags: Optional set of tags for discovery
            capabilities: Optional set of capabilities for discovery
            supervisor_id: Optional ID of the supervisor actor
            state_dir: Optional directory for state persistence
        """
        # Initialize the base actor
        super().__init__(actor_id, actor_type, tags, capabilities, supervisor_id, state_dir)

        # Get the integration components
        self._actor_integration = integrate_actor_system()
        self._message_processor_integration = integrate_message_processor()
        self._starter_integration = integrate_actor_starter()

        # Get the dependency container
        self._container = get_container()

        # Get the enhanced tracker
        self._tracker = self._container.resolve(EnhancedTracker)

        # Get the initializer
        self._initializer = get_initializer()

        # Get the dependency resolver
        self._resolver = get_resolver()

        # Get the synchronization manager
        self._sync_manager = self._container.resolve(SynchronizationManager)

        # Register with the integration components
        asyncio.create_task(self._register_with_integration())

    async def _register_with_integration(self) -> None:
        """Register the actor with the integration components."""
        try:
            # Register with the actor integration
            await self._actor_integration.register_actor(self)

            # Register the message processor with the integration
            await self._message_processor_integration.register_processor(self._message_processor)

            # Register the actor starter with the integration
            await self._starter_integration.register_starter(self._starter)

            logger.info(f"Actor {self.actor_id} registered with integration components")
        except Exception as e:
            logger.error(f"Error registering actor {self.actor_id} with integration: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def initialize(self, config: Optional[Dict[str, Any]] = None,
                       timeout: float = 30.0,
                       dependencies: Optional[List[str]] = None) -> None:
        """
        Initialize the actor with enhanced diagnostics and error handling.

        This method extends the base initialize method with integration with
        the new initialization components.

        Args:
            config: Optional configuration dictionary for the actor
            timeout: Maximum time in seconds to wait for initialization to complete
            dependencies: Optional list of actor IDs that this actor depends on

        Raises:
            InitializationError: If initialization fails
            TimeoutError: If initialization times out
            DependencyError: If a dependency fails to initialize
        """
        # Record initialization start event
        start_time = time.time()
        await self._tracker.record_event(
            category=DiagnosticCategory.INITIALIZATION,
            level=DiagnosticLevel.BASIC,
            actor_id=self.actor_id,
            event_type="initializing",
            details={
                "timeout": timeout,
                "dependencies": dependencies
            }
        )

        try:
            # Update actor state in initializer
            await self._initializer.set_actor_state(
                self.actor_id,
                ActorLifecycleState.INITIALIZING,
                details={
                    "timeout": timeout,
                    "dependencies": dependencies
                }
            )

            # Register dependencies if specified
            if dependencies:
                for dependency_id in dependencies:
                    await self._resolver.register_dependency(
                        self.actor_id,
                        dependency_id,
                        critical=True
                    )

            # Call the base initialize method
            await super().initialize(config, timeout, dependencies)

            # Update actor state in initializer
            await self._initializer.set_actor_state(
                self.actor_id,
                ActorLifecycleState.INITIALIZED,
                details={"initialization_time": time.time() - start_time}
            )

            # Record initialization complete event
            await self._tracker.record_event(
                category=DiagnosticCategory.INITIALIZATION,
                level=DiagnosticLevel.BASIC,
                actor_id=self.actor_id,
                event_type="initialized",
                details={"initialization_time": time.time() - start_time}
            )

            # Notify the synchronization point
            await self._sync_manager.reach_point("initialization_complete", self.actor_id)

        except Exception as e:
            # Record initialization error
            error_details = traceback.format_exc()
            await self._tracker.record_event(
                category=DiagnosticCategory.ERROR,
                level=DiagnosticLevel.BASIC,
                actor_id=self.actor_id,
                event_type="initialization_error",
                details={
                    "error": str(e),
                    "error_details": error_details
                },
                error=e
            )

            # Update actor state in initializer
            await self._initializer.set_actor_state(
                self.actor_id,
                ActorLifecycleState.INITIALIZATION_FAILED,
                details={"error": str(e)},
                error=e
            )

            # Re-raise the exception
            raise

    async def start(self) -> None:
        """
        Start the actor with enhanced diagnostics and error handling.

        This method extends the base start method with integration with
        the new initialization components.

        Raises:
            InitializationError: If starting fails
            TimeoutError: If starting times out
            DependencyError: If a dependency fails to start
        """
        # Use the starter integration to start the actor
        try:
            await self._starter_integration.start_actor(
                self.actor_id,
                timeout=30.0,
                wait_for_dependencies=True
            )
        except Exception as e:
            # The starter integration will handle error recording and state updates
            # Just re-raise the exception
            raise

    async def receive(self, message: Message) -> None:
        """
        Receive a message with enhanced diagnostics.

        This method extends the base receive method with integration with
        the new diagnostics components.

        Args:
            message: The message to receive
        """
        # Record message received event
        await self._tracker.record_event(
            category=DiagnosticCategory.MESSAGING,
            level=DiagnosticLevel.DETAILED,
            actor_id=self.actor_id,
            event_type="message_received",
            details={
                "message_type": message.type.name,
                "sender_id": message.sender_id,
                "recipient_id": message.recipient_id,
                "payload_size": len(str(message.payload))
            }
        )

        # Call the base receive method
        await super().receive(message)

    async def process_messages(self) -> None:
        """
        Process messages with enhanced diagnostics.

        This method extends the base process_messages method with integration with
        the new diagnostics components.
        """
        # Record processing start event
        await self._tracker.record_event(
            category=DiagnosticCategory.MESSAGING,
            level=DiagnosticLevel.DETAILED,
            actor_id=self.actor_id,
            event_type="processing_messages_started"
        )

        try:
            # Call the base process_messages method
            await super().process_messages()
        except Exception as e:
            # Record processing error
            error_details = traceback.format_exc()
            await self._tracker.record_event(
                category=DiagnosticCategory.ERROR,
                level=DiagnosticLevel.BASIC,
                actor_id=self.actor_id,
                event_type="processing_messages_error",
                details={
                    "error": str(e),
                    "error_details": error_details
                },
                error=e
            )

            # Re-raise the exception
            raise

    async def stop(self) -> None:
        """
        Stop the actor with enhanced diagnostics and error handling.

        This method extends the base stop method with integration with
        the new diagnostics components.
        """
        # Record stop event
        await self._tracker.record_event(
            category=DiagnosticCategory.STATE,
            level=DiagnosticLevel.BASIC,
            actor_id=self.actor_id,
            event_type="stopping"
        )

        try:
            # Update actor state in initializer
            await self._initializer.set_actor_state(
                self.actor_id,
                ActorLifecycleState.STOPPING
            )

            # Call the base stop method
            await super().stop()

            # Update actor state in initializer
            await self._initializer.set_actor_state(
                self.actor_id,
                ActorLifecycleState.STOPPED
            )

            # Record stopped event
            await self._tracker.record_event(
                category=DiagnosticCategory.STATE,
                level=DiagnosticLevel.BASIC,
                actor_id=self.actor_id,
                event_type="stopped"
            )
        except Exception as e:
            # Record stop error
            error_details = traceback.format_exc()
            await self._tracker.record_event(
                category=DiagnosticCategory.ERROR,
                level=DiagnosticLevel.BASIC,
                actor_id=self.actor_id,
                event_type="stop_error",
                details={
                    "error": str(e),
                    "error_details": error_details
                },
                error=e
            )

            # Update actor state in initializer
            await self._initializer.set_actor_state(
                self.actor_id,
                ActorLifecycleState.STOP_FAILED,
                details={"error": str(e)},
                error=e
            )

            # Re-raise the exception
            raise

    async def send(self, recipient_id: str, msg_type: MessageType,
                 payload: Dict[str, Any], context: Optional[ContextWave] = None,
                 priority: int = 0, ttl: Optional[int] = None) -> None:
        """
        Send a message with enhanced diagnostics.

        This method extends the base send method with integration with
        the new diagnostics components.

        Args:
            recipient_id: ID of the recipient actor
            msg_type: Type of the message
            payload: Message payload
            context: Optional context to propagate
            priority: Optional priority for the message (0-9, higher is more important)
            ttl: Optional time-to-live in seconds
        """
        # Record message sent event
        await self._tracker.record_event(
            category=DiagnosticCategory.MESSAGING,
            level=DiagnosticLevel.DETAILED,
            actor_id=self.actor_id,
            event_type="message_sent",
            details={
                "message_type": msg_type.name,
                "recipient_id": recipient_id,
                "payload_size": len(str(payload)),
                "priority": priority,
                "ttl": ttl
            }
        )

        try:
            # Call the base send method
            await super().send(recipient_id, msg_type, payload, context, priority, ttl)
        except Exception as e:
            # Record send error
            error_details = traceback.format_exc()
            await self._tracker.record_event(
                category=DiagnosticCategory.ERROR,
                level=DiagnosticLevel.BASIC,
                actor_id=self.actor_id,
                event_type="message_send_error",
                details={
                    "message_type": msg_type.name,
                    "recipient_id": recipient_id,
                    "error": str(e),
                    "error_details": error_details
                },
                error=e
            )

            # Re-raise the exception
            raise

    async def add_dependency(self, dependency_id: str, critical: bool = True) -> None:
        """
        Add a dependency on another actor.

        This method registers a dependency between this actor and another actor.

        Args:
            dependency_id: ID of the actor that this actor depends on
            critical: Whether this is a critical dependency

        Raises:
            CircularDependencyError: If adding this dependency would create a cycle
        """
        try:
            # Register the dependency with the resolver
            await self._resolver.register_dependency(
                self.actor_id,
                dependency_id,
                critical=critical
            )

            # Record dependency added event
            await self._tracker.record_event(
                category=DiagnosticCategory.DEPENDENCY,
                level=DiagnosticLevel.BASIC,
                actor_id=self.actor_id,
                event_type="dependency_added",
                details={
                    "dependency_id": dependency_id,
                    "critical": critical
                }
            )

            logger.info(f"Actor {self.actor_id} added dependency on {dependency_id} (critical={critical})")
        except CircularDependencyError as e:
            # Record circular dependency error
            await self._tracker.record_event(
                category=DiagnosticCategory.ERROR,
                level=DiagnosticLevel.BASIC,
                actor_id=self.actor_id,
                event_type="circular_dependency_error",
                details={
                    "dependency_id": dependency_id,
                    "cycle": e.cycle,
                    "error": str(e)
                },
                error=e
            )

            # Re-raise the exception
            raise
        except Exception as e:
            # Record dependency error
            error_details = traceback.format_exc()
            await self._tracker.record_event(
                category=DiagnosticCategory.ERROR,
                level=DiagnosticLevel.BASIC,
                actor_id=self.actor_id,
                event_type="dependency_error",
                details={
                    "dependency_id": dependency_id,
                    "error": str(e),
                    "error_details": error_details
                },
                error=e
            )

            # Re-raise the exception
            raise

    async def get_dependencies(self) -> Set[str]:
        """
        Get the dependencies of this actor.

        Returns:
            Set of actor IDs that this actor depends on
        """
        return await self._resolver.get_dependencies(self.actor_id)

    async def get_critical_dependencies(self) -> Set[str]:
        """
        Get the critical dependencies of this actor.

        Returns:
            Set of actor IDs that this actor critically depends on
        """
        return await self._resolver.get_critical_dependencies(self.actor_id)

    async def get_optional_dependencies(self) -> Set[str]:
        """
        Get the optional dependencies of this actor.

        Returns:
            Set of actor IDs that this actor optionally depends on
        """
        return await self._resolver.get_optional_dependencies(self.actor_id)

    async def get_dependents(self) -> Set[str]:
        """
        Get the actors that depend on this actor.

        Returns:
            Set of actor IDs that depend on this actor
        """
        return await self._resolver.get_dependents(self.actor_id)