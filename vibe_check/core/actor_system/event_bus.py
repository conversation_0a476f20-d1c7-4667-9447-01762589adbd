"""
Event Bus for the Vibe Check Actor System.

This module provides an event-based communication system for coordinating
actors and components in the Vibe Check actor system. It decouples event
producers from consumers and enables more flexible coordination patterns.

The event bus is a core component of the improved actor system, addressing
issues with rigid synchronization mechanisms and enabling more flexible
communication between components.
"""

import asyncio
import logging
import time
import uuid
from collections import defaultdict
from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Any, Awaitable, Callable, Dict, List, Optional, Set, TypeVar, Generic, Union, cast

# Configure logging
logger = logging.getLogger(__name__)

# Type for event data
T = TypeVar('T')
EventCallback = Callable[[T], Union[None, Awaitable[None]]]


class EventPriority(Enum):
    """Priority levels for event processing."""
    LOW = auto()
    NORMAL = auto()
    HIGH = auto()
    CRITICAL = auto()


@dataclass
class Event(Generic[T]):
    """
    An event in the event bus system.
    
    Attributes:
        event_type: The type of the event
        data: The data associated with the event
        source: The source of the event (e.g., actor ID)
        timestamp: When the event was created
        id: Unique identifier for the event
        priority: Priority level for event processing
        metadata: Additional metadata for the event
    """
    event_type: str
    data: T
    source: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    priority: EventPriority = EventPriority.NORMAL
    metadata: Dict[str, Any] = field(default_factory=dict)


class EventBus:
    """
    An event bus for decoupled communication between components.
    
    The EventBus enables event-based communication between components,
    decoupling event producers from consumers and allowing for more
    flexible coordination patterns.
    
    Attributes:
        _subscribers: Mapping of event types to sets of callback functions
        _events: Cache of the most recent events by type
        _event_history: Optional history of events for debugging
        _max_history: Maximum number of events to keep in history
        _lock: Lock for thread-safe operations
    """
    
    def __init__(self, keep_history: bool = False, max_history: int = 100):
        """
        Initialize the event bus.
        
        Args:
            keep_history: Whether to keep a history of events
            max_history: Maximum number of events to keep in history
        """
        self._subscribers: Dict[str, Set[EventCallback]] = defaultdict(set)
        self._events: Dict[str, Event] = {}
        self._event_history: List[Event] = [] if keep_history else []
        self._max_history = max_history
        self._lock = asyncio.Lock()
        logger.debug("Initialized event bus")
        
    async def subscribe(self, event_type: str, callback: EventCallback) -> None:
        """
        Subscribe to an event type.
        
        Args:
            event_type: The type of event to subscribe to
            callback: The function to call when the event occurs
        """
        async with self._lock:
            self._subscribers[event_type].add(callback)
            logger.debug(f"Subscribed to event type: {event_type}")
            
            # If there's a cached event, immediately notify the new subscriber
            if event_type in self._events:
                event = self._events[event_type]
                try:
                    result = callback(event.data)
                    if asyncio.iscoroutine(result):
                        await result
                except Exception as e:
                    logger.error(f"Error in callback for event {event_type}: {e}")
    
    async def unsubscribe(self, event_type: str, callback: EventCallback) -> None:
        """
        Unsubscribe from an event type.
        
        Args:
            event_type: The type of event to unsubscribe from
            callback: The callback function to remove
        """
        async with self._lock:
            if event_type in self._subscribers and callback in self._subscribers[event_type]:
                self._subscribers[event_type].remove(callback)
                logger.debug(f"Unsubscribed from event type: {event_type}")
                
                # Clean up empty subscriber sets
                if not self._subscribers[event_type]:
                    del self._subscribers[event_type]
    
    async def publish(self, event: Event) -> None:
        """
        Publish an event to all subscribers.
        
        Args:
            event: The event to publish
        """
        # Store the event
        async with self._lock:
            self._events[event.event_type] = event
            
            if self._event_history is not None:
                self._event_history.append(event)
                # Trim history if needed
                if len(self._event_history) > self._max_history:
                    self._event_history = self._event_history[-self._max_history:]
        
        # Get subscribers (outside the lock to reduce contention)
        subscribers = set(self._subscribers.get(event.event_type, set()))
        
        # Notify subscribers
        for callback in subscribers:
            try:
                result = callback(event.data)
                if asyncio.iscoroutine(result):
                    await result
            except Exception as e:
                logger.error(f"Error in callback for event {event.event_type}: {e}")
        
        logger.debug(f"Published event: {event.event_type} from {event.source}")
    
    async def publish_simple(
        self, 
        event_type: str, 
        data: Any, 
        source: Optional[str] = None,
        priority: EventPriority = EventPriority.NORMAL,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Publish a simple event with the given type and data.
        
        This is a convenience method that creates and publishes an Event object.
        
        Args:
            event_type: The type of the event
            data: The data associated with the event
            source: The source of the event (e.g., actor ID)
            priority: Priority level for event processing
            metadata: Additional metadata for the event
        """
        event = Event(
            event_type=event_type,
            data=data,
            source=source,
            priority=priority,
            metadata=metadata or {}
        )
        await self.publish(event)
    
    async def wait_for(self, event_type: str, timeout: Optional[float] = None) -> Optional[Any]:
        """
        Wait for an event of the specified type.
        
        Args:
            event_type: The type of event to wait for
            timeout: Optional timeout in seconds
            
        Returns:
            The event data if received, None if timed out
            
        Raises:
            asyncio.TimeoutError: If the timeout expires
        """
        # Check if we already have this event
        if event_type in self._events:
            return self._events[event_type].data
        
        # Create a future to wait for the event
        future = asyncio.Future()
        
        # Define the callback
        async def callback(data: Any) -> None:
            if not future.done():
                future.set_result(data)
        
        # Subscribe to the event
        await self.subscribe(event_type, callback)
        
        try:
            # Wait for the event with timeout
            if timeout is not None:
                return await asyncio.wait_for(future, timeout)
            else:
                return await future
        except asyncio.TimeoutError:
            logger.warning(f"Timeout waiting for event: {event_type}")
            raise
        finally:
            # Always unsubscribe to avoid memory leaks
            await self.unsubscribe(event_type, callback)
    
    def get_last_event(self, event_type: str) -> Optional[Event]:
        """
        Get the most recent event of the specified type.
        
        Args:
            event_type: The type of event to retrieve
            
        Returns:
            The most recent event of the specified type, or None if not found
        """
        return self._events.get(event_type)
    
    def get_event_history(self) -> List[Event]:
        """
        Get the history of events.
        
        Returns:
            The list of historical events, or an empty list if history is disabled
        """
        return self._event_history.copy() if self._event_history is not None else []
    
    async def clear(self) -> None:
        """Clear all events and subscribers."""
        async with self._lock:
            self._subscribers.clear()
            self._events.clear()
            if self._event_history is not None:
                self._event_history.clear()
            logger.debug("Cleared event bus")


# Singleton instance
_event_bus: Optional[EventBus] = None


def get_event_bus(keep_history: bool = False, max_history: int = 100) -> EventBus:
    """
    Get the singleton event bus instance.
    
    Args:
        keep_history: Whether to keep a history of events
        max_history: Maximum number of events to keep in history
        
    Returns:
        The event bus instance
    """
    global _event_bus
    
    if _event_bus is None:
        _event_bus = EventBus(keep_history=keep_history, max_history=max_history)
        logger.info("Created new event bus")
        
    return _event_bus


def reset_event_bus() -> None:
    """Reset the singleton event bus instance."""
    global _event_bus
    _event_bus = None
    logger.info("Reset event bus")
