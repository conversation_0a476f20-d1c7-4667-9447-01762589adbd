# Vibe Check Actor System

The Vibe Check Actor System is a powerful, flexible, and resilient framework for building distributed, reactive applications. It implements the Contextual Adaptive Wave (CAW) paradigm through a choreographed actor model.

## Core Features

### 1. Enhanced Message Types
- Rich message taxonomy for different kinds of interactions
- Message priorities, TTL, and correlation IDs
- Context propagation through ContextWave
- Support for request-response and publish-subscribe patterns

### 2. Dynamic Actor Discovery
- Actor registry for runtime discovery of actors
- Tag-based and capability-based discovery
- Pattern matching for flexible actor selection
- Discovery callbacks for reactive programming

### 3. Reactive Streams
- Publish-subscribe messaging pattern
- Stream-based communication between actors
- Topic-based message routing
- Backpressure handling

### 4. Actor Supervision
- Hierarchical supervision of actors
- Automatic restart of failed actors
- Configurable restart strategies
- Health monitoring and heartbeats

### 5. Actor Pooling
- Dynamic scaling of actor pools
- Load balancing across pool members
- Resource-aware scaling
- Efficient task distribution

### 6. Distributed Actors
- Location transparency for actors
- Remote actor communication
- Node discovery and management
- Serialization of messages

### 7. Actor State Persistence
- Automatic state saving and recovery
- Configurable persistence strategies
- Crash recovery
- State migration

### 8. Actor Metrics
- Performance monitoring
- Resource usage tracking
- Message flow analysis
- Health status reporting

## Architecture

The actor system is built around these key components:

1. **Actor**: Base class for all actors, providing message handling, lifecycle management, and communication.
2. **Message**: Represents communication between actors, with context, payload, and metadata.
3. **ContextWave**: Propagates context through the system, implementing the CAW paradigm.
4. **ActorRegistry**: Enables dynamic discovery and communication between actors.
5. **SupervisorActor**: Monitors and manages other actors, providing resilience.
6. **ActorPool**: Manages pools of actors for scalability and load balancing.
7. **Node**: Enables distributed actor communication across machine boundaries.
8. **ActorInitializer**: Manages the two-phase initialization process with explicit synchronization.
9. **DependencyManager**: Manages actor dependencies and ensures proper initialization order.
10. **RegistrySynchronizer**: Ensures consistency between the registry and initializer.
11. **ErrorIsolator**: Provides circuit breakers and error isolation mechanisms.
12. **InitializationTracker**: Provides detailed diagnostics for the initialization process.
13. **SystemMonitor**: Monitors the health and performance of the actor system.
14. **SystemInitializer**: Provides a centralized way to initialize all components.

### Modular Architecture

The actor system has been refactored to use a modular architecture, with components extracted into separate modules for better maintainability and testability:

#### Messaging Components
- **MessageProcessor**: Handles message processing logic
- **MessageRouter**: Handles message routing logic
- **StreamManager**: Handles stream/publish-subscribe logic

#### Lifecycle Components
- **ActorStarter**: Handles actor starting logic
- **ActorTerminator**: Handles actor stopping logic
- **TaskManager**: Handles task management logic
- **LifecycleMessageProcessor**: Handles lifecycle message processing logic

#### Supervision Components
- **SupervisorComponent**: Handles supervision logic

#### Persistence Components
- **StateManager**: Handles state persistence logic

#### Metrics Components
- **MetricsCollector**: Handles metrics collection logic

#### Message Handling Components
- **MessageHandler**: Handles message dispatching logic

## Usage Examples

### Basic Actor

```python
from vibe_check.core.actor_system import Actor, Message, MessageType

class MyActor(Actor):
    def __init__(self, actor_id: str):
        super().__init__(actor_id)

    def _initialize_handlers(self):
        super()._initialize_handlers()
        self._message_handlers[MessageType.ANALYZE_PROJECT] = self.handle_analyze_project

    async def handle_analyze_project(self, payload, context):
        # Handle the message
        project_path = payload.get("project_path")
        print(f"Analyzing project at {project_path}")
```

### Actor Discovery

```python
from vibe_check.core.actor_system import get_registry

# Get actors by tag
registry = get_registry()
file_actors = registry.get_actors_by_tag("file")

# Get actors by capability
analysis_actors = registry.get_actors_by_capability("code_analysis")

# Get actors by pattern
worker_actors = registry.get_actors_by_pattern("worker_.*")
```

### Reactive Streams

```python
# Subscribe to a stream
await actor.subscribe("metrics")

# Publish to a stream
await actor.publish("metrics", MessageType.METRICS, {"cpu": 0.5, "memory": 0.3})

# Handle stream data
async def handle_stream_data(self, payload, context):
    stream_id = context.metadata.get("stream_id")
    if stream_id == "metrics":
        # Process metrics data
        cpu = payload.get("cpu")
        memory = payload.get("memory")
        print(f"CPU: {cpu}, Memory: {memory}")
```

### Actor Supervision

```python
# Create a supervisor
supervisor = SupervisorActor("supervisor")
await supervisor.start()

# Supervise an actor
await supervisor.supervise("worker_1", restart_on_failure=True)

# Get actor status
status = supervisor.get_actor_status("worker_1")
print(f"Actor status: {status}")
```

### Actor Pooling

```python
# Create an actor pool
pool = ActorPool(
    pool_id="worker_pool",
    actor_factory=lambda actor_id: WorkerActor(actor_id),
    min_size=5,
    max_size=20
)
await pool.start()

# Submit a task to the pool
result = await pool.submit(
    message_type=MessageType.PROCESS_FILE,
    payload={"file_path": "example.py"}
)
```

### Distributed Actors

```python
# Create a node
node = Node(host="localhost", port=8000)
await node.start()

# Connect to another node
await node.connect_to_node(host="remote-host", port=8000)

# Create a distributed actor
actor = DistributedActor("my_actor")
await actor.start()

# Send a message to a remote actor
await actor.send("remote_actor", MessageType.HELLO, {"message": "Hello!"})

# Invoke a method on a remote actor
result = await actor.invoke_remote("remote_actor", "get_status")
```

## Best Practices

1. **Use the Actor Registry**: Prefer using the registry for actor discovery rather than direct references.
2. **Implement Supervision**: Always use supervision for critical actors to ensure resilience.
3. **Use Reactive Streams**: For broadcast communication, use streams rather than direct messages.
4. **Handle Failures**: Implement proper error handling in message handlers.
5. **Monitor Performance**: Use the built-in metrics to track system performance.
6. **Persist State**: For stateful actors, implement state persistence for recovery.
7. **Scale with Pools**: Use actor pools for parallelizable workloads.
8. **Distribute Carefully**: Only distribute actors across nodes when necessary for scalability or isolation.

## Future Enhancements

### Recent Improvements

The actor system has been enhanced with the following components to improve its reliability, diagnostics, and error handling:

1. **Detailed Initialization Diagnostics**: Tracking and visualization of the initialization process
2. **Dependency Management**: Detection and prevention of circular dependencies
3. **Registry Synchronization**: Consistency between registry and initializer
4. **Error Isolation**: Circuit breakers and error categorization
5. **System Monitoring**: Health checks and resource usage tracking
6. **System Initializer**: Centralized initialization of all components

### Ongoing Refactoring

The actor system is currently being refactored to improve its modularity, testability, and performance. The following components have been extracted or are in the process of being extracted:

1. **MessageRouter**: Handles message routing logic (DONE)
2. **SupervisionHandler**: Enhances the existing SupervisorComponent (IN PROGRESS)
3. **StreamHandler**: Enhances the existing StreamManager (IN PROGRESS)
4. **StateManager**: Enhances the existing StateManager (IN PROGRESS)
5. **MetricsCollector**: Enhances the existing MetricsCollector (IN PROGRESS)

### Future Features

1. **Cluster Awareness**: Automatic cluster formation and management
2. **Actor Migration**: Moving actors between nodes for load balancing
3. **Distributed Transactions**: Coordinated transactions across multiple actors
4. **Enhanced Security**: Authentication and authorization for actor communication
5. **Visualization Tools**: Real-time visualization of actor system topology and metrics
6. **Adaptive Execution Mode**: Dynamically switching between parallel and sequential execution based on workload and resource availability
7. **Meta-Learning**: Optimizing actor configurations based on project types and sizes
