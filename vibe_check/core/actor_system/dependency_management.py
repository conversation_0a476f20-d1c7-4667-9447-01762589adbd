"""
Dependency Management Module
=======================

This module extends the dependency handling in the ActorInitializer to detect
and prevent circular dependencies, implement topological sorting for initialization
order, provide dependency validation, and visualize the dependency graph.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Set, Tuple, Any, Callable
import json
from pathlib import Path
import os

from .consolidated_initializer import ActorState, get_initializer
from .exceptions import ActorInitializationError

logger = logging.getLogger("vibe_check_dependency_management")


class CircularDependencyError(Exception):
    """Exception raised when a circular dependency is detected."""

    def __init__(self, cycle: List[str]):
        """
        Initialize the exception.

        Args:
            cycle: List of actor IDs forming the cycle
        """
        self.cycle = cycle
        cycle_str = " -> ".join(cycle)
        super().__init__(f"Circular dependency detected: {cycle_str}")


class DependencyManager:
    """
    Manages actor dependencies and ensures proper initialization order.

    This class extends the dependency handling in the ActorInitializer to detect
    and prevent circular dependencies, implement topological sorting for initialization
    order, provide dependency validation, and visualize the dependency graph.
    """

    def __init__(self, output_dir: Optional[str] = None):
        """
        Initialize the dependency manager.

        Args:
            output_dir: Optional directory to save dependency data
        """
        self._initializer = get_initializer()
        self._output_dir = output_dir
        self._lock = asyncio.Lock()
        self._initialized = False
        self._dependency_callbacks_registered = False
        self._critical_dependencies: Dict[str, Set[str]] = {}
        self._optional_dependencies: Dict[str, Set[str]] = {}
        self._initialization_order: List[str] = []

        # Create output directory if specified
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

    async def initialize(self) -> None:
        """
        Initialize the dependency manager.

        This method patches the register_dependency method of the initializer
        to detect circular dependencies and calculate initialization order.
        """
        async with self._lock:
            if self._initialized:
                logger.warning("DependencyManager already initialized")
                return

            # Register dependency callback with the initializer
            if not self._dependency_callbacks_registered:
                await self._register_dependency_callbacks()
                self._dependency_callbacks_registered = True

            self._initialized = True
            logger.info("DependencyManager initialized")

    async def _register_dependency_callbacks(self) -> None:
        """
        Register callbacks for dependency registration with the initializer.

        This method patches the register_dependency method of the initializer
        to detect circular dependencies and calculate initialization order.
        """
        # Store the original method
        original_register_dependency = self._initializer.register_dependency

        # Create a wrapper method
        def wrapped_register_dependency(actor_id: str, dependency_id: str, critical: bool = True) -> None:
            # Check for circular dependencies
            try:
                asyncio.create_task(self._check_circular_dependency(actor_id, dependency_id))
            except CircularDependencyError as e:
                logger.error(f"Circular dependency detected: {e}")
                # We'll still register the dependency but log the error
                # This allows the system to continue but with a warning

            # Track critical/optional dependencies
            if critical:
                if actor_id not in self._critical_dependencies:
                    self._critical_dependencies[actor_id] = set()
                self._critical_dependencies[actor_id].add(dependency_id)
            else:
                if actor_id not in self._optional_dependencies:
                    self._optional_dependencies[actor_id] = set()
                self._optional_dependencies[actor_id].add(dependency_id)

            # Call the original method
            original_register_dependency(actor_id, dependency_id)

            # Recalculate initialization order
            asyncio.create_task(self._calculate_initialization_order())

        # Replace the method
        self._initializer.register_dependency = wrapped_register_dependency

        logger.info("Registered dependency callbacks with initializer")

    async def _check_circular_dependency(self, actor_id: str, dependency_id: str) -> None:
        """
        Check if adding a dependency would create a circular dependency.

        Args:
            actor_id: ID of the dependent actor
            dependency_id: ID of the actor that must be ready first

        Raises:
            CircularDependencyError: If adding this dependency would create a cycle
        """
        # Get all dependencies
        dependencies = {}
        with self._initializer._state_lock:
            dependencies = {
                actor_id: set(deps) for actor_id, deps in self._initializer._dependencies.items()
            }

        # Add the new dependency
        if actor_id not in dependencies:
            dependencies[actor_id] = set()
        dependencies[actor_id].add(dependency_id)

        # Check for cycles using DFS
        visited = set()
        path = []
        path_set = set()

        def dfs(node: str) -> None:
            if node in path_set:
                # Found a cycle
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                raise CircularDependencyError(cycle)

            if node in visited:
                return

            visited.add(node)
            path.append(node)
            path_set.add(node)

            for neighbor in dependencies.get(node, set()):
                dfs(neighbor)

            path.pop()
            path_set.remove(node)

        # Check from the actor_id
        dfs(actor_id)

    async def _calculate_initialization_order(self) -> None:
        """
        Calculate the optimal initialization order for actors.

        This uses a topological sort to determine the order.
        """
        # Get all dependencies
        dependencies = {}
        with self._initializer._state_lock:
            dependencies = {
                actor_id: set(deps) for actor_id, deps in self._initializer._dependencies.items()
            }

        # Reset the order
        self._initialization_order = []

        # Create a copy of the dependencies for processing
        in_degree = {}
        for actor_id in dependencies:
            in_degree[actor_id] = 0

        # Calculate in-degree for each node
        for actor_id, deps in dependencies.items():
            for dep_id in deps:
                if dep_id not in in_degree:
                    in_degree[dep_id] = 0

        for actor_id, deps in dependencies.items():
            for dep_id in deps:
                in_degree[actor_id] += 1

        # Start with nodes that have no dependencies
        queue = [actor_id for actor_id, degree in in_degree.items() if degree == 0]

        # Process the queue
        while queue:
            current = queue.pop(0)
            self._initialization_order.append(current)

            # Reduce in-degree of neighbors
            for actor_id, deps in dependencies.items():
                if current in deps:
                    in_degree[actor_id] -= 1
                    if in_degree[actor_id] == 0:
                        queue.append(actor_id)

        # Check if we processed all nodes
        if len(self._initialization_order) != len(in_degree):
            logger.warning("Could not determine initialization order for all actors")
        else:
            logger.info(f"Calculated initialization order for {len(self._initialization_order)} actors")

    async def register_dependency(self, actor_id: str, dependency_id: str, critical: bool = True) -> None:
        """
        Register a dependency between actors.

        Args:
            actor_id: ID of the dependent actor
            dependency_id: ID of the actor that must be ready first
            critical: Whether this is a critical dependency (required for operation)

        Raises:
            CircularDependencyError: If adding this dependency would create a cycle
        """
        async with self._lock:
            # Check for circular dependencies
            await self._check_circular_dependency(actor_id, dependency_id)

            # Track critical/optional dependencies
            if critical:
                if actor_id not in self._critical_dependencies:
                    self._critical_dependencies[actor_id] = set()
                self._critical_dependencies[actor_id].add(dependency_id)
            else:
                if actor_id not in self._optional_dependencies:
                    self._optional_dependencies[actor_id] = set()
                self._optional_dependencies[actor_id].add(dependency_id)

            # Register with initializer
            self._initializer.register_dependency(actor_id, dependency_id)

            # Recalculate initialization order
            await self._calculate_initialization_order()

            logger.info(f"Registered dependency: {actor_id} depends on {dependency_id} (critical={critical})")

    async def get_initialization_order(self) -> List[str]:
        """
        Get the optimal initialization order for actors.

        Returns:
            List of actor IDs in initialization order
        """
        async with self._lock:
            return self._initialization_order.copy()

    async def get_critical_dependencies(self, actor_id: str) -> Set[str]:
        """
        Get critical dependencies for an actor.

        Args:
            actor_id: ID of the actor

        Returns:
            Set of critical dependency actor IDs
        """
        async with self._lock:
            return self._critical_dependencies.get(actor_id, set()).copy()

    async def get_optional_dependencies(self, actor_id: str) -> Set[str]:
        """
        Get optional dependencies for an actor.

        Args:
            actor_id: ID of the actor

        Returns:
            Set of optional dependency actor IDs
        """
        async with self._lock:
            return self._optional_dependencies.get(actor_id, set()).copy()

    async def export_dependency_graph(self, filename: Optional[str] = None) -> Optional[str]:
        """
        Export the dependency graph to a JSON file.

        Args:
            filename: Optional filename to save to

        Returns:
            Path to the saved file, or None if saving failed
        """
        if not self._output_dir:
            logger.warning("Cannot export dependency graph: no output directory specified")
            return None

        try:
            # Create a default filename if none provided
            if not filename:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"dependency_graph_{timestamp}.json"

            filepath = Path(self._output_dir) / filename

            # Get all dependencies
            dependencies = {}
            with self._initializer._state_lock:
                dependencies = {
                    actor_id: list(deps) for actor_id, deps in self._initializer._dependencies.items()
                }

            # Prepare data for serialization
            data = {
                "dependencies": dependencies,
                "critical_dependencies": {
                    actor_id: list(deps) for actor_id, deps in self._critical_dependencies.items()
                },
                "optional_dependencies": {
                    actor_id: list(deps) for actor_id, deps in self._optional_dependencies.items()
                },
                "initialization_order": self._initialization_order,
                "timestamp": time.time()
            }

            # Save to file
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Exported dependency graph to {filepath}")
            return str(filepath)

        except Exception as e:
            logger.error(f"Failed to export dependency graph: {e}")
            return None


# Singleton instance
_manager = None


def get_dependency_manager(output_dir: Optional[str] = None) -> DependencyManager:
    """
    Get the singleton dependency manager instance.

    Args:
        output_dir: Optional directory to save dependency data

    Returns:
        The dependency manager instance
    """
    global _manager

    if _manager is None:
        _manager = DependencyManager(output_dir)
        logger.info("Created new dependency manager")

    return _manager


def reset_dependency_manager() -> None:
    """Reset the singleton dependency manager instance."""
    global _manager

    _manager = None
    logger.info("Reset dependency manager")
