"""
Actor Initializer Compatibility Module
=====================================

This module provides compatibility imports for the actor initializer functionality.
It redirects imports to the ConsolidatedActorInitializer to maintain backward compatibility.

This is a compatibility layer that will be deprecated in future versions.
Use ConsolidatedActorInitializer directly instead.
"""

import warnings
from typing import Any, Dict, List, Optional, Set

# Issue deprecation warning
warnings.warn(
    "The actor_initializer module is deprecated. Use consolidated_initializer instead.",
    DeprecationWarning,
    stacklevel=2
)

# Import from the consolidated initializer
from .consolidated_initializer import (
    ConsolidatedActorInitializer,
    ActorInitializationError,
    ActorDependencyError,
    ActorTimeoutError,
    get_initializer,
    reset_initializer
)
from .actor_state import ActorState

# Provide compatibility aliases
ActorInitializer = ConsolidatedActorInitializer

# Re-export the main functions and classes
__all__ = [
    'ConsolidatedActorInitializer',
    'ActorInitializer',  # Compatibility alias
    'ActorInitializationError',
    'ActorDependencyError', 
    'ActorTimeoutError',
    'ActorState',
    'get_initializer',
    'reset_initializer'
]
