"""
CAW Actor Methods Module
=====================

This module contains the implementation of the message processing and
supervision methods for the CAWActor class.
"""

import asyncio
import json
import logging
import os
import time
import traceback
from typing import Any, Dict, List, Optional, Set, Tuple, Union, cast

from .actor_state import ActorState
from .circuit_breaker import CircuitBreaker, CircuitBreakerOpenError
from .context_wave import ContextWave
from .event_bus import EventBus, EventPriority
from .message import Message, MessageType

# Import exceptions
from .exceptions import (
    ActorSystemError,
    ActorInitializationError,
    ActorDependencyError,
    ActorMessageError,
    ActorTimeoutError,
    ActorNotFoundError
)

# Configure logging
logger = logging.getLogger("vibe_check_actor_system")


async def _process_messages(self) -> None:
    """
    Process messages from the mailbox.

    This method runs in a separate task and continuously processes
    messages from the mailbox until the actor is stopped.
    """
    logger.info(f"Actor {self.actor_id} started processing messages")

    try:
        while self._is_running:
            try:
                # Get the next message from the mailbox
                message = await self.mailbox.get()

                # Process the message
                await self._handle_message(message)

                # Mark the message as done
                self.mailbox.task_done()
            except asyncio.CancelledError:
                logger.info(f"Message processing for actor {self.actor_id} cancelled")
                break
            except Exception as e:
                logger.error(f"Error processing message for actor {self.actor_id}: {e}")
                logger.error(traceback.format_exc())

                # Update metrics
                self._metrics["errors"] += 1

                # Publish error event
                try:
                    await self._event_bus.publish_simple(
                        event_type=f"actor.message_error.{self.actor_id}",
                        data={
                            "actor_id": self.actor_id,
                            "error": str(e),
                            "timestamp": time.time()
                        },
                        source=self.actor_id,
                        priority=EventPriority.HIGH
                    )
                except Exception as event_error:
                    logger.error(f"Error publishing message error event: {event_error}")
    except Exception as e:
        logger.error(f"Fatal error in message processing for actor {self.actor_id}: {e}")
        logger.error(traceback.format_exc())

        # Transition to FAILED state
        try:
            await self.state_machine.transition_to(
                ActorState.FAILED,
                phase="message_processing",
                error=e
            )
        except Exception as state_error:
            logger.error(f"Error transitioning to FAILED state: {state_error}")

        # Update initializer state
        try:
            from .actor_initializer import get_initializer
            initializer = get_initializer()
            if initializer:
                await initializer.set_actor_state(
                    self.actor_id,
                    ActorState.FAILED,
                    error=e,
                    phase="message_processing"
                )
        except Exception as init_error:
            logger.error(f"Error updating initializer state: {init_error}")

        # Publish error event
        try:
            await self._event_bus.publish_simple(
                event_type=f"actor.fatal_error.{self.actor_id}",
                data={
                    "actor_id": self.actor_id,
                    "error": str(e),
                    "timestamp": time.time()
                },
                source=self.actor_id,
                priority=EventPriority.HIGH
            )
        except Exception as event_error:
            logger.error(f"Error publishing fatal error event: {event_error}")
    finally:
        logger.info(f"Actor {self.actor_id} stopped processing messages")


async def _handle_message(self, message: Message) -> None:
    """
    Handle a message from the mailbox.

    This method processes a message by dispatching it to the appropriate
    handler based on the message type.

    Args:
        message: The message to handle
    """
    # Get the message data and context
    message_data = message.data
    context_wave = message.context_wave or self.context_wave

    # Update metrics
    self._metrics["messages_received"] += 1
    self._metrics["last_activity"] = time.time()

    # Process the message with circuit breaker protection
    circuit_breaker = self._circuit_breakers["message_processing"]

    try:
        # Define the message processing function
        async def do_process() -> None:
            await self._process_message(message_data, context_wave)

        # Execute with circuit breaker
        start_time = time.time()
        await circuit_breaker.execute(do_process)
        processing_time = time.time() - start_time

        # Update metrics
        self._metrics["messages_processed"] += 1
        self._metrics["processing_time"] += processing_time
        self._metrics["avg_processing_time"] = (
            self._metrics["processing_time"] / self._metrics["messages_processed"]
        )

        # Publish message processed event
        await self._event_bus.publish_simple(
            event_type=f"actor.message_processed.{self.actor_id}",
            data={
                "actor_id": self.actor_id,
                "message_type": message_data.get("type", "unknown"),
                "processing_time": processing_time,
                "timestamp": time.time()
            },
            source=self.actor_id
        )
    except CircuitBreakerOpenError as e:
        # The circuit breaker is open, too many message processing failures
        logger.error(f"Circuit breaker open for message processing: {e}")

        # Publish message error event
        await self._event_bus.publish_simple(
            event_type=f"actor.message_error.{self.actor_id}",
            data={
                "actor_id": self.actor_id,
                "error": str(e),
                "circuit_breaker_open": True,
                "timestamp": time.time()
            },
            source=self.actor_id,
            priority=EventPriority.HIGH
        )

        # Update metrics
        self._metrics["errors"] += 1
    except Exception as e:
        # Handle other exceptions
        logger.error(f"Error processing message for actor {self.actor_id}: {e}")
        logger.error(traceback.format_exc())

        # Update metrics
        self._metrics["errors"] += 1

        # Publish message error event
        await self._event_bus.publish_simple(
            event_type=f"actor.message_error.{self.actor_id}",
            data={
                "actor_id": self.actor_id,
                "error": str(e),
                "timestamp": time.time()
            },
            source=self.actor_id,
            priority=EventPriority.HIGH
        )


async def _process_message(self, message_data: Dict[str, Any], context_wave: ContextWave) -> None:
    """
    Process a message.

    This method processes a message by dispatching it to the appropriate
    handler based on the message type.

    Args:
        message_data: The message data
        context_wave: The context wave for the message
    """
    # Get the message type
    message_type_str = message_data.get("type", "UNKNOWN")
    try:
        message_type = MessageType[message_type_str]
    except KeyError:
        logger.error(f"Unknown message type: {message_type_str}")
        return

    # Get the handler for this message type
    handler = self._message_handlers.get(message_type)
    if not handler:
        logger.error(f"No handler for message type: {message_type_str}")
        return

    # Call the handler
    try:
        await handler(message_data, context_wave)
    except Exception as e:
        logger.error(f"Error in handler for message type {message_type_str}: {e}")
        logger.error(traceback.format_exc())
        raise


async def _send_heartbeats(self: Any) -> None:
    """
    Send heartbeats to the supervisor.

    This method runs in a separate task and periodically sends heartbeat
    messages to the supervisor to indicate that the actor is still alive.
    """
    logger.info(f"Actor {self.actor_id} started sending heartbeats to supervisor {self._supervisor_id}")

    try:
        while self._is_running:
            try:
                # Send a heartbeat message to the supervisor
                if self._supervisor_id:
                    await self.send_message(
                        self._supervisor_id,
                        {
                            "type": "HEARTBEAT",
                            "actor_id": self.actor_id,
                            "timestamp": time.time(),
                            "metrics": self._metrics
                        }
                    )

                # Update last heartbeat time
                self._last_heartbeat = time.time()

                # Wait for the next heartbeat interval
                await asyncio.sleep(self._heartbeat_interval)
            except asyncio.CancelledError:
                logger.info(f"Heartbeat task for actor {self.actor_id} cancelled")
                break
            except Exception as e:
                logger.error(f"Error sending heartbeat for actor {self.actor_id}: {e}")
                logger.error(traceback.format_exc())

                # Wait a bit before retrying
                await asyncio.sleep(1.0)
    except Exception as e:
        logger.error(f"Fatal error in heartbeat task for actor {self.actor_id}: {e}")
        logger.error(traceback.format_exc())
    finally:
        logger.info(f"Actor {self.actor_id} stopped sending heartbeats")


async def _collect_metrics(self: Any) -> None:
    """
    Collect and report metrics.

    This method runs in a separate task and periodically collects and
    reports metrics about the actor's performance.
    """
    logger.info(f"Actor {self.actor_id} started collecting metrics")

    try:
        while self._is_running:
            try:
                # Update uptime
                self._metrics["uptime"] = time.time() - self._metrics["start_time"]

                # Publish metrics event
                await self._event_bus.publish_simple(
                    event_type=f"actor.metrics.{self.actor_id}",
                    data={
                        "actor_id": self.actor_id,
                        "metrics": self._metrics,
                        "timestamp": time.time()
                    },
                    source=self.actor_id
                )

                # Wait for the next metrics interval
                await asyncio.sleep(self._metrics_interval)
            except asyncio.CancelledError:
                logger.info(f"Metrics task for actor {self.actor_id} cancelled")
                break
            except Exception as e:
                logger.error(f"Error collecting metrics for actor {self.actor_id}: {e}")
                logger.error(traceback.format_exc())

                # Wait a bit before retrying
                await asyncio.sleep(1.0)
    except Exception as e:
        logger.error(f"Fatal error in metrics task for actor {self.actor_id}: {e}")
        logger.error(traceback.format_exc())
    finally:
        logger.info(f"Actor {self.actor_id} stopped collecting metrics")


async def send_message(self: Any, target_actor_id: str, message_data: Dict[str, Any],
                     context_wave: Optional[ContextWave] = None) -> None:
    """
    Send a message to another actor.

    This method sends a message to another actor by putting it in the
    target actor's mailbox.

    Args:
        target_actor_id: ID of the target actor
        message_data: Message data to send
        context_wave: Optional context wave to include with the message

    Raises:
        ActorNotFoundError: If the target actor is not found
        ActorMessageError: If there's an error sending the message
    """
    # Get the circuit breaker for sending messages
    circuit_breaker = self._circuit_breakers["send"]

    try:
        # Define the send function
        async def do_send() -> None:
            # Find the target actor
            target_actor = None

            # First check our known actors
            if target_actor_id in self._known_actors:
                target_actor = self._known_actors[target_actor_id]
            else:
                # Try to find the actor in the registry
                try:
                    from .actor_registry import get_registry
                    registry = get_registry()
                    target_actor = registry.get_actor(target_actor_id)

                    # Cache the actor for future use
                    if target_actor:
                        self._known_actors[target_actor_id] = target_actor
                except (ImportError, AttributeError) as e:
                    logger.error(f"Error getting registry: {e}")

            # If we couldn't find the actor, raise an error
            if not target_actor:
                raise ActorNotFoundError(
                    message=f"Target actor {target_actor_id} not found",
                    actor_id=target_actor_id
                )

            # Create the message
            # Extract message type from the message data
            message_type_str = message_data.get("type", "UNKNOWN")
            try:
                message_type = MessageType[message_type_str]
            except KeyError:
                logger.error(f"Unknown message type: {message_type_str}")
                message_type = MessageType.UNKNOWN

            # Create the message with the correct parameters
            message = Message(
                type=message_type,
                payload=message_data,
                context=context_wave or self.context_wave,
                sender_id=self.actor_id,
                recipient_id=target_actor_id
            )

            # Check if the target actor is ready
            if hasattr(target_actor, 'state_machine'):
                # If the target actor has a state machine, check if it's ready
                target_state = target_actor.state_machine.current_state
                if target_state != ActorState.READY:
                    # If the target actor is not ready, store the message for later
                    if hasattr(target_actor, '_pending_messages'):
                        target_actor._pending_messages.append(message_data)
                        logger.info(f"Target actor {target_actor_id} is not ready, storing message for later")
                        return

            # Put the message in the target actor's mailbox
            await target_actor.mailbox.put(message)

            # Update metrics
            self._metrics["messages_sent"] += 1

            # Publish message sent event
            await self._event_bus.publish_simple(
                event_type=f"actor.message_sent.{self.actor_id}",
                data={
                    "actor_id": self.actor_id,
                    "target_actor_id": target_actor_id,
                    "message_type": message_data.get("type", "unknown"),
                    "timestamp": time.time()
                },
                source=self.actor_id
            )

        # Execute with circuit breaker
        await circuit_breaker.execute(do_send)
    except CircuitBreakerOpenError as e:
        # The circuit breaker is open, too many send failures
        logger.error(f"Circuit breaker open for sending messages: {e}")

        # Publish message error event
        await self._event_bus.publish_simple(
            event_type=f"actor.message_error.{self.actor_id}",
            data={
                "actor_id": self.actor_id,
                "target_actor_id": target_actor_id,
                "error": str(e),
                "circuit_breaker_open": True,
                "timestamp": time.time()
            },
            source=self.actor_id,
            priority=EventPriority.HIGH
        )

        # Raise the exception
        raise ActorMessageError(
            message=f"Circuit breaker open for sending messages: {str(e)}",
            actor_id=self.actor_id,
            message_type="UNKNOWN",
            sender_id=target_actor_id,
            original_error=e
        ) from e
    except ActorNotFoundError:
        # Re-raise actor not found errors
        raise
    except Exception as e:
        # Handle other exceptions
        logger.error(f"Error sending message from actor {self.actor_id} to {target_actor_id}: {e}")
        logger.error(traceback.format_exc())

        # Publish message error event
        await self._event_bus.publish_simple(
            event_type=f"actor.message_error.{self.actor_id}",
            data={
                "actor_id": self.actor_id,
                "target_actor_id": target_actor_id,
                "error": str(e),
                "timestamp": time.time()
            },
            source=self.actor_id,
            priority=EventPriority.HIGH
        )

        # Wrap the exception in our custom exception
        raise ActorMessageError(
            message=str(e),
            actor_id=self.actor_id,
            message_type=message_data.get("type", "UNKNOWN"),
            sender_id=target_actor_id,
            original_error=e
        ) from e
