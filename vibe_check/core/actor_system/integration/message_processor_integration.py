"""
Message Processor Integration Module
===============================

This module provides integration between the new dependency injection,
enhanced diagnostics, and robust initialization components and the existing
MessageProcessor class.

The integration ensures that the MessageProcessor class works seamlessly with the
new components, providing improved reliability, diagnostics, and error handling.
"""

import asyncio
import logging
import time
import traceback
from typing import Any, Dict, List, Optional, Set, Type, TypeVar, Union, Callable, Awaitable

from ..messaging.processor import MessageProcessor
from ..message import Message, MessageType
from ..actor_state import ActorState
from ..dependency_injection import (
    DependencyContainer,
    get_container,
    Component,
    ComponentScope,
    Injectable
)
from ..diagnostics.enhanced_tracker import (
    EnhancedTracker,
    DiagnosticCategory,
    DiagnosticLevel
)
from ..initialization import (
    ActorInitializer,
    get_initializer,
    ActorLifecycleState
)

logger = logging.getLogger("vibe_check_message_processor_integration")

# Type variable for the MessageProcessor class
T = TypeVar('T', bound=MessageProcessor)


class MessageProcessorIntegration:
    """
    Provides integration between the MessageProcessor class and the new components.

    This class enhances the MessageProcessor class with the new dependency injection,
    enhanced diagnostics, and robust initialization components.

    Attributes:
        container: The dependency container
        tracker: The enhanced diagnostics tracker
        initializer: The actor initializer
    """

    def __init__(self):
        """Initialize the message processor integration."""
        self.container = get_container()
        self.tracker = self.container.resolve(EnhancedTracker)
        self.initializer = get_initializer()
        self._processor_registry: Dict[str, MessageProcessor] = {}
        self._processor_lock = asyncio.Lock()

    async def register_processor(self, processor: MessageProcessor) -> None:
        """
        Register a message processor with the integration.

        This method registers the message processor with the dependency container
        and enhanced tracker.

        Args:
            processor: The message processor to register
        """
        async with self._processor_lock:
            actor_id = processor.actor.actor_id
            
            # Register with the processor registry
            self._processor_registry[actor_id] = processor

            # Register with the dependency container
            self.container.register_instance(processor, name=f"{actor_id}_processor")

            # Record registration event with the tracker
            await self.tracker.record_event(
                category=DiagnosticCategory.INITIALIZATION,
                level=DiagnosticLevel.DETAILED,
                actor_id=actor_id,
                event_type="processor_registered",
                details={"processor_id": id(processor)}
            )

            logger.debug(f"Registered message processor for actor {actor_id} with integration")

    async def process_message(
        self,
        actor_id: str,
        message: Message,
        record_metrics: bool = True
    ) -> None:
        """
        Process a message with enhanced diagnostics.

        This method processes a message using the message processor and
        records diagnostic events.

        Args:
            actor_id: ID of the actor that owns the processor
            message: The message to process
            record_metrics: Whether to record metrics for the message

        Raises:
            ValueError: If the processor is not found
        """
        # Get the processor
        processor = self._processor_registry.get(actor_id)
        if not processor:
            raise ValueError(f"Message processor for actor {actor_id} not found")

        # Record message received event
        start_time = time.time()
        await self.tracker.record_event(
            category=DiagnosticCategory.MESSAGING,
            level=DiagnosticLevel.DETAILED,
            actor_id=actor_id,
            event_type="message_received",
            details={
                "message_type": message.type.name,
                "sender_id": message.sender_id,
                "recipient_id": message.recipient_id,
                "payload_size": len(str(message.payload))
            }
        )

        try:
            # Process the message
            await processor._process_message(message)

            # Record message processed event
            processing_time = time.time() - start_time
            await self.tracker.record_event(
                category=DiagnosticCategory.MESSAGING,
                level=DiagnosticLevel.DETAILED,
                actor_id=actor_id,
                event_type="message_processed",
                details={
                    "message_type": message.type.name,
                    "processing_time": processing_time,
                    "sender_id": message.sender_id,
                    "recipient_id": message.recipient_id
                }
            )

            # Record performance metrics
            if record_metrics:
                await self.tracker.record_event(
                    category=DiagnosticCategory.PERFORMANCE,
                    level=DiagnosticLevel.DETAILED,
                    actor_id=actor_id,
                    event_type="message_processing_time",
                    details={
                        "message_type": message.type.name,
                        "processing_time": processing_time
                    }
                )

        except Exception as e:
            # Record message processing error
            error_details = traceback.format_exc()
            await self.tracker.record_event(
                category=DiagnosticCategory.ERROR,
                level=DiagnosticLevel.BASIC,
                actor_id=actor_id,
                event_type="message_processing_error",
                details={
                    "message_type": message.type.name,
                    "error": str(e),
                    "error_details": error_details,
                    "sender_id": message.sender_id,
                    "recipient_id": message.recipient_id
                },
                error=e
            )

            # Re-raise the exception
            raise

    async def process_pending_messages(self, actor_id: str) -> None:
        """
        Process pending messages with enhanced diagnostics.

        This method processes pending messages using the message processor and
        records diagnostic events.

        Args:
            actor_id: ID of the actor that owns the processor

        Raises:
            ValueError: If the processor is not found
        """
        # Get the processor
        processor = self._processor_registry.get(actor_id)
        if not processor:
            raise ValueError(f"Message processor for actor {actor_id} not found")

        # Record pending messages event
        pending_count = len(processor.actor._pending_messages)
        if pending_count > 0:
            await self.tracker.record_event(
                category=DiagnosticCategory.MESSAGING,
                level=DiagnosticLevel.BASIC,
                actor_id=actor_id,
                event_type="processing_pending_messages",
                details={"pending_count": pending_count}
            )

            # Process pending messages
            start_time = time.time()
            await processor._process_pending_messages()
            processing_time = time.time() - start_time

            # Record pending messages processed event
            await self.tracker.record_event(
                category=DiagnosticCategory.MESSAGING,
                level=DiagnosticLevel.BASIC,
                actor_id=actor_id,
                event_type="pending_messages_processed",
                details={
                    "pending_count": pending_count,
                    "processing_time": processing_time
                }
            )


# Singleton instance
_integration: Optional[MessageProcessorIntegration] = None


def integrate_message_processor() -> MessageProcessorIntegration:
    """
    Integrate the message processor with the new components.

    Returns:
        The message processor integration instance
    """
    global _integration

    if _integration is None:
        _integration = MessageProcessorIntegration()
        logger.info("Integrated message processor with new components")

    return _integration


def get_integrated_message_processor() -> Optional[MessageProcessorIntegration]:
    """
    Get the integrated message processor.

    Returns:
        The message processor integration instance, or None if not integrated
    """
    return _integration


def reset_integrated_message_processor() -> None:
    """Reset the integrated message processor."""
    global _integration

    _integration = None
    logger.info("Reset integrated message processor")
