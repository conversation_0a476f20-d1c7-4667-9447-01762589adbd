"""
Actor System Integration Package
===========================

This package provides integration between the new dependency injection,
enhanced diagnostics, and robust initialization components and the existing
actor system.

The integration ensures that the new components work seamlessly with the
existing actor system, providing improved reliability, diagnostics, and
error handling.
"""

from .actor_integration import (
    integrate_actor_system,
    get_integrated_actor_system,
    reset_integrated_actor_system
)
from .message_processor_integration import (
    integrate_message_processor,
    get_integrated_message_processor,
    reset_integrated_message_processor
)
from .starter_integration import (
    integrate_actor_starter,
    get_integrated_actor_starter,
    reset_integrated_actor_starter
)

__all__ = [
    'integrate_actor_system',
    'get_integrated_actor_system',
    'reset_integrated_actor_system',
    'integrate_message_processor',
    'get_integrated_message_processor',
    'reset_integrated_message_processor',
    'integrate_actor_starter',
    'get_integrated_actor_starter',
    'reset_integrated_actor_starter'
]
