"""
Actor Integration Module
====================

This module provides integration between the new dependency injection,
enhanced diagnostics, and robust initialization components and the existing
Actor class.

The integration ensures that the Actor class works seamlessly with the
new components, providing improved reliability, diagnostics, and error handling.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Set, Type, TypeVar, Union, Callable, Awaitable

from ..actor import Actor
from ..actor_state import ActorState
from ..dependency_injection import (
    DependencyContainer,
    get_container,
    Component,
    ComponentScope,
    Injectable
)
from ..diagnostics.enhanced_tracker import (
    EnhancedTracker,
    DiagnosticCategory,
    DiagnosticLevel
)
from ..initialization import (
    ActorInitializer,
    get_initializer,
    ActorLifecycleState,
    DependencyResolver,
    get_resolver,
    SynchronizationManager
)

logger = logging.getLogger("vibe_check_actor_integration")

# Type variable for the Actor class
T = TypeVar('T', bound=Actor)


class ActorIntegration:
    """
    Provides integration between the Actor class and the new components.

    This class enhances the Actor class with the new dependency injection,
    enhanced diagnostics, and robust initialization components.

    Attributes:
        container: The dependency container
        tracker: The enhanced diagnostics tracker
        initializer: The actor initializer
        resolver: The dependency resolver
        sync_manager: The synchronization manager
    """

    def __init__(self):
        """Initialize the actor integration."""
        self.container = get_container()
        self.tracker = self.container.resolve(EnhancedTracker)
        self.initializer = get_initializer()
        self.resolver = get_resolver()
        self.sync_manager = self.container.resolve(SynchronizationManager)
        self._actor_registry: Dict[str, Actor] = {}
        self._actor_types: Dict[str, Type[Actor]] = {}
        self._actor_lock = asyncio.Lock()

    async def register_actor(self, actor: Actor) -> None:
        """
        Register an actor with the integration.

        This method registers the actor with the dependency container,
        initializer, and enhanced tracker.

        Args:
            actor: The actor to register
        """
        async with self._actor_lock:
            # Register with the actor registry
            self._actor_registry[actor.actor_id] = actor

            # Register with the dependency container
            self.container.register_instance(actor, name=actor.actor_id)

            # Register with the initializer
            await self.initializer.register_actor(
                actor.actor_id,
                actor_type=actor.actor_type,
                tags=actor.tags
            )

            # Record registration event with the tracker
            await self.tracker.record_event(
                category=DiagnosticCategory.INITIALIZATION,
                level=DiagnosticLevel.BASIC,
                actor_id=actor.actor_id,
                event_type="registered",
                details={
                    "actor_type": actor.actor_type,
                    "tags": list(actor.tags),
                    "capabilities": list(getattr(actor, "_capabilities", set())),
                    "supervisor_id": getattr(actor, "_supervisor_id", None)
                }
            )

            logger.info(f"Registered actor {actor.actor_id} with integration")

    async def register_actor_type(self, actor_type: Type[Actor], name: Optional[str] = None) -> None:
        """
        Register an actor type with the integration.

        This method registers the actor type with the dependency container.

        Args:
            actor_type: The actor type to register
            name: Optional name for the actor type
        """
        async with self._actor_lock:
            # Register with the actor type registry
            type_name = name or actor_type.__name__
            self._actor_types[type_name] = actor_type

            # Register with the dependency container
            self.container.register(actor_type, scope=ComponentScope.TRANSIENT, name=type_name)

            logger.info(f"Registered actor type {type_name} with integration")

    async def register_dependency(
        self,
        actor_id: str,
        dependency_id: str,
        critical: bool = True
    ) -> None:
        """
        Register a dependency between actors.

        This method registers a dependency between two actors with the
        dependency resolver and enhanced tracker.

        Args:
            actor_id: ID of the dependent actor
            dependency_id: ID of the actor that must be ready first
            critical: Whether this is a critical dependency
        """
        # Register with the dependency resolver
        await self.resolver.register_dependency(actor_id, dependency_id, critical)

        # Record dependency with the tracker
        await self.tracker.record_dependency(actor_id, dependency_id)

        logger.info(f"Registered dependency: {actor_id} depends on {dependency_id} (critical={critical})")

    async def get_actor(self, actor_id: str) -> Optional[Actor]:
        """
        Get an actor by ID.

        Args:
            actor_id: ID of the actor to get

        Returns:
            The actor, or None if not found
        """
        return self._actor_registry.get(actor_id)

    async def create_actor(
        self,
        actor_type: Union[str, Type[Actor]],
        actor_id: str,
        **kwargs
    ) -> Actor:
        """
        Create and register an actor.

        This method creates an actor of the specified type, registers it
        with the integration, and returns it.

        Args:
            actor_type: Type of actor to create (name or class)
            actor_id: ID for the new actor
            **kwargs: Additional arguments to pass to the actor constructor

        Returns:
            The created actor

        Raises:
            ValueError: If the actor type is not registered
        """
        # Get the actor type
        if isinstance(actor_type, str):
            if actor_type not in self._actor_types:
                raise ValueError(f"Actor type {actor_type} not registered")
            actor_class = self._actor_types[actor_type]
        else:
            actor_class = actor_type

        # Create the actor
        actor = actor_class(actor_id=actor_id, **kwargs)

        # Register the actor
        await self.register_actor(actor)

        return actor

    async def initialize_actor(self, actor_id: str, **kwargs) -> None:
        """
        Initialize an actor.

        This method initializes an actor using the new initialization process.

        Args:
            actor_id: ID of the actor to initialize
            **kwargs: Additional arguments to pass to the initialize method

        Raises:
            ValueError: If the actor is not found
        """
        # Get the actor
        actor = await self.get_actor(actor_id)
        if not actor:
            raise ValueError(f"Actor {actor_id} not found")

        # Initialize the actor
        await actor.initialize(**kwargs)

    async def start_actor(self, actor_id: str, **kwargs) -> None:
        """
        Start an actor.

        This method starts an actor using the new initialization process.

        Args:
            actor_id: ID of the actor to start
            **kwargs: Additional arguments to pass to the start method

        Raises:
            ValueError: If the actor is not found
        """
        # Get the actor
        actor = await self.get_actor(actor_id)
        if not actor:
            raise ValueError(f"Actor {actor_id} not found")

        # Start the actor
        await actor.start(**kwargs)

    async def stop_actor(self, actor_id: str) -> None:
        """
        Stop an actor.

        This method stops an actor and updates its state in the initializer.

        Args:
            actor_id: ID of the actor to stop

        Raises:
            ValueError: If the actor is not found
        """
        # Get the actor
        actor = await self.get_actor(actor_id)
        if not actor:
            raise ValueError(f"Actor {actor_id} not found")

        # Stop the actor
        await actor.stop()


# Singleton instance
_integration: Optional[ActorIntegration] = None


def integrate_actor_system() -> ActorIntegration:
    """
    Integrate the actor system with the new components.

    Returns:
        The actor integration instance
    """
    global _integration

    if _integration is None:
        _integration = ActorIntegration()
        logger.info("Integrated actor system with new components")

    return _integration


def get_integrated_actor_system() -> Optional[ActorIntegration]:
    """
    Get the integrated actor system.

    Returns:
        The actor integration instance, or None if not integrated
    """
    return _integration


def reset_integrated_actor_system() -> None:
    """Reset the integrated actor system."""
    global _integration

    _integration = None
    logger.info("Reset integrated actor system")
