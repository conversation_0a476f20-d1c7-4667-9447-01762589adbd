"""
Actor Starter Integration Module
===========================

This module provides integration between the new dependency injection,
enhanced diagnostics, and robust initialization components and the existing
ActorStarter class.

The integration ensures that the ActorStarter class works seamlessly with the
new components, providing improved reliability, diagnostics, and error handling.
"""

import asyncio
import logging
import time
import traceback
from typing import Any, Dict, List, Optional, Set, Type, TypeVar, Union, Callable, Awaitable

from ..lifecycle.starter import ActorStarter
from ..actor_state import ActorState
from ..dependency_injection import (
    DependencyContainer,
    get_container,
    Component,
    ComponentScope,
    Injectable
)
from ..diagnostics.enhanced_tracker import (
    EnhancedTracker,
    DiagnosticCategory,
    DiagnosticLevel
)
from ..initialization import (
    ActorInitializer,
    get_initializer,
    ActorLifecycleState,
    DependencyResolver,
    get_resolver,
    SynchronizationManager
)

logger = logging.getLogger("vibe_check_starter_integration")

# Type variable for the ActorStarter class
T = TypeVar('T', bound=ActorStarter)


class ActorStarterIntegration:
    """
    Provides integration between the ActorStarter class and the new components.

    This class enhances the ActorStarter class with the new dependency injection,
    enhanced diagnostics, and robust initialization components.

    Attributes:
        container: The dependency container
        tracker: The enhanced diagnostics tracker
        initializer: The actor initializer
        resolver: The dependency resolver
        sync_manager: The synchronization manager
    """

    def __init__(self):
        """Initialize the actor starter integration."""
        self.container = get_container()
        self.tracker = self.container.resolve(EnhancedTracker)
        self.initializer = get_initializer()
        self.resolver = get_resolver()
        self.sync_manager = self.container.resolve(SynchronizationManager)
        self._starter_registry: Dict[str, ActorStarter] = {}
        self._starter_lock = asyncio.Lock()

    async def register_starter(self, starter: ActorStarter) -> None:
        """
        Register an actor starter with the integration.

        This method registers the actor starter with the dependency container
        and enhanced tracker.

        Args:
            starter: The actor starter to register
        """
        async with self._starter_lock:
            actor_id = starter.actor.actor_id
            
            # Register with the starter registry
            self._starter_registry[actor_id] = starter

            # Register with the dependency container
            self.container.register_instance(starter, name=f"{actor_id}_starter")

            # Record registration event with the tracker
            await self.tracker.record_event(
                category=DiagnosticCategory.INITIALIZATION,
                level=DiagnosticLevel.DETAILED,
                actor_id=actor_id,
                event_type="starter_registered",
                details={"starter_id": id(starter)}
            )

            logger.debug(f"Registered actor starter for actor {actor_id} with integration")

    async def start_actor(
        self,
        actor_id: str,
        timeout: float = 30.0,
        wait_for_dependencies: bool = True
    ) -> None:
        """
        Start an actor with enhanced diagnostics and dependency management.

        This method starts an actor using the actor starter and records
        diagnostic events. It also handles dependency management and
        synchronization.

        Args:
            actor_id: ID of the actor to start
            timeout: Maximum time in seconds to wait for the actor to start
            wait_for_dependencies: Whether to wait for dependencies to be ready

        Raises:
            ValueError: If the starter is not found
        """
        # Get the starter
        starter = self._starter_registry.get(actor_id)
        if not starter:
            raise ValueError(f"Actor starter for actor {actor_id} not found")

        # Record start event
        start_time = time.time()
        await self.tracker.record_event(
            category=DiagnosticCategory.INITIALIZATION,
            level=DiagnosticLevel.BASIC,
            actor_id=actor_id,
            event_type="actor_starting",
            details={"timeout": timeout}
        )

        try:
            # Update actor state in initializer
            await self.initializer.set_actor_state(
                actor_id,
                ActorLifecycleState.STARTING,
                details={"timeout": timeout}
            )

            # Wait for dependencies if needed
            if wait_for_dependencies:
                # Get dependencies
                dependencies = await self.resolver.get_dependencies(actor_id)
                if dependencies:
                    # Create a synchronization point for dependencies
                    sync_point = await self.sync_manager.create_point(
                        f"{actor_id}_dependencies",
                        required_actors=dependencies
                    )

                    # Wait for dependencies to be ready
                    dependency_timeout = timeout / 2
                    logger.info(f"Waiting for dependencies of actor {actor_id}: {dependencies}")
                    dependencies_ready = await self.sync_manager.wait_for_point(
                        f"{actor_id}_dependencies",
                        timeout=dependency_timeout
                    )

                    if not dependencies_ready:
                        # Get missing dependencies
                        missing = sync_point.get_missing_actors()
                        logger.warning(f"Timeout waiting for dependencies of actor {actor_id}: {missing}")

                        # Record dependency timeout event
                        await self.tracker.record_event(
                            category=DiagnosticCategory.ERROR,
                            level=DiagnosticLevel.BASIC,
                            actor_id=actor_id,
                            event_type="dependency_timeout",
                            details={
                                "missing_dependencies": list(missing),
                                "timeout": dependency_timeout
                            }
                        )

                        # Raise an exception if any critical dependencies are missing
                        critical_deps = await self.resolver.get_critical_dependencies(actor_id)
                        critical_missing = critical_deps.intersection(missing)
                        if critical_missing:
                            raise TimeoutError(
                                f"Timeout waiting for critical dependencies: {critical_missing}"
                            )

            # Start the actor
            await starter.start(timeout=timeout)

            # Update actor state in initializer
            await self.initializer.set_actor_state(
                actor_id,
                ActorLifecycleState.STARTED,
                details={"start_time": time.time() - start_time}
            )

            # Record actor started event
            await self.tracker.record_event(
                category=DiagnosticCategory.INITIALIZATION,
                level=DiagnosticLevel.BASIC,
                actor_id=actor_id,
                event_type="actor_started",
                details={"start_time": time.time() - start_time}
            )

            # Update actor state to ready
            await self.initializer.set_actor_state(
                actor_id,
                ActorLifecycleState.READY,
                details={"ready_time": time.time() - start_time}
            )

            # Notify the synchronization point
            await self.sync_manager.reach_point("ready_complete", actor_id)

            # Record actor ready event
            await self.tracker.record_event(
                category=DiagnosticCategory.STATE,
                level=DiagnosticLevel.BASIC,
                actor_id=actor_id,
                event_type="actor_ready",
                details={"ready_time": time.time() - start_time}
            )

        except Exception as e:
            # Record start error
            error_details = traceback.format_exc()
            await self.tracker.record_event(
                category=DiagnosticCategory.ERROR,
                level=DiagnosticLevel.BASIC,
                actor_id=actor_id,
                event_type="actor_start_error",
                details={
                    "error": str(e),
                    "error_details": error_details
                },
                error=e
            )

            # Update actor state in initializer
            await self.initializer.set_actor_state(
                actor_id,
                ActorLifecycleState.START_FAILED,
                details={"error": str(e)},
                error=e
            )

            # Re-raise the exception
            raise


# Singleton instance
_integration: Optional[ActorStarterIntegration] = None


def integrate_actor_starter() -> ActorStarterIntegration:
    """
    Integrate the actor starter with the new components.

    Returns:
        The actor starter integration instance
    """
    global _integration

    if _integration is None:
        _integration = ActorStarterIntegration()
        logger.info("Integrated actor starter with new components")

    return _integration


def get_integrated_actor_starter() -> Optional[ActorStarterIntegration]:
    """
    Get the integrated actor starter.

    Returns:
        The actor starter integration instance, or None if not integrated
    """
    return _integration


def reset_integrated_actor_starter() -> None:
    """Reset the integrated actor starter."""
    global _integration

    _integration = None
    logger.info("Reset integrated actor starter")
