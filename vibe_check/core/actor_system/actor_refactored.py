"""
Actor Module (Refactored)
=======================

This module defines the Actor base class for the actor system.
Actors are autonomous components that communicate via messages
and implement the CAW principle of choreographed interactions.

The actor system replaces the previous pipeline architecture with
a more flexible, resilient approach that allows for parallel execution
and contextual adaptation.

Enhanced with features for dynamic discovery, supervision, state persistence,
metrics collection, and distributed execution.

The Actor class has been refactored to use a modular architecture, with components
extracted into separate modules for better maintainability and testability:
- MessageProcessor: Handles message processing logic (messaging/processor.py)
- SupervisorComponent: Handles supervision logic (supervision/supervisor.py)
- StreamManager: Handles stream/publish-subscribe logic (messaging/streams.py)
- StateManager: Handles state persistence logic (persistence/state_manager.py)
- MetricsCollector: Handles metrics collection logic (metrics/collector.py)
- ActorStarter: Handles actor starting logic (lifecycle/starter.py)
- ActorTerminator: Handles actor stopping logic (lifecycle/terminator.py)
- TaskManager: Handles task management logic (lifecycle/task_manager.py)

This modular architecture reduces the complexity of the Actor class and makes
it easier to test and maintain. Each component is responsible for a specific
aspect of the actor's lifecycle, and the Actor class delegates to these components
as needed.
"""

import asyncio
import logging
import time
from typing import Any, Awaitable, Callable, Dict, List, Optional, Set, Type, cast

from .actor_base import ActorBase
from .context_wave import ContextWave
from .lifecycle.message_processor import MessageProcessor as LifecycleMessageProcessor
from .lifecycle.starter import ActorStarter
from .lifecycle.task_manager import TaskManager
from .lifecycle.terminator import ActorTerminator
from .message import Message, MessageType
from .message_handling.handler import MessageHandler
from .messaging.processor import MessageProcessor
from .messaging.router import MessageRouter
from .messaging.streams import StreamManager
from .metrics.collector import MetricsCollector
from .persistence.state_manager import StateManager
from .protocols import ActorProtocol
from .supervision.supervisor import SupervisorComponent

# Configure logging
logger = logging.getLogger("vibe_check_actor_system")

# Import ActorState enum from actor_initializer
try:
    from .actor_initializer import ActorInitializationError, ActorState, get_initializer
except ImportError:
    # Define a fallback ActorState enum if actor_initializer is not available
    from enum import Enum
    # Use a different name to avoid name collision
    class _FallbackActorState(Enum):
        """Fallback enum representing the possible states of an actor during initialization."""
        CREATED = "created"
        INITIALIZING = "initializing"
        INITIALIZED = "initialized"
        STARTING = "starting"
        READY = "ready"
        STOPPING = "stopping"
        STOPPED = "stopped"
        FAILED = "failed"
        ROLLBACK = "rollback"

    # Define ActorState as a type variable
    ActorState = _FallbackActorState  # type: ignore

    # Use a different name to avoid name collision
    class _FallbackActorInitializationError(Exception):
        """Fallback exception if actor_initializer is not available."""
        def __init__(self, actor_id: str, phase: str, state: _FallbackActorState,
                     message: str, original_error: Optional[Exception] = None) -> None:
            self.actor_id = actor_id
            self.phase = phase
            self.state = state
            self.original_error = original_error
            super().__init__(f"Actor {actor_id} failed during {phase} phase: {message}")

    # Define ActorInitializationError as a type variable
    ActorInitializationError = _FallbackActorInitializationError  # type: ignore

    def get_initializer() -> Optional[Any]:  # type: ignore
        """Fallback function if actor_initializer is not available."""
        return None


class Actor(ActorBase, ActorProtocol):
    """
    Base actor class for the CAW choreography system.

    Implements the CAW principle of adaptive actors that communicate
    via messages with propagating context.

    Enhanced with features for:
    - Dynamic discovery via the actor registry
    - Supervision and monitoring
    - State persistence
    - Metrics collection
    - Distributed execution
    - Reactive streams

    This class has been refactored to use a modular architecture, with components
    extracted into separate modules for better maintainability and testability:
    - MessageProcessor: Handles message processing logic
    - SupervisorComponent: Handles supervision logic
    - StreamManager: Handles stream/publish-subscribe logic
    - StateManager: Handles state persistence logic
    - MetricsCollector: Handles metrics collection logic

    Implementation:
        The Actor class now delegates to specialized components for different
        aspects of its functionality. This reduces the complexity of the Actor class
        and makes it easier to test and maintain.
    """

    def __init__(self, actor_id: str, actor_type: Optional[str] = None,
                tags: Optional[Set[str]] = None, capabilities: Optional[Set[str]] = None,
                supervisor_id: Optional[str] = None, state_dir: Optional[str] = None):
        """
        Initialize the actor.

        Args:
            actor_id: Unique ID for this actor
            actor_type: Optional type of the actor for discovery
            tags: Optional set of tags for discovery
            capabilities: Optional set of capabilities for discovery
            supervisor_id: Optional ID of the supervisor actor
            state_dir: Optional directory for state persistence
        """
        self._actor_id = actor_id
        self._actor_type = actor_type or self.__class__.__name__
        self._tags = tags or set()
        self._capabilities = capabilities or set()
        self._supervisor_id = supervisor_id
        self.state_dir = state_dir

        # Core actor state
        self.mailbox: asyncio.Queue[Message] = asyncio.Queue()
        self.context_wave: ContextWave = ContextWave()
        self._is_running: bool = False
        self._known_actors: Dict[str, 'Actor'] = {}
        self._message_handlers: Dict[MessageType, Callable[[Dict[str, Any], ContextWave], Awaitable[None]]] = {}
        self._process_task: Optional[asyncio.Task[None]] = None  # Task for the message processing loop
        self._initialization_complete: bool = False
        self._ready: bool = False
        self._pending_messages: List[Message] = []  # Messages received before actor is ready
        self._ready_event: asyncio.Event = asyncio.Event()  # Event to signal when actor is ready

        # Metrics state
        self._metrics: Dict[str, Any] = {
            "messages_received": 0,
            "messages_sent": 0,
            "messages_processed": 0,
            "errors": 0,
            "processing_time": 0.0,
            "avg_processing_time": 0.0,
            "last_activity": time.time(),
            "uptime": 0.0,
            "start_time": time.time(),
            "restarts": 0
        }

        # Component managers
        # Use type: ignore to suppress type errors due to the circular dependency
        # between Actor and its components
        self._message_processor = MessageProcessor(self)  # type: ignore
        self._message_handler = MessageHandler(self)  # type: ignore
        self._message_router = MessageRouter(self)  # type: ignore
        self._supervisor_component = SupervisorComponent(self)  # type: ignore
        self._stream_manager = StreamManager(self)  # type: ignore
        self._state_manager = StateManager(self, state_dir)  # type: ignore
        self._metrics_collector = MetricsCollector(self)  # type: ignore

        # Lifecycle components
        self._starter = ActorStarter(self)  # type: ignore
        self._terminator = ActorTerminator(self)  # type: ignore
        self._task_manager = TaskManager(self)  # type: ignore
        self._lifecycle_message_processor = LifecycleMessageProcessor(self)  # type: ignore

        # Initialize handlers
        self._initialize_handlers()

        # Register with registry if available
        try:
            from .actor_registry import get_registry
            registry = get_registry()
            registry.register_actor(
                actor_id=self.actor_id,
                actor=self,  # type: ignore
                actor_type=self.actor_type,
                tags=self.tags,
                capabilities=self.capabilities
            )
            logger.info(f"Actor {self.actor_id} registered with registry")
        except (ImportError, AttributeError) as e:
            logger.warning(f"Could not register actor {self.actor_id} with registry: {e}")

        # Register with initializer if available
        # This is now handled in an async method to ensure proper synchronization
        # We'll create a task to handle this asynchronously
        self._initialization_task = asyncio.create_task(self._register_with_initializer())

    async def _register_with_initializer(self) -> None:
        """
        Register this actor with the initializer.

        This method is called during initialization to ensure proper
        synchronization with the initializer.
        """
        try:
            initializer = get_initializer()
            if initializer:
                # Register with initializer (now an async method)
                await initializer.register_actor(self.actor_id)
                logger.info(f"Actor {self.actor_id} registered with initializer")

                # Register cleanup resources
                initializer.register_resource(
                    self.actor_id,
                    self._cleanup_resources,
                    [],
                    {}
                )

                # Set initial state
                await initializer.set_actor_state(self.actor_id, ActorState.CREATED)
            else:
                logger.warning(f"Could not register actor {self.actor_id} with initializer: initializer not available")
        except Exception as e:
            logger.error(f"Error registering actor {self.actor_id} with initializer: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def _ensure_registered_with_initializer(self) -> None:
        """
        Ensure the actor is registered with the initializer.

        This method ensures that the actor is properly registered with the initializer
        before proceeding with initialization. It waits for the registration task to
        complete if it's still running, or registers the actor directly if needed.
        """
        try:
            # First, check if we have a pending registration task
            if hasattr(self, '_initialization_task') and not self._initialization_task.done():
                try:
                    # Wait for the task to complete
                    await self._initialization_task
                    logger.info(f"Actor {self.actor_id} registration task completed")
                except Exception as e:
                    logger.error(f"Error in registration task for actor {self.actor_id}: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    # We'll try to register again below

            # Check if we're already registered
            initializer = get_initializer()
            if initializer:
                # Check if we're already registered
                if await initializer.is_actor_registered(self.actor_id):
                    logger.info(f"Actor {self.actor_id} is already registered with initializer")
                    return

                # Register with initializer
                await initializer.register_actor(self.actor_id)
                logger.info(f"Actor {self.actor_id} registered with initializer")

                # Register cleanup resources
                initializer.register_resource(
                    self.actor_id,
                    self._cleanup_resources,
                    [],
                    {}
                )

                # Set initial state
                await initializer.set_actor_state(self.actor_id, ActorState.CREATED)
            else:
                logger.warning(f"Could not register actor {self.actor_id} with initializer: initializer not available")
        except Exception as e:
            logger.error(f"Error ensuring actor {self.actor_id} is registered with initializer: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _initialize_handlers(self) -> None:
        """
        Initialize message handlers for this actor.

        This maps message types to handler methods, allowing for dynamic dispatch
        based on the received message type.
        """
        # Find all handler methods in the class
        for attr_name in dir(self):
            if attr_name.startswith("handle_") and callable(getattr(self, attr_name)):
                # Extract the message type from the handler name
                message_type_name = attr_name[len("handle_"):].upper()
                try:
                    # Try to get the MessageType enum value
                    message_type = MessageType[message_type_name]
                    # Register the handler
                    self._message_handlers[message_type] = getattr(self, attr_name)
                except (KeyError, AttributeError):
                    # If the message type doesn't exist, skip it
                    pass

    def register_actor(self, actor_id: str, actor: 'Actor') -> None:
        """
        Register another actor that this actor can send messages to.

        Args:
            actor_id: ID of the actor to register
            actor: The actor instance
        """
        self._known_actors[actor_id] = actor

    async def send(self, recipient_id: str, msg_type: MessageType,
                  payload: Dict[str, Any], context: Optional[ContextWave] = None,
                  priority: int = 0, ttl: Optional[int] = None) -> None:
        """
        Send a message to another actor.

        Enhanced to support dynamic discovery via the registry if the recipient
        is not directly known to this actor.

        This method now delegates to the MessageRouter component, which encapsulates
        the message routing logic, error handling, and supervisor notification.

        Args:
            recipient_id: ID of the recipient actor
            msg_type: Type of the message
            payload: Message payload
            context: Optional context to propagate
            priority: Optional priority for the message (0-9, higher is more important)
            ttl: Optional time-to-live in seconds

        Raises:
            ValueError: If the recipient is not found
            RuntimeError: If there is an error sending the message
        """
        try:
            await self._message_router.send(
                recipient_id=recipient_id,
                msg_type=msg_type,
                payload=payload,
                context=context,
                priority=priority,
                ttl=ttl
            )
        except (ValueError, RuntimeError) as e:
            # Log the error but don't re-raise it to maintain backward compatibility
            logger.error(f"Error in send method: {e}")
            # The MessageRouter component will have already updated metrics and notified the supervisor

    async def receive(self, message: Message) -> None:
        """
        Receive a message from another actor.

        Enhanced to handle message priorities, TTL, and metrics.
        Modified to queue messages until the actor is ready.

        Args:
            message: The message to receive
        """
        # Check if message has expired
        if message.is_expired():
            logger.warning(f"Actor {self.actor_id} received expired message of type {message.type.name}")
            return

        # Update metrics
        self._metrics["messages_received"] += 1
        self._metrics["last_activity"] = time.time()

        # Log receipt
        logger.debug(f"{self.__class__.__name__} {self.actor_id} received {message.type.name} from {message.sender_id or 'unknown'}")

        # Record sender if not already known
        if message.sender_id and message.sender_id not in self._known_actors:
            try:
                from .actor_registry import get_registry
                registry = get_registry()
                sender = registry.get_actor(message.sender_id)
                if sender:
                    # We need to cast here because of the circular dependency between
                    # Actor and actor_registry. The registry returns Actor from the
                    # original module, but we need Actor from this module.
                    self._known_actors[message.sender_id] = cast('Actor', sender)
                    logger.debug(f"Actor {self.actor_id} discovered sender {message.sender_id} via registry")
            except (ImportError, AttributeError):
                pass

        # Check if the actor is ready to process messages
        if not self._ready and message.type != MessageType.INITIALIZE and message.type != MessageType.START:
            # Queue the message for later processing
            logger.debug(f"Actor {self.actor_id} queuing message of type {message.type.name} until ready")
            self._pending_messages.append(message)
            return

        # Add to mailbox
        await self.mailbox.put(message)

    async def process_messages(self) -> None:
        """
        Process messages from the mailbox.

        Enhanced to handle message priorities, metrics, and heartbeats.
        Implements the CAW principle of adaptive processing based on context.

        This method now delegates to the MessageProcessor component, which encapsulates
        the message processing loop, error handling, and heartbeat sending logic.
        This reduces the complexity of this method and makes it easier to test and maintain.
        """
        await self._message_processor.process_messages()

    async def _handle_message(self, message: Message) -> None:
        """
        Handle an incoming message.

        Enhanced with better error handling for unknown message types and
        improved context handling.

        Args:
            message: The message to handle
        """
        # Delegate to the message handler
        await self._message_handler.handle_message(message)

    async def handle_unknown(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle an unknown message type.

        Args:
            payload: Message payload
            context: Message context
        """
        logger.warning(f"Actor {self.actor_id} received unknown message type")
        # You might want to send an error response back to the sender
        sender_id = context.metadata.get("sender_id")
        if sender_id and sender_id in self._known_actors:
            error_payload = {
                "error": "Unknown message type",
                "actor_id": self.actor_id
            }
            await self.send(sender_id, MessageType.ERROR, error_payload, context)

    async def _cleanup_resources(self) -> None:
        """
        Clean up resources associated with this actor.

        This method is called when the actor fails to initialize or is being
        rolled back. It should release any resources that were acquired during
        initialization.
        """
        logger.info(f"Cleaning up resources for actor {self.actor_id}")

        # Cancel any running tasks
        for task_name in ['_process_task', '_heartbeat_task', '_metrics_task']:
            task = getattr(self, task_name, None)
            if task and not task.done():
                try:
                    task.cancel()
                    logger.info(f"Cancelled {task_name} for actor {self.actor_id}")
                except Exception as e:
                    logger.error(f"Error cancelling {task_name} for actor {self.actor_id}: {e}")

        # Clear the mailbox
        try:
            while not self.mailbox.empty():
                try:
                    self.mailbox.get_nowait()
                    self.mailbox.task_done()
                except asyncio.QueueEmpty:
                    break
            logger.info(f"Cleared mailbox for actor {self.actor_id}")
        except Exception as e:
            logger.error(f"Error clearing mailbox for actor {self.actor_id}: {e}")

        # Unregister from registry
        try:
            from .actor_registry import get_registry
            registry = get_registry()
            registry.unregister_actor(self.actor_id)
            logger.info(f"Unregistered actor {self.actor_id} from registry during cleanup")
        except (ImportError, AttributeError, Exception) as e:
            logger.warning(f"Could not unregister actor {self.actor_id} from registry during cleanup: {e}")

        # Reset state flags
        self._is_running = False
        self._ready = False
        self._initialization_complete = False
        self._ready_event.clear()

        logger.info(f"Cleaned up resources for actor {self.actor_id}")

    async def initialize(self) -> None:
        """
        Initialize the actor (first phase of two-phase initialization).

        This method prepares the actor for starting but doesn't begin processing messages.
        It's part of the two-phase initialization process to ensure all actors are
        properly initialized before they start communicating.

        Enhanced with robust error handling and cleanup on failure.
        Enhanced to ensure proper registration with the initializer before proceeding.
        """
        # First, ensure we're registered with the initializer
        try:
            await self._ensure_registered_with_initializer()
        except Exception as e:
            logger.error(f"Error ensuring actor {self.actor_id} is registered with initializer: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # Continue anyway, we'll handle errors below

        # Get initializer
        initializer = get_initializer()

        try:
            # Update initializer state
            if initializer:
                await initializer.set_actor_state(self.actor_id, ActorState.INITIALIZING,
                                                phase="initialize")

            # Reset metrics for a clean start
            self._metrics["start_time"] = time.time()
            self._metrics["uptime"] = 0.0
            self._metrics["messages_received"] = 0
            self._metrics["messages_sent"] = 0
            self._metrics["messages_processed"] = 0
            self._metrics["errors"] = 0
            self._metrics["processing_time"] = 0.0
            self._metrics["avg_processing_time"] = 0.0
            self._metrics["last_activity"] = time.time()

            # Load state if available
            try:
                await self._state_manager.load_state()
            except Exception as state_error:
                logger.error(f"Error loading state for actor {self.actor_id}: {state_error}")
                import traceback
                logger.error(traceback.format_exc())
                # Continue with initialization despite state loading error

            # Register with registry again in case we were unregistered
            try:
                from .actor_registry import get_registry
                registry = get_registry()
                registry.register_actor(
                    actor_id=self.actor_id,
                    actor=self,  # type: ignore
                    actor_type=self.actor_type,
                    tags=self.tags,
                    capabilities=self.capabilities
                )
            except (ImportError, AttributeError) as e:
                logger.warning(f"Could not register actor {self.actor_id} with registry: {e}")

            # Mark initialization as complete
            self._initialization_complete = True

            # Update initializer state
            if initializer:
                await initializer.set_actor_state(self.actor_id, ActorState.INITIALIZED,
                                                phase="initialize")

            logger.info(f"Actor {self.actor_id} initialized")

        except Exception as e:
            logger.error(f"Error initializing actor {self.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Mark initialization as failed
            self._initialization_complete = False

            # Update initializer state to FAILED
            if initializer:
                try:
                    await initializer.set_actor_state(
                        self.actor_id,
                        ActorState.FAILED,
                        error=e,
                        phase="initialize"
                    )

                    # Roll back initialization
                    await initializer.rollback_actor(self.actor_id)
                except Exception as rollback_error:
                    logger.error(f"Error rolling back actor {self.actor_id}: {rollback_error}")

            # Re-raise as ActorInitializationError
            raise ActorInitializationError(
                actor_id=self.actor_id,
                phase="initialize",
                state=ActorState.INITIALIZING,
                message=str(e),
                original_error=e
            ) from e

    async def wait_for_dependencies(self, timeout: Optional[float] = None) -> bool:
        """
        Wait for all dependencies to be ready before proceeding.

        Args:
            timeout: Optional timeout in seconds

        Returns:
            True if all dependencies are ready, False if timed out
        """
        initializer = get_initializer()
        if not initializer:
            logger.warning(f"Actor {self.actor_id} cannot wait for dependencies: initializer not available")
            return True  # Assume dependencies are ready if no initializer

        return await initializer.wait_for_dependencies(self.actor_id, timeout)

    async def start(self) -> None:
        """
        Start the actor (second phase of two-phase initialization).

        Enhanced to implement a two-phase initialization process with explicit
        synchronization points to ensure all actors are properly initialized
        before they start communicating.

        Enhanced with robust error handling and cleanup on failure.

        This method now delegates to the ActorStarter component.

        Returns:
            None

        Raises:
            ActorInitializationError: If the actor fails to start

        Implementation:
            This method delegates to the ActorStarter.start() method,
            which handles the actual actor starting logic. This is part of the
            refactoring effort to reduce the complexity of the Actor class.
        """
        await self._starter.start()

    async def stop(self) -> None:
        """
        Stop the actor.

        Enhanced to stop additional tasks, save state, and unregister from registry.
        Updated to update the actor state in the initializer.
        Enhanced with robust error handling and cleanup.

        This method now delegates to the ActorTerminator component.

        Returns:
            None

        Implementation:
            This method delegates to the ActorTerminator.stop() method,
            which handles the actual actor stopping logic. This is part of the
            refactoring effort to reduce the complexity of the Actor class.
        """
        await self._terminator.stop()

    async def _cancel_all_tasks(self) -> None:
        """
        Cancel all tasks associated with this actor.

        This is a helper method for the stop() method to make it less complex.

        This method now delegates to the TaskManager component.

        Returns:
            None

        Implementation:
            This method delegates to the TaskManager.cancel_all_tasks() method,
            which handles the actual task cancellation logic. This is part of the
            refactoring effort to reduce the complexity of the Actor class.
        """
        await self._task_manager.cancel_all_tasks()

    async def _process_remaining_messages(self) -> None:
        """
        Process remaining messages in the mailbox before stopping.

        This method now delegates to the LifecycleMessageProcessor component.

        Returns:
            None

        Implementation:
            This method delegates to the LifecycleMessageProcessor.process_remaining_messages() method,
            which handles the actual message processing logic. This is part of the
            refactoring effort to reduce the complexity of the Actor class.
        """
        await self._lifecycle_message_processor.process_remaining_messages()

    # Delegated methods for supervision
    async def supervise(self, actor_id: str) -> None:
        """
        Start supervising another actor.

        Args:
            actor_id: ID of the actor to supervise
        """
        await self._supervisor_component.supervise(actor_id)

    async def handle_heartbeat(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a heartbeat message from a supervised actor.

        Args:
            payload: Heartbeat payload
            context: Message context
        """
        await self._supervisor_component.handle_heartbeat(payload, context)

    async def handle_error(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle an error message.

        Args:
            payload: Error payload
            context: Message context
        """
        await self._supervisor_component.handle_error(payload, context)

    async def handle_supervise(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a supervision request from another actor.

        Args:
            payload: Supervision payload
            context: Message context
        """
        await self._supervisor_component.handle_supervise(payload, context)

    # Delegated methods for streams
    async def subscribe(self, stream_id: str) -> None:
        """
        Subscribe to a stream.

        Args:
            stream_id: ID of the stream to subscribe to
        """
        await self._stream_manager.subscribe(stream_id)

    async def unsubscribe(self, stream_id: str) -> None:
        """
        Unsubscribe from a stream.

        Args:
            stream_id: ID of the stream to unsubscribe from
        """
        await self._stream_manager.unsubscribe(stream_id)

    async def publish(self, stream_id: str, message_type: MessageType, payload: Dict[str, Any]) -> None:
        """
        Publish a message to a stream.

        Args:
            stream_id: ID of the stream to publish to
            message_type: Type of the message
            payload: Message payload
        """
        await self._stream_manager.publish(stream_id, message_type, payload)

    async def handle_stream_data(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle data from a stream.

        Args:
            payload: Stream data payload
            context: Message context
        """
        await self._stream_manager.handle_stream_data(payload, context)

    async def handle_subscribe(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a subscription request.

        Args:
            payload: Subscription payload
            context: Message context
        """
        await self._stream_manager.handle_subscribe(payload, context)

    async def handle_unsubscribe(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle an unsubscription request.

        Args:
            payload: Unsubscription payload
            context: Message context
        """
        await self._stream_manager.handle_unsubscribe(payload, context)

    async def handle_publish(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a publish request.

        Args:
            payload: Publish payload
            context: Message context
        """
        await self._stream_manager.handle_publish(payload, context)

    # Properties
    @property
    def actor_id(self) -> str:
        """Get the actor's unique identifier."""
        return self._actor_id

    @property
    def actor_type(self) -> str:
        """Get the actor's type."""
        return self._actor_type

    @property
    def tags(self) -> Set[str]:
        """Get the actor's tags."""
        return self._tags

    @property
    def capabilities(self) -> Set[str]:
        """Get the actor's capabilities."""
        return self._capabilities

    @property
    def supervisor_id(self) -> Optional[str]:
        """Get the actor's supervisor ID (if any)."""
        return self._supervisor_id

    @property
    def is_running(self) -> bool:
        """Check if the actor is running."""
        return self._is_running

    @is_running.setter
    def is_running(self, value: bool) -> None:
        """Set the actor's running state."""
        self._is_running = value
