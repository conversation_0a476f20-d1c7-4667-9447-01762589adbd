"""
Workload Monitor Module
==================

This module provides workload monitoring capabilities for the actor system,
enabling tracking of message queue depths, processing times, and throughput.

The workload monitor supports configurable thresholds for triggering mode
switches, hysteresis mechanisms to prevent rapid oscillation, and predictive
capabilities for anticipating workload changes.
"""

import asyncio
import logging
import time
import statistics
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable, cast, Deque
from collections import deque

from ..metrics.registry import get_metrics_registry
from ..actor_registry import get_registry
from .execution_mode_manager import ExecutionMode, ModeTransitionReason

logger = logging.getLogger("vibe_check_actor_system.workload_monitor")


class WorkloadLevel(Enum):
    """
    Workload levels for the actor system.

    These levels represent the current workload on the actor system.
    """
    VERY_LOW = "very_low"
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    VERY_HIGH = "very_high"
    CRITICAL = "critical"


class WorkloadTrend(Enum):
    """
    Workload trends for the actor system.

    These trends represent the direction of change in the workload.
    """
    DECREASING_RAPIDLY = "decreasing_rapidly"
    DECREASING = "decreasing"
    STABLE = "stable"
    INCREASING = "increasing"
    INCREASING_RAPIDLY = "increasing_rapidly"


class WorkloadMetrics:
    """
    Workload metrics for the actor system.

    This class represents the current workload metrics for the actor system,
    including message queue depths, processing times, and throughput.
    """

    def __init__(self):
        """Initialize the workload metrics."""
        self.timestamp = time.time()

        # Message metrics
        self.total_messages_queued = 0
        self.total_messages_processed = 0
        self.messages_per_second = 0.0
        self.average_queue_depth = 0.0
        self.max_queue_depth = 0

        # Processing metrics
        self.average_processing_time = 0.0
        self.max_processing_time = 0.0
        self.processing_time_percentiles = {}

        # Actor metrics
        self.active_actors = 0
        self.busy_actors = 0
        self.actor_utilization = 0.0

        # System metrics
        self.system_load = 0.0
        self.system_capacity = 0.0
        self.system_utilization = 0.0

        # Derived metrics
        self.workload_level = WorkloadLevel.NORMAL
        self.workload_trend = WorkloadTrend.STABLE

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the metrics to a dictionary for serialization.

        Returns:
            Dictionary representation of the metrics
        """
        return {
            "timestamp": self.timestamp,
            "total_messages_queued": self.total_messages_queued,
            "total_messages_processed": self.total_messages_processed,
            "messages_per_second": self.messages_per_second,
            "average_queue_depth": self.average_queue_depth,
            "max_queue_depth": self.max_queue_depth,
            "average_processing_time": self.average_processing_time,
            "max_processing_time": self.max_processing_time,
            "processing_time_percentiles": self.processing_time_percentiles,
            "active_actors": self.active_actors,
            "busy_actors": self.busy_actors,
            "actor_utilization": self.actor_utilization,
            "system_load": self.system_load,
            "system_capacity": self.system_capacity,
            "system_utilization": self.system_utilization,
            "workload_level": self.workload_level.value,
            "workload_trend": self.workload_trend.value
        }


class WorkloadMonitor:
    """
    Monitors the workload of the actor system.

    This class provides workload monitoring capabilities for the actor system,
    enabling tracking of message queue depths, processing times, and throughput.

    Attributes:
        metrics_history: History of workload metrics
        thresholds: Thresholds for workload levels
        hysteresis: Hysteresis parameters for preventing oscillation
        prediction_window: Window size for workload prediction
        check_interval: Interval between workload checks
        mode_manager: Execution mode manager
    """

    def __init__(self, config: Dict[str, Any], mode_manager: Any):
        """
        Initialize the workload monitor.

        Args:
            config: Configuration dictionary
            mode_manager: Execution mode manager
        """
        self.config = config
        self.mode_manager = mode_manager

        # Initialize metrics history
        self.metrics_history: Deque[WorkloadMetrics] = deque(maxlen=config.get("history_size", 100))

        # Initialize thresholds
        self.thresholds = {
            "queue_depth": {
                WorkloadLevel.VERY_LOW: config.get("queue_depth_very_low", 1),
                WorkloadLevel.LOW: config.get("queue_depth_low", 5),
                WorkloadLevel.NORMAL: config.get("queue_depth_normal", 20),
                WorkloadLevel.HIGH: config.get("queue_depth_high", 50),
                WorkloadLevel.VERY_HIGH: config.get("queue_depth_very_high", 100),
                WorkloadLevel.CRITICAL: config.get("queue_depth_critical", 200)
            },
            "processing_time": {
                WorkloadLevel.VERY_LOW: config.get("processing_time_very_low", 0.001),
                WorkloadLevel.LOW: config.get("processing_time_low", 0.01),
                WorkloadLevel.NORMAL: config.get("processing_time_normal", 0.1),
                WorkloadLevel.HIGH: config.get("processing_time_high", 0.5),
                WorkloadLevel.VERY_HIGH: config.get("processing_time_very_high", 1.0),
                WorkloadLevel.CRITICAL: config.get("processing_time_critical", 5.0)
            },
            "messages_per_second": {
                WorkloadLevel.VERY_LOW: config.get("messages_per_second_very_low", 1),
                WorkloadLevel.LOW: config.get("messages_per_second_low", 10),
                WorkloadLevel.NORMAL: config.get("messages_per_second_normal", 100),
                WorkloadLevel.HIGH: config.get("messages_per_second_high", 500),
                WorkloadLevel.VERY_HIGH: config.get("messages_per_second_very_high", 1000),
                WorkloadLevel.CRITICAL: config.get("messages_per_second_critical", 5000)
            },
            "system_utilization": {
                WorkloadLevel.VERY_LOW: config.get("system_utilization_very_low", 0.1),
                WorkloadLevel.LOW: config.get("system_utilization_low", 0.3),
                WorkloadLevel.NORMAL: config.get("system_utilization_normal", 0.5),
                WorkloadLevel.HIGH: config.get("system_utilization_high", 0.7),
                WorkloadLevel.VERY_HIGH: config.get("system_utilization_very_high", 0.9),
                WorkloadLevel.CRITICAL: config.get("system_utilization_critical", 0.95)
            }
        }

        # Initialize hysteresis parameters
        self.hysteresis = {
            "time_threshold": config.get("hysteresis_time_threshold", 30.0),  # seconds
            "level_threshold": config.get("hysteresis_level_threshold", 2),  # levels
            "count_threshold": config.get("hysteresis_count_threshold", 3)  # consecutive samples
        }

        # Initialize prediction parameters
        self.prediction_window = config.get("prediction_window", 10)
        self.prediction_horizon = config.get("prediction_horizon", 5)

        # Initialize check interval
        self.check_interval = config.get("check_interval", 5.0)  # seconds

        # Initialize monitoring task
        self.monitoring_task: Optional[asyncio.Task] = None
        self.running = False

        # Initialize mode mapping
        self.mode_mapping = {
            WorkloadLevel.VERY_LOW: ExecutionMode.PARALLEL,
            WorkloadLevel.LOW: ExecutionMode.PARALLEL,
            WorkloadLevel.NORMAL: ExecutionMode.ADAPTIVE,
            WorkloadLevel.HIGH: ExecutionMode.HYBRID,
            WorkloadLevel.VERY_HIGH: ExecutionMode.SEQUENTIAL,
            WorkloadLevel.CRITICAL: ExecutionMode.EMERGENCY
        }

        # Override mode mapping from config
        mode_mapping_config = config.get("mode_mapping", {})
        for level_str, mode_str in mode_mapping_config.items():
            try:
                level = WorkloadLevel(level_str)
                mode = ExecutionMode(mode_str)
                self.mode_mapping[level] = mode
            except ValueError:
                logger.warning(f"Invalid workload level or execution mode in config: {level_str} -> {mode_str}")

        # Initialize last mode switch time
        self.last_mode_switch_time = time.time()

        # Initialize consecutive samples counter
        self.consecutive_samples = {level: 0 for level in WorkloadLevel}

        logger.info("Initialized workload monitor")

    async def start(self) -> None:
        """Start the workload monitor."""
        if self.running:
            logger.warning("Workload monitor is already running")
            return

        self.running = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Started workload monitor")

    async def stop(self) -> None:
        """Stop the workload monitor."""
        if not self.running:
            logger.warning("Workload monitor is not running")
            return

        self.running = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
            self.monitoring_task = None

        logger.info("Stopped workload monitor")

    async def _monitoring_loop(self) -> None:
        """Monitoring loop for the workload monitor."""
        try:
            while self.running:
                # Collect workload metrics
                metrics = await self._collect_workload_metrics()

                # Add metrics to history
                self.metrics_history.append(metrics)

                # Analyze workload
                await self._analyze_workload()

                # Wait for the next check
                await asyncio.sleep(self.check_interval)
        except asyncio.CancelledError:
            logger.info("Workload monitoring loop cancelled")
        except Exception as e:
            logger.error(f"Error in workload monitoring loop: {e}")

    async def _collect_workload_metrics(self) -> WorkloadMetrics:
        """
        Collect workload metrics.

        Returns:
            WorkloadMetrics object with collected metrics
        """
        metrics = WorkloadMetrics()

        # Get metrics registry
        registry = get_metrics_registry()

        # Get actor registry
        actor_registry = get_registry()

        # Collect message metrics
        messages_queued = registry.get_aggregated_metric("messages_queued", "sum") or 0
        messages_processed = registry.get_aggregated_metric("messages_processed", "sum") or 0

        metrics.total_messages_queued = messages_queued
        metrics.total_messages_processed = messages_processed

        # Calculate messages per second
        if len(self.metrics_history) > 0:
            last_metrics = self.metrics_history[-1]
            time_diff = metrics.timestamp - last_metrics.timestamp
            if time_diff > 0:
                messages_diff = messages_processed - last_metrics.total_messages_processed
                metrics.messages_per_second = messages_diff / time_diff

        # Collect queue depth metrics
        queue_depths = []
        for actor_id in actor_registry.get_all_actor_ids():
            queue_depth = registry.get_metric("queue_depth", actor_id)
            if queue_depth:
                queue_depths.append(queue_depth.get("value", 0))

        if queue_depths:
            metrics.average_queue_depth = sum(queue_depths) / len(queue_depths)
            metrics.max_queue_depth = max(queue_depths)

        # Collect processing time metrics
        processing_times = []
        for actor_id in actor_registry.get_all_actor_ids():
            processing_time = registry.get_metric("processing_time", actor_id)
            if processing_time:
                processing_times.append(processing_time.get("value", 0))

        if processing_times:
            metrics.average_processing_time = sum(processing_times) / len(processing_times)
            metrics.max_processing_time = max(processing_times)

            # Calculate percentiles
            if len(processing_times) >= 5:
                metrics.processing_time_percentiles = {
                    "50": statistics.median(processing_times),
                    "90": statistics.quantiles(processing_times, n=10)[8],
                    "95": statistics.quantiles(processing_times, n=20)[18],
                    "99": statistics.quantiles(processing_times, n=100)[98] if len(processing_times) >= 100 else None
                }

        # Collect actor metrics
        active_actors = len(actor_registry.get_all_actor_ids())
        busy_actors = sum(1 for actor_id in actor_registry.get_all_actor_ids() if registry.get_metric("is_busy", actor_id))

        metrics.active_actors = active_actors
        metrics.busy_actors = busy_actors

        if active_actors > 0:
            metrics.actor_utilization = busy_actors / active_actors

        # Collect system metrics
        metrics.system_load = registry.get_aggregated_metric("system_load", "avg") or 0.0
        metrics.system_capacity = registry.get_aggregated_metric("system_capacity", "avg") or 1.0

        if metrics.system_capacity > 0:
            metrics.system_utilization = metrics.system_load / metrics.system_capacity

        # Determine workload level
        metrics.workload_level = self._determine_workload_level(metrics)

        # Determine workload trend
        metrics.workload_trend = self._determine_workload_trend()

        return metrics

    def _determine_workload_level(self, metrics: WorkloadMetrics) -> WorkloadLevel:
        """
        Determine the workload level based on metrics.

        Args:
            metrics: Workload metrics

        Returns:
            Workload level
        """
        # Calculate a score for each metric
        scores = {}

        # Score based on queue depth
        if metrics.average_queue_depth <= self.thresholds["queue_depth"][WorkloadLevel.VERY_LOW]:
            scores["queue_depth"] = 0
        elif metrics.average_queue_depth <= self.thresholds["queue_depth"][WorkloadLevel.LOW]:
            scores["queue_depth"] = 1
        elif metrics.average_queue_depth <= self.thresholds["queue_depth"][WorkloadLevel.NORMAL]:
            scores["queue_depth"] = 2
        elif metrics.average_queue_depth <= self.thresholds["queue_depth"][WorkloadLevel.HIGH]:
            scores["queue_depth"] = 3
        elif metrics.average_queue_depth <= self.thresholds["queue_depth"][WorkloadLevel.VERY_HIGH]:
            scores["queue_depth"] = 4
        else:
            scores["queue_depth"] = 5

        # Score based on processing time
        if metrics.average_processing_time <= self.thresholds["processing_time"][WorkloadLevel.VERY_LOW]:
            scores["processing_time"] = 0
        elif metrics.average_processing_time <= self.thresholds["processing_time"][WorkloadLevel.LOW]:
            scores["processing_time"] = 1
        elif metrics.average_processing_time <= self.thresholds["processing_time"][WorkloadLevel.NORMAL]:
            scores["processing_time"] = 2
        elif metrics.average_processing_time <= self.thresholds["processing_time"][WorkloadLevel.HIGH]:
            scores["processing_time"] = 3
        elif metrics.average_processing_time <= self.thresholds["processing_time"][WorkloadLevel.VERY_HIGH]:
            scores["processing_time"] = 4
        else:
            scores["processing_time"] = 5

        # Score based on messages per second
        if metrics.messages_per_second <= self.thresholds["messages_per_second"][WorkloadLevel.VERY_LOW]:
            scores["messages_per_second"] = 0
        elif metrics.messages_per_second <= self.thresholds["messages_per_second"][WorkloadLevel.LOW]:
            scores["messages_per_second"] = 1
        elif metrics.messages_per_second <= self.thresholds["messages_per_second"][WorkloadLevel.NORMAL]:
            scores["messages_per_second"] = 2
        elif metrics.messages_per_second <= self.thresholds["messages_per_second"][WorkloadLevel.HIGH]:
            scores["messages_per_second"] = 3
        elif metrics.messages_per_second <= self.thresholds["messages_per_second"][WorkloadLevel.VERY_HIGH]:
            scores["messages_per_second"] = 4
        else:
            scores["messages_per_second"] = 5

        # Score based on system utilization
        if metrics.system_utilization <= self.thresholds["system_utilization"][WorkloadLevel.VERY_LOW]:
            scores["system_utilization"] = 0
        elif metrics.system_utilization <= self.thresholds["system_utilization"][WorkloadLevel.LOW]:
            scores["system_utilization"] = 1
        elif metrics.system_utilization <= self.thresholds["system_utilization"][WorkloadLevel.NORMAL]:
            scores["system_utilization"] = 2
        elif metrics.system_utilization <= self.thresholds["system_utilization"][WorkloadLevel.HIGH]:
            scores["system_utilization"] = 3
        elif metrics.system_utilization <= self.thresholds["system_utilization"][WorkloadLevel.VERY_HIGH]:
            scores["system_utilization"] = 4
        else:
            scores["system_utilization"] = 5

        # Calculate the average score
        total_score = sum(scores.values())
        num_metrics = len(scores)
        avg_score = total_score / num_metrics if num_metrics > 0 else 0

        # Map the average score to a workload level
        if avg_score <= 0.5:
            return WorkloadLevel.VERY_LOW
        elif avg_score <= 1.5:
            return WorkloadLevel.LOW
        elif avg_score <= 2.5:
            return WorkloadLevel.NORMAL
        elif avg_score <= 3.5:
            return WorkloadLevel.HIGH
        elif avg_score <= 4.5:
            return WorkloadLevel.VERY_HIGH
        else:
            return WorkloadLevel.CRITICAL

    def _determine_workload_trend(self) -> WorkloadTrend:
        """
        Determine the workload trend based on metrics history.

        Returns:
            Workload trend
        """
        # If we don't have enough history, return STABLE
        if len(self.metrics_history) < 2:
            return WorkloadTrend.STABLE

        # Get the last few metrics
        history_size = min(len(self.metrics_history), self.prediction_window)
        recent_metrics = list(self.metrics_history)[-history_size:]

        # Calculate the trend for each metric
        queue_depth_trend = self._calculate_trend([m.average_queue_depth for m in recent_metrics])
        processing_time_trend = self._calculate_trend([m.average_processing_time for m in recent_metrics])
        messages_per_second_trend = self._calculate_trend([m.messages_per_second for m in recent_metrics])
        system_utilization_trend = self._calculate_trend([m.system_utilization for m in recent_metrics])

        # Combine the trends
        trends = [queue_depth_trend, processing_time_trend, messages_per_second_trend, system_utilization_trend]
        avg_trend = sum(trends) / len(trends) if trends else 0

        # Map the average trend to a workload trend
        if avg_trend < -0.5:
            return WorkloadTrend.DECREASING_RAPIDLY
        elif avg_trend < -0.1:
            return WorkloadTrend.DECREASING
        elif avg_trend < 0.1:
            return WorkloadTrend.STABLE
        elif avg_trend < 0.5:
            return WorkloadTrend.INCREASING
        else:
            return WorkloadTrend.INCREASING_RAPIDLY

    def _calculate_trend(self, values: List[float]) -> float:
        """
        Calculate the trend of a list of values.

        Args:
            values: List of values

        Returns:
            Trend value (-1.0 to 1.0, where negative means decreasing and positive means increasing)
        """
        if not values or len(values) < 2:
            return 0.0

        # Calculate the differences between consecutive values
        diffs = [values[i] - values[i-1] for i in range(1, len(values))]

        # Calculate the average difference
        avg_diff = sum(diffs) / len(diffs) if diffs else 0.0

        # Normalize the average difference
        max_value = max(values) if values else 1.0
        if max_value > 0:
            normalized_diff = avg_diff / max_value
        else:
            normalized_diff = 0.0

        # Clamp the normalized difference to [-1.0, 1.0]
        return max(-1.0, min(1.0, normalized_diff * 10.0))

    async def _analyze_workload(self) -> None:
        """Analyze the workload and trigger mode switches if necessary."""
        # If we don't have any metrics, return
        if not self.metrics_history:
            return

        # Get the latest metrics
        latest_metrics = self.metrics_history[-1]

        # Get the current workload level and trend
        current_level = latest_metrics.workload_level
        current_trend = latest_metrics.workload_trend

        # Get the current execution mode
        current_mode = self.mode_manager.get_execution_mode()

        # Get the target mode based on the workload level
        target_mode = self.mode_mapping.get(current_level, ExecutionMode.ADAPTIVE)

        # If the current mode is different from the target mode, consider switching
        if current_mode != target_mode:
            # Check if we should switch based on hysteresis
            should_switch = self._check_hysteresis(current_level, target_mode)

            if should_switch:
                # Determine the reason for the switch
                if current_trend in [WorkloadTrend.INCREASING, WorkloadTrend.INCREASING_RAPIDLY]:
                    reason = ModeTransitionReason.WORKLOAD
                    context = {
                        "workload_level": current_level.value,
                        "workload_trend": current_trend.value,
                        "metrics": latest_metrics.to_dict()
                    }
                else:
                    reason = ModeTransitionReason.WORKLOAD
                    context = {
                        "workload_level": current_level.value,
                        "workload_trend": current_trend.value,
                        "metrics": latest_metrics.to_dict()
                    }

                # Switch the execution mode
                logger.info(f"Switching execution mode from {current_mode.value} to {target_mode.value} due to workload")
                await self.mode_manager.set_execution_mode(target_mode, reason, context)

                # Reset consecutive samples counter
                self.consecutive_samples = {level: 0 for level in WorkloadLevel}

                # Update last mode switch time
                self.last_mode_switch_time = time.time()

        # Update consecutive samples counter
        for level in WorkloadLevel:
            if level == current_level:
                self.consecutive_samples[level] += 1
            else:
                self.consecutive_samples[level] = 0

    def _check_hysteresis(self, current_level: WorkloadLevel, target_mode: ExecutionMode) -> bool:
        """
        Check if we should switch modes based on hysteresis.

        Args:
            current_level: Current workload level
            target_mode: Target execution mode

        Returns:
            True if we should switch, False otherwise
        """
        # If we haven't switched modes recently, we can switch immediately
        current_time = time.time()
        if current_time - self.last_mode_switch_time > self.hysteresis["time_threshold"]:
            return True

        # If the workload level has been at the current level for a while, we should switch
        if self.consecutive_samples[current_level] >= self.hysteresis["count_threshold"]:
            return True

        # If the workload level is critical, we should switch immediately
        if current_level == WorkloadLevel.CRITICAL:
            return True

        # If the workload level is very low, we should switch immediately
        if current_level == WorkloadLevel.VERY_LOW and target_mode == ExecutionMode.PARALLEL:
            return True

        # Otherwise, don't switch yet
        return False
