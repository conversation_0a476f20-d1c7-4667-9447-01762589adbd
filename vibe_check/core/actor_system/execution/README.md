# Execution Components

This directory contains the execution components for the actor system. These components are responsible for managing execution modes, monitoring workload and resources, defining and enforcing policies, and integrating with the actor system.

## Overview

The execution system provides a comprehensive framework for adaptive execution mode management in the actor system. It supports automatic switching between different execution modes based on system conditions, including workload, resource availability, and custom policies.

The execution system consists of the following components:

- **ExecutionModeManager**: Manages execution modes and transitions
- **WorkloadMonitor**: Monitors workload metrics and triggers mode switches
- **ResourceMonitor**: Monitors resource metrics and triggers mode switches
- **PolicyManager**: Manages custom policies for mode switching
- **ExecutionManager**: Integrates the execution components with the actor system

## Components

### ExecutionModeManager

The `ExecutionModeManager` class is responsible for managing execution modes and transitions between them. It supports multiple execution modes, including parallel, sequential, hybrid, adaptive, and emergency modes, and provides comprehensive logging of mode transitions.

```python
# Example usage
mode_manager = ExecutionModeManager(config)

# Get the current execution mode
current_mode = mode_manager.get_execution_mode()

# Set the execution mode
await mode_manager.set_execution_mode(
    mode=ExecutionMode.HYBRID,
    reason=ModeTransitionReason.MANUAL,
    context={"user_initiated": True}
)

# Get a mode property
concurrency_limit = mode_manager.get_mode_property("concurrency_limit")

# Register a transition handler
mode_manager.register_transition_handler(
    source_mode=ExecutionMode.PARALLEL,
    target_mode=ExecutionMode.SEQUENTIAL,
    handler=lambda transition: print(f"Transitioning from {transition.source_mode.value} to {transition.target_mode.value}")
)
```

### WorkloadMonitor

The `WorkloadMonitor` class is responsible for monitoring workload metrics and triggering mode switches based on those metrics. It tracks message queue depths, processing times, and throughput, and provides configurable thresholds for triggering mode switches.

```python
# Example usage
workload_monitor = WorkloadMonitor(config, mode_manager)

# Start the workload monitor
await workload_monitor.start()

# Stop the workload monitor
await workload_monitor.stop()
```

### ResourceMonitor

The `ResourceMonitor` class is responsible for monitoring resource metrics and triggering mode switches based on those metrics. It tracks CPU usage, memory consumption, and I/O operations, and provides adaptive algorithms for optimizing execution mode based on available resources.

```python
# Example usage
resource_monitor = ResourceMonitor(config, mode_manager)

# Start the resource monitor
await resource_monitor.start()

# Stop the resource monitor
await resource_monitor.stop()
```

### PolicyManager

The `PolicyManager` class is responsible for managing custom policies for mode switching. It supports rule-based, time-based, and event-based policies, and allows combining multiple policies with configurable weights and priorities.

```python
# Example usage
policy_manager = PolicyManager(config, mode_manager, workload_monitor, resource_monitor)

# Start the policy manager
await policy_manager.start()

# Stop the policy manager
await policy_manager.stop()
```

### ExecutionManager

The `ExecutionManager` class is responsible for integrating the execution components with the actor system. It provides a unified interface for the actor system to interact with the execution mode management components.

```python
# Example usage
execution_manager = get_execution_manager(config)

# Initialize the execution manager
await initialize_execution_manager(config)

# Get the current execution mode
current_mode = execution_manager.get_execution_mode()

# Set the execution mode
await execution_manager.set_execution_mode(
    mode=ExecutionMode.HYBRID,
    reason=ModeTransitionReason.MANUAL,
    context={"user_initiated": True}
)

# Check if a message should be processed
should_process = execution_manager.should_process_message(
    actor_id="actor1",
    message_id="message1",
    priority=1
)

# Get the batch size for an actor
batch_size = execution_manager.get_batch_size("actor1")

# Get the resource allocation for an actor
cpu_allocation = execution_manager.get_resource_allocation("actor1", "cpu")

# Shut down the execution manager
await shutdown_execution_manager()
```

## Integration with Actor System

The execution system is integrated with the actor system through the following mechanisms:

1. The `ExecutionManager` provides methods for the actor system to check if messages should be processed, get batch sizes, and get resource allocations.
2. The `WorkloadMonitor` monitors the workload of the actor system and triggers mode switches based on workload metrics.
3. The `ResourceMonitor` monitors the resources of the actor system and triggers mode switches based on resource metrics.
4. The `PolicyManager` enforces custom policies for mode switching based on system conditions.

## Execution Modes

The execution system supports the following execution modes:

- **PARALLEL**: Fully parallel execution with maximum concurrency
- **SEQUENTIAL**: Sequential execution with minimal concurrency
- **HYBRID**: Hybrid mode with controlled concurrency
- **ADAPTIVE**: Adaptive mode that automatically adjusts concurrency based on workload
- **EMERGENCY**: Emergency mode for critical situations

Each mode has its own set of properties, including concurrency limits, message batch sizes, priority boosts, and resource allocations.

## Usage Examples

### Basic Usage

```python
# Get the execution manager
execution_manager = get_execution_manager()

# Initialize the execution manager
await initialize_execution_manager()

# Get the current execution mode
current_mode = execution_manager.get_execution_mode()
print(f"Current execution mode: {current_mode.value}")

# Set the execution mode
await execution_manager.set_execution_mode(ExecutionMode.HYBRID)
print(f"New execution mode: {execution_manager.get_execution_mode().value}")

# Shut down the execution manager
await shutdown_execution_manager()
```

### Message Processing

```python
# Get the execution manager
execution_manager = get_execution_manager()

# Check if a message should be processed
should_process = execution_manager.should_process_message(
    actor_id="actor1",
    message_id="message1",
    priority=1
)

if should_process:
    # Process the message
    batch_size = execution_manager.get_batch_size("actor1")
    print(f"Processing message with batch size: {batch_size}")
else:
    # Skip the message
    print("Skipping message due to execution mode constraints")
```

### Resource Allocation

```python
# Get the execution manager
execution_manager = get_execution_manager()

# Get the resource allocation for an actor
cpu_allocation = execution_manager.get_resource_allocation("actor1", "cpu")
memory_allocation = execution_manager.get_resource_allocation("actor1", "memory")

print(f"CPU allocation: {cpu_allocation}")
print(f"Memory allocation: {memory_allocation}")

# Adjust resource usage based on allocations
# ...
```

### Custom Policies

```python
# Define a custom policy
policy_config = {
    "type": "rule_based",
    "rules": [
        {
            "name": "high_workload_rule",
            "priority": 10,
            "conditions": [
                {
                    "type": "workload_level",
                    "parameters": {
                        "operator": ">=",
                        "value": "HIGH"
                    }
                }
            ],
            "actions": [
                {
                    "type": "switch_mode",
                    "parameters": {
                        "mode": "SEQUENTIAL",
                        "reason": "WORKLOAD"
                    }
                }
            ]
        }
    ]
}

# Register the policy
execution_manager.register_policy("high_workload_policy", policy_config)

# Enable the policy
execution_manager.enable_policy("high_workload_policy")
```

### Transition Handlers

```python
# Define a transition handler
def handle_transition_to_sequential(transition):
    print(f"Transitioning from {transition.source_mode.value} to {transition.target_mode.value}")
    print(f"Reason: {transition.reason.value}")
    print(f"Context: {transition.context}")

# Register the transition handler
execution_manager.register_transition_handler(
    source_mode=None,  # Any source mode
    target_mode=ExecutionMode.SEQUENTIAL,
    handler=handle_transition_to_sequential
)
```
