"""
Resource Monitor Module
==================

This module provides resource monitoring capabilities for the actor system,
enabling tracking of CPU usage, memory consumption, and I/O operations.

The resource monitor supports adaptive algorithms for optimizing execution mode
based on available resources, resource reservation mechanisms, and prioritization
of resource allocation based on actor importance.
"""

import asyncio
import logging
import os
import platform
import psutil
import time
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable, cast, Deque
from collections import deque

from ..metrics.registry import get_metrics_registry
from ..actor_registry import get_registry
from .execution_mode_manager import ExecutionMode, ModeTransitionReason

logger = logging.getLogger("vibe_check_actor_system.resource_monitor")


class ResourceType(Enum):
    """
    Types of resources monitored by the resource monitor.

    These types represent the different resources that can be monitored.
    """
    CPU = "cpu"
    MEMORY = "memory"
    DISK_IO = "disk_io"
    NETWORK_IO = "network_io"
    FILE_HANDLES = "file_handles"
    THREADS = "threads"
    CONNECTIONS = "connections"


class ResourceStatus(Enum):
    """
    Status of a resource.

    These statuses represent the current state of a resource.
    """
    ABUNDANT = "abundant"
    SUFFICIENT = "sufficient"
    LIMITED = "limited"
    SCARCE = "scarce"
    CRITICAL = "critical"


class ResourceMetrics:
    """
    Resource metrics for the actor system.

    This class represents the current resource metrics for the actor system,
    including CPU usage, memory consumption, and I/O operations.
    """

    def __init__(self):
        """Initialize the resource metrics."""
        self.timestamp = time.time()

        # CPU metrics
        self.cpu_percent = 0.0
        self.cpu_count = 0
        self.cpu_frequency = 0.0
        self.cpu_times = {}

        # Memory metrics
        self.memory_percent = 0.0
        self.memory_available = 0
        self.memory_used = 0
        self.memory_total = 0

        # Disk I/O metrics
        self.disk_io_read_count = 0
        self.disk_io_write_count = 0
        self.disk_io_read_bytes = 0
        self.disk_io_write_bytes = 0

        # Network I/O metrics
        self.network_io_sent_bytes = 0
        self.network_io_recv_bytes = 0
        self.network_io_sent_packets = 0
        self.network_io_recv_packets = 0

        # Process metrics
        self.process_count = 0
        self.thread_count = 0
        self.file_handle_count = 0
        self.connection_count = 0

        # Resource status
        self.resource_status = {
            ResourceType.CPU: ResourceStatus.SUFFICIENT,
            ResourceType.MEMORY: ResourceStatus.SUFFICIENT,
            ResourceType.DISK_IO: ResourceStatus.SUFFICIENT,
            ResourceType.NETWORK_IO: ResourceStatus.SUFFICIENT,
            ResourceType.FILE_HANDLES: ResourceStatus.SUFFICIENT,
            ResourceType.THREADS: ResourceStatus.SUFFICIENT,
            ResourceType.CONNECTIONS: ResourceStatus.SUFFICIENT
        }

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the metrics to a dictionary for serialization.

        Returns:
            Dictionary representation of the metrics
        """
        return {
            "timestamp": self.timestamp,
            "cpu_percent": self.cpu_percent,
            "cpu_count": self.cpu_count,
            "cpu_frequency": self.cpu_frequency,
            "cpu_times": self.cpu_times,
            "memory_percent": self.memory_percent,
            "memory_available": self.memory_available,
            "memory_used": self.memory_used,
            "memory_total": self.memory_total,
            "disk_io_read_count": self.disk_io_read_count,
            "disk_io_write_count": self.disk_io_write_count,
            "disk_io_read_bytes": self.disk_io_read_bytes,
            "disk_io_write_bytes": self.disk_io_write_bytes,
            "network_io_sent_bytes": self.network_io_sent_bytes,
            "network_io_recv_bytes": self.network_io_recv_bytes,
            "network_io_sent_packets": self.network_io_sent_packets,
            "network_io_recv_packets": self.network_io_recv_packets,
            "process_count": self.process_count,
            "thread_count": self.thread_count,
            "file_handle_count": self.file_handle_count,
            "connection_count": self.connection_count,
            "resource_status": {k.value: v.value for k, v in self.resource_status.items()}
        }


class ResourceReservation:
    """
    Resource reservation for an actor.

    This class represents a reservation of resources for an actor.
    """

    def __init__(self, actor_id: str, resources: Dict[ResourceType, float], priority: int = 0):
        """
        Initialize the resource reservation.

        Args:
            actor_id: ID of the actor
            resources: Dictionary mapping resource types to amounts
            priority: Priority of the reservation (higher is more important)
        """
        self.actor_id = actor_id
        self.resources = resources
        self.priority = priority
        self.timestamp = time.time()
        self.expiration: Optional[float] = None
        self.active = True

    def set_expiration(self, seconds: float) -> None:
        """
        Set the expiration time for the reservation.

        Args:
            seconds: Number of seconds until expiration
        """
        self.expiration = time.time() + seconds

    def is_expired(self) -> bool:
        """
        Check if the reservation is expired.

        Returns:
            True if the reservation is expired, False otherwise
        """
        if self.expiration is None:
            return False
        return time.time() > self.expiration

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the reservation to a dictionary for serialization.

        Returns:
            Dictionary representation of the reservation
        """
        return {
            "actor_id": self.actor_id,
            "resources": {k.value: v for k, v in self.resources.items()},
            "priority": self.priority,
            "timestamp": self.timestamp,
            "expiration": self.expiration,
            "active": self.active
        }


class ResourceMonitor:
    """
    Monitors the resources of the actor system.

    This class provides resource monitoring capabilities for the actor system,
    enabling tracking of CPU usage, memory consumption, and I/O operations.

    Attributes:
        metrics_history: History of resource metrics
        thresholds: Thresholds for resource statuses
        check_interval: Interval between resource checks
        mode_manager: Execution mode manager
        reservations: Dictionary of resource reservations by actor ID
    """

    def __init__(self, config: Dict[str, Any], mode_manager: Any):
        """
        Initialize the resource monitor.

        Args:
            config: Configuration dictionary
            mode_manager: Execution mode manager
        """
        self.config = config
        self.mode_manager = mode_manager

        # Initialize metrics history
        self.metrics_history: Deque[ResourceMetrics] = deque(maxlen=config.get("history_size", 100))

        # Initialize thresholds
        self.thresholds = {
            ResourceType.CPU: {
                ResourceStatus.ABUNDANT: config.get("cpu_abundant", 20.0),
                ResourceStatus.SUFFICIENT: config.get("cpu_sufficient", 50.0),
                ResourceStatus.LIMITED: config.get("cpu_limited", 70.0),
                ResourceStatus.SCARCE: config.get("cpu_scarce", 85.0),
                ResourceStatus.CRITICAL: config.get("cpu_critical", 95.0)
            },
            ResourceType.MEMORY: {
                ResourceStatus.ABUNDANT: config.get("memory_abundant", 20.0),
                ResourceStatus.SUFFICIENT: config.get("memory_sufficient", 50.0),
                ResourceStatus.LIMITED: config.get("memory_limited", 70.0),
                ResourceStatus.SCARCE: config.get("memory_scarce", 85.0),
                ResourceStatus.CRITICAL: config.get("memory_critical", 95.0)
            },
            ResourceType.DISK_IO: {
                ResourceStatus.ABUNDANT: config.get("disk_io_abundant", 20.0),
                ResourceStatus.SUFFICIENT: config.get("disk_io_sufficient", 50.0),
                ResourceStatus.LIMITED: config.get("disk_io_limited", 70.0),
                ResourceStatus.SCARCE: config.get("disk_io_scarce", 85.0),
                ResourceStatus.CRITICAL: config.get("disk_io_critical", 95.0)
            },
            ResourceType.NETWORK_IO: {
                ResourceStatus.ABUNDANT: config.get("network_io_abundant", 20.0),
                ResourceStatus.SUFFICIENT: config.get("network_io_sufficient", 50.0),
                ResourceStatus.LIMITED: config.get("network_io_limited", 70.0),
                ResourceStatus.SCARCE: config.get("network_io_scarce", 85.0),
                ResourceStatus.CRITICAL: config.get("network_io_critical", 95.0)
            },
            ResourceType.FILE_HANDLES: {
                ResourceStatus.ABUNDANT: config.get("file_handles_abundant", 20.0),
                ResourceStatus.SUFFICIENT: config.get("file_handles_sufficient", 50.0),
                ResourceStatus.LIMITED: config.get("file_handles_limited", 70.0),
                ResourceStatus.SCARCE: config.get("file_handles_scarce", 85.0),
                ResourceStatus.CRITICAL: config.get("file_handles_critical", 95.0)
            },
            ResourceType.THREADS: {
                ResourceStatus.ABUNDANT: config.get("threads_abundant", 20.0),
                ResourceStatus.SUFFICIENT: config.get("threads_sufficient", 50.0),
                ResourceStatus.LIMITED: config.get("threads_limited", 70.0),
                ResourceStatus.SCARCE: config.get("threads_scarce", 85.0),
                ResourceStatus.CRITICAL: config.get("threads_critical", 95.0)
            },
            ResourceType.CONNECTIONS: {
                ResourceStatus.ABUNDANT: config.get("connections_abundant", 20.0),
                ResourceStatus.SUFFICIENT: config.get("connections_sufficient", 50.0),
                ResourceStatus.LIMITED: config.get("connections_limited", 70.0),
                ResourceStatus.SCARCE: config.get("connections_scarce", 85.0),
                ResourceStatus.CRITICAL: config.get("connections_critical", 95.0)
            }
        }

        # Initialize check interval
        self.check_interval = config.get("check_interval", 5.0)  # seconds

        # Initialize monitoring task
        self.monitoring_task: Optional[asyncio.Task] = None
        self.running = False

        # Initialize resource reservations
        self.reservations: Dict[str, ResourceReservation] = {}

        # Initialize mode mapping
        self.mode_mapping = {
            ResourceStatus.ABUNDANT: ExecutionMode.PARALLEL,
            ResourceStatus.SUFFICIENT: ExecutionMode.PARALLEL,
            ResourceStatus.LIMITED: ExecutionMode.HYBRID,
            ResourceStatus.SCARCE: ExecutionMode.SEQUENTIAL,
            ResourceStatus.CRITICAL: ExecutionMode.EMERGENCY
        }

        # Override mode mapping from config
        mode_mapping_config = config.get("mode_mapping", {})
        for status_str, mode_str in mode_mapping_config.items():
            try:
                status = ResourceStatus(status_str)
                mode = ExecutionMode(mode_str)
                self.mode_mapping[status] = mode
            except ValueError:
                logger.warning(f"Invalid resource status or execution mode in config: {status_str} -> {mode_str}")

        # Initialize last mode switch time
        self.last_mode_switch_time = time.time()

        # Initialize resource weights
        self.resource_weights = {
            ResourceType.CPU: config.get("cpu_weight", 1.0),
            ResourceType.MEMORY: config.get("memory_weight", 1.0),
            ResourceType.DISK_IO: config.get("disk_io_weight", 0.5),
            ResourceType.NETWORK_IO: config.get("network_io_weight", 0.5),
            ResourceType.FILE_HANDLES: config.get("file_handles_weight", 0.2),
            ResourceType.THREADS: config.get("threads_weight", 0.3),
            ResourceType.CONNECTIONS: config.get("connections_weight", 0.3)
        }

        logger.info("Initialized resource monitor")

    async def start(self) -> None:
        """Start the resource monitor."""
        if self.running:
            logger.warning("Resource monitor is already running")
            return

        self.running = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Started resource monitor")

    async def stop(self) -> None:
        """Stop the resource monitor."""
        if not self.running:
            logger.warning("Resource monitor is not running")
            return

        self.running = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
            self.monitoring_task = None

        logger.info("Stopped resource monitor")

    async def _monitoring_loop(self) -> None:
        """Monitoring loop for the resource monitor."""
        try:
            while self.running:
                # Collect resource metrics
                metrics = await self._collect_resource_metrics()

                # Add metrics to history
                self.metrics_history.append(metrics)

                # Analyze resources
                await self._analyze_resources()

                # Clean up expired reservations
                self._clean_up_reservations()

                # Wait for the next check
                await asyncio.sleep(self.check_interval)
        except asyncio.CancelledError:
            logger.info("Resource monitoring loop cancelled")
        except Exception as e:
            logger.error(f"Error in resource monitoring loop: {e}")

    async def _collect_resource_metrics(self) -> ResourceMetrics:
        """
        Collect resource metrics.

        Returns:
            ResourceMetrics object with collected metrics
        """
        metrics = ResourceMetrics()

        try:
            # Collect CPU metrics
            metrics.cpu_percent = psutil.cpu_percent(interval=0.1)
            metrics.cpu_count = psutil.cpu_count()

            if hasattr(psutil, "cpu_freq"):
                cpu_freq = psutil.cpu_freq()
                if cpu_freq:
                    metrics.cpu_frequency = cpu_freq.current

            cpu_times = psutil.cpu_times_percent(interval=0.1)
            metrics.cpu_times = {
                "user": cpu_times.user,
                "system": cpu_times.system,
                "idle": cpu_times.idle
            }

            # Collect memory metrics
            memory = psutil.virtual_memory()
            metrics.memory_percent = memory.percent
            metrics.memory_available = memory.available
            metrics.memory_used = memory.used
            metrics.memory_total = memory.total

            # Collect disk I/O metrics
            disk_io = psutil.disk_io_counters()
            if disk_io:
                metrics.disk_io_read_count = disk_io.read_count
                metrics.disk_io_write_count = disk_io.write_count
                metrics.disk_io_read_bytes = disk_io.read_bytes
                metrics.disk_io_write_bytes = disk_io.write_bytes

            # Collect network I/O metrics
            net_io = psutil.net_io_counters()
            if net_io:
                metrics.network_io_sent_bytes = net_io.bytes_sent
                metrics.network_io_recv_bytes = net_io.bytes_recv
                metrics.network_io_sent_packets = net_io.packets_sent
                metrics.network_io_recv_packets = net_io.packets_recv

            # Collect process metrics
            metrics.process_count = len(psutil.pids())

            # Collect thread metrics
            current_process = psutil.Process()
            metrics.thread_count = current_process.num_threads()

            # Collect file handle metrics
            if platform.system() != "Windows":
                metrics.file_handle_count = current_process.num_fds()

            # Collect connection metrics
            connections = current_process.connections()
            metrics.connection_count = len(connections)

            # Determine resource status
            metrics.resource_status = self._determine_resource_status(metrics)
        except Exception as e:
            logger.error(f"Error collecting resource metrics: {e}")

        return metrics

    def _determine_resource_status(self, metrics: ResourceMetrics) -> Dict[ResourceType, ResourceStatus]:
        """
        Determine the status of each resource based on metrics.

        Args:
            metrics: Resource metrics

        Returns:
            Dictionary mapping resource types to statuses
        """
        resource_status = {}

        # Determine CPU status
        if metrics.cpu_percent <= self.thresholds[ResourceType.CPU][ResourceStatus.ABUNDANT]:
            resource_status[ResourceType.CPU] = ResourceStatus.ABUNDANT
        elif metrics.cpu_percent <= self.thresholds[ResourceType.CPU][ResourceStatus.SUFFICIENT]:
            resource_status[ResourceType.CPU] = ResourceStatus.SUFFICIENT
        elif metrics.cpu_percent <= self.thresholds[ResourceType.CPU][ResourceStatus.LIMITED]:
            resource_status[ResourceType.CPU] = ResourceStatus.LIMITED
        elif metrics.cpu_percent <= self.thresholds[ResourceType.CPU][ResourceStatus.SCARCE]:
            resource_status[ResourceType.CPU] = ResourceStatus.SCARCE
        else:
            resource_status[ResourceType.CPU] = ResourceStatus.CRITICAL

        # Determine memory status
        if metrics.memory_percent <= self.thresholds[ResourceType.MEMORY][ResourceStatus.ABUNDANT]:
            resource_status[ResourceType.MEMORY] = ResourceStatus.ABUNDANT
        elif metrics.memory_percent <= self.thresholds[ResourceType.MEMORY][ResourceStatus.SUFFICIENT]:
            resource_status[ResourceType.MEMORY] = ResourceStatus.SUFFICIENT
        elif metrics.memory_percent <= self.thresholds[ResourceType.MEMORY][ResourceStatus.LIMITED]:
            resource_status[ResourceType.MEMORY] = ResourceStatus.LIMITED
        elif metrics.memory_percent <= self.thresholds[ResourceType.MEMORY][ResourceStatus.SCARCE]:
            resource_status[ResourceType.MEMORY] = ResourceStatus.SCARCE
        else:
            resource_status[ResourceType.MEMORY] = ResourceStatus.CRITICAL

        # Determine disk I/O status (based on previous metrics if available)
        if len(self.metrics_history) > 1:
            prev_metrics = self.metrics_history[-2]
            time_diff = metrics.timestamp - prev_metrics.timestamp

            if time_diff > 0:
                # Calculate disk I/O rate
                read_rate = (metrics.disk_io_read_bytes - prev_metrics.disk_io_read_bytes) / time_diff
                write_rate = (metrics.disk_io_write_bytes - prev_metrics.disk_io_write_bytes) / time_diff

                # Calculate disk I/O utilization (as a percentage of some maximum value)
                # This is a simplified approach; in a real system, you would need to determine
                # the maximum disk I/O capacity of the system
                max_io_rate = 100 * 1024 * 1024  # 100 MB/s as an example
                io_utilization = (read_rate + write_rate) / max_io_rate * 100 if max_io_rate > 0 else 0

                # Determine disk I/O status
                if io_utilization <= self.thresholds[ResourceType.DISK_IO][ResourceStatus.ABUNDANT]:
                    resource_status[ResourceType.DISK_IO] = ResourceStatus.ABUNDANT
                elif io_utilization <= self.thresholds[ResourceType.DISK_IO][ResourceStatus.SUFFICIENT]:
                    resource_status[ResourceType.DISK_IO] = ResourceStatus.SUFFICIENT
                elif io_utilization <= self.thresholds[ResourceType.DISK_IO][ResourceStatus.LIMITED]:
                    resource_status[ResourceType.DISK_IO] = ResourceStatus.LIMITED
                elif io_utilization <= self.thresholds[ResourceType.DISK_IO][ResourceStatus.SCARCE]:
                    resource_status[ResourceType.DISK_IO] = ResourceStatus.SCARCE
                else:
                    resource_status[ResourceType.DISK_IO] = ResourceStatus.CRITICAL
            else:
                resource_status[ResourceType.DISK_IO] = ResourceStatus.SUFFICIENT
        else:
            resource_status[ResourceType.DISK_IO] = ResourceStatus.SUFFICIENT

        # Determine network I/O status (similar to disk I/O)
        if len(self.metrics_history) > 1:
            prev_metrics = self.metrics_history[-2]
            time_diff = metrics.timestamp - prev_metrics.timestamp

            if time_diff > 0:
                # Calculate network I/O rate
                sent_rate = (metrics.network_io_sent_bytes - prev_metrics.network_io_sent_bytes) / time_diff
                recv_rate = (metrics.network_io_recv_bytes - prev_metrics.network_io_recv_bytes) / time_diff

                # Calculate network I/O utilization
                max_net_rate = 100 * 1024 * 1024  # 100 MB/s as an example
                net_utilization = (sent_rate + recv_rate) / max_net_rate * 100 if max_net_rate > 0 else 0

                # Determine network I/O status
                if net_utilization <= self.thresholds[ResourceType.NETWORK_IO][ResourceStatus.ABUNDANT]:
                    resource_status[ResourceType.NETWORK_IO] = ResourceStatus.ABUNDANT
                elif net_utilization <= self.thresholds[ResourceType.NETWORK_IO][ResourceStatus.SUFFICIENT]:
                    resource_status[ResourceType.NETWORK_IO] = ResourceStatus.SUFFICIENT
                elif net_utilization <= self.thresholds[ResourceType.NETWORK_IO][ResourceStatus.LIMITED]:
                    resource_status[ResourceType.NETWORK_IO] = ResourceStatus.LIMITED
                elif net_utilization <= self.thresholds[ResourceType.NETWORK_IO][ResourceStatus.SCARCE]:
                    resource_status[ResourceType.NETWORK_IO] = ResourceStatus.SCARCE
                else:
                    resource_status[ResourceType.NETWORK_IO] = ResourceStatus.CRITICAL
            else:
                resource_status[ResourceType.NETWORK_IO] = ResourceStatus.SUFFICIENT
        else:
            resource_status[ResourceType.NETWORK_IO] = ResourceStatus.SUFFICIENT

        # For other resource types, use a simplified approach
        # File handles
        max_file_handles = 1024  # Example value
        file_handle_utilization = metrics.file_handle_count / max_file_handles * 100 if max_file_handles > 0 else 0

        if file_handle_utilization <= self.thresholds[ResourceType.FILE_HANDLES][ResourceStatus.ABUNDANT]:
            resource_status[ResourceType.FILE_HANDLES] = ResourceStatus.ABUNDANT
        elif file_handle_utilization <= self.thresholds[ResourceType.FILE_HANDLES][ResourceStatus.SUFFICIENT]:
            resource_status[ResourceType.FILE_HANDLES] = ResourceStatus.SUFFICIENT
        elif file_handle_utilization <= self.thresholds[ResourceType.FILE_HANDLES][ResourceStatus.LIMITED]:
            resource_status[ResourceType.FILE_HANDLES] = ResourceStatus.LIMITED
        elif file_handle_utilization <= self.thresholds[ResourceType.FILE_HANDLES][ResourceStatus.SCARCE]:
            resource_status[ResourceType.FILE_HANDLES] = ResourceStatus.SCARCE
        else:
            resource_status[ResourceType.FILE_HANDLES] = ResourceStatus.CRITICAL

        # Threads
        max_threads = 100  # Example value
        thread_utilization = metrics.thread_count / max_threads * 100 if max_threads > 0 else 0

        if thread_utilization <= self.thresholds[ResourceType.THREADS][ResourceStatus.ABUNDANT]:
            resource_status[ResourceType.THREADS] = ResourceStatus.ABUNDANT
        elif thread_utilization <= self.thresholds[ResourceType.THREADS][ResourceStatus.SUFFICIENT]:
            resource_status[ResourceType.THREADS] = ResourceStatus.SUFFICIENT
        elif thread_utilization <= self.thresholds[ResourceType.THREADS][ResourceStatus.LIMITED]:
            resource_status[ResourceType.THREADS] = ResourceStatus.LIMITED
        elif thread_utilization <= self.thresholds[ResourceType.THREADS][ResourceStatus.SCARCE]:
            resource_status[ResourceType.THREADS] = ResourceStatus.SCARCE
        else:
            resource_status[ResourceType.THREADS] = ResourceStatus.CRITICAL

        # Connections
        max_connections = 100  # Example value
        connection_utilization = metrics.connection_count / max_connections * 100 if max_connections > 0 else 0

        if connection_utilization <= self.thresholds[ResourceType.CONNECTIONS][ResourceStatus.ABUNDANT]:
            resource_status[ResourceType.CONNECTIONS] = ResourceStatus.ABUNDANT
        elif connection_utilization <= self.thresholds[ResourceType.CONNECTIONS][ResourceStatus.SUFFICIENT]:
            resource_status[ResourceType.CONNECTIONS] = ResourceStatus.SUFFICIENT
        elif connection_utilization <= self.thresholds[ResourceType.CONNECTIONS][ResourceStatus.LIMITED]:
            resource_status[ResourceType.CONNECTIONS] = ResourceStatus.LIMITED
        elif connection_utilization <= self.thresholds[ResourceType.CONNECTIONS][ResourceStatus.SCARCE]:
            resource_status[ResourceType.CONNECTIONS] = ResourceStatus.SCARCE
        else:
            resource_status[ResourceType.CONNECTIONS] = ResourceStatus.CRITICAL

        return resource_status

    async def _analyze_resources(self) -> None:
        """Analyze the resources and trigger mode switches if necessary."""
        # If we don't have any metrics, return
        if not self.metrics_history:
            return

        # Get the latest metrics
        latest_metrics = self.metrics_history[-1]

        # Get the current resource status
        resource_status = latest_metrics.resource_status

        # Get the current execution mode
        # Note: get_execution_mode is not an async method, so we don't need to await it
        current_mode = self.mode_manager.get_execution_mode()

        # Determine the most critical resource status
        most_critical_status = ResourceStatus.ABUNDANT
        most_critical_resource = None

        # Ensure resource_status is a dictionary before processing
        if not isinstance(resource_status, dict):
            # Handle non-dictionary resource_status
            logger.warning(f"Invalid resource_status type: {type(resource_status)}")

            # Create a default resource status dictionary to use instead
            resource_status = {
                ResourceType.CPU: ResourceStatus.SUFFICIENT,
                ResourceType.MEMORY: ResourceStatus.SUFFICIENT,
                ResourceType.DISK_IO: ResourceStatus.SUFFICIENT,
                ResourceType.NETWORK_IO: ResourceStatus.SUFFICIENT,
                ResourceType.FILE_HANDLES: ResourceStatus.SUFFICIENT,
                ResourceType.THREADS: ResourceStatus.SUFFICIENT,
                ResourceType.CONNECTIONS: ResourceStatus.SUFFICIENT
            }

        # Now process the resource status dictionary
        try:
            for resource_type, status in resource_status.items():
                # Skip resources with low weights
                if self.resource_weights.get(resource_type, 0) < 0.1:
                    continue

                # Check if this resource is more critical than the current most critical
                if self._is_more_critical(status, most_critical_status):
                    most_critical_status = status
                    most_critical_resource = resource_type
        except (TypeError, AttributeError) as e:
            # If there's still an error, log it and continue
            logger.warning(f"Error iterating over resource_status: {e}")

        # Get the target mode based on the most critical resource status
        target_mode = self.mode_mapping.get(most_critical_status, ExecutionMode.ADAPTIVE)

        # If the current mode is different from the target mode, consider switching
        if current_mode != target_mode:
            # Check if we should switch based on the resource status
            should_switch = self._should_switch_mode(most_critical_status, most_critical_resource)

            if should_switch:
                # Create context for the mode switch
                context = {
                    "resource_status": {k.value: v.value for k, v in resource_status.items()},
                    "most_critical_resource": most_critical_resource.value if most_critical_resource else None,
                    "most_critical_status": most_critical_status.value,
                    "metrics": latest_metrics.to_dict()
                }

                # Switch the execution mode
                logger.info(f"Switching execution mode from {current_mode.value} to {target_mode.value} due to resource constraints")
                await self.mode_manager.set_execution_mode(target_mode, ModeTransitionReason.RESOURCE, context)

                # Update last mode switch time
                self.last_mode_switch_time = time.time()

    def _is_more_critical(self, status1: ResourceStatus, status2: ResourceStatus) -> bool:
        """
        Check if status1 is more critical than status2.

        Args:
            status1: First resource status
            status2: Second resource status

        Returns:
            True if status1 is more critical than status2, False otherwise
        """
        # Define the criticality order (higher index = more critical)
        criticality_order = [
            ResourceStatus.ABUNDANT,
            ResourceStatus.SUFFICIENT,
            ResourceStatus.LIMITED,
            ResourceStatus.SCARCE,
            ResourceStatus.CRITICAL
        ]

        # Get the indices of the statuses in the criticality order
        try:
            status1_index = criticality_order.index(status1)
            status2_index = criticality_order.index(status2)

            # Compare the indices
            return status1_index > status2_index
        except ValueError:
            # If a status is not in the criticality order, assume it's not more critical
            logger.warning(f"Status {status1.value} or {status2.value} not found in criticality order")
            return False

    def _should_switch_mode(self, status: ResourceStatus, resource_type: Optional[ResourceType]) -> bool:
        """
        Check if we should switch modes based on the resource status.

        Args:
            status: Resource status
            resource_type: Resource type

        Returns:
            True if we should switch, False otherwise
        """
        # If we haven't switched modes recently, we can switch immediately
        current_time = time.time()
        if current_time - self.last_mode_switch_time > 60.0:  # 60 seconds cooldown
            return True

        # If the resource status is critical, we should switch immediately
        if status == ResourceStatus.CRITICAL:
            return True

        # If the resource status is abundant, we should switch immediately
        if status == ResourceStatus.ABUNDANT:
            return True

        # Otherwise, don't switch yet
        return False

    def _clean_up_reservations(self) -> None:
        """Clean up expired resource reservations."""
        # Get the current time
        current_time = time.time()

        # Identify expired reservations
        expired_reservations = []
        for actor_id, reservation in self.reservations.items():
            if reservation.is_expired():
                expired_reservations.append(actor_id)

        # Remove expired reservations
        for actor_id in expired_reservations:
            logger.info(f"Removing expired resource reservation for actor {actor_id}")
            del self.reservations[actor_id]
