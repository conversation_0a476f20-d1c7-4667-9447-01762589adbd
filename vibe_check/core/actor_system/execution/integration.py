"""
Execution Integration Module
========================

This module provides integration between the actor system and the execution mode
management components, enabling the actor system to use the enhanced execution
mode capabilities.

The integration module ensures backward compatibility with the existing execution
mode system while providing access to the new features.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable, cast

from .execution_mode_manager import ExecutionMode, ModeTransitionReason, ExecutionModeManager
from .workload_monitor import WorkloadMonitor
from .resource_monitor import ResourceMonitor
from .policy_manager import PolicyManager

logger = logging.getLogger("vibe_check_actor_system.execution_integration")


class ExecutionManager:
    """
    Manages execution for the actor system.

    This class provides a unified interface for the actor system to interact with
    the execution mode management components, including the execution mode manager,
    workload monitor, resource monitor, and policy manager.

    Attributes:
        mode_manager: Execution mode manager
        workload_monitor: Workload monitor
        resource_monitor: Resource monitor
        policy_manager: Policy manager
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the execution manager.

        Args:
            config: Configuration dictionary
        """
        self.config = config

        # Initialize execution mode manager
        self.mode_manager = ExecutionModeManager(config.get("execution_mode_manager", {}))

        # Initialize workload monitor
        self.workload_monitor = WorkloadMonitor(config.get("workload_monitor", {}), self.mode_manager)

        # Initialize resource monitor
        self.resource_monitor = ResourceMonitor(config.get("resource_monitor", {}), self.mode_manager)

        # Initialize policy manager
        self.policy_manager = PolicyManager(
            config.get("policy_manager", {}),
            self.mode_manager,
            self.workload_monitor,
            self.resource_monitor
        )

        logger.info("Initialized execution manager")

    async def start(self) -> None:
        """Start the execution manager."""
        # Start the execution mode manager
        await self.mode_manager.start()

        # Start the workload monitor
        await self.workload_monitor.start()

        # Start the resource monitor
        await self.resource_monitor.start()

        # Start the policy manager
        await self.policy_manager.start()

        logger.info("Started execution manager")

    async def stop(self) -> None:
        """Stop the execution manager."""
        # Stop the policy manager
        await self.policy_manager.stop()

        # Stop the resource monitor
        await self.resource_monitor.stop()

        # Stop the workload monitor
        await self.workload_monitor.stop()

        # Stop the execution mode manager
        await self.mode_manager.stop()

        logger.info("Stopped execution manager")

    def get_execution_mode(self) -> ExecutionMode:
        """
        Get the current execution mode.

        Returns:
            Current execution mode
        """
        return self.mode_manager.get_execution_mode()

    async def set_execution_mode(self, mode: Union[str, ExecutionMode],
                               reason: Union[str, ModeTransitionReason] = ModeTransitionReason.MANUAL,
                               context: Optional[Dict[str, Any]] = None) -> bool:
        """
        Set the execution mode.

        Args:
            mode: Execution mode to set
            reason: Reason for the mode change
            context: Optional context for the mode change

        Returns:
            True if the mode was changed, False otherwise
        """
        return await self.mode_manager.set_execution_mode(mode, reason, context)

    def get_mode_property(self, property_name: str) -> Any:
        """
        Get a property of the current execution mode.

        Args:
            property_name: Name of the property to get

        Returns:
            Property value, or None if not found
        """
        return self.mode_manager.get_mode_property(property_name)

    def should_process_message(self, actor_id: str, message_id: str, priority: int = 0) -> bool:
        """
        Check if a message should be processed based on the current execution mode.

        Args:
            actor_id: ID of the actor
            message_id: ID of the message
            priority: Priority of the message

        Returns:
            True if the message should be processed, False otherwise
        """
        # Get the current execution mode
        mode = self.get_execution_mode()

        # Get the concurrency limit for the current mode
        concurrency_limit = self.get_mode_property("concurrency_limit")

        # If there's no concurrency limit, always process the message
        if concurrency_limit is None:
            return True

        # If the concurrency limit is "auto", use adaptive logic
        if concurrency_limit == "auto":
            # In adaptive mode, we adjust the concurrency limit based on the current workload
            # and resource availability

            # Get the workload level from the workload monitor
            workload_level = None
            if self.workload_monitor and self.workload_monitor.metrics_history:
                latest_metrics = self.workload_monitor.metrics_history[-1]
                workload_level = latest_metrics.workload_level

            # Get the resource status from the resource monitor
            resource_status = {}
            if self.resource_monitor and self.resource_monitor.metrics_history:
                # Use Any to avoid type issues
                resource_metrics: Any = self.resource_monitor.metrics_history[-1]
                if hasattr(resource_metrics, "resource_status"):
                    resource_status = resource_metrics.resource_status

            # Determine the effective concurrency limit based on workload and resources
            effective_limit = self._determine_adaptive_concurrency_limit(workload_level, resource_status)

            # Get the number of messages currently being processed by the actor
            # In a real implementation, we would get this from a metrics registry
            # For now, we'll use a placeholder value
            current_processing = 0  # Placeholder

            # Check if the actor is already processing too many messages
            return current_processing < effective_limit

        # If the concurrency limit is a number, use it directly
        if isinstance(concurrency_limit, int):
            # Get the number of messages currently being processed by the actor
            # In a real implementation, we would get this from a metrics registry
            # For now, we'll use a placeholder value
            current_processing = 0  # Placeholder

            # Get the priority boost for the current mode
            priority_boost = self.get_mode_property("priority_boost")

            # Initialize effective priority with the original priority
            effective_priority = priority

            # Apply priority boost if available
            if isinstance(priority_boost, dict):
                # In a real implementation, we would get the actor type from the actor registry
                # For now, we'll use a placeholder value
                actor_type = "default"  # Placeholder
                effective_priority += priority_boost.get(actor_type, 0)

            # High-priority messages can bypass the concurrency limit
            if effective_priority >= 8:  # Arbitrary threshold for high priority
                return True

            # Check if the actor is already processing too many messages
            return current_processing < concurrency_limit

        # Default to allowing the message to be processed
        return True

    def _determine_adaptive_concurrency_limit(self, workload_level: Any, resource_status: Dict[Any, Any]) -> int:
        """
        Determine the adaptive concurrency limit based on workload and resources.

        Args:
            workload_level: Current workload level
            resource_status: Current resource status

        Returns:
            Adaptive concurrency limit
        """
        # Default to a moderate concurrency limit
        default_limit = 10

        # If we don't have workload or resource information, use the default
        if not workload_level or not resource_status:
            return default_limit

        # Adjust the limit based on workload level
        workload_factor = 1.0
        if hasattr(workload_level, "value"):
            if workload_level.value == "very_low":
                workload_factor = 2.0
            elif workload_level.value == "low":
                workload_factor = 1.5
            elif workload_level.value == "normal":
                workload_factor = 1.0
            elif workload_level.value == "high":
                workload_factor = 0.5
            elif workload_level.value == "very_high":
                workload_factor = 0.2
            elif workload_level.value == "critical":
                workload_factor = 0.1

        # Adjust the limit based on resource status
        resource_factor = 1.0
        cpu_status = None
        memory_status = None

        for resource_type, status in resource_status.items():
            if hasattr(resource_type, "value") and resource_type.value == "cpu":
                cpu_status = status
            elif hasattr(resource_type, "value") and resource_type.value == "memory":
                memory_status = status

        if cpu_status and hasattr(cpu_status, "value"):
            if cpu_status.value == "abundant":
                resource_factor *= 2.0
            elif cpu_status.value == "sufficient":
                resource_factor *= 1.5
            elif cpu_status.value == "limited":
                resource_factor *= 1.0
            elif cpu_status.value == "scarce":
                resource_factor *= 0.5
            elif cpu_status.value == "critical":
                resource_factor *= 0.2

        if memory_status and hasattr(memory_status, "value"):
            if memory_status.value == "abundant":
                resource_factor *= 1.5
            elif memory_status.value == "sufficient":
                resource_factor *= 1.2
            elif memory_status.value == "limited":
                resource_factor *= 1.0
            elif memory_status.value == "scarce":
                resource_factor *= 0.8
            elif memory_status.value == "critical":
                resource_factor *= 0.5

        # Calculate the final limit
        final_limit = int(default_limit * workload_factor * resource_factor)

        # Ensure the limit is at least 1
        return max(1, final_limit)

    def get_batch_size(self, actor_id: Optional[str] = None) -> int:
        """
        Get the batch size for an actor based on the current execution mode.

        Args:
            actor_id: ID of the actor, or None for default batch size

        Returns:
            Batch size for the actor
        """
        # Get the current execution mode
        mode = self.get_execution_mode()

        # Get the message batch size for the current mode
        batch_size = self.get_mode_property("message_batch_size")

        # If the batch size is "auto", use adaptive logic
        if batch_size == "auto":
            # In adaptive mode, we adjust the batch size based on the current workload
            # and resource availability

            # Get the workload level from the workload monitor
            workload_level = None
            if self.workload_monitor and self.workload_monitor.metrics_history:
                latest_metrics = self.workload_monitor.metrics_history[-1]
                workload_level = latest_metrics.workload_level

            # Get the resource status from the resource monitor
            resource_status = {}
            if self.resource_monitor and self.resource_monitor.metrics_history:
                # Use Any to avoid type issues
                resource_metrics: Any = self.resource_monitor.metrics_history[-1]
                if hasattr(resource_metrics, "resource_status"):
                    resource_status = resource_metrics.resource_status

            # Determine the adaptive batch size based on workload and resources
            return self._determine_adaptive_batch_size(workload_level, resource_status, actor_id)

        # If the batch size is not a number, use a default value
        if not isinstance(batch_size, int):
            return 1

        return batch_size

    def _determine_adaptive_batch_size(self, workload_level: Any, resource_status: Dict[Any, Any], actor_id: Optional[str] = None) -> int:
        """
        Determine the adaptive batch size based on workload and resources.

        Args:
            workload_level: Current workload level
            resource_status: Current resource status
            actor_id: ID of the actor, or None for default batch size

        Returns:
            Adaptive batch size
        """
        # Default to a moderate batch size
        default_batch_size = 10

        # If we don't have workload or resource information, use the default
        if not workload_level or not resource_status:
            return default_batch_size

        # Adjust the batch size based on workload level
        workload_factor = 1.0
        if hasattr(workload_level, "value"):
            if workload_level.value == "very_low":
                workload_factor = 2.0
            elif workload_level.value == "low":
                workload_factor = 1.5
            elif workload_level.value == "normal":
                workload_factor = 1.0
            elif workload_level.value == "high":
                workload_factor = 0.7
            elif workload_level.value == "very_high":
                workload_factor = 0.5
            elif workload_level.value == "critical":
                workload_factor = 0.2

        # Adjust the batch size based on resource status
        resource_factor = 1.0
        cpu_status = None
        memory_status = None

        for resource_type, status in resource_status.items():
            if hasattr(resource_type, "value") and resource_type.value == "cpu":
                cpu_status = status
            elif hasattr(resource_type, "value") and resource_type.value == "memory":
                memory_status = status

        if cpu_status and hasattr(cpu_status, "value"):
            if cpu_status.value == "abundant":
                resource_factor *= 1.5
            elif cpu_status.value == "sufficient":
                resource_factor *= 1.2
            elif cpu_status.value == "limited":
                resource_factor *= 1.0
            elif cpu_status.value == "scarce":
                resource_factor *= 0.7
            elif cpu_status.value == "critical":
                resource_factor *= 0.5

        if memory_status and hasattr(memory_status, "value"):
            if memory_status.value == "abundant":
                resource_factor *= 1.3
            elif memory_status.value == "sufficient":
                resource_factor *= 1.1
            elif memory_status.value == "limited":
                resource_factor *= 1.0
            elif memory_status.value == "scarce":
                resource_factor *= 0.8
            elif memory_status.value == "critical":
                resource_factor *= 0.6

        # Calculate the final batch size
        final_batch_size = int(default_batch_size * workload_factor * resource_factor)

        # Ensure the batch size is at least 1
        return max(1, final_batch_size)

    def get_resource_allocation(self, actor_id: Optional[str] = None, resource_type: str = "cpu") -> float:
        """
        Get the resource allocation for an actor based on the current execution mode.

        Args:
            actor_id: ID of the actor, or None for default allocation
            resource_type: Type of resource (default: "cpu")

        Returns:
            Resource allocation for the actor (0.0 to 1.0)
        """
        # Get the current execution mode
        mode = self.get_execution_mode()

        # Get the resource allocation for the current mode
        resource_allocation = self.get_mode_property("resource_allocation") or {}

        # Get the allocation for the specified resource type
        allocation = resource_allocation.get(resource_type, 1.0)

        # If the allocation is "auto", use adaptive logic
        if allocation == "auto":
            # In adaptive mode, we adjust the resource allocation based on the current workload
            # and resource availability

            # Get the workload level from the workload monitor
            workload_level = None
            if self.workload_monitor and self.workload_monitor.metrics_history:
                latest_metrics = self.workload_monitor.metrics_history[-1]
                workload_level = latest_metrics.workload_level

            # Get the resource status from the resource monitor
            resource_status = {}
            if self.resource_monitor and self.resource_monitor.metrics_history:
                # Use Any to avoid type issues
                resource_metrics: Any = self.resource_monitor.metrics_history[-1]
                if hasattr(resource_metrics, "resource_status"):
                    resource_status = resource_metrics.resource_status

            # Determine the adaptive resource allocation based on workload and resources
            return self._determine_adaptive_resource_allocation(workload_level, resource_status, actor_id, resource_type)

        # If the allocation is not a number, use a default value
        if not isinstance(allocation, (int, float)):
            return 1.0

        return allocation

    def _determine_adaptive_resource_allocation(self, workload_level: Any, resource_status: Dict[Any, Any],
                                              actor_id: Optional[str] = None, resource_type: str = "cpu") -> float:
        """
        Determine the adaptive resource allocation based on workload and resources.

        Args:
            workload_level: Current workload level
            resource_status: Current resource status
            actor_id: ID of the actor, or None for default allocation
            resource_type: Type of resource (default: "cpu")

        Returns:
            Adaptive resource allocation (0.0 to 1.0)
        """
        # Default to a moderate allocation
        default_allocation = 0.8

        # If we don't have workload or resource information, use the default
        if not workload_level or not resource_status:
            return default_allocation

        # Adjust the allocation based on workload level
        workload_factor = 1.0
        if hasattr(workload_level, "value"):
            if workload_level.value == "very_low":
                workload_factor = 1.2
            elif workload_level.value == "low":
                workload_factor = 1.1
            elif workload_level.value == "normal":
                workload_factor = 1.0
            elif workload_level.value == "high":
                workload_factor = 0.9
            elif workload_level.value == "very_high":
                workload_factor = 0.8
            elif workload_level.value == "critical":
                workload_factor = 0.7

        # Adjust the allocation based on resource status
        resource_factor = 1.0

        # Find the status of the requested resource type
        requested_status = None
        for res_type, status in resource_status.items():
            if hasattr(res_type, "value") and res_type.value == resource_type:
                requested_status = status
                break

        if requested_status and hasattr(requested_status, "value"):
            if requested_status.value == "abundant":
                resource_factor = 1.2
            elif requested_status.value == "sufficient":
                resource_factor = 1.1
            elif requested_status.value == "limited":
                resource_factor = 1.0
            elif requested_status.value == "scarce":
                resource_factor = 0.8
            elif requested_status.value == "critical":
                resource_factor = 0.6

        # Calculate the final allocation
        final_allocation = default_allocation * workload_factor * resource_factor

        # Ensure the allocation is between 0.0 and 1.0
        return max(0.0, min(1.0, final_allocation))

    def register_policy(self, policy_name: str, policy_config: Dict[str, Any]) -> None:
        """
        Register a policy.

        Args:
            policy_name: Name of the policy
            policy_config: Configuration for the policy
        """
        if self.policy_manager:
            # In a real implementation, we would register the policy with the policy manager
            # For now, we'll just log it
            logger.info(f"Registering policy: {policy_name}")

    def enable_policy(self, policy_name: str) -> None:
        """
        Enable a policy.

        Args:
            policy_name: Name of the policy to enable
        """
        if self.policy_manager:
            # In a real implementation, we would enable the policy in the policy manager
            # For now, we'll just log it
            logger.info(f"Enabling policy: {policy_name}")

    def disable_policy(self, policy_name: str) -> None:
        """
        Disable a policy.

        Args:
            policy_name: Name of the policy to disable
        """
        if self.policy_manager:
            # In a real implementation, we would disable the policy in the policy manager
            # For now, we'll just log it
            logger.info(f"Disabling policy: {policy_name}")

    def get_status(self) -> Dict[str, Any]:
        """
        Get the status of the execution manager.

        Returns:
            Dictionary with status information
        """
        status = {
            "execution_mode": self.get_execution_mode().value,
            "workload_monitor": {
                "running": self.workload_monitor.running if self.workload_monitor else False
            },
            "resource_monitor": {
                "running": self.resource_monitor.running if self.resource_monitor else False
            },
            "policy_manager": {
                "running": self.policy_manager.running if self.policy_manager else False,
                "policies": len(self.policy_manager.policies) if self.policy_manager else 0
            }
        }

        # Add mode properties
        status["mode_properties"] = {
            "concurrency_limit": self.get_mode_property("concurrency_limit"),
            "message_batch_size": self.get_mode_property("message_batch_size"),
            "priority_boost": self.get_mode_property("priority_boost"),
            "resource_allocation": self.get_mode_property("resource_allocation")
        }

        # Add workload metrics if available
        if self.workload_monitor and self.workload_monitor.metrics_history:
            workload_metrics: Any = self.workload_monitor.metrics_history[-1]
            status["workload_metrics"] = {
                "workload_level": workload_metrics.workload_level.value if hasattr(workload_metrics.workload_level, "value") else None,
                "workload_trend": workload_metrics.workload_trend.value if hasattr(workload_metrics.workload_trend, "value") else None,
                "messages_per_second": workload_metrics.messages_per_second,
                "average_queue_depth": workload_metrics.average_queue_depth,
                "actor_utilization": workload_metrics.actor_utilization
            }

        # Add resource metrics if available
        if self.resource_monitor and self.resource_monitor.metrics_history:
            resource_metrics: Any = self.resource_monitor.metrics_history[-1]
            status["resource_metrics"] = {
                "cpu_percent": resource_metrics.cpu_percent if hasattr(resource_metrics, "cpu_percent") else 0,
                "memory_percent": resource_metrics.memory_percent if hasattr(resource_metrics, "memory_percent") else 0,
                "disk_io_read_bytes": resource_metrics.disk_io_read_bytes if hasattr(resource_metrics, "disk_io_read_bytes") else 0,
                "disk_io_write_bytes": resource_metrics.disk_io_write_bytes if hasattr(resource_metrics, "disk_io_write_bytes") else 0,
                "network_io_sent_bytes": resource_metrics.network_io_sent_bytes if hasattr(resource_metrics, "network_io_sent_bytes") else 0,
                "network_io_recv_bytes": resource_metrics.network_io_recv_bytes if hasattr(resource_metrics, "network_io_recv_bytes") else 0
            }

        return status

    def register_transition_handler(self, source_mode: Optional[ExecutionMode],
                                  target_mode: ExecutionMode,
                                  handler: Callable[[Any], None]) -> None:
        """
        Register a handler for mode transitions.

        Args:
            source_mode: Source execution mode (None for any source mode)
            target_mode: Target execution mode
            handler: Handler function to call during the transition
        """
        self.mode_manager.register_transition_handler(source_mode, target_mode, handler)


# Global execution manager instance
_execution_manager: Optional[ExecutionManager] = None


def get_execution_manager(config: Optional[Dict[str, Any]] = None) -> ExecutionManager:
    """
    Get the global execution manager instance.

    Args:
        config: Optional configuration dictionary

    Returns:
        Global execution manager instance
    """
    global _execution_manager
    if _execution_manager is None:
        if config is None:
            config = {}
        _execution_manager = ExecutionManager(config)
    return _execution_manager


async def initialize_execution_manager(config: Optional[Dict[str, Any]] = None) -> None:
    """
    Initialize the execution manager.

    Args:
        config: Optional configuration dictionary
    """
    manager = get_execution_manager(config)
    await manager.start()


async def shutdown_execution_manager() -> None:
    """Shut down the execution manager."""
    global _execution_manager
    if _execution_manager is not None:
        await _execution_manager.stop()
        _execution_manager = None
