"""
Execution Package
=============

This package provides adaptive execution mode management for the actor system,
enabling automatic switching between different execution modes based on system
conditions.

The execution package includes components for managing execution modes, monitoring
workload and resources, defining and enforcing policies, and integrating with
the actor system.
"""

from .execution_mode_manager import (
    ExecutionMode,
    ModeTransitionReason,
    ModeTransition,
    ExecutionModeManager
)

from .workload_monitor import (
    WorkloadLevel,
    WorkloadTrend,
    WorkloadMetrics,
    WorkloadMonitor
)

from .resource_monitor import (
    ResourceType,
    ResourceStatus,
    ResourceMetrics,
    ResourceReservation,
    ResourceMonitor
)

from .policy_manager import (
    PolicyType,
    PolicyAction,
    PolicyCondition,
    PolicyRule,
    Policy,
    PolicyManager
)

from .integration import (
    ExecutionManager,
    get_execution_manager,
    initialize_execution_manager,
    shutdown_execution_manager
)

__all__ = [
    # Execution Mode Manager
    'ExecutionMode',
    'ModeTransitionReason',
    'ModeTransition',
    'ExecutionModeManager',
    
    # Workload Monitor
    'WorkloadLevel',
    'WorkloadTrend',
    'WorkloadMetrics',
    'WorkloadMonitor',
    
    # Resource Monitor
    'ResourceType',
    'ResourceStatus',
    'ResourceMetrics',
    'ResourceReservation',
    'ResourceMonitor',
    
    # Policy Manager
    'PolicyType',
    'PolicyAction',
    'PolicyCondition',
    'PolicyRule',
    'Policy',
    'PolicyManager',
    
    # Integration
    'ExecutionManager',
    'get_execution_manager',
    'initialize_execution_manager',
    'shutdown_execution_manager'
]
