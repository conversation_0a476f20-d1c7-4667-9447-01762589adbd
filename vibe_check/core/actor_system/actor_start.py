"""
Actor Start Method
================

This module contains the implementation of the start method for the Actor class.
It has been extracted to a separate file to make it easier to maintain and test.

Enhanced with comprehensive debugging capabilities for diagnosing and resolving
start-up issues, including detailed logging, timing information, and error tracking.
"""

import asyncio
import logging
import time
import traceback
from typing import Any, Dict, List, Optional, Set, Union, TYPE_CHECKING, cast

if TYPE_CHECKING:
    from .actor import Actor
    from .message import Message

from .actor_state import ActorState
from .exceptions import (
    ActorInitializationError,
    ActorTimeoutError
)
from .consolidated_initializer import get_initializer
from .diagnostics import get_tracker, InitializationStep
from .logging.initialization_debug import (
    InitializationStep as DebugStep,
    log_init_event,
    init_step_timing,
    init_debug_async_decorator,
    is_init_debugging_enabled
)

# Configure logging
logger = logging.getLogger("vibe_check_actor_system")


@init_debug_async_decorator(DebugStep.START)
async def start_actor(self: 'Actor', timeout: float = 120.0) -> None:
    """
    Start the actor (second phase of two-phase initialization).

    This method starts the actor and begins processing messages. It's the second
    phase of the two-phase initialization process, following the initialize method.

    Args:
        self: The actor instance
        timeout: Maximum time in seconds to wait for the actor to start

    Raises:
        ActorInitializationError: If the actor fails to start
        ActorTimeoutError: If starting times out

    Enhanced with robust error handling, explicit synchronization points, and
    improved diagnostics. Now uses the new ActorInitializationManager.
    """
    # First, ensure we're registered with both initializers
    try:
        # Log the registration check
        if is_init_debugging_enabled():
            log_init_event(
                actor_id=self.actor_id,
                step=DebugStep.REGISTRATION,
                message="Ensuring actor is registered with initializers before starting",
                details={"timeout": timeout}
            )

        await self._ensure_registered_with_initializer()

        # Log successful registration check
        if is_init_debugging_enabled():
            log_init_event(
                actor_id=self.actor_id,
                step=DebugStep.REGISTRATION,
                message="Actor successfully verified registration with initializers"
            )
    except Exception as e:
        logger.error(f"Error ensuring actor {self.actor_id} is registered with initializers: {e}")
        import traceback
        logger.error(traceback.format_exc())

        # Log the registration failure with detailed error information
        if is_init_debugging_enabled():
            log_init_event(
                actor_id=self.actor_id,
                step=DebugStep.FAILED,
                message=f"Failed to verify registration with initializers: {str(e)}",
                error=e,
                stack_trace=True,
                details={"phase": "registration", "state": ActorState.CREATED.value}
            )

        raise ActorInitializationError(
            message=f"Failed to register with initializers: {str(e)}",
            actor_id=self.actor_id,
            phase="registration",
            state=ActorState.CREATED,
            original_error=e
        )

    # Get the ConsolidatedActorInitializer
    initializer = get_initializer()

    # Create a timeout for the entire start process
    try:
        # Use wait_for instead of asyncio.timeout for compatibility with older Python versions
        async def _start_with_timeout() -> None:
            try:
                # Check if the actor is initialized
                if not self._initialization_complete:
                    logger.warning(f"Actor {self.actor_id} is not initialized, initializing now")
                    await self.initialize(timeout=timeout)

                # Set actor state to STARTING in the ConsolidatedActorInitializer
                if initializer:
                    await initializer.set_actor_state(
                        actor_id=self.actor_id,
                        state=ActorState.STARTING,
                        error=None,
                        phase="start"
                    )

                # No legacy components to update

                # Record start begin with the tracker
                tracker = get_tracker()
                if tracker:
                    await tracker.record_event(
                        actor_id=self.actor_id,
                        step=InitializationStep.START_BEGIN,
                        details={"state": ActorState.STARTING.value}
                    )

                # Start the actor using the starter component
                # This delegates to the ActorStarter component
                await self._starter.start()

                # Set the actor as running
                self._is_running = True

                # Start the message processing task if not already running
                if self._process_task is None or self._process_task.done():
                    self._process_task = asyncio.create_task(self.process_messages())
                    logger.debug(f"Started message processing task for actor {self.actor_id}")

                # Start the heartbeat task if not already running
                if self._heartbeat_task is None or self._heartbeat_task.done():
                    self._heartbeat_task = asyncio.create_task(self._send_heartbeats())
                    logger.debug(f"Started heartbeat task for actor {self.actor_id}")

                # Start the metrics task if not already running
                if self._metrics_task is None or self._metrics_task.done():
                    self._metrics_task = asyncio.create_task(self._collect_metrics())
                    logger.debug(f"Started metrics task for actor {self.actor_id}")

                # Process any pending messages
                if self._pending_messages:
                    logger.info(f"Processing {len(self._pending_messages)} pending messages for actor {self.actor_id}")
                    for queued_message in self._pending_messages:
                        # Handle different types of pending messages
                        try:
                            if isinstance(queued_message, dict) and "message" in queued_message and "queued_at" in queued_message:
                                message = queued_message["message"]
                                queued_at = queued_message["queued_at"]
                                wait_time = time.time() - queued_at

                                # Get message type name safely
                                type_name = "unknown"
                                if hasattr(message, "type") and hasattr(message.type, "name"):
                                    type_name = message.type.name

                                logger.debug(f"Processing pending message of type {type_name} (waited {wait_time:.2f}s)")
                                # Use cast to tell mypy that this is a Message
                                from .message import Message
                                await self.mailbox.put(cast(Message, message))
                            else:
                                # Handle legacy format or direct messages
                                logger.debug(f"Processing pending message in legacy format")
                                # Skip messages that don't match the expected type
                                if hasattr(queued_message, "type"):
                                    # Use cast to tell mypy that this is a Message
                                    from .message import Message
                                    await self.mailbox.put(cast(Message, queued_message))
                                else:
                                    logger.warning(f"Skipping invalid pending message: {queued_message}")
                        except Exception as e:
                            logger.warning(f"Error processing pending message: {e}")

                    # Clear the pending messages
                    self._pending_messages.clear()

                # Set the actor as ready
                self._ready = True
                self._ready_event.set()

                # Set actor state to READY in the ConsolidatedActorInitializer
                if initializer:
                    await initializer.set_actor_state(
                        actor_id=self.actor_id,
                        state=ActorState.READY,
                        error=None,
                        phase="start"
                    )

                # No legacy components to update

                # Record ready with the tracker
                if tracker:
                    await tracker.record_event(
                        actor_id=self.actor_id,
                        step=InitializationStep.READY,
                        details={"state": ActorState.READY.value}
                    )

                logger.info(f"Actor {self.actor_id} started and ready")
            except Exception as e:
                logger.error(f"Error starting actor {self.actor_id}: {e}")
                import traceback
                logger.error(traceback.format_exc())

                # Set actor state to FAILED in the ConsolidatedActorInitializer
                try:
                    # Set state in the ConsolidatedActorInitializer
                    if initializer:
                        await initializer.set_actor_state(
                            actor_id=self.actor_id,
                            state=ActorState.FAILED,
                            error=e,
                            phase="start"
                        )

                        # Clean up resources in the ConsolidatedActorInitializer
                        await initializer.cleanup_actor(self.actor_id)

                    # No legacy components to update
                except Exception as state_error:
                    logger.error(f"Error setting actor {self.actor_id} state to FAILED: {state_error}")

                # Record failure with the tracker
                tracker = get_tracker()
                if tracker:
                    await tracker.record_event(
                        actor_id=self.actor_id,
                        step=InitializationStep.FAILED,
                        details={"phase": "start", "error": str(e)},
                        error=e
                    )

                # Wrap the exception in an ActorInitializationError
                raise ActorInitializationError(
                    message=f"Failed to start actor {self.actor_id}: {str(e)}",
                    actor_id=self.actor_id,
                    phase="start",
                    state=ActorState.STARTING,
                    original_error=e
                ) from e

        # Execute the start process with a timeout
        try:
            # Log start timeout start
            if is_init_debugging_enabled():
                log_init_event(
                    actor_id=self.actor_id,
                    step=DebugStep.START,
                    message=f"Starting actor with timeout of {timeout} seconds",
                    details={"timeout": timeout}
                )

            # Start timing the start process
            start_time = time.time()
            await asyncio.wait_for(_start_with_timeout(), timeout=timeout)

            # Log successful start with timing
            if is_init_debugging_enabled():
                duration = time.time() - start_time
                log_init_event(
                    actor_id=self.actor_id,
                    step=DebugStep.READY,
                    message=f"Actor started successfully in {duration:.4f} seconds",
                    details={"duration": duration, "timeout": timeout}
                )

        except asyncio.TimeoutError:
            # Calculate actual duration before timeout
            duration = time.time() - start_time
            logger.error(f"Timeout starting actor {self.actor_id} after {timeout} seconds (actual duration: {duration:.4f}s)")

            # Create a timeout exception
            timeout_error = ActorTimeoutError(
                message=f"Start timed out after {timeout} seconds",
                actor_id=self.actor_id,
                operation="start",
                timeout=timeout
            )

            # Log timeout error with detailed information
            if is_init_debugging_enabled():
                log_init_event(
                    actor_id=self.actor_id,
                    step=DebugStep.TIMEOUT,
                    message=f"Start timed out after {timeout} seconds",
                    error=timeout_error,
                    details={
                        "timeout": timeout,
                        "actual_duration": duration,
                        "operation": "start"
                    }
                )

            # Set actor state to FAILED in the initializer
            try:
                # Set state in the initializer
                await initializer.set_actor_state(
                    actor_id=self.actor_id,
                    state=ActorState.FAILED,
                    error=timeout_error,
                    phase="start"
                )

                # Clean up resources in the initializer
                await initializer.cleanup_actor(self.actor_id)
            except Exception as state_error:
                logger.error(f"Error setting actor {self.actor_id} state to FAILED: {state_error}")

            # Clean up resources
            await self._cleanup_resources()

            # Raise the timeout exception
            raise timeout_error
    except Exception as e:
        # This catches any exceptions from the timeout context
        logger.error(f"Error in start process for actor {self.actor_id}: {e}")

        # Clean up resources
        await self._cleanup_resources()

        # Re-raise the exception
        raise
