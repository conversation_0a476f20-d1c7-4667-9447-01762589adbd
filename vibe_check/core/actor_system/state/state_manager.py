"""
Actor State Management Module
============================

This module provides state management functionality for actors,
including state persistence, loading, and metrics collection.
"""

import asyncio
import json
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict, Optional, Set, TYPE_CHECKING

if TYPE_CHECKING:
    from ..actor import Actor

logger = logging.getLogger("vibe_check_actor_state")


class ActorStateManager:
    """
    Manages actor state persistence and metrics collection.
    
    This class handles saving and loading actor state to/from disk,
    as well as collecting and reporting metrics.
    """

    def __init__(self, actor: 'Actor'):
        """
        Initialize the actor state manager.

        Args:
            actor: The actor instance that owns this state manager
        """
        self.actor = actor
        self._metrics_task: Optional[asyncio.Task[None]] = None
        self._metrics_interval: float = 30.0  # seconds

    async def save_state(self) -> None:
        """Save actor state to disk."""
        if not self.actor.state_dir:
            return

        try:
            # Create state directory if it doesn't exist
            os.makedirs(self.actor.state_dir, exist_ok=True)

            # Get state data
            state_data = self._get_state()

            # Save to file
            state_file = Path(self.actor.state_dir) / f"{self.actor.actor_id}_state.json"
            with open(state_file, 'w') as f:
                json.dump(state_data, f, indent=2)

            logger.debug(f"Saved state for actor {self.actor.actor_id} to {state_file}")
        except Exception as e:
            logger.error(f"Failed to save state for actor {self.actor.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def load_state(self) -> None:
        """Load actor state from disk."""
        if not self.actor.state_dir:
            return

        try:
            state_file = Path(self.actor.state_dir) / f"{self.actor.actor_id}_state.json"
            if not state_file.exists():
                logger.debug(f"No state file found for actor {self.actor.actor_id}")
                return

            # Load state data
            with open(state_file, 'r') as f:
                state_data = json.load(f)

            # Restore state
            self._set_state(state_data)

            logger.debug(f"Loaded state for actor {self.actor.actor_id} from {state_file}")
        except Exception as e:
            logger.error(f"Failed to load state for actor {self.actor.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _get_state(self) -> Dict[str, Any]:
        """
        Get actor state for persistence.

        Returns:
            Dictionary containing actor state
        """
        return {
            "actor_id": self.actor.actor_id,
            "actor_type": self.actor.actor_type,
            "tags": list(self.actor.tags),
            "capabilities": list(self.actor.capabilities),
            "metrics": self.actor._metrics,
            "timestamp": time.time()
        }

    def _set_state(self, state: Dict[str, Any]) -> None:
        """
        Set actor state from persistence.

        Args:
            state: State dictionary to restore
        """
        # Restore metrics
        if "metrics" in state:
            self.actor._metrics.update(state["metrics"])

        # Restore tags
        if "tags" in state:
            self.actor._tags = set(state["tags"])

        # Restore capabilities
        if "capabilities" in state:
            self.actor._capabilities = set(state["capabilities"])

    async def start_metrics_collection(self) -> None:
        """Start the metrics collection task."""
        if self._metrics_task is None or self._metrics_task.done():
            self._metrics_task = asyncio.create_task(self._collect_metrics())
            logger.debug(f"Started metrics collection for actor {self.actor.actor_id}")

    async def stop_metrics_collection(self) -> None:
        """Stop the metrics collection task."""
        if self._metrics_task and not self._metrics_task.done():
            self._metrics_task.cancel()
            try:
                await self._metrics_task
            except asyncio.CancelledError:
                pass
            logger.debug(f"Stopped metrics collection for actor {self.actor.actor_id}")

    async def _collect_metrics(self) -> None:
        """Collect and report metrics periodically."""
        try:
            while self.actor.is_running:
                try:
                    # Update metrics
                    current_time = time.time()
                    self.actor._metrics["uptime"] = current_time - self.actor._metrics["start_time"]

                    # Calculate average processing time
                    if self.actor._metrics["messages_processed"] > 0:
                        self.actor._metrics["avg_processing_time"] = (
                            self.actor._metrics["processing_time"] / self.actor._metrics["messages_processed"]
                        )

                    # Report metrics to supervisor if available
                    if self.actor.supervisor_id:
                        metrics_payload = {
                            "actor_id": self.actor.actor_id,
                            "metrics": self.actor._metrics.copy(),
                            "timestamp": current_time
                        }
                        await self.actor.send(
                            self.actor.supervisor_id,
                            MessageType.METRICS,
                            metrics_payload
                        )

                    logger.debug(f"Actor {self.actor.actor_id} collected metrics: {self.actor._metrics}")

                except Exception as e:
                    logger.error(f"Error collecting metrics for actor {self.actor.actor_id}: {e}")

                # Wait for next collection
                await asyncio.sleep(self._metrics_interval)
        except asyncio.CancelledError:
            logger.debug(f"Metrics collection task cancelled for actor {self.actor.actor_id}")
        except Exception as e:
            logger.error(f"Error in metrics task for actor {self.actor.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def update_metric(self, metric_name: str, value: Any) -> None:
        """
        Update a specific metric.

        Args:
            metric_name: Name of the metric to update
            value: New value for the metric
        """
        self.actor._metrics[metric_name] = value
        self.actor._metrics["last_activity"] = time.time()

    def increment_metric(self, metric_name: str, increment: float = 1.0) -> None:
        """
        Increment a numeric metric.

        Args:
            metric_name: Name of the metric to increment
            increment: Amount to increment by (default: 1.0)
        """
        current_value = self.actor._metrics.get(metric_name, 0)
        self.actor._metrics[metric_name] = current_value + increment
        self.actor._metrics["last_activity"] = time.time()

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get a copy of current metrics.

        Returns:
            Dictionary containing current metrics
        """
        return self.actor._metrics.copy()

    def reset_metrics(self) -> None:
        """Reset all metrics to their initial values."""
        start_time = self.actor._metrics.get("start_time", time.time())
        self.actor._metrics = {
            "messages_received": 0,
            "messages_sent": 0,
            "messages_processed": 0,
            "errors": 0,
            "processing_time": 0.0,
            "avg_processing_time": 0.0,
            "last_activity": time.time(),
            "uptime": 0.0,
            "start_time": start_time,
            "restarts": 0
        }

    async def cleanup_state(self) -> None:
        """Clean up state management resources."""
        try:
            # Stop metrics collection
            await self.stop_metrics_collection()

            # Save final state
            await self.save_state()

            logger.debug(f"Cleaned up state management for actor {self.actor.actor_id}")
        except Exception as e:
            logger.error(f"Error cleaning up state management for actor {self.actor.actor_id}: {e}")

    @property
    def metrics_interval(self) -> float:
        """Get the metrics collection interval."""
        return self._metrics_interval

    @metrics_interval.setter
    def metrics_interval(self, interval: float) -> None:
        """Set the metrics collection interval."""
        self._metrics_interval = max(1.0, interval)  # Minimum 1 second
