"""
Distributed Node Module
====================

This module defines the Node class for distributed actor systems.
It implements the CAW principle of location transparency by allowing
actors to communicate across machine boundaries.
"""

import asyncio
import json
import logging
import os
import time
import uuid
from typing import Any, Dict, List, Optional, Set, Tuple, Type, Union

from ..actor import Actor
from ..context_wave import ContextWave
from ..message import Message, MessageType
from ..actor_registry import ActorRegistry, get_registry

logger = logging.getLogger("vibe_check_distributed_node")


class Node:
    """
    Node in a distributed actor system.

    Implements the CAW principle of location transparency by allowing
    actors to communicate across machine boundaries.
    """

    def __init__(self, node_id: Optional[str] = None, host: str = "localhost", port: int = 8000):
        """
        Initialize the node.

        Args:
            node_id: Optional unique ID for this node (generated if not provided)
            host: Host to bind to
            port: Port to bind to
        """
        self.node_id = node_id or f"node_{uuid.uuid4()}"
        self.host = host
        self.port = port

        # Node state
        self.is_running = False
        self.registry = get_registry()
        self.remote_nodes: Dict[str, Dict[str, Any]] = {}
        self.remote_actors: Dict[str, Dict[str, Any]] = {}

        # Server state
        self._server = None
        self._clients: Dict[str, asyncio.StreamWriter] = {}
        self._server_task = None

        # Discovery state
        self._discovery_task = None
        self._discovery_interval = 10.0  # seconds

        # Heartbeat state
        self._heartbeat_task = None
        self._heartbeat_interval = 5.0  # seconds

        # Message queue for outgoing messages
        self._outgoing_queue = asyncio.Queue()
        self._outgoing_task = None

    async def start(self) -> None:
        """Start the node."""
        if self.is_running:
            logger.warning(f"Node {self.node_id} is already running")
            return

        logger.info(f"Starting node {self.node_id} on {self.host}:{self.port}")

        # Start the server
        self._server = await asyncio.start_server(
            self._handle_connection, self.host, self.port
        )

        # Start the server task
        self._server_task = asyncio.create_task(self._server.serve_forever())

        # Start the discovery task
        self._discovery_task = asyncio.create_task(self._discover_nodes())

        # Start the heartbeat task
        self._heartbeat_task = asyncio.create_task(self._send_heartbeats())

        # Start the outgoing message task
        self._outgoing_task = asyncio.create_task(self._process_outgoing_messages())

        # Set running flag
        self.is_running = True

        logger.info(f"Node {self.node_id} started")

    async def stop(self) -> None:
        """Stop the node."""
        if not self.is_running:
            logger.info(f"Node {self.node_id} is already stopped")
            return

        logger.info(f"Stopping node {self.node_id}")

        # Set running flag to false
        self.is_running = False

        # Cancel tasks
        for task in [self._discovery_task, self._heartbeat_task, self._outgoing_task]:
            if task and not task.done():
                task.cancel()
                try:
                    await asyncio.wait_for(asyncio.shield(task), timeout=0.5)
                except (asyncio.TimeoutError, asyncio.CancelledError):
                    pass

        # Close all client connections
        for writer in self._clients.values():
            writer.close()
            await writer.wait_closed()

        # Close the server
        if self._server:
            self._server.close()
            await self._server.wait_closed()

        # Cancel server task
        if self._server_task and not self._server_task.done():
            self._server_task.cancel()
            try:
                await asyncio.wait_for(asyncio.shield(self._server_task), timeout=0.5)
            except (asyncio.TimeoutError, asyncio.CancelledError):
                pass

        logger.info(f"Node {self.node_id} stopped")

    async def connect_to_node(self, host: str, port: int) -> None:
        """
        Connect to another node.

        Args:
            host: Host to connect to
            port: Port to connect to
        """
        try:
            # Connect to the node
            reader, writer = await asyncio.open_connection(host, port)

            # Send handshake
            handshake = {
                "type": "handshake",
                "node_id": self.node_id,
                "host": self.host,
                "port": self.port
            }
            writer.write(json.dumps(handshake).encode() + b"\n")
            await writer.drain()

            # Wait for handshake response
            response_data = await reader.readline()
            response = json.loads(response_data.decode())

            if response["type"] == "handshake_ack":
                remote_node_id = response["node_id"]

                # Add to remote nodes
                self.remote_nodes[remote_node_id] = {
                    "node_id": remote_node_id,
                    "host": host,
                    "port": port,
                    "last_heartbeat": time.time()
                }

                # Add to clients
                self._clients[remote_node_id] = writer

                # Start reading messages
                asyncio.create_task(self._read_messages(remote_node_id, reader, writer))

                logger.info(f"Connected to node {remote_node_id} at {host}:{port}")
            else:
                logger.warning(f"Unexpected handshake response: {response}")
                writer.close()
                await writer.wait_closed()
        except Exception as e:
            logger.error(f"Failed to connect to node at {host}:{port}: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def _handle_connection(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter) -> None:
        """
        Handle a new connection.

        Args:
            reader: Stream reader
            writer: Stream writer
        """
        try:
            # Get peer info
            peer_info = writer.get_extra_info("peername")
            peer_host, peer_port = peer_info[:2]

            logger.info(f"New connection from {peer_host}:{peer_port}")

            # Wait for handshake
            handshake_data = await reader.readline()
            handshake = json.loads(handshake_data.decode())

            if handshake["type"] == "handshake":
                remote_node_id = handshake["node_id"]
                remote_host = handshake["host"]
                remote_port = handshake["port"]

                # Send handshake acknowledgement
                handshake_ack = {
                    "type": "handshake_ack",
                    "node_id": self.node_id
                }
                writer.write(json.dumps(handshake_ack).encode() + b"\n")
                await writer.drain()

                # Add to remote nodes
                self.remote_nodes[remote_node_id] = {
                    "node_id": remote_node_id,
                    "host": remote_host,
                    "port": remote_port,
                    "last_heartbeat": time.time()
                }

                # Add to clients
                self._clients[remote_node_id] = writer

                # Start reading messages
                await self._read_messages(remote_node_id, reader, writer)
            else:
                logger.warning(f"Unexpected message type: {handshake['type']}")
                writer.close()
                await writer.wait_closed()
        except Exception as e:
            logger.error(f"Error handling connection: {e}")
            import traceback
            logger.error(traceback.format_exc())
            writer.close()
            await writer.wait_closed()

    async def _read_messages(self, node_id: str, reader: asyncio.StreamReader, writer: asyncio.StreamWriter) -> None:
        """
        Read messages from a connection.

        Args:
            node_id: ID of the remote node
            reader: Stream reader
            writer: Stream writer
        """
        try:
            while self.is_running:
                # Read a message
                message_data = await reader.readline()

                if not message_data:
                    # Connection closed
                    logger.info(f"Connection to node {node_id} closed")
                    break

                # Parse the message
                message = json.loads(message_data.decode())

                # Handle the message
                await self._handle_message(node_id, message)
        except asyncio.CancelledError:
            logger.info(f"Reading messages from node {node_id} cancelled")
        except Exception as e:
            logger.error(f"Error reading messages from node {node_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
        finally:
            # Remove from clients
            self._clients.pop(node_id, None)

            # Remove from remote nodes
            self.remote_nodes.pop(node_id, None)

            # Close the connection
            writer.close()
            await writer.wait_closed()

            logger.info(f"Connection to node {node_id} closed")

    async def _handle_message(self, node_id: str, message: Dict[str, Any]) -> None:
        """
        Handle a message from a remote node.

        Args:
            node_id: ID of the remote node
            message: Message to handle
        """
        message_type = message.get("type")

        if message_type == "heartbeat":
            # Update last heartbeat time
            if node_id in self.remote_nodes:
                self.remote_nodes[node_id]["last_heartbeat"] = time.time()

            # Update remote actors
            remote_actors = message.get("actors", [])
            for actor_info in remote_actors:
                actor_id = actor_info["actor_id"]
                self.remote_actors[actor_id] = {
                    "actor_id": actor_id,
                    "node_id": node_id,
                    "actor_type": actor_info.get("actor_type"),
                    "tags": actor_info.get("tags", []),
                    "capabilities": actor_info.get("capabilities", [])
                }

            logger.debug(f"Received heartbeat from node {node_id}")
        elif message_type == "actor_message":
            # Get message details
            recipient_id = message.get("recipient_id")
            sender_id = message.get("sender_id")
            message_type_name = message.get("message_type")
            payload = message.get("payload", {})
            context_data = message.get("context", {})

            # Get the recipient actor
            recipient = self.registry.get_actor(recipient_id)

            if recipient:
                # Create context
                context = ContextWave.from_dict(context_data)

                # Create message type
                try:
                    msg_type = MessageType[message_type_name]
                except KeyError:
                    logger.warning(f"Unknown message type: {message_type_name}")
                    return

                # Send the message to the recipient
                try:
                    await recipient.receive(Message(
                        type=msg_type,
                        payload=payload,
                        context=context,
                        recipient_id=recipient_id,
                        sender_id=sender_id
                    ))
                    logger.debug(f"Delivered message from {sender_id} to {recipient_id}")
                except Exception as e:
                    logger.error(f"Failed to deliver message to {recipient_id}: {e}")
            else:
                logger.warning(f"Recipient actor {recipient_id} not found")
        elif message_type == "discover":
            # Send node info
            await self._send_node_info(node_id)
        elif message_type == "node_info":
            # Update remote nodes
            remote_nodes = message.get("nodes", {})
            for remote_node_id, node_info in remote_nodes.items():
                if remote_node_id != self.node_id and remote_node_id not in self.remote_nodes:
                    # Connect to the node
                    host = node_info.get("host")
                    port = node_info.get("port")
                    if host and port:
                        asyncio.create_task(self.connect_to_node(host, port))
        else:
            logger.warning(f"Unknown message type: {message_type}")

    async def _send_node_info(self, node_id: str) -> None:
        """
        Send node info to a remote node.

        Args:
            node_id: ID of the remote node
        """
        # Get writer
        writer = self._clients.get(node_id)
        if not writer:
            logger.warning(f"No connection to node {node_id}")
            return

        # Create node info message
        node_info = {
            "type": "node_info",
            "nodes": {
                node_id: {
                    "node_id": node_id,
                    "host": node_info.get("host"),
                    "port": node_info.get("port")
                }
                for node_id, node_info in self.remote_nodes.items()
            }
        }

        # Send the message
        try:
            writer.write(json.dumps(node_info).encode() + b"\n")
            await writer.drain()
            logger.debug(f"Sent node info to node {node_id}")
        except Exception as e:
            logger.error(f"Failed to send node info to node {node_id}: {e}")

    async def _discover_nodes(self) -> None:
        """Discover other nodes in the network."""
        try:
            while self.is_running:
                # Send discover message to all connected nodes
                for node_id, writer in list(self._clients.items()):
                    try:
                        discover_message = {
                            "type": "discover"
                        }
                        writer.write(json.dumps(discover_message).encode() + b"\n")
                        await writer.drain()
                        logger.debug(f"Sent discover message to node {node_id}")
                    except Exception as e:
                        logger.error(f"Failed to send discover message to node {node_id}: {e}")

                # Wait before discovering again
                await asyncio.sleep(self._discovery_interval)
        except asyncio.CancelledError:
            logger.info(f"Node {self.node_id} discovery task cancelled")
        except Exception as e:
            logger.error(f"Error in node {self.node_id} discovery task: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def _send_heartbeats(self) -> None:
        """Send heartbeats to other nodes."""
        try:
            while self.is_running:
                # Get local actors
                local_actors = []
                for actor_id, actor in self.registry._actors.items():
                    local_actors.append({
                        "actor_id": actor_id,
                        "actor_type": getattr(actor, "actor_type", None),
                        "tags": list(getattr(actor, "tags", [])),
                        "capabilities": list(getattr(actor, "capabilities", []))
                    })

                # Send heartbeat to all connected nodes
                for node_id, writer in list(self._clients.items()):
                    try:
                        heartbeat_message = {
                            "type": "heartbeat",
                            "node_id": self.node_id,
                            "timestamp": time.time(),
                            "actors": local_actors
                        }
                        writer.write(json.dumps(heartbeat_message).encode() + b"\n")
                        await writer.drain()
                        logger.debug(f"Sent heartbeat to node {node_id}")
                    except Exception as e:
                        logger.error(f"Failed to send heartbeat to node {node_id}: {e}")

                # Wait before sending heartbeats again
                await asyncio.sleep(self._heartbeat_interval)
        except asyncio.CancelledError:
            logger.info(f"Node {self.node_id} heartbeat task cancelled")
        except Exception as e:
            logger.error(f"Error in node {self.node_id} heartbeat task: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def _process_outgoing_messages(self) -> None:
        """Process outgoing messages to remote actors."""
        try:
            while self.is_running:
                try:
                    # Get a message from the queue
                    message = await asyncio.wait_for(self._outgoing_queue.get(), timeout=0.1)

                    # Process the message
                    await self._send_remote_message(message)

                    # Mark the message as done
                    self._outgoing_queue.task_done()
                except asyncio.TimeoutError:
                    # No message received, continue loop
                    await asyncio.sleep(0)
                    continue
                except asyncio.CancelledError:
                    # Task was cancelled, exit gracefully
                    logger.info(f"Node {self.node_id} outgoing message task cancelled")
                    break
                except Exception as e:
                    logger.error(f"Error processing outgoing message: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

                    # Mark the message as done even if processing failed
                    self._outgoing_queue.task_done()
        except asyncio.CancelledError:
            logger.info(f"Node {self.node_id} outgoing message task cancelled")
        except Exception as e:
            logger.error(f"Fatal error in node {self.node_id} outgoing message task: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def _send_remote_message(self, message: Message) -> None:
        """
        Send a message to a remote actor.

        Args:
            message: Message to send
        """
        recipient_id = message.recipient_id

        if not recipient_id:
            logger.warning("Cannot send message without recipient_id")
            return

        # Check if the recipient is a remote actor
        if recipient_id not in self.remote_actors:
            logger.warning(f"Recipient {recipient_id} is not a remote actor")
            return

        # Get the node ID for the recipient
        node_id = self.remote_actors[recipient_id]["node_id"]

        # Get the writer for the node
        writer = self._clients.get(node_id)
        if not writer:
            logger.warning(f"No connection to node {node_id} for recipient {recipient_id}")
            return

        # Create the message
        actor_message = {
            "type": "actor_message",
            "recipient_id": recipient_id,
            "sender_id": message.sender_id,
            "message_type": message.type.name,
            "payload": message.payload,
            "context": message.context.to_dict() if message.context else {}
        }

        # Send the message
        try:
            writer.write(json.dumps(actor_message).encode() + b"\n")
            await writer.drain()
            logger.debug(f"Sent message to remote actor {recipient_id} on node {node_id}")
        except Exception as e:
            logger.error(f"Failed to send message to remote actor {recipient_id}: {e}")

    async def send_message(self, message: Message) -> None:
        """
        Send a message to a remote actor.

        Args:
            message: Message to send
        """
        await self._outgoing_queue.put(message)

    def get_remote_actor_info(self, actor_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a remote actor.

        Args:
            actor_id: ID of the remote actor

        Returns:
            Information about the remote actor or None if not found
        """
        return self.remote_actors.get(actor_id)

    def get_remote_node_info(self, node_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a remote node.

        Args:
            node_id: ID of the remote node

        Returns:
            Information about the remote node or None if not found
        """
        return self.remote_nodes.get(node_id)

    def get_all_remote_actors(self) -> Dict[str, Dict[str, Any]]:
        """
        Get information about all remote actors.

        Returns:
            Dictionary of actor IDs to actor information
        """
        return self.remote_actors.copy()

    def get_all_remote_nodes(self) -> Dict[str, Dict[str, Any]]:
        """
        Get information about all remote nodes.

        Returns:
            Dictionary of node IDs to node information
        """
        return self.remote_nodes.copy()


# Singleton instance
_node = None

def get_node() -> Node:
    """
    Get the singleton node instance.

    Returns:
        The node instance
    """
    global _node
    if _node is None:
        _node = Node()
    return _node

def reset_node() -> None:
    """Reset the singleton node instance."""
    global _node
    if _node is not None:
        asyncio.create_task(_node.stop())
    _node = None
