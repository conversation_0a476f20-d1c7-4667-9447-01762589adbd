"""
Distributed Actor System Package
============================

This package provides classes and utilities for distributed actor systems.
It implements the CAW principle of location transparency by allowing
actors to communicate across machine boundaries.
"""

from .node import Node, get_node, reset_node
from .distributed_actor import DistributedActor

__all__ = [
    "Node",
    "get_node",
    "reset_node",
    "DistributedActor"
]
