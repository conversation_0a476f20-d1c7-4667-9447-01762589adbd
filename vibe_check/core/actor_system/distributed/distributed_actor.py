"""
Distributed Actor Module
=====================

This module defines the DistributedActor class, which extends the base Actor
class to support distributed execution across multiple nodes.
"""

import asyncio
import logging
from typing import Any, Dict, Optional, Set, Type

from ..actor import Actor
from ..context_wave import ContextWave
from ..message import Message, MessageType
from .node import get_node

logger = logging.getLogger("vibe_check_distributed_actor")


class DistributedActor(Actor):
    """
    Actor that can communicate with actors on remote nodes.

    Extends the base Actor class to support distributed execution across
    multiple nodes.
    """

    def __init__(self, actor_id: str, actor_type: Optional[str] = None,
                tags: Optional[Set[str]] = None, capabilities: Optional[Set[str]] = None,
                supervisor_id: Optional[str] = None, state_dir: Optional[str] = None):
        """
        Initialize the distributed actor.

        Args:
            actor_id: Unique ID for this actor
            actor_type: Optional type of the actor for discovery
            tags: Optional set of tags for discovery
            capabilities: Optional set of capabilities for discovery
            supervisor_id: Optional ID of the supervisor actor
            state_dir: Optional directory for state persistence
        """
        super().__init__(
            actor_id=actor_id,
            actor_type=actor_type,
            tags=tags,
            capabilities=capabilities,
            supervisor_id=supervisor_id,
            state_dir=state_dir
        )

        # Add distributed tag
        self.tags.add("distributed")

        # Get the node
        self.node = get_node()

    async def start(self) -> None:
        """Start the distributed actor."""
        # Start the node if not already running
        if not self.node.is_running:
            await self.node.start()

        await super().start()

    async def stop(self) -> None:
        """Stop the distributed actor."""
        await super().stop()

    async def send(self, recipient_id: str, msg_type: MessageType,
                  payload: Dict[str, Any], context: Optional[ContextWave] = None,
                  priority: int = 0, ttl: Optional[int] = None) -> None:
        """
        Send a message to another actor, which may be on a remote node.

        Args:
            recipient_id: ID of the recipient actor
            msg_type: Type of the message
            payload: Message payload
            context: Optional context to propagate
            priority: Optional priority for the message (0-9, higher is more important)
            ttl: Optional time-to-live in seconds
        """
        # Try to get recipient from known actors
        recipient = self._known_actors.get(recipient_id)

        # If not found, try to get from registry
        if recipient is None:
            try:
                from ..actor_registry import get_registry
                registry = get_registry()
                recipient = registry.get_actor(recipient_id)

                # If found in registry, add to known actors
                if recipient:
                    self._known_actors[recipient_id] = recipient
            except (ImportError, AttributeError) as e:
                logger.warning(f"Could not use registry to find actor {recipient_id}: {e}")

        # If still not found, check if it's a remote actor
        if recipient is None:
            remote_actor_info = self.node.get_remote_actor_info(recipient_id)

            if remote_actor_info:
                # Create and propagate context
                propagated_context = self.context_wave.propagate(context)

                # Add sender info to context metadata
                propagated_context.metadata["sender_id"] = self.actor_id

                # Add history entry
                history_entry = {
                    "timestamp": asyncio.get_event_loop().time(),
                    "sender": self.actor_id,
                    "recipient": recipient_id,
                    "type": msg_type.name
                }
                propagated_context.history.append(history_entry)

                # Create message
                message = Message(
                    type=msg_type,
                    payload=payload,
                    context=propagated_context,
                    recipient_id=recipient_id,
                    sender_id=self.actor_id,
                    priority=priority,
                    ttl=ttl
                )

                # Send to remote actor
                await self.node.send_message(message)

                # Update metrics
                self._metrics["messages_sent"] += 1
                self._metrics["last_activity"] = asyncio.get_event_loop().time()

                logger.debug(f"Sent {msg_type.name} to remote actor {recipient_id}")
                return

        # If not a remote actor, use the normal send method
        await super().send(recipient_id, msg_type, payload, context, priority, ttl)

    def _initialize_handlers(self) -> None:
        """Initialize message handlers."""
        super()._initialize_handlers()

        # Add handlers for distributed messages
        self._message_handlers[MessageType.REMOTE_INVOKE] = self.handle_remote_invoke
        self._message_handlers[MessageType.REMOTE_RESULT] = self.handle_remote_result
        self._message_handlers[MessageType.NODE_JOINED] = self.handle_node_joined
        self._message_handlers[MessageType.NODE_LEFT] = self.handle_node_left

    async def handle_remote_invoke(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a remote method invocation.

        Args:
            payload: Invocation payload
            context: Message context
        """
        method_name = payload.get("method_name")
        args = payload.get("args", [])
        kwargs = payload.get("kwargs", {})
        invocation_id = payload.get("invocation_id")

        if not method_name:
            logger.warning("Received remote invoke without method_name")
            return

        if not hasattr(self, method_name):
            logger.warning(f"Method {method_name} not found")

            # Send error response
            error_payload = {
                "invocation_id": invocation_id,
                "error": f"Method {method_name} not found",
                "result": None
            }

            # Get sender ID from context
            sender_id = context.metadata.get("sender_id")
            if sender_id:
                await self.send(sender_id, MessageType.REMOTE_RESULT, error_payload, context)

            return

        # Get the method
        method = getattr(self, method_name)

        # Invoke the method
        try:
            result = method(*args, **kwargs)

            # Handle coroutines
            if asyncio.iscoroutine(result):
                result = await result

            # Send response
            response_payload = {
                "invocation_id": invocation_id,
                "error": None,
                "result": result
            }

            # Get sender ID from context
            sender_id = context.metadata.get("sender_id")
            if sender_id:
                await self.send(sender_id, MessageType.REMOTE_RESULT, response_payload, context)
        except Exception as e:
            logger.error(f"Error invoking method {method_name}: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Send error response
            error_payload = {
                "invocation_id": invocation_id,
                "error": str(e),
                "result": None
            }

            # Get sender ID from context
            sender_id = context.metadata.get("sender_id")
            if sender_id:
                await self.send(sender_id, MessageType.REMOTE_RESULT, error_payload, context)

    async def handle_remote_result(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a remote method invocation result.

        Args:
            payload: Result payload
            context: Message context
        """
        # Override in subclasses to handle remote results
        pass

    async def handle_node_joined(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a node joined message.

        Args:
            payload: Node joined payload
            context: Message context
        """
        node_id = payload.get("node_id")
        host = payload.get("host")
        port = payload.get("port")

        if not node_id:
            logger.warning("Received node joined without node_id")
            return

        logger.info(f"Node {node_id} joined at {host}:{port}")

    async def handle_node_left(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a node left message.

        Args:
            payload: Node left payload
            context: Message context
        """
        node_id = payload.get("node_id")
        reason = payload.get("reason", "unknown")

        if not node_id:
            logger.warning("Received node left without node_id")
            return

        logger.info(f"Node {node_id} left (reason: {reason})")

    async def invoke_remote(self, actor_id: str, method_name: str, *args, **kwargs) -> Any:
        """
        Invoke a method on a remote actor.

        Args:
            actor_id: ID of the remote actor
            method_name: Name of the method to invoke
            *args: Positional arguments to pass to the method
            **kwargs: Keyword arguments to pass to the method

        Returns:
            Result of the method invocation
        """
        import uuid

        # Check if the actor is remote
        remote_actor_info = self.node.get_remote_actor_info(actor_id)

        if not remote_actor_info:
            raise ValueError(f"Actor {actor_id} is not a remote actor")

        # Create invocation ID
        invocation_id = str(uuid.uuid4())

        # Create future for the result
        result_future = asyncio.Future()

        # Store the future
        if not hasattr(self, "_remote_invocations"):
            self._remote_invocations = {}
        self._remote_invocations[invocation_id] = result_future

        # Create payload
        payload = {
            "method_name": method_name,
            "args": args,
            "kwargs": kwargs,
            "invocation_id": invocation_id
        }

        # Create context
        context = ContextWave()
        context.metadata["invocation_id"] = invocation_id

        # Send the message
        await self.send(actor_id, MessageType.REMOTE_INVOKE, payload, context)

        # Wait for the result
        try:
            return await asyncio.wait_for(result_future, timeout=60.0)
        except asyncio.TimeoutError:
            # Remove the future
            self._remote_invocations.pop(invocation_id, None)
            raise TimeoutError(f"Remote invocation {invocation_id} timed out")
        finally:
            # Remove the future
            self._remote_invocations.pop(invocation_id, None)

    async def handle_remote_result(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a remote method invocation result.

        Args:
            payload: Result payload
            context: Message context
        """
        invocation_id = payload.get("invocation_id")
        error = payload.get("error")
        result = payload.get("result")

        if not invocation_id:
            logger.warning("Received remote result without invocation_id")
            return

        # Get the future
        if not hasattr(self, "_remote_invocations"):
            self._remote_invocations = {}

        future = self._remote_invocations.get(invocation_id)

        if not future:
            logger.warning(f"No future found for invocation {invocation_id}")
            return

        # Set the result or exception
        if error:
            future.set_exception(Exception(error))
        else:
            future.set_result(result)

        # Remove the future
        self._remote_invocations.pop(invocation_id, None)
