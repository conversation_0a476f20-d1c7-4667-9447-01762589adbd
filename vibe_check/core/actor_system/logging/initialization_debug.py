"""
Initialization Debug Logger
=======================

This module provides specialized logging capabilities for debugging the actor
initialization process. It includes detailed logging of initialization steps,
dependency resolution, state transitions, and error handling.

It's designed to help diagnose and resolve issues with the actor initialization
process by providing comprehensive logging and diagnostics.
"""

import asyncio
import inspect
import logging
import os
import sys
import time
import traceback
from contextlib import contextmanager
from datetime import datetime
from enum import Enum, auto
from functools import wraps
from pathlib import Path
from typing import (
    Any, Callable, Dict, Generator, Iterable, List, Optional,
    Set, Tuple, TypeVar, Union, cast
)

from ..actor_state import ActorState
from .debug_core import (
    InitializationStep, InitializationStepCategory, DependencyResolutionEventType,
    enable_init_debugging, disable_init_debugging, is_init_debugging_enabled,
    log_init_event, get_init_log_buffer, clear_init_log_buffer, get_debug_logger
)
from .debug_timing import (
    init_step_timing, time_operation, record_performance_metric,
    get_init_timing_data, get_operation_timing_data, get_performance_metrics,
    get_critical_path_data, clear_timing_data
)

# Type variables for function decorators
F = TypeVar('F', bound=Callable[..., Any])
AsyncF = TypeVar('AsyncF', bound=Callable[..., Any])

# Create a specialized logger for initialization debugging
_init_debug_logger = logging.getLogger("vibe_check.actor_system.initialization_debug")

# Default log level
_DEFAULT_LOG_LEVEL = logging.DEBUG

# Flag to indicate if initialization debugging is enabled
_INIT_DEBUG_ENABLED = False

# Log file for initialization debugging
_INIT_DEBUG_LOG_FILE: Optional[str] = None

# In-memory log buffer for initialization events
_INIT_DEBUG_LOG_BUFFER: List[Dict[str, Any]] = []

# Maximum buffer size
_MAX_BUFFER_SIZE = 10000

# Initialization step timing data
# Structure: {actor_id: {step_name: {start_time, end_time, duration, sub_operations: [{name, start, end, duration}]}}}
_INIT_TIMING_DATA: Dict[str, Dict[str, Dict[str, Any]]] = {}

# Detailed operation timing within steps
_OPERATION_TIMING_DATA: Dict[str, List[Dict[str, Any]]] = {}

# Current operation being timed (for nested operations)
_CURRENT_OPERATIONS: Dict[str, List[Dict[str, Any]]] = {}

# Dependency resolution events
_DEPENDENCY_RESOLUTION_EVENTS: List[Dict[str, Any]] = []

# Dependency relationships
_DEPENDENCY_RELATIONSHIPS: Dict[str, Dict[str, Any]] = {}

# Dependency resolution paths
_DEPENDENCY_RESOLUTION_PATHS: Dict[str, List[Dict[str, Any]]] = {}

# Critical path data
# Structure: {actor_id: {path: [step_names], duration: float, bottlenecks: [step_names]}}
_CRITICAL_PATH_DATA: Dict[str, Dict[str, Any]] = {}

# Initialization sequence data
# Structure: {sequence_id: {actors: [actor_ids], steps: [{actor_id, step, start_time, end_time, duration}], dependencies: [{from, to, type}]}}
_INIT_SEQUENCE_DATA: Dict[str, Dict[str, Any]] = {}

# Current initialization sequence ID
_CURRENT_SEQUENCE_ID: Optional[str] = None

# Step dependencies
# Structure: {actor_id: {step_name: {depends_on: [{actor_id, step}], depended_by: [{actor_id, step}]}}}
_STEP_DEPENDENCIES: Dict[str, Dict[str, Dict[str, List[Dict[str, Any]]]]] = {}

# Performance metrics
# Structure: {actor_id: {metric_name: value}}
_PERFORMANCE_METRICS: Dict[str, Dict[str, Any]] = {}








def log_init_event(
    actor_id: str,
    step: InitializationStep,
    message: str,
    details: Optional[Dict[str, Any]] = None,
    error: Optional[Exception] = None,
    stack_trace: bool = False
) -> None:
    """
    Log an initialization event.

    Args:
        actor_id: ID of the actor
        step: Initialization step
        message: Log message
        details: Optional details to include in the log
        error: Optional error to include in the log
        stack_trace: Whether to include a stack trace in the log
    """
    if not _INIT_DEBUG_ENABLED:
        return

    # Create the log entry
    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "actor_id": actor_id,
        "step": step.name,
        "message": message,
        "details": details or {},
    }

    # Add error information if provided
    if error:
        log_entry["error"] = str(error)
        log_entry["error_type"] = type(error).__name__

        # Add stack trace if requested
        if stack_trace:
            log_entry["stack_trace"] = traceback.format_exc()

    # Add the entry to the buffer
    _INIT_DEBUG_LOG_BUFFER.append(log_entry)

    # Trim the buffer if it exceeds the maximum size
    if len(_INIT_DEBUG_LOG_BUFFER) > _MAX_BUFFER_SIZE:
        _INIT_DEBUG_LOG_BUFFER.pop(0)

    # Log the event
    log_message = f"[{actor_id}] [{step.name}] {message}"
    if details:
        log_message += f" - Details: {details}"

    if error:
        _init_debug_logger.error(log_message, exc_info=error if stack_trace else None)
    else:
        _init_debug_logger.debug(log_message)


@contextmanager
def init_step_timing(
    actor_id: str,
    step: InitializationStep,
    details: Optional[Dict[str, Any]] = None
) -> Generator[Dict[str, Any], None, None]:
    """
    Context manager for timing initialization steps.

    Args:
        actor_id: ID of the actor
        step: Initialization step
        details: Optional details about the step

    Yields:
        Dictionary for storing additional context during the step
    """
    if not _INIT_DEBUG_ENABLED:
        empty_context: Dict[str, Any] = {}
        yield empty_context
        return

    # Create context data dictionary for storing additional information during the step
    context_data: Dict[str, Any] = {
        "actor_id": actor_id,
        "step": step,
        "start_time": time.time(),
        "sub_operations": [],
        "dependencies": [],
        "metrics": {},
        "details": details or {}
    }

    # Log the start of the step
    step_details = {
        "start_time": context_data["start_time"],
        "category": step.category.name
    }
    if details:
        step_details.update(details)

    log_init_event(
        actor_id=actor_id,
        step=step,
        message=f"Starting {step.name} step",
        details=step_details
    )

    try:
        # Yield the context data for the caller to use
        yield context_data
    finally:
        # Calculate timing information
        end_time = time.time()
        start_time = float(context_data["start_time"])
        duration = end_time - start_time

        # Update context data
        context_data["end_time"] = end_time
        context_data["duration"] = duration

        # Store timing data
        if actor_id not in _INIT_TIMING_DATA:
            _INIT_TIMING_DATA[actor_id] = {}

        # Get typed references to collections
        sub_operations = context_data["sub_operations"]
        dependencies = context_data["dependencies"]
        metrics = context_data["metrics"]
        step_details = context_data["details"]

        # Create or update the step timing data
        if step.name not in _INIT_TIMING_DATA[actor_id]:
            _INIT_TIMING_DATA[actor_id][step.name] = {
                "start_time": start_time,
                "end_time": end_time,
                "duration": duration,
                "sub_operations": sub_operations,
                "dependencies": dependencies,
                "metrics": metrics,
                "category": step.category.name,
                "details": step_details
            }
        else:
            # Update existing timing data
            _INIT_TIMING_DATA[actor_id][step.name]["end_time"] = end_time
            _INIT_TIMING_DATA[actor_id][step.name]["duration"] = duration
            _INIT_TIMING_DATA[actor_id][step.name]["sub_operations"] = sub_operations
            _INIT_TIMING_DATA[actor_id][step.name]["dependencies"] = dependencies
            _INIT_TIMING_DATA[actor_id][step.name]["metrics"] = metrics
            _INIT_TIMING_DATA[actor_id][step.name]["details"].update(step_details)

        # Add step to current initialization sequence if one exists
        if _CURRENT_SEQUENCE_ID:
            sequence_details: Dict[str, Any] = {
                "sub_operations": sub_operations,
                "dependencies": dependencies,
                "metrics": metrics,
                "category": step.category.name
            }
            if step_details:
                sequence_details.update(step_details)

            add_step_to_sequence(
                actor_id=actor_id,
                step=step,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                details=sequence_details
            )

        # Log timing information
        log_details = {
            "duration": duration,
            "start_time": start_time,
            "end_time": end_time,
            "category": step.category.name,
            "sub_operations_count": len(sub_operations),
            "dependencies_count": len(dependencies),
            "metrics_count": len(metrics)
        }
        if step_details:
            log_details.update(step_details)

        log_init_event(
            actor_id=actor_id,
            step=step,
            message=f"Step {step.name} completed in {duration:.4f} seconds",
            details=log_details
        )


def init_debug_decorator(step: InitializationStep) -> Callable[[F], F]:
    """
    Decorator for debugging initialization functions.

    Args:
        step: Initialization step

    Returns:
        Decorated function
    """
    def decorator(func: F) -> F:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # Extract actor_id from args or kwargs
            actor_id = _extract_actor_id(args, kwargs)
            if not actor_id:
                return func(*args, **kwargs)

            # Create step details
            step_details: Dict[str, Any] = {
                "function_name": func.__name__,
                "args": str(args),
                "kwargs": str(kwargs),
                "category": step.category.name
            }

            # Log the start of the step
            log_init_event(
                actor_id=actor_id,
                step=step,
                message=f"Starting {func.__name__}",
                details=step_details
            )

            # Time the step
            with init_step_timing(actor_id, step, step_details) as context:
                try:
                    result = func(*args, **kwargs)

                    # Update context with result
                    context["result"] = str(result)

                    # Log the completion of the step
                    log_init_event(
                        actor_id=actor_id,
                        step=step,
                        message=f"Completed {func.__name__}",
                        details={
                            "result": str(result),
                            "function_name": func.__name__,
                            "category": step.category.name,
                            "sub_operations_count": len(context["sub_operations"]),
                            "dependencies_count": len(context["dependencies"]),
                            "metrics_count": len(context["metrics"])
                        }
                    )
                    return result
                except Exception as e:
                    # Update context with error
                    context["error"] = str(e)
                    context["error_type"] = type(e).__name__

                    # Log the error
                    log_init_event(
                        actor_id=actor_id,
                        step=step,
                        message=f"Error in {func.__name__}: {e}",
                        error=e,
                        stack_trace=True,
                        details={
                            "error_type": type(e).__name__,
                            "function_name": func.__name__,
                            "category": step.category.name,
                            "sub_operations_count": len(context["sub_operations"]),
                            "dependencies_count": len(context["dependencies"]),
                            "metrics_count": len(context["metrics"])
                        }
                    )
                    raise

        return cast(F, wrapper)
    return decorator


def init_debug_async_decorator(step: InitializationStep) -> Callable[[AsyncF], AsyncF]:
    """
    Decorator for debugging asynchronous initialization functions.

    Args:
        step: Initialization step

    Returns:
        Decorated function
    """
    def decorator(func: AsyncF) -> AsyncF:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            # Extract actor_id from args or kwargs
            actor_id = _extract_actor_id(args, kwargs)
            if not actor_id:
                return await func(*args, **kwargs)

            # Create step details
            step_details: Dict[str, Any] = {
                "function_name": func.__name__,
                "args": str(args),
                "kwargs": str(kwargs),
                "category": step.category.name
            }

            # Log the start of the step
            log_init_event(
                actor_id=actor_id,
                step=step,
                message=f"Starting {func.__name__}",
                details=step_details
            )

            # Time the step
            start_time = time.time()

            # Create context data for the step
            context_data: Dict[str, Any] = {
                "actor_id": actor_id,
                "step": step,
                "start_time": start_time,
                "sub_operations": [],
                "dependencies": [],
                "metrics": {},
                "details": step_details
            }

            # Add step to current initialization sequence if one exists
            if _CURRENT_SEQUENCE_ID:
                add_step_to_sequence(
                    actor_id=actor_id,
                    step=step,
                    start_time=start_time,
                    end_time=0.0,  # Will be updated later
                    duration=0.0,  # Will be updated later
                    details=step_details
                )

            try:
                result = await func(*args, **kwargs)
                # Calculate duration
                end_time = time.time()
                duration = end_time - start_time

                # Update context data
                context_data["end_time"] = end_time
                context_data["duration"] = duration
                context_data["result"] = str(result)

                # Store timing data
                if actor_id not in _INIT_TIMING_DATA:
                    _INIT_TIMING_DATA[actor_id] = {}

                # Get typed references to collections
                sub_operations = context_data["sub_operations"]
                dependencies = context_data["dependencies"]
                metrics = context_data["metrics"]

                # Create or update the step timing data
                if step.name not in _INIT_TIMING_DATA[actor_id]:
                    _INIT_TIMING_DATA[actor_id][step.name] = {
                        "start_time": start_time,
                        "end_time": end_time,
                        "duration": duration,
                        "sub_operations": sub_operations,
                        "dependencies": dependencies,
                        "metrics": metrics,
                        "function_name": func.__name__,
                        "category": step.category.name,
                        "details": step_details,
                        "result": str(result)
                    }
                else:
                    # Update existing timing data
                    _INIT_TIMING_DATA[actor_id][step.name]["end_time"] = end_time
                    _INIT_TIMING_DATA[actor_id][step.name]["duration"] = duration
                    _INIT_TIMING_DATA[actor_id][step.name]["function_name"] = func.__name__
                    _INIT_TIMING_DATA[actor_id][step.name]["sub_operations"] = sub_operations
                    _INIT_TIMING_DATA[actor_id][step.name]["dependencies"] = dependencies
                    _INIT_TIMING_DATA[actor_id][step.name]["metrics"] = metrics
                    _INIT_TIMING_DATA[actor_id][step.name]["result"] = str(result)

                # Update step in initialization sequence if one exists
                if _CURRENT_SEQUENCE_ID:
                    # Find the step in the sequence
                    sequence = _INIT_SEQUENCE_DATA.get(_CURRENT_SEQUENCE_ID, {})
                    steps = sequence.get("steps", [])
                    for seq_step in steps:
                        if (seq_step.get("actor_id") == actor_id and
                            seq_step.get("step") == step.name and
                            abs(seq_step.get("start_time", 0) - start_time) < 0.001):
                            # Update the step
                            seq_step["end_time"] = end_time
                            seq_step["duration"] = duration
                            seq_step["details"]["result"] = str(result)
                            seq_step["details"]["sub_operations"] = sub_operations
                            seq_step["details"]["dependencies"] = dependencies
                            seq_step["details"]["metrics"] = metrics
                            break

                # Log the completion of the step
                log_details = {
                    "duration": duration,
                    "result": str(result),
                    "start_time": start_time,
                    "end_time": end_time,
                    "category": step.category.name,
                    "sub_operations_count": len(sub_operations),
                    "dependencies_count": len(dependencies),
                    "metrics_count": len(metrics),
                    "function_name": func.__name__
                }

                log_init_event(
                    actor_id=actor_id,
                    step=step,
                    message=f"Completed {func.__name__} in {duration:.4f} seconds",
                    details=log_details
                )
                return result
            except Exception as e:
                # Calculate duration
                end_time = time.time()
                duration = end_time - start_time

                # Update context data
                context_data["end_time"] = end_time
                context_data["duration"] = duration
                context_data["error"] = str(e)
                context_data["error_type"] = type(e).__name__

                # Store timing data
                if actor_id not in _INIT_TIMING_DATA:
                    _INIT_TIMING_DATA[actor_id] = {}

                # Get typed references to collections
                sub_operations = context_data["sub_operations"]
                dependencies = context_data["dependencies"]
                metrics = context_data["metrics"]

                # Create or update the step timing data with error information
                if step.name not in _INIT_TIMING_DATA[actor_id]:
                    _INIT_TIMING_DATA[actor_id][step.name] = {
                        "start_time": start_time,
                        "end_time": end_time,
                        "duration": duration,
                        "sub_operations": sub_operations,
                        "dependencies": dependencies,
                        "metrics": metrics,
                        "function_name": func.__name__,
                        "category": step.category.name,
                        "error": str(e),
                        "error_type": type(e).__name__,
                        "details": step_details
                    }
                else:
                    # Update existing timing data
                    _INIT_TIMING_DATA[actor_id][step.name]["end_time"] = end_time
                    _INIT_TIMING_DATA[actor_id][step.name]["duration"] = duration
                    _INIT_TIMING_DATA[actor_id][step.name]["error"] = str(e)
                    _INIT_TIMING_DATA[actor_id][step.name]["error_type"] = type(e).__name__
                    _INIT_TIMING_DATA[actor_id][step.name]["sub_operations"] = sub_operations
                    _INIT_TIMING_DATA[actor_id][step.name]["dependencies"] = dependencies
                    _INIT_TIMING_DATA[actor_id][step.name]["metrics"] = metrics

                # Update step in initialization sequence if one exists
                if _CURRENT_SEQUENCE_ID:
                    # Find the step in the sequence
                    sequence = _INIT_SEQUENCE_DATA.get(_CURRENT_SEQUENCE_ID, {})
                    steps = sequence.get("steps", [])
                    for seq_step in steps:
                        if (seq_step.get("actor_id") == actor_id and
                            seq_step.get("step") == step.name and
                            abs(seq_step.get("start_time", 0) - start_time) < 0.001):
                            # Update the step
                            seq_step["end_time"] = end_time
                            seq_step["duration"] = duration
                            seq_step["details"]["error"] = str(e)
                            seq_step["details"]["error_type"] = type(e).__name__
                            seq_step["details"]["sub_operations"] = sub_operations
                            seq_step["details"]["dependencies"] = dependencies
                            seq_step["details"]["metrics"] = metrics
                            break

                # Log the error
                log_details = {
                    "duration": duration,
                    "start_time": start_time,
                    "end_time": end_time,
                    "error_type": type(e).__name__,
                    "category": step.category.name,
                    "sub_operations_count": len(sub_operations),
                    "dependencies_count": len(dependencies),
                    "metrics_count": len(metrics),
                    "function_name": func.__name__
                }

                log_init_event(
                    actor_id=actor_id,
                    step=step,
                    message=f"Error in {func.__name__} after {duration:.4f} seconds: {e}",
                    error=e,
                    stack_trace=True,
                    details=log_details
                )
                raise

        return cast(AsyncF, wrapper)
    return decorator


def _extract_actor_id(args: Tuple[Any, ...], kwargs: Dict[str, Any]) -> Optional[str]:
    """
    Extract actor_id from function arguments.

    Args:
        args: Positional arguments
        kwargs: Keyword arguments

    Returns:
        Actor ID if found, None otherwise
    """
    # Check kwargs first
    if "actor_id" in kwargs:
        return str(kwargs["actor_id"])

    # Check if first arg is a string (likely actor_id)
    if args and isinstance(args[0], str):
        return args[0]

    # Check if first arg is an actor with actor_id attribute
    if args and hasattr(args[0], "actor_id"):
        return str(getattr(args[0], "actor_id"))

    # Check if self is an actor with actor_id attribute
    if args and len(args) > 0 and hasattr(args[0], "actor_id"):
        return str(getattr(args[0], "actor_id"))

    return None


def get_init_timing_data() -> Dict[str, Dict[str, Dict[str, Any]]]:
    """
    Get initialization timing data.

    Returns:
        Dictionary mapping actor IDs to dictionaries mapping step names to timing data
    """
    return _INIT_TIMING_DATA


def get_critical_path_data() -> Dict[str, Dict[str, Any]]:
    """
    Get critical path data for initialization.

    Returns:
        Dictionary mapping actor IDs to critical path data
    """
    return _CRITICAL_PATH_DATA


def get_init_sequence_data() -> Dict[str, Dict[str, Any]]:
    """
    Get initialization sequence data.

    Returns:
        Dictionary mapping sequence IDs to sequence data
    """
    return _INIT_SEQUENCE_DATA


def get_step_dependencies() -> Dict[str, Dict[str, Dict[str, List[Dict[str, Any]]]]]:
    """
    Get step dependencies.

    Returns:
        Dictionary mapping actor IDs to step dependencies
    """
    return _STEP_DEPENDENCIES


def get_performance_metrics() -> Dict[str, Dict[str, Any]]:
    """
    Get performance metrics.

    Returns:
        Dictionary mapping actor IDs to performance metrics
    """
    return _PERFORMANCE_METRICS


def start_initialization_sequence() -> str:
    """
    Start a new initialization sequence.

    Returns:
        Sequence ID
    """
    global _CURRENT_SEQUENCE_ID

    # Generate a sequence ID
    sequence_id = f"init_sequence_{int(time.time() * 1000)}"

    # Create a new sequence entry
    _INIT_SEQUENCE_DATA[sequence_id] = {
        "actors": [],
        "steps": [],
        "dependencies": [],
        "start_time": time.time(),
        "end_time": None,
        "duration": None
    }

    # Set the current sequence ID
    _CURRENT_SEQUENCE_ID = sequence_id

    return sequence_id


def end_initialization_sequence(sequence_id: Optional[str] = None) -> None:
    """
    End an initialization sequence.

    Args:
        sequence_id: Optional sequence ID (uses current sequence if not provided)
    """
    global _CURRENT_SEQUENCE_ID

    # Use current sequence ID if not provided
    if sequence_id is None:
        sequence_id = _CURRENT_SEQUENCE_ID

    # If no sequence ID, do nothing
    if sequence_id is None or sequence_id not in _INIT_SEQUENCE_DATA:
        return

    # Update sequence end time and duration
    _INIT_SEQUENCE_DATA[sequence_id]["end_time"] = time.time()
    _INIT_SEQUENCE_DATA[sequence_id]["duration"] = (
        _INIT_SEQUENCE_DATA[sequence_id]["end_time"] -
        _INIT_SEQUENCE_DATA[sequence_id]["start_time"]
    )

    # Reset current sequence ID if it matches
    if _CURRENT_SEQUENCE_ID == sequence_id:
        _CURRENT_SEQUENCE_ID = None

    # Calculate critical paths for all actors in the sequence
    _calculate_critical_paths(sequence_id)


def add_step_to_sequence(
    actor_id: str,
    step: InitializationStep,
    start_time: float,
    end_time: float,
    duration: float,
    details: Optional[Dict[str, Any]] = None,
    sequence_id: Optional[str] = None
) -> None:
    """
    Add a step to an initialization sequence.

    Args:
        actor_id: Actor ID
        step: Initialization step
        start_time: Step start time
        end_time: Step end time
        duration: Step duration
        details: Optional step details
        sequence_id: Optional sequence ID (uses current sequence if not provided)
    """
    # Use current sequence ID if not provided
    if sequence_id is None:
        sequence_id = _CURRENT_SEQUENCE_ID

    # If no sequence ID, do nothing
    if sequence_id is None or sequence_id not in _INIT_SEQUENCE_DATA:
        return

    # Add actor to sequence if not already present
    if actor_id not in _INIT_SEQUENCE_DATA[sequence_id]["actors"]:
        _INIT_SEQUENCE_DATA[sequence_id]["actors"].append(actor_id)

    # Add step to sequence
    _INIT_SEQUENCE_DATA[sequence_id]["steps"].append({
        "actor_id": actor_id,
        "step": step.name,
        "step_category": step.category.name,
        "start_time": start_time,
        "end_time": end_time,
        "duration": duration,
        "details": details or {}
    })


def add_step_dependency(
    from_actor_id: str,
    from_step: InitializationStep,
    to_actor_id: str,
    to_step: InitializationStep,
    dependency_type: str,
    details: Optional[Dict[str, Any]] = None,
    sequence_id: Optional[str] = None
) -> None:
    """
    Add a dependency between initialization steps.

    Args:
        from_actor_id: Source actor ID
        from_step: Source step
        to_actor_id: Target actor ID
        to_step: Target step
        dependency_type: Type of dependency
        details: Optional dependency details
        sequence_id: Optional sequence ID (uses current sequence if not provided)
    """
    # Use current sequence ID if not provided
    if sequence_id is None:
        sequence_id = _CURRENT_SEQUENCE_ID

    # If no sequence ID, do nothing
    if sequence_id is None or sequence_id not in _INIT_SEQUENCE_DATA:
        return

    # Add dependency to sequence
    _INIT_SEQUENCE_DATA[sequence_id]["dependencies"].append({
        "from_actor_id": from_actor_id,
        "from_step": from_step.name,
        "to_actor_id": to_actor_id,
        "to_step": to_step.name,
        "type": dependency_type,
        "details": details or {}
    })

    # Add dependency to step dependencies
    if from_actor_id not in _STEP_DEPENDENCIES:
        _STEP_DEPENDENCIES[from_actor_id] = {}

    if from_step.name not in _STEP_DEPENDENCIES[from_actor_id]:
        _STEP_DEPENDENCIES[from_actor_id][from_step.name] = {
            "depends_on": [],
            "depended_by": []
        }

    # Add to depended_by list
    _STEP_DEPENDENCIES[from_actor_id][from_step.name]["depended_by"].append({
        "actor_id": to_actor_id,
        "step": to_step.name,
        "type": dependency_type,
        "details": details or {}
    })

    # Add to to_actor's depends_on list
    if to_actor_id not in _STEP_DEPENDENCIES:
        _STEP_DEPENDENCIES[to_actor_id] = {}

    if to_step.name not in _STEP_DEPENDENCIES[to_actor_id]:
        _STEP_DEPENDENCIES[to_actor_id][to_step.name] = {
            "depends_on": [],
            "depended_by": []
        }

    _STEP_DEPENDENCIES[to_actor_id][to_step.name]["depends_on"].append({
        "actor_id": from_actor_id,
        "step": from_step.name,
        "type": dependency_type,
        "details": details or {}
    })


def _calculate_critical_paths(sequence_id: str) -> None:
    """
    Calculate critical paths for all actors in a sequence.

    Args:
        sequence_id: Sequence ID
    """
    if sequence_id not in _INIT_SEQUENCE_DATA:
        return

    # Get sequence data
    sequence = _INIT_SEQUENCE_DATA[sequence_id]

    # Calculate critical path for each actor
    for actor_id in sequence["actors"]:
        # Get all steps for this actor
        actor_steps = [
            step for step in sequence["steps"]
            if step["actor_id"] == actor_id
        ]

        # Sort steps by start time
        actor_steps.sort(key=lambda s: s["start_time"])

        # Calculate the critical path
        critical_path = []
        total_duration = 0
        bottlenecks = []

        for step in actor_steps:
            critical_path.append(step["step"])
            total_duration += step["duration"]

            # Check if this step is a bottleneck (takes more than 20% of total time)
            if step["duration"] > 0.2 * total_duration:
                bottlenecks.append(step["step"])

        # Store critical path data
        _CRITICAL_PATH_DATA[actor_id] = {
            "path": critical_path,
            "duration": total_duration,
            "bottlenecks": bottlenecks
        }


def record_performance_metric(
    actor_id: str,
    metric_name: str,
    value: Any,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Record a performance metric.

    Args:
        actor_id: Actor ID
        metric_name: Metric name
        value: Metric value
        details: Optional metric details
    """
    if actor_id not in _PERFORMANCE_METRICS:
        _PERFORMANCE_METRICS[actor_id] = {}

    _PERFORMANCE_METRICS[actor_id][metric_name] = {
        "value": value,
        "timestamp": time.time(),
        "details": details or {}
    }


@contextmanager
def time_operation(
    actor_id: str,
    operation_name: str,
    details: Optional[Dict[str, Any]] = None,
    step_context: Optional[Dict[str, Any]] = None,
    category: Optional[str] = None,
    step: Optional[InitializationStep] = None
) -> Generator[Dict[str, Any], None, None]:
    """
    Context manager for timing operations within initialization steps.

    This allows for detailed timing of specific operations within initialization steps,
    such as dependency resolution, actor lookup, etc.

    Args:
        actor_id: ID of the actor
        operation_name: Name of the operation
        details: Optional details about the operation
        step_context: Optional step context data (from init_step_timing)
        category: Optional category for the operation
        step: Optional initialization step for the operation

    Yields:
        Dictionary for storing additional context during the operation
    """
    if not _INIT_DEBUG_ENABLED:
        empty_context: Dict[str, Any] = {}
        yield empty_context
        return

    # Get the current time
    start_time = time.time()

    # Create operation data
    operation_data = {
        "name": operation_name,
        "start_time": start_time,
        "category": category,
        "details": details or {},
        "sub_operations": [],
        "metrics": {}
    }

    # Add to current operations
    if actor_id not in _CURRENT_OPERATIONS:
        _CURRENT_OPERATIONS[actor_id] = []

    _CURRENT_OPERATIONS[actor_id].append(operation_data)

    try:
        # Yield the operation data for the caller to use
        yield operation_data
    finally:
        # Get the end time
        end_time = time.time()

        # Calculate duration
        duration = end_time - start_time

        # Update operation data
        operation_data["end_time"] = end_time
        operation_data["duration"] = duration

        # Remove from current operations
        if actor_id in _CURRENT_OPERATIONS and _CURRENT_OPERATIONS[actor_id]:
            _CURRENT_OPERATIONS[actor_id].pop()

        # Add to operation timing data
        if actor_id not in _OPERATION_TIMING_DATA:
            _OPERATION_TIMING_DATA[actor_id] = []

        _OPERATION_TIMING_DATA[actor_id].append(operation_data)

        # Add to step context if provided
        if step_context is not None and "sub_operations" in step_context:
            step_context["sub_operations"].append(operation_data)
        else:
            # Add to the current step's sub_operations if we're in a step
            if actor_id in _INIT_TIMING_DATA:
                # Find the current step (the one with the latest start_time)
                current_step = None
                latest_start_time = 0

                for step_name, step_data in _INIT_TIMING_DATA[actor_id].items():
                    if isinstance(step_data, dict) and "start_time" in step_data:
                        if step_data["start_time"] > latest_start_time:
                            latest_start_time = step_data["start_time"]
                            current_step = step_name

                # Add to the current step's sub_operations
                if current_step and current_step in _INIT_TIMING_DATA[actor_id]:
                    step_data = _INIT_TIMING_DATA[actor_id][current_step]
                    if isinstance(step_data, dict):
                        if "sub_operations" not in step_data:
                            step_data["sub_operations"] = []

                        step_data["sub_operations"].append({
                            "name": operation_name,
                            "start_time": start_time,
                            "end_time": end_time,
                            "duration": duration,
                            "category": category,
                            "details": details or {}
                        })

        # Log the operation
        log_details = {
            "operation": operation_name,
            "duration": duration,
            "start_time": start_time,
            "end_time": end_time,
            "category": category
        }

        # Add additional details if provided
        if details:
            log_details.update(details)

        # Use provided step or default to INITIALIZE
        log_step = step if step is not None else InitializationStep.INITIALIZE

        log_init_event(
            actor_id=actor_id,
            step=log_step,
            message=f"Operation '{operation_name}' completed in {duration:.4f} seconds",
            details=log_details
        )


def log_dependency_event(
    actor_id: str,
    event_type: DependencyResolutionEventType,
    message: str,
    dependency_id: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    error: Optional[Exception] = None,
    stack_trace: bool = False
) -> None:
    """
    Log a dependency resolution event.

    This function records detailed information about dependency resolution events,
    including the actor, dependency, event type, and any additional details.

    Args:
        actor_id: ID of the actor
        event_type: Type of dependency resolution event
        message: Log message
        dependency_id: Optional ID of the dependency
        details: Optional details to include in the log
        error: Optional error to include in the log
        stack_trace: Whether to include a stack trace in the log
    """
    if not _INIT_DEBUG_ENABLED:
        return

    # Create the event entry
    event_entry = {
        "timestamp": datetime.now().isoformat(),
        "actor_id": actor_id,
        "event_type": event_type.name,
        "message": message,
        "dependency_id": dependency_id,
        "details": details or {},
    }

    # Add error information if provided
    if error:
        event_entry["error"] = str(error)
        event_entry["error_type"] = type(error).__name__

        # Add stack trace if requested
        if stack_trace:
            event_entry["stack_trace"] = traceback.format_exc()

    # Add the event to the buffer
    _DEPENDENCY_RESOLUTION_EVENTS.append(event_entry)

    # Add to the resolution path for this actor
    if actor_id not in _DEPENDENCY_RESOLUTION_PATHS:
        _DEPENDENCY_RESOLUTION_PATHS[actor_id] = []

    _DEPENDENCY_RESOLUTION_PATHS[actor_id].append(event_entry)

    # Update dependency relationships
    if dependency_id:
        # Create relationship entry if it doesn't exist
        relationship_key = f"{actor_id}:{dependency_id}"
        if relationship_key not in _DEPENDENCY_RELATIONSHIPS:
            _DEPENDENCY_RELATIONSHIPS[relationship_key] = {
                "actor_id": actor_id,
                "dependency_id": dependency_id,
                "events": [],
                "optional": details.get("optional", False) if details else False,
                "resolved": False,
                "failed": False,
                "skipped": False,
                "cycle_detected": False
            }

        # Update relationship based on event type
        relationship = _DEPENDENCY_RELATIONSHIPS[relationship_key]
        relationship["events"].append(event_entry)

        if event_type == DependencyResolutionEventType.DEPENDENCY_RESOLVED:
            relationship["resolved"] = True
        elif event_type == DependencyResolutionEventType.DEPENDENCY_FAILED:
            relationship["failed"] = True
        elif event_type == DependencyResolutionEventType.DEPENDENCY_OPTIONAL_SKIPPED:
            relationship["skipped"] = True
        elif event_type == DependencyResolutionEventType.DEPENDENCY_CYCLE_DETECTED:
            relationship["cycle_detected"] = True

    # Log the event using the standard logging mechanism
    log_message = f"[{actor_id}] [{event_type.name}] {message}"
    if dependency_id:
        log_message += f" - Dependency: {dependency_id}"
    if details:
        log_message += f" - Details: {details}"

    if error:
        _init_debug_logger.error(log_message, exc_info=error if stack_trace else None)
    else:
        _init_debug_logger.debug(log_message)

    # Also log as an initialization event for consistency
    log_init_event(
        actor_id=actor_id,
        step=InitializationStep.DEPENDENCY_RESOLUTION,
        message=message,
        details={
            "event_type": event_type.name,
            "dependency_id": dependency_id,
            **(details or {})
        },
        error=error,
        stack_trace=stack_trace
    )


def register_dependency_relationship(
    actor_id: str,
    dependency_id: str,
    optional: bool = False,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Register a dependency relationship between two actors.

    This function records the dependency relationship between an actor and its dependency,
    which can be used for dependency graph visualization and analysis.

    Args:
        actor_id: ID of the actor
        dependency_id: ID of the dependency
        optional: Whether the dependency is optional
        details: Optional details about the dependency relationship
    """
    if not _INIT_DEBUG_ENABLED:
        return

    # Create the relationship entry
    relationship_key = f"{actor_id}:{dependency_id}"
    if relationship_key not in _DEPENDENCY_RELATIONSHIPS:
        _DEPENDENCY_RELATIONSHIPS[relationship_key] = {
            "actor_id": actor_id,
            "dependency_id": dependency_id,
            "events": [],
            "optional": optional,
            "resolved": False,
            "failed": False,
            "skipped": False,
            "cycle_detected": False,
            "details": details or {}
        }
    else:
        # Update existing relationship
        _DEPENDENCY_RELATIONSHIPS[relationship_key]["optional"] = optional
        if details:
            _DEPENDENCY_RELATIONSHIPS[relationship_key]["details"].update(details)

    # Log the dependency registration
    log_dependency_event(
        actor_id=actor_id,
        event_type=DependencyResolutionEventType.DEPENDENCY_REGISTERED,
        message=f"Registered dependency on {dependency_id}",
        dependency_id=dependency_id,
        details={
            "optional": optional,
            **(details or {})
        }
    )


def get_dependency_resolution_events() -> List[Dict[str, Any]]:
    """
    Get all dependency resolution events.

    Returns:
        List of dependency resolution events
    """
    return _DEPENDENCY_RESOLUTION_EVENTS


def get_dependency_relationships() -> Dict[str, Dict[str, Any]]:
    """
    Get all dependency relationships.

    Returns:
        Dictionary mapping relationship keys to relationship details
    """
    return _DEPENDENCY_RELATIONSHIPS


def get_dependency_resolution_path(actor_id: str) -> List[Dict[str, Any]]:
    """
    Get the dependency resolution path for a specific actor.

    Args:
        actor_id: ID of the actor

    Returns:
        List of dependency resolution events for the actor
    """
    return _DEPENDENCY_RESOLUTION_PATHS.get(actor_id, [])


def get_init_log_buffer() -> List[Dict[str, Any]]:
    """
    Get the initialization log buffer.

    Returns:
        List of log entries
    """
    return _INIT_DEBUG_LOG_BUFFER


def save_init_debug_logs(filename: str) -> None:
    """
    Save initialization debug logs to a file.

    Args:
        filename: File to save logs to
    """
    import json

    # Create directory if it doesn't exist
    log_dir = os.path.dirname(filename)
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)

    # Save the logs
    with open(filename, "w") as f:
        json.dump(_INIT_DEBUG_LOG_BUFFER, f, indent=2)

    _init_debug_logger.info(f"Saved initialization debug logs to {filename}")


def generate_init_debug_report(filename: str) -> None:
    """
    Generate a comprehensive initialization debug report.

    Args:
        filename: File to save the report to
    """
    import json

    # Create directory if it doesn't exist
    report_dir = os.path.dirname(filename)
    if report_dir:
        os.makedirs(report_dir, exist_ok=True)

    # Generate the report
    report = {
        "timestamp": datetime.now().isoformat(),
        "timing_data": _INIT_TIMING_DATA,
        "log_buffer": _INIT_DEBUG_LOG_BUFFER,
        "dependency_resolution_events": _DEPENDENCY_RESOLUTION_EVENTS,
        "dependency_relationships": _DEPENDENCY_RELATIONSHIPS,
        "critical_path_data": _CRITICAL_PATH_DATA,
        "init_sequence_data": _INIT_SEQUENCE_DATA,
        "step_dependencies": _STEP_DEPENDENCIES,
        "performance_metrics": _PERFORMANCE_METRICS,
        "summary": _generate_init_debug_summary()
    }

    # Save the report
    with open(filename, "w") as f:
        json.dump(report, f, indent=2)

    _init_debug_logger.info(f"Generated initialization debug report: {filename}")


def build_dependency_graph() -> Dict[str, Any]:
    """
    Build a graph representation of actor dependencies.

    This function analyzes the dependency relationships and builds a graph
    representation that can be used for visualization and analysis.

    Returns:
        Dictionary with nodes, edges, and graph properties
    """
    # Extract nodes (actors) and edges (dependencies) from relationships
    nodes: Dict[str, Dict[str, Any]] = {}
    edges: List[Dict[str, Any]] = []

    # First, collect all actors (both dependents and dependencies)
    actor_ids = set()
    for relationship_key, relationship in _DEPENDENCY_RELATIONSHIPS.items():
        actor_id = relationship["actor_id"]
        dependency_id = relationship["dependency_id"]
        actor_ids.add(actor_id)
        actor_ids.add(dependency_id)

    # Create nodes for each actor
    for actor_id in actor_ids:
        # Count incoming and outgoing dependencies
        incoming = sum(1 for r in _DEPENDENCY_RELATIONSHIPS.values() if r["dependency_id"] == actor_id)
        outgoing = sum(1 for r in _DEPENDENCY_RELATIONSHIPS.values() if r["actor_id"] == actor_id)

        # Check if this actor has any resolution events
        has_events = actor_id in _DEPENDENCY_RESOLUTION_PATHS

        # Create the node
        nodes[actor_id] = {
            "id": actor_id,
            "incoming_dependencies": incoming,
            "outgoing_dependencies": outgoing,
            "has_resolution_events": has_events
        }

    # Create edges for each relationship
    for relationship_key, relationship in _DEPENDENCY_RELATIONSHIPS.items():
        actor_id = relationship["actor_id"]
        dependency_id = relationship["dependency_id"]

        # Create the edge
        edge = {
            "source": actor_id,
            "target": dependency_id,
            "optional": relationship["optional"],
            "resolved": relationship["resolved"],
            "failed": relationship["failed"],
            "skipped": relationship["skipped"],
            "cycle_detected": relationship["cycle_detected"]
        }

        edges.append(edge)

    # Detect cycles in the graph
    cycles = detect_dependency_cycles(nodes, edges)

    # Return the graph
    return {
        "nodes": nodes,
        "edges": edges,
        "cycles": cycles,
        "total_nodes": len(nodes),
        "total_edges": len(edges),
        "total_cycles": len(cycles)
    }


def detect_dependency_cycles(
    nodes: Dict[str, Dict[str, Any]],
    edges: List[Dict[str, Any]]
) -> List[List[str]]:
    """
    Detect cycles in the dependency graph.

    This function uses a depth-first search algorithm to detect cycles in the
    dependency graph.

    Args:
        nodes: Dictionary mapping actor IDs to node properties
        edges: List of edges representing dependencies

    Returns:
        List of cycles, where each cycle is a list of actor IDs
    """
    # Build an adjacency list for the graph
    adjacency_list: Dict[str, List[str]] = {node_id: [] for node_id in nodes}
    for edge in edges:
        source = edge["source"]
        target = edge["target"]
        adjacency_list[source].append(target)

    # Track visited nodes and nodes in the current path
    visited: Set[str] = set()
    path: List[str] = []
    cycles: List[List[str]] = []

    # DFS function to detect cycles
    def dfs(node: str) -> None:
        if node in path:
            # Found a cycle
            cycle_start = path.index(node)
            cycles.append(path[cycle_start:] + [node])
            return

        if node in visited:
            return

        visited.add(node)
        path.append(node)

        for neighbor in adjacency_list.get(node, []):
            dfs(neighbor)

        path.pop()

    # Run DFS from each node
    for node in nodes:
        if node not in visited:
            dfs(node)

    return cycles


def analyze_dependency_resolution() -> Dict[str, Any]:
    """
    Analyze the dependency resolution process.

    This function analyzes the dependency resolution events and relationships
    to identify patterns, issues, and statistics.

    Returns:
        Dictionary with analysis results
    """
    # Build the dependency graph
    graph = build_dependency_graph()

    # Count events by type
    event_counts: Dict[str, int] = {}
    for event in _DEPENDENCY_RESOLUTION_EVENTS:
        event_type = event["event_type"]
        if event_type not in event_counts:
            event_counts[event_type] = 0
        event_counts[event_type] += 1

    # Count relationships by status
    relationship_counts = {
        "total": len(_DEPENDENCY_RELATIONSHIPS),
        "resolved": sum(1 for r in _DEPENDENCY_RELATIONSHIPS.values() if r["resolved"]),
        "failed": sum(1 for r in _DEPENDENCY_RELATIONSHIPS.values() if r["failed"]),
        "skipped": sum(1 for r in _DEPENDENCY_RELATIONSHIPS.values() if r["skipped"]),
        "cycle_detected": sum(1 for r in _DEPENDENCY_RELATIONSHIPS.values() if r["cycle_detected"]),
        "optional": sum(1 for r in _DEPENDENCY_RELATIONSHIPS.values() if r["optional"]),
        "required": sum(1 for r in _DEPENDENCY_RELATIONSHIPS.values() if not r["optional"])
    }

    # Identify problematic dependencies
    problematic_dependencies = []
    for relationship_key, relationship in _DEPENDENCY_RELATIONSHIPS.items():
        if relationship["failed"] or relationship["cycle_detected"]:
            problematic_dependencies.append({
                "actor_id": relationship["actor_id"],
                "dependency_id": relationship["dependency_id"],
                "optional": relationship["optional"],
                "failed": relationship["failed"],
                "cycle_detected": relationship["cycle_detected"]
            })

    # Return the analysis results
    return {
        "graph": graph,
        "event_counts": event_counts,
        "relationship_counts": relationship_counts,
        "problematic_dependencies": problematic_dependencies,
        "cycles": graph["cycles"]
    }


def _generate_init_debug_summary() -> Dict[str, Any]:
    """
    Generate a summary of initialization debug data.

    Returns:
        Summary dictionary
    """
    # Count events by step
    step_counts: Dict[str, int] = {}
    for entry in _INIT_DEBUG_LOG_BUFFER:
        step = entry["step"]
        if step not in step_counts:
            step_counts[step] = 0
        step_counts[step] += 1

    # Count errors by step
    error_counts: Dict[str, int] = {}
    for entry in _INIT_DEBUG_LOG_BUFFER:
        if "error" in entry:
            step = entry["step"]
            if step not in error_counts:
                error_counts[step] = 0
            error_counts[step] += 1

    # Calculate average durations by step
    avg_durations_dict: Dict[str, List[float]] = {}
    max_durations_dict: Dict[str, float] = {}
    min_durations_dict: Dict[str, float] = {}
    error_steps: Dict[str, List[str]] = {}

    # Process timing data
    for actor_id, steps in _INIT_TIMING_DATA.items():
        for step_name, step_data in steps.items():
            # Get the duration from the step data
            if isinstance(step_data, dict) and "duration" in step_data:
                duration = step_data["duration"]

                # Initialize lists if needed
                if step_name not in avg_durations_dict:
                    avg_durations_dict[step_name] = []

                # Add duration to the list
                avg_durations_dict[step_name].append(duration)

                # Update max duration
                if step_name not in max_durations_dict or duration > max_durations_dict[step_name]:
                    max_durations_dict[step_name] = duration

                # Update min duration
                if step_name not in min_durations_dict or duration < min_durations_dict[step_name]:
                    min_durations_dict[step_name] = duration

                # Check for errors
                if "error" in step_data:
                    if step_name not in error_steps:
                        error_steps[step_name] = []
                    error_steps[step_name].append(actor_id)

    # Calculate average durations
    avg_durations_result = {
        step: sum(durations) / len(durations)
        for step, durations in avg_durations_dict.items()
    }

    # Calculate timing statistics
    timing_stats = {
        step: {
            "avg": avg_durations_result.get(step, 0),
            "min": min_durations_dict.get(step, 0),
            "max": max_durations_dict.get(step, 0),
            "count": len(durations)
        }
        for step, durations in avg_durations_dict.items()
    }

    # Identify slow actors (taking more than 2x the average time)
    slow_actors: Dict[str, List[str]] = {}
    for actor_id, steps in _INIT_TIMING_DATA.items():
        for step_name, step_data in steps.items():
            if isinstance(step_data, dict) and "duration" in step_data:
                duration = step_data["duration"]
                avg_duration = avg_durations_result.get(step_name, 0)

                if avg_duration > 0 and duration > 2 * avg_duration:
                    if step_name not in slow_actors:
                        slow_actors[step_name] = []
                    slow_actors[step_name].append(actor_id)

    # Get dependency resolution analysis
    dependency_analysis = analyze_dependency_resolution() if _DEPENDENCY_RESOLUTION_EVENTS else None

    # Analyze critical paths
    critical_path_stats = {
        "actor_count": len(_CRITICAL_PATH_DATA),
        "bottleneck_steps": {},
        "avg_path_length": 0,
        "max_path_length": 0,
        "min_path_length": float('inf') if _CRITICAL_PATH_DATA else 0
    }

    # Calculate critical path statistics
    if _CRITICAL_PATH_DATA:
        # Calculate path lengths
        path_lengths = [len(data["path"]) for data in _CRITICAL_PATH_DATA.values()]
        critical_path_stats["avg_path_length"] = sum(path_lengths) / len(path_lengths)
        critical_path_stats["max_path_length"] = max(path_lengths)
        critical_path_stats["min_path_length"] = min(path_lengths)

        # Count bottleneck steps
        for actor_id, data in _CRITICAL_PATH_DATA.items():
            for step in data.get("bottlenecks", []):
                if step not in critical_path_stats["bottleneck_steps"]:
                    critical_path_stats["bottleneck_steps"][step] = 0
                critical_path_stats["bottleneck_steps"][step] += 1

    # Analyze initialization sequences
    sequence_stats = {
        "sequence_count": len(_INIT_SEQUENCE_DATA),
        "avg_duration": 0,
        "max_duration": 0,
        "min_duration": float('inf') if _INIT_SEQUENCE_DATA else 0,
        "total_steps": 0,
        "total_dependencies": 0
    }

    # Calculate sequence statistics
    if _INIT_SEQUENCE_DATA:
        # Calculate durations
        durations = [
            data.get("duration", 0)
            for data in _INIT_SEQUENCE_DATA.values()
            if data.get("duration") is not None
        ]

        if durations:
            sequence_stats["avg_duration"] = sum(durations) / len(durations)
            sequence_stats["max_duration"] = max(durations)
            sequence_stats["min_duration"] = min(durations)

        # Count steps and dependencies
        for sequence_id, data in _INIT_SEQUENCE_DATA.items():
            sequence_stats["total_steps"] += len(data.get("steps", []))
            sequence_stats["total_dependencies"] += len(data.get("dependencies", []))

    # Analyze step dependencies
    step_dependency_stats = {
        "actor_count": len(_STEP_DEPENDENCIES),
        "total_dependencies": 0,
        "steps_with_dependencies": 0,
        "max_dependencies_per_step": 0
    }

    # Calculate step dependency statistics
    for actor_id, steps in _STEP_DEPENDENCIES.items():
        for step_name, deps in steps.items():
            depends_on = deps.get("depends_on", [])
            depended_by = deps.get("depended_by", [])

            total_deps = len(depends_on) + len(depended_by)
            step_dependency_stats["total_dependencies"] += total_deps

            if total_deps > 0:
                step_dependency_stats["steps_with_dependencies"] += 1

            if total_deps > step_dependency_stats["max_dependencies_per_step"]:
                step_dependency_stats["max_dependencies_per_step"] = total_deps

    # Analyze performance metrics
    performance_stats = {
        "actor_count": len(_PERFORMANCE_METRICS),
        "metric_count": sum(len(metrics) for metrics in _PERFORMANCE_METRICS.values()),
        "metric_types": {}
    }

    # Count metric types
    for actor_id, metrics in _PERFORMANCE_METRICS.items():
        for metric_name in metrics:
            if metric_name not in performance_stats["metric_types"]:
                performance_stats["metric_types"][metric_name] = 0
            performance_stats["metric_types"][metric_name] += 1

    return {
        "total_events": len(_INIT_DEBUG_LOG_BUFFER),
        "events_by_step": step_counts,
        "errors_by_step": error_counts,
        "timing_stats": timing_stats,
        "slow_actors": slow_actors,
        "error_actors": error_steps,
        "actor_count": len(_INIT_TIMING_DATA),
        "dependency_analysis": dependency_analysis,
        "critical_path_stats": critical_path_stats,
        "sequence_stats": sequence_stats,
        "step_dependency_stats": step_dependency_stats,
        "performance_stats": performance_stats
    }
