"""
Log Filtering and Routing Module
============================

This module provides log filtering and routing capabilities for the actor system,
enabling filtering of logs based on severity, actor type, and custom criteria,
and routing of logs to different destinations.

The log filtering and routing system is designed to work with the structured logger
to provide flexible and configurable logging for the actor system.
"""

import asyncio
import logging
import os
import re
import sys
import threading
import time
from abc import ABC, abstractmethod
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Pattern, Set, Tuple, Union, cast

from .structured_logger import LogLevel, StructuredLogger, get_structured_logger


class LogFilterType(Enum):
    """Types of log filters."""
    LEVEL = "level"
    ACTOR_ID = "actor_id"
    ACTOR_TYPE = "actor_type"
    MESSAGE_TYPE = "message_type"
    CORRELATION_ID = "correlation_id"
    REGEX = "regex"
    CUSTOM = "custom"


class LogFilter(logging.Filter):
    """
    Base class for log filters.

    This class extends the standard logging.Filter to provide additional
    filtering capabilities for the actor system.

    Attributes:
        name: Name of the filter
        filter_type: Type of the filter
        enabled: Whether the filter is enabled
    """

    def __init__(self, name: str, filter_type: LogFilterType):
        """
        Initialize the log filter.

        Args:
            name: Name of the filter
            filter_type: Type of the filter
        """
        super().__init__(name)
        self.filter_type = filter_type
        self.enabled = True

    def filter(self, record: logging.LogRecord) -> bool:
        """
        Filter a log record.

        Args:
            record: Log record to filter

        Returns:
            True if the record should be logged, False otherwise
        """
        if not self.enabled:
            return True
        return self._filter(record)

    @abstractmethod
    def _filter(self, record: logging.LogRecord) -> bool:
        """
        Internal filter method to be implemented by subclasses.

        Args:
            record: Log record to filter

        Returns:
            True if the record should be logged, False otherwise
        """
        pass

    def enable(self) -> None:
        """Enable the filter."""
        self.enabled = True

    def disable(self) -> None:
        """Disable the filter."""
        self.enabled = False


class LevelFilter(LogFilter):
    """
    Filter logs based on level.

    This filter allows logs at or above the specified level.

    Attributes:
        level: Minimum log level to allow
    """

    def __init__(self, name: str, level: Union[int, LogLevel]):
        """
        Initialize the level filter.

        Args:
            name: Name of the filter
            level: Minimum log level to allow
        """
        super().__init__(name, LogFilterType.LEVEL)
        self.level = level.value if isinstance(level, LogLevel) else level

    def _filter(self, record: logging.LogRecord) -> bool:
        """
        Filter a log record based on level.

        Args:
            record: Log record to filter

        Returns:
            True if the record's level is at or above the filter's level
        """
        return record.levelno >= self.level

    def set_level(self, level: Union[int, LogLevel]) -> None:
        """
        Set the minimum log level.

        Args:
            level: Minimum log level to allow
        """
        self.level = level.value if isinstance(level, LogLevel) else level


class ActorIdFilter(LogFilter):
    """
    Filter logs based on actor ID.

    This filter allows logs from the specified actors.

    Attributes:
        actor_ids: Set of actor IDs to allow
        exclude: Whether to exclude the specified actors instead of including them
    """

    def __init__(self, name: str, actor_ids: Union[str, List[str], Set[str]], exclude: bool = False):
        """
        Initialize the actor ID filter.

        Args:
            name: Name of the filter
            actor_ids: Actor ID or list/set of actor IDs to allow
            exclude: Whether to exclude the specified actors instead of including them
        """
        super().__init__(name, LogFilterType.ACTOR_ID)
        self.actor_ids = {actor_ids} if isinstance(actor_ids, str) else set(actor_ids)
        self.exclude = exclude

    def _filter(self, record: logging.LogRecord) -> bool:
        """
        Filter a log record based on actor ID.

        Args:
            record: Log record to filter

        Returns:
            True if the record's actor ID is allowed
        """
        actor_id = getattr(record, 'actor_id', None)
        if actor_id is None:
            return True  # Allow logs without actor_id
        
        if self.exclude:
            return actor_id not in self.actor_ids
        else:
            return actor_id in self.actor_ids

    def add_actor_id(self, actor_id: str) -> None:
        """
        Add an actor ID to the filter.

        Args:
            actor_id: Actor ID to add
        """
        self.actor_ids.add(actor_id)

    def remove_actor_id(self, actor_id: str) -> None:
        """
        Remove an actor ID from the filter.

        Args:
            actor_id: Actor ID to remove
        """
        self.actor_ids.discard(actor_id)


class ActorTypeFilter(LogFilter):
    """
    Filter logs based on actor type.

    This filter allows logs from actors of the specified types.

    Attributes:
        actor_types: Set of actor types to allow
        exclude: Whether to exclude the specified types instead of including them
    """

    def __init__(self, name: str, actor_types: Union[str, List[str], Set[str]], exclude: bool = False):
        """
        Initialize the actor type filter.

        Args:
            name: Name of the filter
            actor_types: Actor type or list/set of actor types to allow
            exclude: Whether to exclude the specified types instead of including them
        """
        super().__init__(name, LogFilterType.ACTOR_TYPE)
        self.actor_types = {actor_types} if isinstance(actor_types, str) else set(actor_types)
        self.exclude = exclude

    def _filter(self, record: logging.LogRecord) -> bool:
        """
        Filter a log record based on actor type.

        Args:
            record: Log record to filter

        Returns:
            True if the record's actor type is allowed
        """
        actor_type = getattr(record, 'actor_type', None)
        if actor_type is None:
            return True  # Allow logs without actor_type
        
        if self.exclude:
            return actor_type not in self.actor_types
        else:
            return actor_type in self.actor_types

    def add_actor_type(self, actor_type: str) -> None:
        """
        Add an actor type to the filter.

        Args:
            actor_type: Actor type to add
        """
        self.actor_types.add(actor_type)

    def remove_actor_type(self, actor_type: str) -> None:
        """
        Remove an actor type from the filter.

        Args:
            actor_type: Actor type to remove
        """
        self.actor_types.discard(actor_type)


class RegexFilter(LogFilter):
    """
    Filter logs based on a regular expression.

    This filter allows logs whose message matches the specified regular expression.

    Attributes:
        pattern: Regular expression pattern to match
        exclude: Whether to exclude matching logs instead of including them
    """

    def __init__(self, name: str, pattern: Union[str, Pattern], exclude: bool = False):
        """
        Initialize the regex filter.

        Args:
            name: Name of the filter
            pattern: Regular expression pattern to match
            exclude: Whether to exclude matching logs instead of including them
        """
        super().__init__(name, LogFilterType.REGEX)
        self.pattern = re.compile(pattern) if isinstance(pattern, str) else pattern
        self.exclude = exclude

    def _filter(self, record: logging.LogRecord) -> bool:
        """
        Filter a log record based on a regular expression.

        Args:
            record: Log record to filter

        Returns:
            True if the record's message matches the pattern
        """
        message = record.getMessage()
        match = bool(self.pattern.search(message))
        
        if self.exclude:
            return not match
        else:
            return match

    def set_pattern(self, pattern: Union[str, Pattern]) -> None:
        """
        Set the regular expression pattern.

        Args:
            pattern: Regular expression pattern to match
        """
        self.pattern = re.compile(pattern) if isinstance(pattern, str) else pattern


class CustomFilter(LogFilter):
    """
    Filter logs based on a custom function.

    This filter allows logs that pass the specified custom function.

    Attributes:
        filter_func: Custom filter function
    """

    def __init__(self, name: str, filter_func: Callable[[logging.LogRecord], bool]):
        """
        Initialize the custom filter.

        Args:
            name: Name of the filter
            filter_func: Custom filter function
        """
        super().__init__(name, LogFilterType.CUSTOM)
        self.filter_func = filter_func

    def _filter(self, record: logging.LogRecord) -> bool:
        """
        Filter a log record using the custom function.

        Args:
            record: Log record to filter

        Returns:
            Result of the custom filter function
        """
        return self.filter_func(record)

    def set_filter_func(self, filter_func: Callable[[logging.LogRecord], bool]) -> None:
        """
        Set the custom filter function.

        Args:
            filter_func: Custom filter function
        """
        self.filter_func = filter_func


class LogRouter:
    """
    Routes logs to different destinations.

    This class provides methods for routing logs to different destinations,
    such as files, console, or external systems.

    Attributes:
        logger: Logger for the router
        handlers: Dictionary of handlers by name
    """

    def __init__(self, name: str = 'log_router'):
        """
        Initialize the log router.

        Args:
            name: Name for the router's logger
        """
        self.logger = get_structured_logger(f"vibe_check_actor_system.{name}")
        self.handlers: Dict[str, logging.Handler] = {}
        self._lock = threading.RLock()

    def add_handler(self, name: str, handler: logging.Handler) -> None:
        """
        Add a handler to the router.

        Args:
            name: Name of the handler
            handler: Handler to add
        """
        with self._lock:
            self.handlers[name] = handler
            self.logger.info(f"Added handler {name}")

    def remove_handler(self, name: str) -> None:
        """
        Remove a handler from the router.

        Args:
            name: Name of the handler to remove
        """
        with self._lock:
            if name in self.handlers:
                del self.handlers[name]
                self.logger.info(f"Removed handler {name}")

    def get_handler(self, name: str) -> Optional[logging.Handler]:
        """
        Get a handler by name.

        Args:
            name: Name of the handler

        Returns:
            Handler, or None if not found
        """
        with self._lock:
            return self.handlers.get(name)

    def add_console_handler(self, name: str, level: Union[int, LogLevel] = LogLevel.INFO,
                           formatter: Optional[logging.Formatter] = None) -> None:
        """
        Add a console handler to the router.

        Args:
            name: Name of the handler
            level: Logging level
            formatter: Optional formatter for the handler
        """
        with self._lock:
            # Create the handler
            handler = logging.StreamHandler(sys.stdout)
            
            # Set the level
            handler.setLevel(level.value if isinstance(level, LogLevel) else level)
            
            # Set the formatter
            if formatter:
                handler.setFormatter(formatter)
            
            # Add the handler
            self.add_handler(name, handler)

    def add_file_handler(self, name: str, filename: Union[str, Path], level: Union[int, LogLevel] = LogLevel.INFO,
                        formatter: Optional[logging.Formatter] = None, mode: str = 'a',
                        encoding: str = 'utf-8') -> None:
        """
        Add a file handler to the router.

        Args:
            name: Name of the handler
            filename: Path to the log file
            level: Logging level
            formatter: Optional formatter for the handler
            mode: File mode ('a' for append, 'w' for write)
            encoding: File encoding
        """
        with self._lock:
            # Create the directory if it doesn't exist
            path = Path(filename)
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # Create the handler
            handler = logging.FileHandler(filename, mode=mode, encoding=encoding)
            
            # Set the level
            handler.setLevel(level.value if isinstance(level, LogLevel) else level)
            
            # Set the formatter
            if formatter:
                handler.setFormatter(formatter)
            
            # Add the handler
            self.add_handler(name, handler)

    def add_json_file_handler(self, name: str, filename: Union[str, Path], level: Union[int, LogLevel] = LogLevel.INFO,
                             mode: str = 'a', encoding: str = 'utf-8') -> None:
        """
        Add a JSON file handler to the router.

        Args:
            name: Name of the handler
            filename: Path to the log file
            level: Logging level
            mode: File mode ('a' for append, 'w' for write)
            encoding: File encoding
        """
        from .structured_logger import JsonFormatter
        formatter = JsonFormatter()
        self.add_file_handler(name, filename, level, formatter, mode, encoding)
