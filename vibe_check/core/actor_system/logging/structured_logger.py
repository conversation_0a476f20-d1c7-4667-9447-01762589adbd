"""
Structured Logger Module
====================

This module provides a structured logger for the actor system, which outputs logs
in a structured format (JSON) with consistent fields. It supports context-aware
logging, different log levels, and thread safety for concurrent logging operations.

The structured logger extends Python's standard logging module with additional
functionality specific to the actor system, such as actor ID, message ID, and
operation context.
"""

import asyncio
import inspect
import json
import logging
import os
import sys
import threading
import time
import traceback
import uuid
from datetime import datetime
from enum import Enum
from functools import wraps
from pathlib import Path
from typing import Any, Callable, Dict, List, Mapping, Optional, Set, Tuple, Union, TypeVar, cast

# Constants
DEFAULT_LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
JSON_LOG_FORMAT = '{"timestamp": "%(asctime)s", "logger": "%(name)s", "level": "%(levelname)s", "message": "%(message)s"}'


class LogLevel(Enum):
    """Log levels for the actor system."""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


class StructuredLogRecord(logging.LogRecord):
    """
    Extended LogRecord class that supports structured data.

    This class extends the standard LogRecord to include additional fields
    for structured logging, such as actor_id, message_id, correlation_id,
    and context.
    """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        """Initialize the structured log record."""
        super().__init__(*args, **kwargs)
        self.actor_id = getattr(self, 'actor_id', None)
        self.message_id = getattr(self, 'message_id', None)
        self.correlation_id = getattr(self, 'correlation_id', None)
        self.context = getattr(self, 'context', {})
        self.thread_id = threading.get_ident()
        task = asyncio.current_task()
        self.task_id = task.get_name() if task else None


class StructuredLogger(logging.Logger):
    """
    Structured logger for the actor system.

    This logger outputs logs in a structured format (JSON) with consistent fields.
    It supports context-aware logging, different log levels, and thread safety
    for concurrent logging operations.

    Attributes:
        actor_id: ID of the actor associated with this logger
        context: Default context for log messages
    """

    def __init__(self, name: str, level: int = logging.NOTSET, actor_id: Optional[str] = None):
        """
        Initialize the structured logger.

        Args:
            name: Name of the logger
            level: Logging level
            actor_id: Optional ID of the actor associated with this logger
        """
        super().__init__(name, level)
        self.actor_id = actor_id
        self.context: Dict[str, Any] = {}
        self._lock = threading.RLock()

    def makeRecord(self, name: str, level: int, fn: str, lno: int, msg: Any, args: Any, exc_info: Any, func: Optional[str] = None, extra: Optional[Mapping[str, object]] = None, sinfo: Optional[str] = None) -> StructuredLogRecord:
        """
        Create a LogRecord with additional structured data.

        Args:
            name: Logger name
            level: Logging level
            fn: Filename
            lno: Line number
            msg: Log message
            args: Message arguments
            exc_info: Exception info
            func: Function name
            extra: Extra data
            sinfo: Stack info

        Returns:
            StructuredLogRecord with additional fields
        """
        # Create a copy of the context to avoid modifying the original
        context = self.context.copy()

        # Convert extra to a mutable dict if it's not None
        extra_dict: Dict[str, Any] = dict(extra) if extra is not None else {}

        # Add actor_id to extra if it's not already there
        if 'actor_id' not in extra_dict and self.actor_id:
            extra_dict['actor_id'] = self.actor_id

        # Add context to extra if it's not already there
        if 'context' not in extra_dict:
            extra_dict['context'] = context
        else:
            # Merge the context with the existing context
            if isinstance(extra_dict['context'], dict):
                context.update(extra_dict['context'])
            extra_dict['context'] = context

        # Create the record
        record = StructuredLogRecord(name, level, fn, lno, msg, args, exc_info, func, sinfo)

        # Add extra attributes
        for key, value in extra_dict.items():
            setattr(record, key, value)

        return record

    def with_context(self, **context: Any) -> 'StructuredLogger':
        """
        Create a new logger with additional context.

        Args:
            **context: Context key-value pairs

        Returns:
            New structured logger with updated context
        """
        with self._lock:
            # Create a new logger with the same name and level
            logger = StructuredLogger(self.name, self.level, self.actor_id)

            # Copy handlers and filters
            for handler in self.handlers:
                logger.addHandler(handler)
            for filter in self.filters:
                logger.addFilter(filter)

            # Merge the context
            logger.context = {**self.context, **context}

            return logger

    def with_correlation_id(self, correlation_id: str) -> 'StructuredLogger':
        """
        Create a new logger with a correlation ID.

        Args:
            correlation_id: Correlation ID

        Returns:
            New structured logger with correlation ID
        """
        return self.with_context(correlation_id=correlation_id)

    def with_message_id(self, message_id: str) -> 'StructuredLogger':
        """
        Create a new logger with a message ID.

        Args:
            message_id: Message ID

        Returns:
            New structured logger with message ID
        """
        return self.with_context(message_id=message_id)

    def with_actor_id(self, actor_id: str) -> 'StructuredLogger':
        """
        Create a new logger with an actor ID.

        Args:
            actor_id: Actor ID

        Returns:
            New structured logger with actor ID
        """
        with self._lock:
            # Create a new logger with the same name and level
            logger = StructuredLogger(self.name, self.level, actor_id)

            # Copy handlers and filters
            for handler in self.handlers:
                logger.addHandler(handler)
            for filter in self.filters:
                logger.addFilter(filter)

            # Copy the context
            logger.context = self.context.copy()

            return logger

    def get_json_formatter(self) -> logging.Formatter:
        """
        Get a JSON formatter for the logger.

        Returns:
            JSON formatter
        """
        return JsonFormatter()


class JsonFormatter(logging.Formatter):
    """
    Formatter that outputs log records as JSON.

    This formatter converts log records to JSON format, including all
    additional fields from the StructuredLogRecord.
    """

    def format(self, record: logging.LogRecord) -> str:
        """
        Format the log record as JSON.

        Args:
            record: Log record to format

        Returns:
            JSON-formatted log record
        """
        # Create a dictionary with the basic log record fields
        log_data = {
            'timestamp': self.formatTime(record, self.datefmt),
            'logger': record.name,
            'level': record.levelname,
            'message': record.getMessage(),
            'path': record.pathname,
            'line': record.lineno,
            'function': record.funcName,
            'thread_id': getattr(record, 'thread_id', threading.get_ident()),
            'process_id': record.process
        }

        # Add task_id if available
        task_id = getattr(record, 'task_id', None)
        if task_id:
            log_data['task_id'] = task_id

        # Add actor_id if available
        actor_id = getattr(record, 'actor_id', None)
        if actor_id:
            log_data['actor_id'] = actor_id

        # Add message_id if available
        message_id = getattr(record, 'message_id', None)
        if message_id:
            log_data['message_id'] = message_id

        # Add correlation_id if available
        correlation_id = getattr(record, 'correlation_id', None)
        if correlation_id:
            log_data['correlation_id'] = correlation_id

        # Add context if available
        context = getattr(record, 'context', None)
        if context:
            log_data['context'] = context

        # Add exception info if available
        if record.exc_info and record.exc_info[0] is not None:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': self.formatException(record.exc_info)
            }

        # Convert to JSON
        return json.dumps(log_data)


def get_structured_logger(name: str, actor_id: Optional[str] = None) -> StructuredLogger:
    """
    Get a structured logger with the specified name.

    Args:
        name: Logger name
        actor_id: Optional ID of the actor associated with this logger

    Returns:
        Structured logger instance
    """
    # Register the StructuredLogger class with the logging module
    logging.setLoggerClass(StructuredLogger)

    # Get the logger
    logger = logging.getLogger(name)

    # If the logger is not a StructuredLogger, create a new one
    if not isinstance(logger, StructuredLogger):
        logger = StructuredLogger(name, logger.level, actor_id)

    # Set the actor_id if provided
    if actor_id and not logger.actor_id:
        logger.actor_id = actor_id

    return logger
