"""
Actor System Logging Module
=======================

This module provides comprehensive logging capabilities for the actor system,
including structured logging, log correlation, filtering, routing, aggregation,
and analysis.

It supports detailed logging of initialization, message passing, state transitions,
and component states, as well as visualization of the actor system state and message flow.

Enhanced with specialized initialization debugging capabilities for diagnosing
and resolving issues with the actor initialization process.
"""

# Legacy logging functions
from .enhanced_logger import (
    setup_actor_system_logging,
    get_actor_logger as get_legacy_actor_logger,
    enable_debug_mode,
    disable_debug_mode,
    is_debug_mode_enabled,
    set_log_file,
    get_log_file,
    log_exception,
    log_timing,
    log_state_transition,
    log_message,
    log_initialization,
    log_component_state,
    save_logs_to_file,
    visualize_actor_system
)

# Initialization debugging
from .initialization_debug import (
    InitializationStep,
    DependencyResolutionEventType,
    enable_init_debugging,
    disable_init_debugging,
    is_init_debugging_enabled,
    log_init_event,
    init_step_timing,
    time_operation,
    init_debug_decorator,
    init_debug_async_decorator,
    get_init_timing_data,
    get_init_log_buffer,
    save_init_debug_logs,
    generate_init_debug_report,
    # Dependency resolution tracking
    log_dependency_event,
    register_dependency_relationship,
    get_dependency_resolution_events,
    get_dependency_relationships,
    get_dependency_resolution_path,
    build_dependency_graph,
    detect_dependency_cycles,
    analyze_dependency_resolution
)

# Structured logging
from .structured_logger import (
    LogLevel,
    StructuredLogger,
    StructuredLogRecord,
    JsonFormatter,
    get_structured_logger
)

# Log correlation
from .correlation import (
    CorrelationContext,
    LogCorrelator,
    get_correlator,
    create_correlation_context,
    get_correlation_context,
    add_message_flow
)

# Log filtering and routing
from .filtering import (
    LogFilterType,
    LogFilter,
    LevelFilter,
    ActorIdFilter,
    ActorTypeFilter,
    RegexFilter,
    CustomFilter,
    LogRouter
)

# Log aggregation and analysis
from .aggregator import (
    LogEntry,
    LogEntryType,
    LogAggregator,
    LogAnalyzer
)

# Integration with actor system
from .integration import (
    LoggingManager,
    get_logging_manager,
    initialize_logging,
    get_actor_logger,
    create_actor_correlation_context
)

__all__ = [
    # Legacy logging functions
    'setup_actor_system_logging',
    'get_legacy_actor_logger',
    'enable_debug_mode',
    'disable_debug_mode',
    'is_debug_mode_enabled',
    'set_log_file',
    'get_log_file',
    'log_exception',
    'log_timing',
    'log_state_transition',
    'log_message',
    'log_initialization',
    'log_component_state',
    'save_logs_to_file',
    'visualize_actor_system',

    # Initialization debugging
    'InitializationStep',
    'DependencyResolutionEventType',
    'enable_init_debugging',
    'disable_init_debugging',
    'is_init_debugging_enabled',
    'log_init_event',
    'init_step_timing',
    'time_operation',
    'init_debug_decorator',
    'init_debug_async_decorator',
    'get_init_timing_data',
    'get_init_log_buffer',
    'save_init_debug_logs',
    'generate_init_debug_report',
    # Dependency resolution tracking
    'log_dependency_event',
    'register_dependency_relationship',
    'get_dependency_resolution_events',
    'get_dependency_relationships',
    'get_dependency_resolution_path',
    'build_dependency_graph',
    'detect_dependency_cycles',
    'analyze_dependency_resolution',

    # Structured logging
    'LogLevel',
    'StructuredLogger',
    'StructuredLogRecord',
    'JsonFormatter',
    'get_structured_logger',

    # Log correlation
    'CorrelationContext',
    'LogCorrelator',
    'get_correlator',
    'create_correlation_context',
    'get_correlation_context',
    'add_message_flow',

    # Log filtering and routing
    'LogFilterType',
    'LogFilter',
    'LevelFilter',
    'ActorIdFilter',
    'ActorTypeFilter',
    'RegexFilter',
    'CustomFilter',
    'LogRouter',

    # Log aggregation and analysis
    'LogEntry',
    'LogEntryType',
    'LogAggregator',
    'LogAnalyzer',

    # Integration with actor system
    'LoggingManager',
    'get_logging_manager',
    'initialize_logging',
    'get_actor_logger',
    'create_actor_correlation_context'
]
