"""
Log Aggregation and Analysis Module
===============================

This module provides log aggregation and analysis capabilities for the actor system,
enabling collection of logs from multiple actors, analysis of logs to identify
patterns and anomalies, and querying and searching of logs.

The log aggregation and analysis system is designed to work with the structured
logger to provide a comprehensive view of the actor system's behavior.
"""

import asyncio
import json
import logging
import os
import re
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Pattern, Set, Tuple, Union, cast, Deque

from .structured_logger import LogLevel, StructuredLogger, get_structured_logger


class LogEntryType(Enum):
    """Types of log entries."""
    MESSAGE = "message"
    EXCEPTION = "exception"
    STATE_TRANSITION = "state_transition"
    COMPONENT_STATE = "component_state"
    INITIALIZATION = "initialization"
    TIMING = "timing"
    CUSTOM = "custom"


@dataclass
class LogEntry:
    """
    Log entry for aggregation and analysis.

    This class represents a log entry in the aggregation system, including
    timestamp, level, message, and additional fields.

    Attributes:
        timestamp: Time when the log entry was created
        level: Log level
        message: Log message
        logger_name: Name of the logger
        entry_type: Type of the log entry
        actor_id: Optional ID of the actor
        correlation_id: Optional correlation ID
        context: Additional context for the log entry
    """
    timestamp: float
    level: int
    message: str
    logger_name: str
    entry_type: LogEntryType
    actor_id: Optional[str] = None
    correlation_id: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)

    @classmethod
    def from_record(cls, record: logging.LogRecord, entry_type: Optional[LogEntryType] = None) -> 'LogEntry':
        """
        Create a log entry from a log record.

        Args:
            record: Log record to convert
            entry_type: Optional type of the log entry

        Returns:
            Log entry
        """
        # Determine the entry type
        if entry_type is None:
            if hasattr(record, 'entry_type'):
                entry_type = getattr(record, 'entry_type')
            elif record.exc_info:
                entry_type = LogEntryType.EXCEPTION
            else:
                entry_type = LogEntryType.MESSAGE

        # Extract context
        context = getattr(record, 'context', {})
        if not context:
            context = {}

        # Create the log entry
        return cls(
            timestamp=record.created,
            level=record.levelno,
            message=record.getMessage(),
            logger_name=record.name,
            entry_type=entry_type,
            actor_id=getattr(record, 'actor_id', None),
            correlation_id=getattr(record, 'correlation_id', None),
            context=context
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the log entry to a dictionary.

        Returns:
            Dictionary representation of the log entry
        """
        return {
            'timestamp': self.timestamp,
            'level': self.level,
            'level_name': logging.getLevelName(self.level),
            'message': self.message,
            'logger_name': self.logger_name,
            'entry_type': self.entry_type.value,
            'actor_id': self.actor_id,
            'correlation_id': self.correlation_id,
            'context': self.context
        }

    def to_json(self) -> str:
        """
        Convert the log entry to JSON.

        Returns:
            JSON representation of the log entry
        """
        return json.dumps(self.to_dict())


class LogAggregator:
    """
    Aggregates logs from multiple actors.

    This class provides methods for collecting logs from multiple actors,
    storing them in memory or on disk, and retrieving them for analysis.

    Attributes:
        logger: Logger for the aggregator
        entries: List of log entries
        max_entries: Maximum number of entries to keep in memory
        handlers: List of handlers for processing log entries
    """

    def __init__(self, name: str = 'log_aggregator', max_entries: int = 10000):
        """
        Initialize the log aggregator.

        Args:
            name: Name for the aggregator's logger
            max_entries: Maximum number of entries to keep in memory
        """
        self.logger = get_structured_logger(f"vibe_check_actor_system.{name}")
        self.entries: Deque[LogEntry] = deque(maxlen=max_entries)
        self.max_entries = max_entries
        self.handlers: List[Callable[[LogEntry], None]] = []
        self._lock = asyncio.Lock()

    async def add_entry(self, entry: LogEntry) -> None:
        """
        Add a log entry to the aggregator.

        Args:
            entry: Log entry to add
        """
        async with self._lock:
            self.entries.append(entry)
            
            # Process the entry with all handlers
            for handler in self.handlers:
                try:
                    handler(entry)
                except Exception as e:
                    self.logger.error(f"Error processing log entry with handler: {e}")

    async def add_record(self, record: logging.LogRecord, entry_type: Optional[LogEntryType] = None) -> None:
        """
        Add a log record to the aggregator.

        Args:
            record: Log record to add
            entry_type: Optional type of the log entry
        """
        entry = LogEntry.from_record(record, entry_type)
        await self.add_entry(entry)

    async def get_entries(self, start_time: Optional[float] = None, end_time: Optional[float] = None,
                         level: Optional[int] = None, entry_type: Optional[LogEntryType] = None,
                         actor_id: Optional[str] = None, correlation_id: Optional[str] = None,
                         message_pattern: Optional[Union[str, Pattern]] = None,
                         limit: Optional[int] = None) -> List[LogEntry]:
        """
        Get log entries matching the specified criteria.

        Args:
            start_time: Optional start time for filtering entries
            end_time: Optional end time for filtering entries
            level: Optional minimum log level for filtering entries
            entry_type: Optional entry type for filtering entries
            actor_id: Optional actor ID for filtering entries
            correlation_id: Optional correlation ID for filtering entries
            message_pattern: Optional message pattern for filtering entries
            limit: Optional maximum number of entries to return

        Returns:
            List of log entries matching the criteria
        """
        async with self._lock:
            # Convert message pattern to regex if it's a string
            if message_pattern is not None and isinstance(message_pattern, str):
                message_pattern = re.compile(message_pattern)
            
            # Filter entries
            filtered_entries = []
            for entry in self.entries:
                # Check start time
                if start_time is not None and entry.timestamp < start_time:
                    continue
                
                # Check end time
                if end_time is not None and entry.timestamp > end_time:
                    continue
                
                # Check level
                if level is not None and entry.level < level:
                    continue
                
                # Check entry type
                if entry_type is not None and entry.entry_type != entry_type:
                    continue
                
                # Check actor ID
                if actor_id is not None and entry.actor_id != actor_id:
                    continue
                
                # Check correlation ID
                if correlation_id is not None and entry.correlation_id != correlation_id:
                    continue
                
                # Check message pattern
                if message_pattern is not None and not message_pattern.search(entry.message):
                    continue
                
                # Add the entry to the filtered list
                filtered_entries.append(entry)
                
                # Check limit
                if limit is not None and len(filtered_entries) >= limit:
                    break
            
            return filtered_entries

    async def clear_entries(self) -> None:
        """Clear all log entries."""
        async with self._lock:
            self.entries.clear()

    def add_handler(self, handler: Callable[[LogEntry], None]) -> None:
        """
        Add a handler for processing log entries.

        Args:
            handler: Handler function that takes a log entry
        """
        self.handlers.append(handler)

    def remove_handler(self, handler: Callable[[LogEntry], None]) -> None:
        """
        Remove a handler for processing log entries.

        Args:
            handler: Handler function to remove
        """
        if handler in self.handlers:
            self.handlers.remove(handler)


class LogAnalyzer:
    """
    Analyzes logs to identify patterns and anomalies.

    This class provides methods for analyzing logs to identify patterns,
    anomalies, and trends, and for generating reports and visualizations.

    Attributes:
        logger: Logger for the analyzer
        aggregator: Log aggregator for retrieving logs
    """

    def __init__(self, aggregator: LogAggregator, name: str = 'log_analyzer'):
        """
        Initialize the log analyzer.

        Args:
            aggregator: Log aggregator for retrieving logs
            name: Name for the analyzer's logger
        """
        self.logger = get_structured_logger(f"vibe_check_actor_system.{name}")
        self.aggregator = aggregator

    async def count_entries_by_level(self, start_time: Optional[float] = None, end_time: Optional[float] = None,
                                   actor_id: Optional[str] = None) -> Dict[int, int]:
        """
        Count log entries by level.

        Args:
            start_time: Optional start time for filtering entries
            end_time: Optional end time for filtering entries
            actor_id: Optional actor ID for filtering entries

        Returns:
            Dictionary mapping log levels to counts
        """
        entries = await self.aggregator.get_entries(start_time, end_time, actor_id=actor_id)
        counts = defaultdict(int)
        for entry in entries:
            counts[entry.level] += 1
        return dict(counts)

    async def count_entries_by_actor(self, start_time: Optional[float] = None, end_time: Optional[float] = None,
                                   level: Optional[int] = None) -> Dict[str, int]:
        """
        Count log entries by actor.

        Args:
            start_time: Optional start time for filtering entries
            end_time: Optional end time for filtering entries
            level: Optional minimum log level for filtering entries

        Returns:
            Dictionary mapping actor IDs to counts
        """
        entries = await self.aggregator.get_entries(start_time, end_time, level=level)
        counts = defaultdict(int)
        for entry in entries:
            if entry.actor_id:
                counts[entry.actor_id] += 1
        return dict(counts)

    async def find_error_patterns(self, start_time: Optional[float] = None, end_time: Optional[float] = None,
                                actor_id: Optional[str] = None) -> List[Tuple[str, int]]:
        """
        Find common error patterns.

        Args:
            start_time: Optional start time for filtering entries
            end_time: Optional end time for filtering entries
            actor_id: Optional actor ID for filtering entries

        Returns:
            List of tuples containing error messages and counts
        """
        entries = await self.aggregator.get_entries(
            start_time, end_time, level=logging.ERROR, actor_id=actor_id
        )
        error_counts = defaultdict(int)
        for entry in entries:
            error_counts[entry.message] += 1
        
        # Sort by count in descending order
        return sorted(error_counts.items(), key=lambda x: x[1], reverse=True)

    async def find_anomalies(self, start_time: Optional[float] = None, end_time: Optional[float] = None,
                           actor_id: Optional[str] = None, threshold: float = 2.0) -> List[LogEntry]:
        """
        Find anomalous log entries.

        Args:
            start_time: Optional start time for filtering entries
            end_time: Optional end time for filtering entries
            actor_id: Optional actor ID for filtering entries
            threshold: Threshold for detecting anomalies (standard deviations from mean)

        Returns:
            List of anomalous log entries
        """
        entries = await self.aggregator.get_entries(start_time, end_time, actor_id=actor_id)
        
        # Group entries by actor and level
        grouped_entries = defaultdict(list)
        for entry in entries:
            key = (entry.actor_id or 'unknown', entry.level)
            grouped_entries[key].append(entry)
        
        # Find anomalies in each group
        anomalies = []
        for (actor, level), group_entries in grouped_entries.items():
            # Skip groups with too few entries
            if len(group_entries) < 10:
                continue
            
            # Calculate mean and standard deviation of timestamps
            timestamps = [entry.timestamp for entry in group_entries]
            mean_timestamp = sum(timestamps) / len(timestamps)
            std_timestamp = (sum((t - mean_timestamp) ** 2 for t in timestamps) / len(timestamps)) ** 0.5
            
            # Find entries with timestamps more than threshold standard deviations from the mean
            for entry in group_entries:
                if abs(entry.timestamp - mean_timestamp) > threshold * std_timestamp:
                    anomalies.append(entry)
        
        return anomalies
