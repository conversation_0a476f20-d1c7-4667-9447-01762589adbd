"""
Logging Integration Module
=====================

This module provides integration between the actor system and the logging system,
enabling actors to use the structured logger, log correlation, filtering, and
aggregation capabilities.

The logging integration system is designed to work with the actor system to
provide comprehensive logging capabilities for actors.
"""

import asyncio
import logging
import os
import sys
import threading
import time
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union, cast, TYPE_CHECKING

from .structured_logger import LogLevel, StructuredLogger, get_structured_logger, JsonFormatter
from .correlation import LogCorrelator, CorrelationContext, get_correlator, create_correlation_context
from .filtering import LogFilter, LogFilterType, LogRouter, LevelFilter, ActorIdFilter, RegexFilter
from .aggregator import LogAggregator, LogAnalyzer, LogEntry, LogEntryType

# Avoid circular imports
if TYPE_CHECKING:
    from ..actor import Actor
    from ..actor_system import ActorSystem


class LoggingManager:
    """
    Manages logging for the actor system.

    This class provides methods for configuring and managing logging for the
    actor system, including setting up loggers, handlers, filters, and
    aggregators.

    Attributes:
        logger: Logger for the manager
        router: Log router for routing logs to different destinations
        aggregator: Log aggregator for collecting logs
        analyzer: Log analyzer for analyzing logs
        correlator: Log correlator for correlating logs
    """

    def __init__(self, name: str = 'logging_manager'):
        """
        Initialize the logging manager.

        Args:
            name: Name for the manager's logger
        """
        self.logger = get_structured_logger(f"vibe_check_actor_system.{name}")
        self.router = LogRouter()
        self.aggregator = LogAggregator()
        self.analyzer = LogAnalyzer(self.aggregator)
        self.correlator = get_correlator()
        self._lock = threading.RLock()
        self._initialized = False

    def initialize(self, log_dir: Optional[Union[str, Path]] = None, console_level: LogLevel = LogLevel.INFO,
                  file_level: LogLevel = LogLevel.DEBUG, json_format: bool = True) -> None:
        """
        Initialize the logging system.

        Args:
            log_dir: Optional directory for log files
            console_level: Log level for console output
            file_level: Log level for file output
            json_format: Whether to use JSON format for logs
        """
        with self._lock:
            if self._initialized:
                return

            # Set up console handler
            if json_format:
                formatter = JsonFormatter()
            else:
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                )

            self.router.add_console_handler('console', console_level, formatter)

            # Set up file handlers if log_dir is provided
            if log_dir:
                log_dir = Path(log_dir)
                log_dir.mkdir(parents=True, exist_ok=True)

                # Set up main log file
                main_log_file = log_dir / 'actor_system.log'
                if json_format:
                    self.router.add_json_file_handler('main_file', main_log_file, file_level)
                else:
                    self.router.add_file_handler('main_file', main_log_file, file_level, formatter)

                # Set up error log file
                error_log_file = log_dir / 'actor_system_errors.log'
                if json_format:
                    self.router.add_json_file_handler('error_file', error_log_file, LogLevel.ERROR)
                else:
                    self.router.add_file_handler('error_file', error_log_file, LogLevel.ERROR, formatter)

            # Set up log aggregator handler
            self.aggregator.add_handler(self._handle_log_entry)

            self._initialized = True
            self.logger.info("Logging system initialized")

    def _handle_log_entry(self, entry: LogEntry) -> None:
        """
        Handle a log entry.

        Args:
            entry: Log entry to handle
        """
        # Add additional processing for log entries here
        pass

    def get_actor_logger(self, actor: Union['Actor', str]) -> StructuredLogger:
        """
        Get a logger for an actor.

        Args:
            actor: Actor object or actor_id string to get a logger for

        Returns:
            Structured logger for the actor
        """
        # Handle the case where actor is a string (actor_id)
        if isinstance(actor, str):
            actor_id = actor
        else:
            actor_id = actor.actor_id

        logger_name = f"vibe_check_actor_system.actor.{actor_id}"
        logger = get_structured_logger(logger_name, actor_id)

        # Add handlers from the router
        for handler in self.router.handlers.values():
            logger.addHandler(handler)

        return logger

    async def create_correlation_context(self, actor: 'Actor', parent_id: Optional[str] = None,
                                       metadata: Optional[Dict[str, Any]] = None) -> CorrelationContext:
        """
        Create a correlation context for an actor.

        Args:
            actor: Actor to create a correlation context for
            parent_id: Optional ID of the parent operation
            metadata: Optional metadata for the context

        Returns:
            Correlation context
        """
        # Add actor information to metadata
        if metadata is None:
            metadata = {}
        metadata['actor_id'] = actor.actor_id
        metadata['actor_type'] = actor.actor_type

        # Create the context
        return await create_correlation_context(parent_id, metadata)

    async def log_message(self, actor: 'Actor', level: LogLevel, message: str, correlation_id: Optional[str] = None,
                        context: Optional[Dict[str, Any]] = None) -> None:
        """
        Log a message for an actor.

        Args:
            actor: Actor to log the message for
            level: Log level
            message: Log message
            correlation_id: Optional correlation ID
            context: Optional context for the log message
        """
        # Get the actor's logger
        logger = self.get_actor_logger(actor)

        # Create a log record
        record = logger.makeRecord(
            logger.name,
            level.value,
            __file__,
            0,
            message,
            (),
            None,
            func=None,
            extra={
                'actor_id': actor.actor_id,
                'correlation_id': correlation_id,
                'context': context or {},
                'entry_type': LogEntryType.MESSAGE
            }
        )

        # Log the record
        logger.handle(record)

        # Add the record to the aggregator
        await self.aggregator.add_record(record, LogEntryType.MESSAGE)

    async def log_exception(self, actor: 'Actor', exc_info: Tuple[type, Exception, Any],
                          correlation_id: Optional[str] = None, context: Optional[Dict[str, Any]] = None) -> None:
        """
        Log an exception for an actor.

        Args:
            actor: Actor to log the exception for
            exc_info: Exception info (type, value, traceback)
            correlation_id: Optional correlation ID
            context: Optional context for the log message
        """
        # Get the actor's logger
        logger = self.get_actor_logger(actor)

        # Create a log record
        record = logger.makeRecord(
            logger.name,
            LogLevel.ERROR.value,
            __file__,
            0,
            str(exc_info[1]),
            (),
            exc_info,
            func=None,
            extra={
                'actor_id': actor.actor_id,
                'correlation_id': correlation_id,
                'context': context or {},
                'entry_type': LogEntryType.EXCEPTION
            }
        )

        # Log the record
        logger.handle(record)

        # Add the record to the aggregator
        await self.aggregator.add_record(record, LogEntryType.EXCEPTION)

    async def log_state_transition(self, actor: 'Actor', old_state: str, new_state: str,
                                 correlation_id: Optional[str] = None, context: Optional[Dict[str, Any]] = None) -> None:
        """
        Log a state transition for an actor.

        Args:
            actor: Actor to log the state transition for
            old_state: Old state
            new_state: New state
            correlation_id: Optional correlation ID
            context: Optional context for the log message
        """
        # Get the actor's logger
        logger = self.get_actor_logger(actor)

        # Create a log record
        message = f"State transition: {old_state} -> {new_state}"
        record = logger.makeRecord(
            logger.name,
            LogLevel.INFO.value,
            __file__,
            0,
            message,
            (),
            None,
            func=None,
            extra={
                'actor_id': actor.actor_id,
                'correlation_id': correlation_id,
                'context': context or {},
                'entry_type': LogEntryType.STATE_TRANSITION,
                'old_state': old_state,
                'new_state': new_state
            }
        )

        # Log the record
        logger.handle(record)

        # Add the record to the aggregator
        await self.aggregator.add_record(record, LogEntryType.STATE_TRANSITION)

    async def log_component_state(self, actor: 'Actor', component: str, state: Dict[str, Any],
                                correlation_id: Optional[str] = None, context: Optional[Dict[str, Any]] = None) -> None:
        """
        Log a component state for an actor.

        Args:
            actor: Actor to log the component state for
            component: Component name
            state: Component state
            correlation_id: Optional correlation ID
            context: Optional context for the log message
        """
        # Get the actor's logger
        logger = self.get_actor_logger(actor)

        # Create a log record
        message = f"Component state: {component}"
        record = logger.makeRecord(
            logger.name,
            LogLevel.DEBUG.value,
            __file__,
            0,
            message,
            (),
            None,
            func=None,
            extra={
                'actor_id': actor.actor_id,
                'correlation_id': correlation_id,
                'context': context or {},
                'entry_type': LogEntryType.COMPONENT_STATE,
                'component': component,
                'state': state
            }
        )

        # Log the record
        logger.handle(record)

        # Add the record to the aggregator
        await self.aggregator.add_record(record, LogEntryType.COMPONENT_STATE)


# Global logging manager instance
_logging_manager: Optional[LoggingManager] = None


def get_logging_manager() -> LoggingManager:
    """
    Get the global logging manager instance.

    Returns:
        Global logging manager instance
    """
    global _logging_manager
    if _logging_manager is None:
        _logging_manager = LoggingManager()
    return _logging_manager


def initialize_logging(log_dir: Optional[Union[str, Path]] = None, console_level: LogLevel = LogLevel.INFO,
                      file_level: LogLevel = LogLevel.DEBUG, json_format: bool = True) -> None:
    """
    Initialize the logging system.

    Args:
        log_dir: Optional directory for log files
        console_level: Log level for console output
        file_level: Log level for file output
        json_format: Whether to use JSON format for logs
    """
    manager = get_logging_manager()
    manager.initialize(log_dir, console_level, file_level, json_format)


def get_actor_logger(actor: Union['Actor', str]) -> StructuredLogger:
    """
    Get a logger for an actor.

    Args:
        actor: Actor object or actor_id string to get a logger for

    Returns:
        Structured logger for the actor
    """
    manager = get_logging_manager()

    # Handle the case where actor is a string (actor_id)
    if isinstance(actor, str):
        logger_name = f"vibe_check_actor_system.actor.{actor}"
        return get_structured_logger(logger_name, actor)

    # Otherwise, use the manager's method for Actor objects
    return manager.get_actor_logger(actor)


async def create_actor_correlation_context(actor: 'Actor', parent_id: Optional[str] = None,
                                         metadata: Optional[Dict[str, Any]] = None) -> CorrelationContext:
    """
    Create a correlation context for an actor.

    Args:
        actor: Actor to create a correlation context for
        parent_id: Optional ID of the parent operation
        metadata: Optional metadata for the context

    Returns:
        Correlation context
    """
    manager = get_logging_manager()
    return await manager.create_correlation_context(actor, parent_id, metadata)
