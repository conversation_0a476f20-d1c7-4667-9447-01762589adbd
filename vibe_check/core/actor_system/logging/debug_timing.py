"""
Debug Timing Module
==================

This module provides timing and performance tracking functionality for the actor
initialization process, including step timing, operation timing, and performance metrics.
"""

import time
from contextlib import contextmanager
from typing import Any, Dict, Generator, List, Optional

from .debug_core import InitializationStep, log_init_event, is_init_debugging_enabled

# Initialization step timing data
# Structure: {actor_id: {step_name: {start_time, end_time, duration, sub_operations: [{name, start, end, duration}]}}}
_INIT_TIMING_DATA: Dict[str, Dict[str, Dict[str, Any]]] = {}

# Detailed operation timing within steps
_OPERATION_TIMING_DATA: Dict[str, List[Dict[str, Any]]] = {}

# Current operation being timed (for nested operations)
_CURRENT_OPERATIONS: Dict[str, List[Dict[str, Any]]] = {}

# Performance metrics
# Structure: {actor_id: {metric_name: value}}
_PERFORMANCE_METRICS: Dict[str, Dict[str, Any]] = {}

# Critical path data
# Structure: {actor_id: {path: [step_names], duration: float, bottlenecks: [step_names]}}
_CRITICAL_PATH_DATA: Dict[str, Dict[str, Any]] = {}


@contextmanager
def init_step_timing(
    actor_id: str,
    step: InitializationStep,
    details: Optional[Dict[str, Any]] = None
) -> Generator[Dict[str, Any], None, None]:
    """
    Context manager for timing initialization steps.

    Args:
        actor_id: ID of the actor
        step: Initialization step
        details: Optional details about the step

    Yields:
        Dictionary for storing additional context during the step
    """
    if not is_init_debugging_enabled():
        empty_context: Dict[str, Any] = {}
        yield empty_context
        return

    # Create context data dictionary for storing additional information during the step
    context_data: Dict[str, Any] = {
        "actor_id": actor_id,
        "step": step,
        "start_time": time.time(),
        "sub_operations": [],
        "dependencies": [],
        "metrics": {},
        "details": details or {}
    }

    # Log the start of the step
    step_details = {
        "start_time": context_data["start_time"],
        "category": step.category.name
    }
    if details:
        step_details.update(details)

    log_init_event(
        actor_id=actor_id,
        step=step,
        message=f"Starting {step.name} step",
        details=step_details
    )

    try:
        # Yield the context data for the caller to use
        yield context_data
    finally:
        # Calculate timing information
        end_time = time.time()
        start_time = float(context_data["start_time"])
        duration = end_time - start_time

        # Update context data
        context_data["end_time"] = end_time
        context_data["duration"] = duration

        # Store timing data
        if actor_id not in _INIT_TIMING_DATA:
            _INIT_TIMING_DATA[actor_id] = {}

        # Get typed references to collections
        sub_operations = context_data["sub_operations"]
        dependencies = context_data["dependencies"]
        metrics = context_data["metrics"]
        step_details = context_data["details"]

        # Create or update the step timing data
        if step.name not in _INIT_TIMING_DATA[actor_id]:
            _INIT_TIMING_DATA[actor_id][step.name] = {
                "start_time": start_time,
                "end_time": end_time,
                "duration": duration,
                "sub_operations": sub_operations,
                "dependencies": dependencies,
                "metrics": metrics,
                "category": step.category.name,
                "details": step_details
            }
        else:
            # Update existing timing data
            _INIT_TIMING_DATA[actor_id][step.name]["end_time"] = end_time
            _INIT_TIMING_DATA[actor_id][step.name]["duration"] = duration
            _INIT_TIMING_DATA[actor_id][step.name]["sub_operations"] = sub_operations
            _INIT_TIMING_DATA[actor_id][step.name]["dependencies"] = dependencies
            _INIT_TIMING_DATA[actor_id][step.name]["metrics"] = metrics
            _INIT_TIMING_DATA[actor_id][step.name]["details"].update(step_details)

        # Log timing information
        log_details = {
            "duration": duration,
            "start_time": start_time,
            "end_time": end_time,
            "category": step.category.name,
            "sub_operations_count": len(sub_operations),
            "dependencies_count": len(dependencies),
            "metrics_count": len(metrics)
        }
        if step_details:
            log_details.update(step_details)

        log_init_event(
            actor_id=actor_id,
            step=step,
            message=f"Step {step.name} completed in {duration:.4f} seconds",
            details=log_details
        )


@contextmanager
def time_operation(
    actor_id: str,
    operation_name: str,
    details: Optional[Dict[str, Any]] = None,
    step_context: Optional[Dict[str, Any]] = None,
    category: Optional[str] = None,
    parent_operation: Optional[str] = None
) -> Generator[Dict[str, Any], None, None]:
    """
    Context manager for timing operations within initialization steps.

    Args:
        actor_id: ID of the actor
        operation_name: Name of the operation
        details: Optional details about the operation
        step_context: Optional step context to add the operation to
        category: Optional category for the operation
        parent_operation: Optional parent operation name for nested operations

    Yields:
        Dictionary for storing additional context during the operation
    """
    if not is_init_debugging_enabled():
        empty_context: Dict[str, Any] = {}
        yield empty_context
        return

    # Create operation context
    operation_context: Dict[str, Any] = {
        "actor_id": actor_id,
        "operation_name": operation_name,
        "start_time": time.time(),
        "category": category,
        "parent_operation": parent_operation,
        "details": details or {},
        "sub_operations": [],
        "metrics": {}
    }

    # Initialize current operations tracking for this actor if needed
    if actor_id not in _CURRENT_OPERATIONS:
        _CURRENT_OPERATIONS[actor_id] = []

    # Add this operation to the current operations stack
    _CURRENT_OPERATIONS[actor_id].append(operation_context)

    try:
        yield operation_context
    finally:
        # Calculate timing
        end_time = time.time()
        start_time = float(operation_context["start_time"])
        duration = end_time - start_time

        # Update operation context
        operation_context["end_time"] = end_time
        operation_context["duration"] = duration

        # Remove from current operations stack
        if actor_id in _CURRENT_OPERATIONS and _CURRENT_OPERATIONS[actor_id]:
            _CURRENT_OPERATIONS[actor_id].pop()

        # Store operation timing data
        if actor_id not in _OPERATION_TIMING_DATA:
            _OPERATION_TIMING_DATA[actor_id] = []

        _OPERATION_TIMING_DATA[actor_id].append({
            "operation_name": operation_name,
            "start_time": start_time,
            "end_time": end_time,
            "duration": duration,
            "category": category,
            "parent_operation": parent_operation,
            "details": details or {},
            "sub_operations": operation_context["sub_operations"],
            "metrics": operation_context["metrics"]
        })

        # Add to step context if provided
        if step_context is not None and "sub_operations" in step_context:
            step_context["sub_operations"].append({
                "name": operation_name,
                "start_time": start_time,
                "end_time": end_time,
                "duration": duration,
                "category": category,
                "details": details or {}
            })

        # Log operation completion
        log_details = {
            "duration": duration,
            "start_time": start_time,
            "end_time": end_time,
            "category": category or "unknown",
            "parent_operation": parent_operation
        }
        if details:
            log_details.update(details)

        log_init_event(
            actor_id=actor_id,
            step=InitializationStep.INITIALIZE,  # Default step for operations
            message=f"Operation '{operation_name}' completed in {duration:.4f} seconds",
            details=log_details
        )


def record_performance_metric(
    actor_id: str,
    metric_name: str,
    value: Any,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Record a performance metric for an actor.

    Args:
        actor_id: ID of the actor
        metric_name: Name of the metric
        value: Value of the metric
        details: Optional details about the metric
    """
    if not is_init_debugging_enabled():
        return

    if actor_id not in _PERFORMANCE_METRICS:
        _PERFORMANCE_METRICS[actor_id] = {}

    _PERFORMANCE_METRICS[actor_id][metric_name] = {
        "value": value,
        "timestamp": time.time(),
        "details": details or {}
    }


def get_init_timing_data() -> Dict[str, Dict[str, Dict[str, Any]]]:
    """
    Get initialization timing data.

    Returns:
        Dictionary mapping actor IDs to dictionaries mapping step names to timing data
    """
    return _INIT_TIMING_DATA


def get_operation_timing_data() -> Dict[str, List[Dict[str, Any]]]:
    """
    Get operation timing data.

    Returns:
        Dictionary mapping actor IDs to lists of operation timing data
    """
    return _OPERATION_TIMING_DATA


def get_performance_metrics() -> Dict[str, Dict[str, Any]]:
    """
    Get performance metrics.

    Returns:
        Dictionary mapping actor IDs to performance metrics
    """
    return _PERFORMANCE_METRICS


def get_critical_path_data() -> Dict[str, Dict[str, Any]]:
    """
    Get critical path data for initialization.

    Returns:
        Dictionary mapping actor IDs to critical path data
    """
    return _CRITICAL_PATH_DATA


def clear_timing_data() -> None:
    """Clear all timing and performance data."""
    global _INIT_TIMING_DATA, _OPERATION_TIMING_DATA, _PERFORMANCE_METRICS, _CRITICAL_PATH_DATA
    _INIT_TIMING_DATA = {}
    _OPERATION_TIMING_DATA = {}
    _PERFORMANCE_METRICS = {}
    _CRITICAL_PATH_DATA = {}
