"""
Registry Debug Module
====================

This module provides utilities for debugging the actor registry, including:
- Registry operation logging
- Registry state tracking
- Registry consistency checking
- Registry operation correlation

These utilities help diagnose and resolve issues with the actor registry
and its synchronization with the initialization manager.
"""

import logging
import time
import traceback
from datetime import datetime
from enum import Enum, auto
from typing import Any, Dict, List, Optional, Set, Tuple, Union, cast

# Configure logging
_registry_debug_logger = logging.getLogger("vibe_check_actor_system.registry_debug")

# Debug state
_REGISTRY_DEBUG_ENABLED = False
_REGISTRY_DEBUG_LOG_BUFFER: List[Dict[str, Any]] = []
_REGISTRY_OPERATIONS: List[Dict[str, Any]] = []
_REGISTRY_STATES: List[Dict[str, Any]] = []
_REGISTRY_INCONSISTENCIES: List[Dict[str, Any]] = []
_REGISTRY_LOOKUP_RETRIES: Dict[str, List[Dict[str, Any]]] = {}
_REGISTRY_CORRELATION_MAP: Dict[str, List[str]] = {}
_CURRENT_CORRELATION_ID: Optional[str] = None


class RegistryOperationType(Enum):
    """Enumeration of registry operation types for detailed tracking."""
    REGISTER = auto()           # Register an actor
    UNREGISTER = auto()         # Unregister an actor
    LOOKUP = auto()             # Look up an actor
    LOOKUP_RETRY = auto()       # Retry a lookup operation
    LOOKUP_FAILED = auto()      # Lookup operation failed
    LOOKUP_SUCCEEDED = auto()   # Lookup operation succeeded
    GET_BY_TYPE = auto()        # Get actors by type
    GET_BY_TAG = auto()         # Get actors by tag
    GET_BY_CAPABILITY = auto()  # Get actors by capability
    ADD_TAG = auto()            # Add a tag to an actor
    REMOVE_TAG = auto()         # Remove a tag from an actor
    ADD_CAPABILITY = auto()     # Add a capability to an actor
    REMOVE_CAPABILITY = auto()  # Remove a capability from an actor
    CONSISTENCY_CHECK = auto()  # Check registry consistency
    INCONSISTENCY_DETECTED = auto()  # Inconsistency detected
    INCONSISTENCY_RESOLVED = auto()  # Inconsistency resolved
    STATE_SNAPSHOT = auto()     # Take a snapshot of registry state


def enable_registry_debugging(log_level: int = logging.DEBUG, log_file: Optional[str] = None) -> None:
    """
    Enable registry debugging.
    
    Args:
        log_level: Log level to use
        log_file: Optional file to write logs to
    """
    global _REGISTRY_DEBUG_ENABLED
    
    if _REGISTRY_DEBUG_ENABLED:
        _registry_debug_logger.debug("Registry debugging already enabled")
        return
    
    _REGISTRY_DEBUG_ENABLED = True
    
    # Configure logger
    _registry_debug_logger.setLevel(log_level)
    
    # Add console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [REGISTRY] %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    _registry_debug_logger.addHandler(console_handler)
    
    # Add file handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(log_level)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [REGISTRY] %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        _registry_debug_logger.addHandler(file_handler)
    
    _registry_debug_logger.info("Registry debugging enabled")


def disable_registry_debugging() -> None:
    """Disable registry debugging."""
    global _REGISTRY_DEBUG_ENABLED
    
    if not _REGISTRY_DEBUG_ENABLED:
        return
    
    _REGISTRY_DEBUG_ENABLED = False
    
    # Remove handlers from the logger
    for handler in _registry_debug_logger.handlers[:]:
        _registry_debug_logger.removeHandler(handler)
    
    _registry_debug_logger.info("Registry debugging disabled")


def is_registry_debugging_enabled() -> bool:
    """
    Check if registry debugging is enabled.
    
    Returns:
        True if registry debugging is enabled, False otherwise
    """
    return _REGISTRY_DEBUG_ENABLED


def start_correlation(correlation_id: str) -> None:
    """
    Start a correlation context for registry operations.
    
    This allows tracking related operations across components.
    
    Args:
        correlation_id: Unique identifier for the correlation
    """
    global _CURRENT_CORRELATION_ID
    
    if not _REGISTRY_DEBUG_ENABLED:
        return
    
    _CURRENT_CORRELATION_ID = correlation_id
    
    if correlation_id not in _REGISTRY_CORRELATION_MAP:
        _REGISTRY_CORRELATION_MAP[correlation_id] = []
    
    _registry_debug_logger.debug(f"Started correlation context: {correlation_id}")


def end_correlation() -> None:
    """End the current correlation context."""
    global _CURRENT_CORRELATION_ID
    
    if not _REGISTRY_DEBUG_ENABLED or _CURRENT_CORRELATION_ID is None:
        return
    
    _registry_debug_logger.debug(f"Ended correlation context: {_CURRENT_CORRELATION_ID}")
    _CURRENT_CORRELATION_ID = None


def log_registry_operation(
    operation_type: RegistryOperationType,
    actor_id: Optional[str] = None,
    message: str = "",
    details: Optional[Dict[str, Any]] = None,
    error: Optional[Exception] = None,
    stack_trace: bool = False,
    correlation_id: Optional[str] = None
) -> str:
    """
    Log a registry operation.
    
    Args:
        operation_type: Type of registry operation
        actor_id: Optional ID of the actor involved
        message: Log message
        details: Optional details to include in the log
        error: Optional error to include in the log
        stack_trace: Whether to include a stack trace in the log
        correlation_id: Optional correlation ID to associate with this operation
    
    Returns:
        Operation ID
    """
    if not _REGISTRY_DEBUG_ENABLED:
        return ""
    
    # Generate an operation ID
    operation_id = f"registry_op_{int(time.time() * 1000)}_{len(_REGISTRY_OPERATIONS)}"
    
    # Use the current correlation ID if none is provided
    if correlation_id is None and _CURRENT_CORRELATION_ID is not None:
        correlation_id = _CURRENT_CORRELATION_ID
    
    # Create the operation entry
    operation_entry = {
        "operation_id": operation_id,
        "timestamp": datetime.now().isoformat(),
        "operation_type": operation_type.name,
        "actor_id": actor_id,
        "message": message,
        "details": details or {},
        "correlation_id": correlation_id
    }
    
    # Add error information if provided
    if error:
        operation_entry["error"] = str(error)
        operation_entry["error_type"] = type(error).__name__
        
        # Add stack trace if requested
        if stack_trace:
            operation_entry["stack_trace"] = traceback.format_exc()
    
    # Add the operation to the buffer
    _REGISTRY_OPERATIONS.append(operation_entry)
    
    # Add to correlation map if correlation ID is provided
    if correlation_id:
        if correlation_id not in _REGISTRY_CORRELATION_MAP:
            _REGISTRY_CORRELATION_MAP[correlation_id] = []
        
        _REGISTRY_CORRELATION_MAP[correlation_id].append(operation_id)
    
    # Log the operation using the standard logging mechanism
    log_message = f"[{operation_type.name}]"
    if actor_id:
        log_message += f" Actor: {actor_id}"
    if message:
        log_message += f" - {message}"
    if details:
        log_message += f" - Details: {details}"
    
    if error:
        _registry_debug_logger.error(log_message, exc_info=error if stack_trace else None)
    else:
        _registry_debug_logger.debug(log_message)
    
    return operation_id


def log_registry_state(
    registry_state: Dict[str, Any],
    message: str = "Registry state snapshot",
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log the current state of the registry.
    
    Args:
        registry_state: Current state of the registry
        message: Log message
        details: Optional details to include in the log
    """
    if not _REGISTRY_DEBUG_ENABLED:
        return
    
    # Create the state entry
    state_entry = {
        "timestamp": datetime.now().isoformat(),
        "message": message,
        "details": details or {},
        "state": registry_state
    }
    
    # Add the state to the buffer
    _REGISTRY_STATES.append(state_entry)
    
    # Log the operation
    operation_id = log_registry_operation(
        operation_type=RegistryOperationType.STATE_SNAPSHOT,
        message=message,
        details=details
    )
    
    # Add the state entry ID to the operation details
    for op in _REGISTRY_OPERATIONS:
        if op.get("operation_id") == operation_id:
            op["details"]["state_entry_index"] = len(_REGISTRY_STATES) - 1
            break
    
    _registry_debug_logger.debug(f"Registry state snapshot taken: {message}")


def log_registry_inconsistency(
    inconsistency_type: str,
    message: str,
    details: Optional[Dict[str, Any]] = None,
    actor_id: Optional[str] = None,
    resolution_suggestion: Optional[str] = None
) -> None:
    """
    Log a detected inconsistency in the registry.
    
    Args:
        inconsistency_type: Type of inconsistency
        message: Description of the inconsistency
        details: Optional details about the inconsistency
        actor_id: Optional ID of the actor involved
        resolution_suggestion: Optional suggestion for resolving the inconsistency
    """
    if not _REGISTRY_DEBUG_ENABLED:
        return
    
    # Create the inconsistency entry
    inconsistency_entry = {
        "timestamp": datetime.now().isoformat(),
        "inconsistency_type": inconsistency_type,
        "message": message,
        "details": details or {},
        "actor_id": actor_id,
        "resolution_suggestion": resolution_suggestion,
        "resolved": False,
        "resolution_timestamp": None,
        "resolution_details": None
    }
    
    # Add the inconsistency to the buffer
    _REGISTRY_INCONSISTENCIES.append(inconsistency_entry)
    
    # Log the operation
    log_registry_operation(
        operation_type=RegistryOperationType.INCONSISTENCY_DETECTED,
        actor_id=actor_id,
        message=message,
        details={
            "inconsistency_type": inconsistency_type,
            "inconsistency_index": len(_REGISTRY_INCONSISTENCIES) - 1,
            "resolution_suggestion": resolution_suggestion,
            **(details or {})
        }
    )
    
    _registry_debug_logger.warning(f"Registry inconsistency detected: {message}")


def mark_inconsistency_resolved(
    inconsistency_index: int,
    resolution_details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Mark an inconsistency as resolved.
    
    Args:
        inconsistency_index: Index of the inconsistency in the buffer
        resolution_details: Optional details about the resolution
    """
    if not _REGISTRY_DEBUG_ENABLED or inconsistency_index >= len(_REGISTRY_INCONSISTENCIES):
        return
    
    # Update the inconsistency entry
    inconsistency = _REGISTRY_INCONSISTENCIES[inconsistency_index]
    inconsistency["resolved"] = True
    inconsistency["resolution_timestamp"] = datetime.now().isoformat()
    inconsistency["resolution_details"] = resolution_details or {}
    
    # Log the operation
    log_registry_operation(
        operation_type=RegistryOperationType.INCONSISTENCY_RESOLVED,
        actor_id=inconsistency.get("actor_id"),
        message=f"Resolved inconsistency: {inconsistency['message']}",
        details={
            "inconsistency_type": inconsistency["inconsistency_type"],
            "inconsistency_index": inconsistency_index,
            "resolution_details": resolution_details
        }
    )
    
    _registry_debug_logger.info(f"Registry inconsistency resolved: {inconsistency['message']}")


def log_lookup_retry(
    actor_id: str,
    attempt: int,
    reason: str,
    details: Optional[Dict[str, Any]] = None,
    error: Optional[Exception] = None
) -> None:
    """
    Log a retry attempt for actor lookup.
    
    Args:
        actor_id: ID of the actor being looked up
        attempt: Retry attempt number
        reason: Reason for the retry
        details: Optional details about the retry
        error: Optional error that triggered the retry
    """
    if not _REGISTRY_DEBUG_ENABLED:
        return
    
    # Create the retry entry
    retry_entry = {
        "timestamp": datetime.now().isoformat(),
        "actor_id": actor_id,
        "attempt": attempt,
        "reason": reason,
        "details": details or {},
        "success": None  # Will be updated when the retry completes
    }
    
    # Add error information if provided
    if error:
        retry_entry["error"] = str(error)
        retry_entry["error_type"] = type(error).__name__
    
    # Initialize the actor's retry list if needed
    if actor_id not in _REGISTRY_LOOKUP_RETRIES:
        _REGISTRY_LOOKUP_RETRIES[actor_id] = []
    
    # Add the retry to the buffer
    _REGISTRY_LOOKUP_RETRIES[actor_id].append(retry_entry)
    
    # Log the operation
    log_registry_operation(
        operation_type=RegistryOperationType.LOOKUP_RETRY,
        actor_id=actor_id,
        message=f"Retry attempt {attempt} for actor lookup: {reason}",
        details={
            "attempt": attempt,
            "reason": reason,
            "retry_index": len(_REGISTRY_LOOKUP_RETRIES[actor_id]) - 1,
            **(details or {})
        },
        error=error
    )
    
    _registry_debug_logger.debug(f"Retry attempt {attempt} for actor {actor_id} lookup: {reason}")


def update_lookup_retry_result(
    actor_id: str,
    retry_index: int,
    success: bool,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Update the result of a lookup retry attempt.
    
    Args:
        actor_id: ID of the actor being looked up
        retry_index: Index of the retry in the actor's retry list
        success: Whether the retry was successful
        details: Optional details about the result
    """
    if not _REGISTRY_DEBUG_ENABLED or actor_id not in _REGISTRY_LOOKUP_RETRIES or retry_index >= len(_REGISTRY_LOOKUP_RETRIES[actor_id]):
        return
    
    # Update the retry entry
    retry = _REGISTRY_LOOKUP_RETRIES[actor_id][retry_index]
    retry["success"] = success
    retry["result_timestamp"] = datetime.now().isoformat()
    retry["result_details"] = details or {}
    
    # Log the operation
    operation_type = RegistryOperationType.LOOKUP_SUCCEEDED if success else RegistryOperationType.LOOKUP_FAILED
    log_registry_operation(
        operation_type=operation_type,
        actor_id=actor_id,
        message=f"Lookup retry {'succeeded' if success else 'failed'} for attempt {retry['attempt']}",
        details={
            "attempt": retry["attempt"],
            "retry_index": retry_index,
            **(details or {})
        }
    )
    
    _registry_debug_logger.debug(f"Lookup retry for actor {actor_id} {'succeeded' if success else 'failed'} for attempt {retry['attempt']}")


def get_registry_operations() -> List[Dict[str, Any]]:
    """
    Get all registry operations.
    
    Returns:
        List of registry operations
    """
    return _REGISTRY_OPERATIONS


def get_registry_states() -> List[Dict[str, Any]]:
    """
    Get all registry state snapshots.
    
    Returns:
        List of registry state snapshots
    """
    return _REGISTRY_STATES


def get_registry_inconsistencies() -> List[Dict[str, Any]]:
    """
    Get all detected registry inconsistencies.
    
    Returns:
        List of registry inconsistencies
    """
    return _REGISTRY_INCONSISTENCIES


def get_registry_lookup_retries(actor_id: Optional[str] = None) -> Union[Dict[str, List[Dict[str, Any]]], List[Dict[str, Any]]]:
    """
    Get registry lookup retries.
    
    Args:
        actor_id: Optional ID of the actor to get retries for
        
    Returns:
        Dictionary mapping actor IDs to lists of retry entries, or a list of retry entries for the specified actor
    """
    if actor_id:
        return _REGISTRY_LOOKUP_RETRIES.get(actor_id, [])
    return _REGISTRY_LOOKUP_RETRIES


def get_correlated_operations(correlation_id: str) -> List[Dict[str, Any]]:
    """
    Get operations associated with a correlation ID.
    
    Args:
        correlation_id: Correlation ID to get operations for
        
    Returns:
        List of operations associated with the correlation ID
    """
    if correlation_id not in _REGISTRY_CORRELATION_MAP:
        return []
    
    operation_ids = _REGISTRY_CORRELATION_MAP[correlation_id]
    return [op for op in _REGISTRY_OPERATIONS if op.get("operation_id") in operation_ids]
