"""
Actor Pool Module
===============

This module provides a pool of actors for handling large workloads.
It implements the CAW principle of adaptive resource allocation by
dynamically scaling the number of actors based on workload.
"""

import asyncio
import logging
import time
import uuid
from typing import Any, Callable, Dict, List, Optional, Set, Type, Union

from .actor import Actor
from .context_wave import ContextWave
from .message import Message, MessageType

logger = logging.getLogger("vibe_check_actor_pool")


class ActorPool:
    """
    Pool of actors for handling large workloads.

    Implements the CAW principle of adaptive resource allocation by
    dynamically scaling the number of actors based on workload.
    """

    def __init__(self,
                pool_id: str,
                actor_factory: Callable[..., Actor],
                min_size: int = 1,
                max_size: int = 10,
                actor_args: Optional[Dict[str, Any]] = None,
                actor_kwargs: Optional[Dict[str, Any]] = None):
        """
        Initialize the actor pool.

        Args:
            pool_id: Unique ID for this pool
            actor_factory: Function that creates new actors
            min_size: Minimum number of actors in the pool
            max_size: Maximum number of actors in the pool
            actor_args: Positional arguments to pass to actor_factory
            actor_kwargs: Keyword arguments to pass to actor_factory
        """
        self.pool_id = pool_id
        self.actor_factory = actor_factory
        self.min_size = min_size
        self.max_size = max_size
        self.actor_args = actor_args or {}
        self.actor_kwargs = actor_kwargs or {}

        # Pool state
        self.actors: List[Actor] = []
        self.busy_actors: Set[str] = set()
        self.is_running = False
        self._task_queue = asyncio.Queue()
        self._result_queue = asyncio.Queue()
        self._process_task = None
        self._scaling_task = None
        self._metrics = {
            "tasks_received": 0,
            "tasks_completed": 0,
            "tasks_failed": 0,
            "avg_processing_time": 0.0,
            "total_processing_time": 0.0,
            "current_size": 0,
            "max_size_reached": 0,
            "scale_up_count": 0,
            "scale_down_count": 0
        }

    def get_actors(self) -> Dict[str, Actor]:
        """
        Get all actors in the pool.

        Returns:
            Dictionary of actor IDs to actor instances
        """
        return {actor.actor_id: actor for actor in self.actors}

    async def initialize(self) -> None:
        """
        Initialize the actor pool (first phase of two-phase initialization).

        This method creates the initial actors but doesn't start processing tasks.
        It's part of the two-phase initialization process to ensure all actors are
        properly initialized before they start communicating.

        Enhanced with robust error handling and cleanup on failure.
        """
        logger.info(f"Initializing actor pool {self.pool_id}")

        # Get initializer
        try:
            from .actor_initializer import get_initializer, ActorInitializationError
            initializer = get_initializer()
        except ImportError:
            initializer = None

        # Track successfully initialized actors
        initialized_actors = []

        # Create initial actors
        for i in range(self.min_size):
            actor_id = f"{self.pool_id}_actor_{len(self.actors)}"
            actor = None

            try:
                # Create the actor
                actor = self.actor_factory(actor_id, **self.actor_kwargs)

                # Initialize the actor
                await actor.initialize()

                # Register actor with registry
                try:
                    from .actor_registry import get_registry
                    registry = get_registry()
                    registry.register_actor(
                        actor_id=actor_id,
                        actor=actor,
                        actor_type=getattr(actor, "actor_type", "pool_actor"),
                        tags=getattr(actor, "tags", {"pool_actor"}),
                        capabilities=getattr(actor, "capabilities", {"pool_actor"})
                    )
                    logger.info(f"Registered actor {actor_id} with registry")
                except (ImportError, AttributeError) as e:
                    logger.warning(f"Could not register actor {actor_id} with registry: {e}")

                # Add to pool
                self.actors.append(actor)
                initialized_actors.append(actor)

                # Update metrics
                self._metrics["current_size"] = len(self.actors)

            except Exception as e:
                logger.error(f"Failed to initialize actor {actor_id} for pool {self.pool_id}: {e}")
                import traceback
                logger.error(traceback.format_exc())

                # If fail-fast is enabled, roll back all initialized actors and re-raise
                if initializer and initializer.get_fail_fast():
                    logger.error(f"Fail-fast enabled, rolling back pool initialization")
                    await self._rollback_initialization(initialized_actors)

                    # Re-raise the exception
                    if isinstance(e, ActorInitializationError):
                        raise
                    else:
                        from .actor_initializer import ActorInitializationError
                        raise ActorInitializationError(
                            actor_id=actor_id,
                            phase="pool_initialize",
                            state="initializing",
                            message=f"Failed to initialize actor in pool {self.pool_id}: {str(e)}",
                            original_error=e
                        ) from e

        # Check if we have at least one actor
        if not self.actors:
            error_msg = f"Failed to initialize any actors for pool {self.pool_id}"
            logger.error(error_msg)
            raise RuntimeError(error_msg)

        logger.info(f"Actor pool {self.pool_id} initialized with {len(self.actors)} actors")

    async def _rollback_initialization(self, actors_to_rollback: List['Actor']) -> None:
        """
        Roll back initialization for a list of actors.

        This method is called when initialization fails and we need to clean up
        already initialized actors.

        Args:
            actors_to_rollback: List of actors to roll back
        """
        logger.info(f"Rolling back initialization for {len(actors_to_rollback)} actors in pool {self.pool_id}")

        for actor in actors_to_rollback:
            try:
                # Stop the actor if it's running
                if getattr(actor, 'is_running', False):
                    await actor.stop()

                # Try to unregister from registry
                try:
                    from .actor_registry import get_registry
                    registry = get_registry()
                    registry.unregister_actor(actor.actor_id)
                    logger.info(f"Unregistered actor {actor.actor_id} from registry during rollback")
                except (ImportError, AttributeError, Exception) as e:
                    logger.warning(f"Could not unregister actor {actor.actor_id} from registry during rollback: {e}")

                # Try to unregister from initializer
                try:
                    from .actor_initializer import get_initializer
                    initializer = get_initializer()
                    if initializer:
                        await initializer.unregister_actor(actor.actor_id)
                        logger.info(f"Unregistered actor {actor.actor_id} from initializer during rollback")
                except (ImportError, AttributeError, Exception) as e:
                    logger.warning(f"Could not unregister actor {actor.actor_id} from initializer during rollback: {e}")

            except Exception as e:
                logger.error(f"Error rolling back actor {actor.actor_id}: {e}")
                import traceback
                logger.error(traceback.format_exc())

        # Clear the actors list
        self.actors = []
        self.busy_actors = set()

        logger.info(f"Rolled back initialization for pool {self.pool_id}")

    async def start(self) -> None:
        """
        Start the actor pool (second phase of two-phase initialization).

        This method starts all actors in the pool and begins processing tasks.
        It's part of the two-phase initialization process to ensure all actors are
        properly initialized before they start communicating.

        Enhanced with robust error handling and cleanup on failure.
        """
        # Get initializer
        try:
            from .actor_initializer import get_initializer, ActorInitializationError
            initializer = get_initializer()
        except ImportError:
            initializer = None

        if self.is_running:
            logger.warning(f"Actor pool {self.pool_id} is already running")
            return

        logger.info(f"Starting actor pool {self.pool_id}")

        # Track successfully started actors
        started_actors = []

        try:
            # Start all actors in the pool
            for actor in self.actors:
                try:
                    await actor.start()
                    started_actors.append(actor)
                except Exception as e:
                    logger.error(f"Failed to start actor {actor.actor_id} in pool {self.pool_id}: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

                    # If fail-fast is enabled, roll back all started actors and re-raise
                    if initializer and initializer.get_fail_fast():
                        logger.error("Fail-fast enabled, rolling back pool start")
                        await self._rollback_start(started_actors)

                        # Re-raise the exception
                        if isinstance(e, ActorInitializationError):
                            raise
                        else:
                            from .actor_initializer import ActorInitializationError
                            raise ActorInitializationError(
                                actor_id=actor.actor_id,
                                phase="pool_start",
                                state="starting",
                                message=f"Failed to start actor in pool {self.pool_id}: {str(e)}",
                                original_error=e
                            ) from e

            # Check if we have at least one actor started
            if not started_actors:
                error_msg = f"Failed to start any actors in pool {self.pool_id}"
                logger.error(error_msg)
                raise RuntimeError(error_msg)

            logger.info(f"All actors in pool {self.pool_id} started")

            # Start processing tasks
            self.is_running = True
            self._process_task = asyncio.create_task(self._process_tasks())
            self._scaling_task = asyncio.create_task(self._scale_pool())

            logger.info(f"Actor pool {self.pool_id} started with {len(self.actors)} actors")

        except Exception as e:
            logger.error(f"Error starting actor pool {self.pool_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Roll back started actors
            await self._rollback_start(started_actors)

            # Re-raise the exception
            raise

    async def _rollback_start(self, started_actors: List['Actor']) -> None:
        """
        Roll back start for a list of actors.

        This method is called when start fails and we need to clean up
        already started actors.

        Args:
            started_actors: List of actors to roll back
        """
        logger.info(f"Rolling back start for {len(started_actors)} actors in pool {self.pool_id}")

        for actor in started_actors:
            try:
                await actor.stop()
                logger.info(f"Stopped actor {actor.actor_id} during rollback")
            except Exception as e:
                logger.error(f"Error stopping actor {actor.actor_id} during rollback: {e}")
                import traceback
                logger.error(traceback.format_exc())

        # Cancel any pool tasks
        if self._process_task and not self._process_task.done():
            self._process_task.cancel()
            logger.info("Cancelled process task during rollback")

        if self._scaling_task and not self._scaling_task.done():
            self._scaling_task.cancel()
            logger.info("Cancelled scaling task during rollback")

        # Reset running state
        self.is_running = False

        logger.info(f"Rolled back start for pool {self.pool_id}")

    async def stop(self) -> None:
        """Stop the actor pool."""
        if not self.is_running:
            logger.info(f"Actor pool {self.pool_id} is already stopped")
            return

        logger.info(f"Stopping actor pool {self.pool_id}")

        # Stop accepting new tasks
        self.is_running = False

        # Cancel scaling task
        if self._scaling_task and not self._scaling_task.done():
            self._scaling_task.cancel()
            try:
                await asyncio.wait_for(asyncio.shield(self._scaling_task), timeout=0.5)
            except (asyncio.TimeoutError, asyncio.CancelledError):
                pass
            except Exception as e:
                logger.error(f"Error cancelling scaling task: {e}")

        # Process remaining tasks
        if not self._task_queue.empty():
            logger.info(f"Processing {self._task_queue.qsize()} remaining tasks")
            try:
                # Wait for remaining tasks with a timeout
                await asyncio.wait_for(self._task_queue.join(), timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning("Timeout waiting for tasks to complete")
            except Exception as e:
                logger.error(f"Error processing remaining tasks: {e}")

        # Cancel process task
        if self._process_task and not self._process_task.done():
            self._process_task.cancel()
            try:
                await asyncio.wait_for(asyncio.shield(self._process_task), timeout=0.5)
            except (asyncio.TimeoutError, asyncio.CancelledError):
                pass
            except Exception as e:
                logger.error(f"Error cancelling process task: {e}")

        # Stop all actors
        stop_tasks = []
        for actor in list(self.actors):  # Create a copy of the list to avoid modification during iteration
            try:
                # Unregister from registry
                try:
                    from .actor_registry import get_registry
                    registry = get_registry()
                    registry.unregister_actor(actor.actor_id)
                    logger.info(f"Unregistered actor {actor.actor_id} from registry")
                except (ImportError, AttributeError) as e:
                    logger.warning(f"Could not unregister actor {actor.actor_id} from registry: {e}")

                # Stop the actor
                stop_tasks.append(actor.stop())
            except Exception as e:
                logger.error(f"Error stopping actor {actor.actor_id}: {e}")

        if stop_tasks:
            try:
                # Wait for all actors to stop with a timeout
                await asyncio.wait_for(asyncio.gather(*stop_tasks, return_exceptions=True), timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning("Timeout waiting for actors to stop")
            except Exception as e:
                logger.error(f"Error stopping actors: {e}")

        # Clear actor lists
        self.actors = []
        self.busy_actors = set()

        # Clear any pending futures
        while not self._task_queue.empty():
            try:
                task = self._task_queue.get_nowait()
                if "result_future" in task and not task["result_future"].done():
                    task["result_future"].set_exception(
                        RuntimeError("Task cancelled due to pool shutdown")
                    )
                self._task_queue.task_done()
            except asyncio.QueueEmpty:
                break
            except Exception as e:
                logger.error(f"Error clearing task queue: {e}")

        logger.info(f"Actor pool {self.pool_id} stopped")

    async def submit(self, message_type: MessageType, payload: Dict[str, Any],
                    context: Optional[ContextWave] = None) -> Dict[str, Any]:
        """
        Submit a task to the pool.

        Args:
            message_type: Type of message to send to the actor
            payload: Message payload
            context: Optional context to propagate

        Returns:
            Result of the task

        Raises:
            RuntimeError: If the pool is not running or task times out
        """
        if not self.is_running:
            raise RuntimeError(f"Actor pool {self.pool_id} is not running")

        # Create task ID
        task_id = str(uuid.uuid4())

        # Create a copy of the payload to avoid modification
        payload_copy = payload.copy() if payload else {}

        # Add task ID to payload for tracking
        payload_copy["task_id"] = task_id

        # Create or update context
        if context is None:
            context = ContextWave()
        context.metadata["task_id"] = task_id
        context.metadata["pool_id"] = self.pool_id

        # Create result future
        result_future = asyncio.Future()

        # Create task
        task = {
            "task_id": task_id,
            "message_type": message_type,
            "payload": payload_copy,
            "context": context,
            "result_future": result_future,
            "submit_time": time.time()
        }

        # Add to queue
        await self._task_queue.put(task)

        # Update metrics
        self._metrics["tasks_received"] += 1
        self._metrics["queue_size"] = self._task_queue.qsize()

        logger.debug(f"Task {task_id} submitted to pool {self.pool_id}")

        # Wait for result with timeout
        try:
            # Use a reasonable timeout to avoid hanging forever
            return await asyncio.wait_for(result_future, timeout=30.0)
        except asyncio.TimeoutError as timeout_err:
            logger.error(f"Timeout waiting for result of task {task_id}")
            raise RuntimeError(f"Timeout waiting for result of task {task_id}") from timeout_err
        except Exception as e:
            logger.error(f"Error processing task {task_id}: {e}")
            raise

    async def _process_tasks(self) -> None:
        """Process tasks from the queue."""
        try:
            while self.is_running:
                try:
                    # Get a task from the queue
                    task = await asyncio.wait_for(self._task_queue.get(), timeout=0.1)

                    # Process the task
                    asyncio.create_task(self._process_task_with_actor(task))
                except asyncio.TimeoutError:
                    # No task received, continue loop
                    await asyncio.sleep(0)
                    continue
                except asyncio.CancelledError:
                    # Task was cancelled, exit gracefully
                    logger.info(f"Actor pool {self.pool_id} task processing cancelled")
                    break
                except Exception as e:
                    logger.error(f"Error processing task in pool {self.pool_id}: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
        except asyncio.CancelledError:
            logger.info(f"Actor pool {self.pool_id} task processing cancelled")
        except Exception as e:
            logger.error(f"Fatal error in actor pool {self.pool_id} task processing: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def _process_task_with_actor(self, task: Dict[str, Any]) -> None:
        """
        Process a task with an available actor.

        Args:
            task: Task to process
        """
        task_id = task["task_id"]
        message_type = task["message_type"]
        payload = task["payload"]
        context = task["context"]
        result_future = task["result_future"]
        submit_time = task["submit_time"]

        # Get an available actor
        actor = await self._get_available_actor()

        if actor is None:
            # No actor available, requeue the task
            logger.warning(f"No actor available for task {task_id}, requeuing")
            await self._task_queue.put(task)
            self._task_queue.task_done()
            return

        # Mark actor as busy
        self.busy_actors.add(actor.actor_id)

        try:
            # Create a context with the task ID
            if context is None:
                context = ContextWave()
            context.metadata["task_id"] = task_id
            context.metadata["pool_id"] = self.pool_id
            context.metadata["sender_id"] = self.pool_id  # Set sender ID for reply routing

            # Send the message directly to the actor's handle method
            # This bypasses the message queue and directly calls the handler
            try:
                # Get the handler for this message type
                handler = actor._message_handlers.get(message_type)

                if handler is None:
                    raise ValueError(f"Actor {actor.actor_id} has no handler for message type {message_type}")

                # Call the handler directly and get the result
                result = await handler(payload, context)

                # If the handler doesn't return a result, create a default one
                if result is None:
                    result = {"status": "completed", "actor_id": actor.actor_id}

                # Set the result
                if not result_future.done():
                    result_future.set_result(result)

                # Update metrics
                self._metrics["tasks_completed"] += 1
                processing_time = time.time() - submit_time
                self._metrics["total_processing_time"] += processing_time
                self._metrics["avg_processing_time"] = (
                    self._metrics["total_processing_time"] / self._metrics["tasks_completed"]
                )

                logger.debug(f"Task {task_id} completed in {processing_time:.6f}s")
            except Exception as e:
                # Task failed during execution
                if not result_future.done():
                    result_future.set_exception(e)

                # Update metrics
                self._metrics["tasks_failed"] += 1

                logger.error(f"Error executing task {task_id} with actor {actor.actor_id}: {e}")
                import traceback
                logger.error(traceback.format_exc())
        except Exception as e:
            # Task failed during setup
            if not result_future.done():
                result_future.set_exception(e)

            # Update metrics
            self._metrics["tasks_failed"] += 1

            logger.error(f"Error processing task {task_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
        finally:
            # Mark actor as available
            self.busy_actors.discard(actor.actor_id)

            # Mark task as done
            self._task_queue.task_done()

    async def _get_available_actor(self) -> Optional[Actor]:
        """
        Get an available actor from the pool.

        Returns:
            Available actor or None if no actor is available
        """
        # Check for available actors
        for actor in self.actors:
            if actor.actor_id not in self.busy_actors:
                return actor

        # No available actor, check if we can create a new one
        if len(self.actors) < self.max_size:
            return await self._create_actor()

        # No available actor and can't create a new one
        return None

    async def _create_actor(self) -> Optional[Actor]:
        """
        Create a new actor for the pool.

        Uses the two-phase initialization process to ensure the actor is properly
        initialized before it starts communicating.

        Returns:
            Newly created actor or None if creation failed
        """
        try:
            # Generate actor ID
            actor_id = f"{self.pool_id}_actor_{len(self.actors)}"

            # Create actor
            actor = self.actor_factory(actor_id, **self.actor_kwargs)

            # Initialize the actor (first phase)
            await actor.initialize()

            # Register actor with registry
            try:
                from .actor_registry import get_registry
                registry = get_registry()
                registry.register_actor(
                    actor_id=actor_id,
                    actor=actor,
                    actor_type=getattr(actor, "actor_type", "pool_actor"),
                    tags=getattr(actor, "tags", {"pool_actor"}),
                    capabilities=getattr(actor, "capabilities", {"pool_actor"})
                )
                logger.info(f"Registered actor {actor_id} with registry")
            except (ImportError, AttributeError) as e:
                logger.warning(f"Could not register actor {actor_id} with registry: {e}")

            # Start the actor (second phase)
            await actor.start()

            # Add to pool
            self.actors.append(actor)

            # Update metrics
            self._metrics["current_size"] = len(self.actors)
            self._metrics["max_size_reached"] = max(self._metrics["max_size_reached"], len(self.actors))
            self._metrics["scale_up_count"] += 1

            logger.info(f"Created new actor {actor_id} for pool {self.pool_id}")

            return actor
        except Exception as e:
            logger.error(f"Failed to create actor for pool {self.pool_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    async def _scale_pool(self) -> None:
        """Scale the pool based on workload."""
        try:
            while self.is_running:
                try:
                    # Calculate current load
                    total_actors = len(self.actors)
                    busy_actors = len(self.busy_actors)
                    load = busy_actors / total_actors if total_actors > 0 else 0

                    # Scale up if load is high and we're not at max size
                    if load > 0.8 and total_actors < self.max_size:
                        logger.info(f"Scaling up pool {self.pool_id} from {total_actors} to {total_actors + 1}")
                        await self._create_actor()

                    # Scale down if load is low and we're above min size
                    elif load < 0.2 and total_actors > self.min_size:
                        logger.info(f"Scaling down pool {self.pool_id} from {total_actors} to {total_actors - 1}")

                        # Find an idle actor to remove
                        for actor in self.actors:
                            if actor.actor_id not in self.busy_actors:
                                # Remove from pool
                                self.actors.remove(actor)

                                # Stop the actor
                                await actor.stop()

                                # Update metrics
                                self._metrics["current_size"] = len(self.actors)
                                self._metrics["scale_down_count"] += 1

                                logger.info(f"Removed actor {actor.actor_id} from pool {self.pool_id}")
                                break

                    # Wait before checking again
                    await asyncio.sleep(10.0)
                except Exception as e:
                    logger.error(f"Error scaling pool {self.pool_id}: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    await asyncio.sleep(10.0)
        except asyncio.CancelledError:
            logger.info(f"Actor pool {self.pool_id} scaling task cancelled")
        except Exception as e:
            logger.error(f"Fatal error in actor pool {self.pool_id} scaling task: {e}")
            import traceback
            logger.error(traceback.format_exc())
