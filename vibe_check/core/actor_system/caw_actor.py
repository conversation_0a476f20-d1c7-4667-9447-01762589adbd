"""
CAW Actor Module
==============

This module defines the CAWActor base class for the actor system.
It integrates the new infrastructure components (ActorStateMachine, EventBus, CircuitBreaker)
to provide a more robust and flexible actor implementation following the CAW principles.

The CAWActor class addresses issues with the previous Actor implementation:
- Improper state transitions
- Lack of resilience to failures
- Poor error handling
- Rigid synchronization mechanisms
- Tight coupling between components

It implements the CAW principle of choreographed adaptive actors that communicate
via messages with propagating context.
"""

import asyncio
import json
import logging
import os
import time
import uuid
from typing import Any, Awaitable, Callable, Dict, List, Optional, Set, Tuple, Union, cast

from .actor_base import ActorBase
from .actor_state import ActorState
from .actor_state_machine import ActorStateMachine, StateTransitionContext
from .circuit_breaker import CircuitBreaker, CircuitBreakerOpenError
from .context_wave import ContextWave
from .event_bus import EventBus, EventPriority, get_event_bus
from .message import Message, MessageType

# Import actor methods
from .caw_actor_methods import (
    _process_messages,
    _handle_message,
    _process_message,
    _send_heartbeats,
    _collect_metrics,
    send_message
)

# Import CAW modules
from .caw.state_hooks import CAWStateHookManager
from .caw.initialization import CAWInitializationManager
from .caw.state_persistence import CAWStatePersistenceManager

# These will be added to the CAWActor class

# Import actor initialization types
from .actor_initializer import get_initializer

# Import exceptions
from .exceptions import (
    ActorSystemError,
    ActorInitializationError,
    ActorDependencyError,
    ActorMessageError,
    ActorTimeoutError,
    ActorNotFoundError
)

# Configure logging
logger = logging.getLogger("vibe_check_actor_system")


class CAWActor(ActorBase):
    # Add the imported methods to the class
    _process_messages = _process_messages
    _handle_message = _handle_message
    _process_message = _process_message
    _send_heartbeats = _send_heartbeats
    _collect_metrics = _collect_metrics
    send_message = send_message

    # Implement abstract properties
    @property
    def actor_id(self) -> str:
        """Get the actor's unique identifier."""
        return self._actor_id

    @property
    def actor_type(self) -> str:
        """Get the actor's type."""
        return self._actor_type

    @property
    def tags(self) -> Set[str]:
        """Get the actor's tags."""
        return self._tags

    @property
    def capabilities(self) -> Set[str]:
        """Get the actor's capabilities."""
        return self._capabilities

    @property
    def supervisor_id(self) -> Optional[str]:
        """Get the actor's supervisor ID (if any)."""
        return self._supervisor_id

    @property
    def is_running(self) -> bool:
        """Check if the actor is running."""
        return self._is_running

    # Implement abstract methods
    async def receive(self, message: Message) -> None:
        """
        Receive a message from another actor.

        Args:
            message: The message to receive
        """
        await self.mailbox.put(message)

    async def send(self, recipient_id: str, msg_type: MessageType,
                  payload: Dict[str, Any], context: Optional[ContextWave] = None,
                  priority: int = 0, ttl: Optional[int] = None) -> None:
        """
        Send a message to another actor.

        Args:
            recipient_id: ID of the recipient actor
            msg_type: Type of the message
            payload: Message payload
            context: Optional context to propagate
            priority: Optional priority for the message (0-9, higher is more important)
            ttl: Optional time-to-live in seconds
        """
        # Create a message data dictionary
        message_data = {
            "type": msg_type.name,
            **payload
        }

        # Send the message
        await self.send_message(recipient_id, message_data, context)

    async def subscribe(self, stream_id: str) -> None:
        """
        Subscribe to a stream.

        Args:
            stream_id: ID of the stream to subscribe to
        """
        self._subscriptions.add(stream_id)

        # Publish subscription event
        await self._event_bus.publish_simple(
            event_type=f"actor.subscribe.{self.actor_id}",
            data={
                "actor_id": self.actor_id,
                "stream_id": stream_id,
                "timestamp": time.time()
            },
            source=self.actor_id
        )

    async def unsubscribe(self, stream_id: str) -> None:
        """
        Unsubscribe from a stream.

        Args:
            stream_id: ID of the stream to unsubscribe from
        """
        if stream_id in self._subscriptions:
            self._subscriptions.remove(stream_id)

            # Publish unsubscription event
            await self._event_bus.publish_simple(
                event_type=f"actor.unsubscribe.{self.actor_id}",
                data={
                    "actor_id": self.actor_id,
                    "stream_id": stream_id,
                    "timestamp": time.time()
                },
                source=self.actor_id
            )

    async def publish(self, stream_id: str, message_type: MessageType,
                     payload: Dict[str, Any]) -> None:
        """
        Publish a message to a stream.

        Args:
            stream_id: ID of the stream to publish to
            message_type: Type of the message to publish
            payload: Message payload
        """
        # Publish to the event bus
        await self._event_bus.publish_simple(
            event_type=f"stream.{stream_id}",
            data={
                "type": message_type.name,
                "actor_id": self.actor_id,
                "stream_id": stream_id,
                "payload": payload,
                "timestamp": time.time()
            },
            source=self.actor_id
        )

    async def stop(self) -> None:
        """
        Stop the actor.

        This method stops the actor's message processing loop and other background tasks.
        It also performs cleanup operations such as saving state and unregistering from
        the registry.
        """
        # Set the running flag to False
        self._is_running = False

        # Cancel any running tasks
        for task_name in ['_process_task', '_heartbeat_task', '_metrics_task']:
            task = getattr(self, task_name, None)
            if task and not task.done():
                try:
                    task.cancel()
                    logger.info(f"Cancelled {task_name} for actor {self.actor_id}")
                except Exception as e:
                    logger.error(f"Error cancelling {task_name} for actor {self.actor_id}: {e}")

        # Save state if available
        if self.state_dir:
            try:
                await self._state_persistence_manager.save_state_to_disk()
            except Exception as e:
                logger.error(f"Error saving state for actor {self.actor_id}: {e}")

        # Transition to STOPPING state
        try:
            await self.state_machine.transition_to(
                ActorState.STOPPING,
                phase="stop"
            )
        except Exception as e:
            logger.error(f"Error transitioning to STOPPING state: {e}")

        # Publish stop event
        try:
            await self._state_hook_manager.publish_stop_event()
        except Exception as e:
            logger.error(f"Error publishing stop event: {e}")

        # Transition to STOPPED state
        try:
            await self.state_machine.transition_to(
                ActorState.STOPPED,
                phase="stop"
            )
        except Exception as e:
            logger.error(f"Error transitioning to STOPPED state: {e}")

        # Clean up resources
        await self._initialization_manager.cleanup_resources()
    """
    CAW actor class for the CAW choreography system.

    Implements the CAW principle of adaptive actors that communicate
    via messages with propagating context. Integrates the new infrastructure
    components (ActorStateMachine, EventBus, CircuitBreaker) to provide a
    more robust and flexible actor implementation.

    Features:
    - Formal state machine for lifecycle management
    - Event-based communication for decoupling components
    - Circuit breaker for preventing cascading failures
    - Improved error handling and recovery
    - Enhanced observability and metrics

    Attributes:
        actor_id: Unique ID for this actor
        actor_type: Type of the actor for discovery
        tags: Set of tags for discovery
        capabilities: Set of capabilities for discovery
        supervisor_id: ID of the supervisor actor
        state_dir: Directory for state persistence
        mailbox: Queue for incoming messages
        context_wave: Context wave for the actor
        state_machine: State machine for managing actor lifecycle
        _event_bus: Event bus for communication
        _circuit_breakers: Circuit breakers for failure protection
    """

    def __init__(self, actor_id: str, actor_type: Optional[str] = None,
                tags: Optional[Set[str]] = None, capabilities: Optional[Set[str]] = None,
                supervisor_id: Optional[str] = None, state_dir: Optional[str] = None):
        """
        Initialize the CAW actor.

        Args:
            actor_id: Unique ID for this actor
            actor_type: Optional type of the actor for discovery
            tags: Optional set of tags for discovery
            capabilities: Optional set of capabilities for discovery
            supervisor_id: Optional ID of the supervisor actor
            state_dir: Optional directory for state persistence
        """
        self._actor_id = actor_id
        self._actor_type = actor_type or self.__class__.__name__
        self._tags = tags or set()
        self._capabilities = capabilities or set()
        self._supervisor_id = supervisor_id
        self.state_dir = state_dir

        # Core actor state
        self.mailbox: asyncio.Queue[Message] = asyncio.Queue()
        self.context_wave: ContextWave = ContextWave()
        self._is_running = False
        self._known_actors: Dict[str, 'ActorBase'] = {}
        self._message_handlers: Dict[MessageType, Callable[[Dict[str, Any], ContextWave], Awaitable[Any]]] = {}
        self._process_task: Optional[asyncio.Task[None]] = None
        self._ready_event: asyncio.Event = asyncio.Event()
        self._pending_messages: List[Dict[str, Any]] = []

        # Supervision state
        self._supervised_actors: Set[str] = set()
        self._last_heartbeat: float = time.time()
        self._heartbeat_interval: float = 10.0
        self._heartbeat_task: Optional[asyncio.Task[None]] = None
        self._restart_count: int = 0
        self._max_restarts: int = 3
        self._restart_window: float = 60.0
        self._last_restart_time: float = 0.0

        # Initialization state
        self._initialization_task: Optional[asyncio.Task[None]] = None
        self._initialization_coro: Optional[Awaitable[None]] = None
        self._actor_created_event_coro: Optional[Awaitable[None]] = None

        # Metrics state
        self._metrics: Dict[str, Any] = {
            "messages_received": 0,
            "messages_sent": 0,
            "messages_processed": 0,
            "errors": 0,
            "processing_time": 0.0,
            "avg_processing_time": 0.0,
            "last_activity": time.time(),
            "uptime": 0.0,
            "start_time": time.time(),
            "restarts": 0
        }
        self._metrics_task: Optional[asyncio.Task[None]] = None
        self._metrics_interval: float = 30.0

        # Stream state
        self._subscriptions: Set[str] = set()

        # New infrastructure components
        self.state_machine = ActorStateMachine(actor_id, initial_state=ActorState.CREATED)
        self._event_bus = get_event_bus(keep_history=True)

        # Circuit breakers for different operations
        self._circuit_breakers: Dict[str, CircuitBreaker] = {
            "initialize": CircuitBreaker(
                name=f"{actor_id}_initialize",
                failure_threshold=3,
                reset_timeout=30.0
            ),
            "start": CircuitBreaker(
                name=f"{actor_id}_start",
                failure_threshold=3,
                reset_timeout=30.0
            ),
            "message_processing": CircuitBreaker(
                name=f"{actor_id}_message_processing",
                failure_threshold=5,
                reset_timeout=15.0
            ),
            "send": CircuitBreaker(
                name=f"{actor_id}_send",
                failure_threshold=10,
                reset_timeout=5.0
            ),
            "dependency": CircuitBreaker(
                name=f"{actor_id}_dependency",
                failure_threshold=3,
                reset_timeout=30.0
            )
        }

        # Initialize component managers
        self._state_hook_manager = CAWStateHookManager(self)
        self._initialization_manager = CAWInitializationManager(self)
        self._state_persistence_manager = CAWStatePersistenceManager(self)

        # Initialize handlers
        self._initialization_manager.initialize_handlers()

        # Register with registry if available
        self._initialization_manager.register_with_registry()

        # Register with initializer asynchronously
        # Use create_task only if there's a running event loop
        try:
            self._initialization_task = asyncio.create_task(self._initialization_manager.register_with_initializer())
        except RuntimeError:
            # No running event loop, store the coroutine for later execution
            self._initialization_task = None
            self._initialization_coro = self._initialization_manager.register_with_initializer()

        # Set up state machine hooks
        self._state_hook_manager.setup_state_machine_hooks()

        # Publish actor created event
        try:
            asyncio.create_task(self._state_hook_manager.publish_actor_created_event())
        except RuntimeError:
            # No running event loop, store the coroutine for later execution
            self._actor_created_event_coro = self._state_hook_manager.publish_actor_created_event()

    def _setup_state_machine_hooks(self) -> None:
        """Set up hooks for the actor state machine."""
        # Add state entry hooks
        self.state_machine.add_state_entry_hook(
            ActorState.INITIALIZING,
            lambda ctx: logger.info(f"Actor {self.actor_id} is initializing")
        )
        self.state_machine.add_state_entry_hook(
            ActorState.INITIALIZED,
            lambda ctx: logger.info(f"Actor {self.actor_id} is initialized")
        )
        self.state_machine.add_state_entry_hook(
            ActorState.STARTING,
            lambda ctx: logger.info(f"Actor {self.actor_id} is starting")
        )
        self.state_machine.add_state_entry_hook(
            ActorState.READY,
            lambda ctx: self._ready_event.set()
        )
        self.state_machine.add_state_entry_hook(
            ActorState.FAILED,
            lambda ctx: logger.error(f"Actor {self.actor_id} failed: {ctx.error}")
        )

        # Add state exit hooks
        self.state_machine.add_state_exit_hook(
            ActorState.READY,
            lambda ctx: self._ready_event.clear()
        )

        # Add transition hooks with safe task creation
        def safe_create_task(ctx: StateTransitionContext) -> None:
            try:
                asyncio.create_task(self._publish_state_change_event(ctx))
            except RuntimeError:
                # No running event loop, just log the state change
                logger.info(f"Actor {self.actor_id} state changed from {ctx.old_state.value} to {ctx.new_state.value}")

        # Add hooks for all transitions
        self.state_machine.add_transition_hook(
            ActorState.CREATED,
            ActorState.INITIALIZING,
            safe_create_task
        )
        self.state_machine.add_transition_hook(
            ActorState.INITIALIZING,
            ActorState.INITIALIZED,
            safe_create_task
        )
        self.state_machine.add_transition_hook(
            ActorState.INITIALIZED,
            ActorState.STARTING,
            safe_create_task
        )
        self.state_machine.add_transition_hook(
            ActorState.STARTING,
            ActorState.READY,
            safe_create_task
        )
        self.state_machine.add_transition_hook(
            ActorState.READY,
            ActorState.STOPPING,
            safe_create_task
        )
        self.state_machine.add_transition_hook(
            ActorState.STOPPING,
            ActorState.STOPPED,
            safe_create_task
        )

    async def _publish_state_change_event(self, context: StateTransitionContext) -> None:
        """
        Publish an event for a state transition.

        Args:
            context: The state transition context
        """
        try:
            await self._event_bus.publish_simple(
                event_type=f"actor.state_change.{self.actor_id}",
                data={
                    "actor_id": self.actor_id,
                    "old_state": context.old_state.value,
                    "new_state": context.new_state.value,
                    "phase": context.phase,
                    "timestamp": time.time(),
                    "metadata": context.metadata
                },
                source=self.actor_id
            )
        except Exception as e:
            logger.error(f"Error publishing state change event: {e}")

    async def _register_with_initializer(self) -> None:
        """
        Register this actor with the initializer.

        This method is called during initialization to ensure proper
        synchronization with the initializer.
        """
        try:
            initializer = get_initializer()
            if initializer:
                # Check if already registered to avoid duplicate registration
                if await initializer.is_actor_registered(self.actor_id):
                    logger.info(f"Actor {self.actor_id} is already registered with initializer")
                    return

                # Register with initializer
                await initializer.register_actor(self.actor_id)
                logger.info(f"Actor {self.actor_id} registered with initializer")

                # Register cleanup resources
                initializer.register_resource(
                    self.actor_id,
                    self._cleanup_resources,
                    [],
                    {}
                )

                # Set initial state
                await initializer.set_actor_state(self.actor_id, ActorState.CREATED)
            else:
                logger.warning(f"Could not register actor {self.actor_id} with initializer: initializer not available")
        except Exception as e:
            logger.error(f"Error registering actor {self.actor_id} with initializer: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # Re-raise the exception to ensure the caller knows registration failed
            raise

    def _initialize_handlers(self) -> None:
        """
        Initialize message handlers for this actor.

        This maps message types to handler methods, allowing for dynamic dispatch
        based on the received message type.
        """
        # Find all handler methods in the class
        for attr_name in dir(self):
            if attr_name.startswith("handle_") and callable(getattr(self, attr_name)):
                # Extract the message type from the handler name
                message_type_name = attr_name[len("handle_"):].upper()
                try:
                    # Try to get the MessageType enum value
                    message_type = MessageType[message_type_name]
                    # Register the handler
                    self._message_handlers[message_type] = getattr(self, attr_name)
                except (KeyError, AttributeError):
                    # If the message type doesn't exist, skip it
                    pass

    async def _cleanup_resources(self) -> None:
        """
        Clean up resources associated with this actor.

        This method is called when the actor fails to initialize or is being
        rolled back. It should release any resources that were acquired during
        initialization.
        """
        logger.info(f"Cleaning up resources for actor {self.actor_id}")

        # Cancel any running tasks
        for task_name in ['_process_task', '_heartbeat_task', '_metrics_task']:
            task = getattr(self, task_name, None)
            if task and not task.done():
                try:
                    task.cancel()
                    logger.info(f"Cancelled {task_name} for actor {self.actor_id}")
                except Exception as e:
                    logger.error(f"Error cancelling {task_name} for actor {self.actor_id}: {e}")

        # Clear the mailbox
        try:
            while not self.mailbox.empty():
                try:
                    self.mailbox.get_nowait()
                    self.mailbox.task_done()
                except asyncio.QueueEmpty:
                    break
            logger.info(f"Cleared mailbox for actor {self.actor_id}")
        except Exception as e:
            logger.error(f"Error clearing mailbox for actor {self.actor_id}: {e}")

        # Unregister from registry
        try:
            from .actor_registry import get_registry
            registry = get_registry()
            registry.unregister_actor(self.actor_id)
            logger.info(f"Unregistered actor {self.actor_id} from registry during cleanup")
        except (ImportError, AttributeError, Exception) as e:
            logger.warning(f"Could not unregister actor {self.actor_id} from registry during cleanup: {e}")

        # Reset state flags
        self._is_running = False
        self._ready_event.clear()

        # Publish cleanup event
        try:
            try:
                asyncio.create_task(self._event_bus.publish_simple(
                    event_type=f"actor.cleanup.{self.actor_id}",
                    data={
                        "actor_id": self.actor_id,
                        "timestamp": time.time()
                    },
                    source=self.actor_id
                ))
            except RuntimeError:
                # No running event loop, just log the cleanup
                logger.info(f"Actor {self.actor_id} cleaned up (no event published)")
        except Exception as e:
            logger.error(f"Error publishing cleanup event: {e}")

        logger.info(f"Cleaned up resources for actor {self.actor_id}")

    async def _load_state_from_disk(self) -> None:
        """
        Load actor state from disk.

        This method loads the actor's state from a JSON file in the state directory.
        It's called during initialization to restore the actor's state.
        """
        if not self.state_dir:
            return

        try:
            # Create state file path
            state_file = os.path.join(self.state_dir, f"{self.actor_id}.json")

            # Check if state file exists
            if not os.path.exists(state_file):
                logger.info(f"No state file found for actor {self.actor_id}")
                return

            # Load state from file
            with open(state_file, "r") as f:
                state = json.load(f)

            # Restore state
            self._set_state(state)

            logger.info(f"Actor {self.actor_id} loaded state from {state_file}")
        except Exception as e:
            logger.error(f"Failed to load state for actor {self.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise

    async def _save_state_to_disk(self) -> None:
        """
        Save actor state to disk.

        This method saves the actor's state to a JSON file in the state directory.
        It's called during shutdown to persist the actor's state.
        """
        if not self.state_dir:
            return

        try:
            # Create state directory if it doesn't exist
            os.makedirs(self.state_dir, exist_ok=True)

            # Create state file path
            state_file = os.path.join(self.state_dir, f"{self.actor_id}.json")

            # Get state to save
            state = self._get_state()

            # Save state to file
            with open(state_file, "w") as f:
                json.dump(state, f, indent=2)

            logger.info(f"Actor {self.actor_id} saved state to {state_file}")
        except Exception as e:
            logger.error(f"Failed to save state for actor {self.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise

    def _get_state(self) -> Dict[str, Any]:
        """
        Get actor state for persistence.

        Override this method in subclasses to customize state persistence.

        Returns:
            Dictionary of state to save
        """
        return {
            "actor_id": self._actor_id,
            "actor_type": self._actor_type,
            "tags": list(self._tags),
            "capabilities": list(self._capabilities),
            "metrics": self._metrics,
            "timestamp": time.time()
        }

    def _set_state(self, state: Dict[str, Any]) -> None:
        """
        Set actor state from persistence.

        Override this method in subclasses to customize state restoration.

        Args:
            state: Dictionary of state to restore
        """
        # Restore metrics
        if "metrics" in state:
            self._metrics.update(state["metrics"])

        # Restore tags
        if "tags" in state:
            self._tags = set(state["tags"])

        # Restore capabilities
        if "capabilities" in state:
            self._capabilities = set(state["capabilities"])

    async def initialize(self, config: Optional[Dict[str, Any]] = None,
                       timeout: float = 30.0,
                       dependencies: Optional[List[str]] = None) -> None:
        """
        Initialize the actor (first phase of two-phase initialization).

        This method prepares the actor for starting but doesn't begin processing messages.
        It's part of the two-phase initialization process to ensure all actors are
        properly initialized before they start communicating.

        The method uses a circuit breaker to prevent cascading failures during initialization.

        Args:
            config: Optional configuration dictionary for the actor
            timeout: Maximum time in seconds to wait for initialization to complete
            dependencies: Optional list of actor IDs that this actor depends on

        Raises:
            ActorInitializationError: If initialization fails
            ActorTimeoutError: If initialization times out
            ActorDependencyError: If a dependency fails to initialize
            CircuitBreakerOpenError: If the circuit breaker is open
        """
        # First, ensure we're registered with the initializer
        try:
            # Wait for the initialization task to complete if it's still running
            if self._initialization_task is not None and not self._initialization_task.done():
                try:
                    await self._initialization_task
                    logger.info(f"Actor {self.actor_id} registration task completed")
                except Exception as e:
                    logger.error(f"Error in registration task for actor {self.actor_id}: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    # Re-register if the task failed
                    await self._register_with_initializer()
            # If we have a coroutine but no task (no event loop when created), execute it now
            elif self._initialization_coro is not None:
                try:
                    await self._initialization_coro
                    logger.info(f"Actor {self.actor_id} registration coroutine completed")
                    # Clear the coroutine to avoid awaiting it again
                    self._initialization_coro = None
                except Exception as e:
                    logger.error(f"Error in registration coroutine for actor {self.actor_id}: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    # Re-register if the coroutine failed
                    await self._register_with_initializer()

            # Execute the actor created event coroutine if it exists
            if self._actor_created_event_coro is not None:
                try:
                    await self._actor_created_event_coro
                    logger.info(f"Actor {self.actor_id} created event published")
                    # Clear the coroutine to avoid awaiting it again
                    self._actor_created_event_coro = None
                except Exception as e:
                    logger.error(f"Error publishing actor created event: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

            # Check if we're already registered
            initializer = get_initializer()
            if not initializer:
                logger.warning(f"Could not register actor {self.actor_id} with initializer: initializer not available")
            else:
                # Check if we're already registered
                if not await initializer.is_actor_registered(self.actor_id):
                    # Register with initializer
                    await self._register_with_initializer()
        except Exception as e:
            logger.error(f"Error ensuring actor {self.actor_id} is registered with initializer: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise ActorInitializationError(
                message=f"Failed to register with initializer: {str(e)}",
                actor_id=self.actor_id,
                phase="registration",
                state=ActorState.CREATED,
                original_error=e
            )

        # Get initializer
        initializer = get_initializer()
        if not initializer:
            logger.warning(f"Actor {self.actor_id} initializing without initializer")

        # Get the circuit breaker for initialization
        circuit_breaker = self._circuit_breakers["initialize"]

        # Create a timeout for the entire initialization process
        try:
            # Define the initialization function
            async def do_initialize() -> bool:
                # Transition to INITIALIZING state
                await self.state_machine.transition_to(
                    ActorState.INITIALIZING,
                    phase="initialize",
                    metadata={"timeout": timeout, "dependencies": dependencies}
                )

                # Update initializer state
                if initializer:
                    await initializer.set_actor_state(
                        self.actor_id,
                        ActorState.INITIALIZING,
                        phase="initialize"
                    )

                # Register dependencies if specified
                if dependencies and initializer:
                    for dep_id in dependencies:
                        try:
                            # The register_dependency method is async
                            await initializer.register_dependency(self.actor_id, dep_id)
                            logger.debug(f"Registered dependency {dep_id} for actor {self.actor_id}")
                        except Exception as e:
                            logger.error(f"Error registering dependency {dep_id} for actor {self.actor_id}: {e}")
                            # Continue with other dependencies

                # Wait for dependencies to be initialized if specified
                if dependencies and initializer:
                    logger.info(f"Actor {self.actor_id} waiting for dependencies: {dependencies}")
                    dependency_errors = {}

                    # Wait for all dependencies to be initialized
                    for dep_id in dependencies:
                        try:
                            # Check if dependency is registered
                            if not await initializer.is_actor_registered(dep_id):
                                error_msg = f"Dependency {dep_id} is not registered"
                                logger.error(error_msg)
                                dependency_errors[dep_id] = error_msg
                                continue

                            # Wait for dependency to be initialized
                            state = await initializer.get_actor_state(dep_id)
                            if state == ActorState.FAILED:
                                error_msg = f"Dependency {dep_id} failed to initialize"
                                logger.error(error_msg)
                                dependency_errors[dep_id] = error_msg
                                continue

                            # Wait for the dependency to be at least initialized
                            await initializer.wait_for_actor_initialized(dep_id, timeout)
                            logger.info(f"Dependency {dep_id} is initialized")
                        except Exception as e:
                            error_msg = f"Error waiting for dependency {dep_id}: {str(e)}"
                            logger.error(error_msg)
                            dependency_errors[dep_id] = error_msg

                    # If any dependencies failed, raise an error
                    if dependency_errors:
                        raise ActorDependencyError(
                            message=f"Failed to initialize due to dependency errors",
                            actor_id=self.actor_id,
                            dependencies=dependency_errors
                        )

                # Reset metrics for a clean start
                self._metrics["start_time"] = time.time()
                self._metrics["uptime"] = 0.0
                self._metrics["messages_received"] = 0
                self._metrics["messages_sent"] = 0
                self._metrics["messages_processed"] = 0
                self._metrics["errors"] = 0
                self._metrics["processing_time"] = 0.0
                self._metrics["avg_processing_time"] = 0.0
                self._metrics["last_activity"] = time.time()

                # Load state if available
                if self.state_dir:
                    try:
                        await self._load_state_from_disk()
                    except Exception as state_error:
                        logger.error(f"Error loading state for actor {self.actor_id}: {state_error}")
                        import traceback
                        logger.error(traceback.format_exc())
                        # Continue with initialization despite state loading error

                # Call the actor-specific initialization
                # This is a hook for subclasses to implement their own initialization logic
                if hasattr(self, '_initialize') and callable(getattr(self, '_initialize')):
                    await self._initialize(config or {})

                # Transition to INITIALIZED state
                await self.state_machine.transition_to(
                    ActorState.INITIALIZED,
                    phase="initialize"
                )

                # Update initializer state
                if initializer:
                    await initializer.set_actor_state(
                        self.actor_id,
                        ActorState.INITIALIZED,
                        phase="initialize"
                    )

                logger.info(f"Actor {self.actor_id} initialized successfully")
                return True

            # Execute the initialization with circuit breaker protection
            try:
                # Publish initialization start event
                await self._event_bus.publish_simple(
                    event_type=f"actor.initialize.start.{self.actor_id}",
                    data={
                        "actor_id": self.actor_id,
                        "timeout": timeout,
                        "dependencies": dependencies,
                        "timestamp": time.time()
                    },
                    source=self.actor_id
                )

                # Execute with circuit breaker and timeout
                try:
                    await asyncio.wait_for(
                        circuit_breaker.execute(do_initialize),
                        timeout=timeout
                    )

                    # Publish initialization success event
                    await self._event_bus.publish_simple(
                        event_type=f"actor.initialize.success.{self.actor_id}",
                        data={
                            "actor_id": self.actor_id,
                            "timestamp": time.time()
                        },
                        source=self.actor_id
                    )

                except asyncio.TimeoutError:
                    logger.error(f"Timeout initializing actor {self.actor_id} after {timeout} seconds")

                    # Transition to FAILED state
                    await self.state_machine.transition_to(
                        ActorState.FAILED,
                        phase="initialize",
                        error=ActorTimeoutError(
                            message=f"Initialization timed out after {timeout} seconds",
                            actor_id=self.actor_id,
                            operation="initialize",
                            timeout=timeout
                        )
                    )

                    # Update initializer state
                    if initializer:
                        await initializer.set_actor_state(
                            self.actor_id,
                            ActorState.FAILED,
                            error=ActorTimeoutError(
                                message=f"Initialization timed out after {timeout} seconds",
                                actor_id=self.actor_id,
                                operation="initialize",
                                timeout=timeout
                            ),
                            phase="initialize"
                        )

                    # Publish initialization failure event
                    await self._event_bus.publish_simple(
                        event_type=f"actor.initialize.failure.{self.actor_id}",
                        data={
                            "actor_id": self.actor_id,
                            "error": f"Timeout after {timeout} seconds",
                            "timestamp": time.time()
                        },
                        source=self.actor_id,
                        priority=EventPriority.HIGH
                    )

                    # Clean up resources
                    await self._cleanup_resources()

                    # Raise the timeout exception
                    raise ActorTimeoutError(
                        message=f"Initialization timed out after {timeout} seconds",
                        actor_id=self.actor_id,
                        operation="initialize",
                        timeout=timeout
                    )

            except CircuitBreakerOpenError as e:
                # The circuit breaker is open, too many initialization failures
                logger.error(f"Circuit breaker open for actor initialization: {e}")

                # Transition to FAILED state
                await self.state_machine.transition_to(
                    ActorState.FAILED,
                    phase="initialize",
                    error=e,
                    metadata={"circuit_breaker": "open"}
                )

                # Update initializer state
                if initializer:
                    await initializer.set_actor_state(
                        self.actor_id,
                        ActorState.FAILED,
                        error=e,
                        phase="initialize"
                    )

                # Publish initialization failure event
                await self._event_bus.publish_simple(
                    event_type=f"actor.initialize.failure.{self.actor_id}",
                    data={
                        "actor_id": self.actor_id,
                        "error": str(e),
                        "circuit_breaker_open": True,
                        "timestamp": time.time()
                    },
                    source=self.actor_id,
                    priority=EventPriority.HIGH
                )

                # Clean up resources
                await self._cleanup_resources()

                # Re-raise the exception
                raise

        except (ActorTimeoutError, CircuitBreakerOpenError):
            # These exceptions are already handled above
            raise
        except Exception as e:
            # Handle other exceptions
            logger.error(f"Error initializing actor {self.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Transition to FAILED state
            try:
                await self.state_machine.transition_to(
                    ActorState.FAILED,
                    phase="initialize",
                    error=e
                )
            except Exception as state_error:
                logger.error(f"Error transitioning to FAILED state: {state_error}")

            # Update initializer state
            if initializer:
                try:
                    await initializer.set_actor_state(
                        self.actor_id,
                        ActorState.FAILED,
                        error=e,
                        phase="initialize"
                    )
                except Exception as state_error:
                    logger.error(f"Error updating initializer state: {state_error}")

            # Publish initialization failure event
            try:
                await self._event_bus.publish_simple(
                    event_type=f"actor.initialize.failure.{self.actor_id}",
                    data={
                        "actor_id": self.actor_id,
                        "error": str(e),
                        "timestamp": time.time()
                    },
                    source=self.actor_id,
                    priority=EventPriority.HIGH
                )
            except Exception as event_error:
                logger.error(f"Error publishing initialization failure event: {event_error}")

            # Clean up resources
            try:
                await self._cleanup_resources()
            except Exception as cleanup_error:
                logger.error(f"Error cleaning up resources: {cleanup_error}")

            # Wrap the exception in our custom exception
            if not isinstance(e, ActorSystemError):
                raise ActorInitializationError(
                    message=str(e),
                    actor_id=self.actor_id,
                    phase="initialize",
                    state=ActorState.INITIALIZING,
                    original_error=e
                ) from e
            else:
                raise

    async def check_readiness(self) -> bool:
        """
        Check if the actor is ready to process messages.

        This method is called after the start method completes to verify
        that the actor is fully operational. Subclasses can override this
        method to perform additional readiness checks.

        Returns:
            bool: True if the actor is ready, False otherwise
        """
        # Check if the actor is in the READY state
        current_state = self.state_machine.get_state()
        if current_state != ActorState.READY:
            logger.warning(f"Actor {self.actor_id} is not ready: current state is {current_state.value}")
            return False

        # Check if the actor is running
        if not self._is_running:
            logger.warning(f"Actor {self.actor_id} is not running")
            return False

        # Check if the ready event is set
        if not self._ready_event.is_set():
            logger.warning(f"Actor {self.actor_id} ready event is not set")
            return False

        # Check if the message processing task is running
        if self._process_task is None or self._process_task.done():
            logger.warning(f"Actor {self.actor_id} message processing task is not running")
            return False

        # All checks passed
        return True

    async def start(self, timeout: float = 30.0) -> None:
        """
        Start the actor (second phase of two-phase initialization).

        This method starts the actor's message processing and sets its state to READY.
        It's part of the two-phase initialization process to ensure all actors are
        properly initialized before they start communicating.

        The method uses a circuit breaker to prevent cascading failures during startup.

        Args:
            timeout: Maximum time in seconds to wait for startup to complete

        Raises:
            ActorInitializationError: If startup fails
            ActorTimeoutError: If startup times out
            ActorDependencyError: If a dependency fails to start
            CircuitBreakerOpenError: If the circuit breaker is open
        """
        # Get initializer
        initializer = get_initializer()
        if not initializer:
            logger.warning(f"Actor {self.actor_id} starting without initializer")

        # Get the circuit breaker for startup
        circuit_breaker = self._circuit_breakers["start"]

        # Create a timeout for the entire startup process
        try:
            # Define the startup function
            async def do_start() -> bool:
                # Transition to STARTING state
                await self.state_machine.transition_to(
                    ActorState.STARTING,
                    phase="start",
                    metadata={"timeout": timeout}
                )

                # Update initializer state
                if initializer:
                    await initializer.set_actor_state(
                        self.actor_id,
                        ActorState.STARTING,
                        phase="start"
                    )

                # Wait for initialization synchronization point if using initializer
                if initializer:
                    # Get the initialization_complete sync point
                    # Use the get_sync_points method if available, otherwise try to access _sync_points directly
                    try:
                        if hasattr(initializer, "get_sync_points"):
                            sync_points = initializer.get_sync_points()
                            sync_point = sync_points.get("initialization_complete")
                        else:
                            # Skip waiting for the synchronization point
                            logger.warning(f"Initializer does not have get_sync_points method")
                            sync_point = None

                        if sync_point and not sync_point.is_complete():
                            logger.info(f"Actor {self.actor_id} waiting for initialization synchronization point")
                            if not await sync_point.wait(timeout):
                                logger.error(f"Timeout waiting for initialization synchronization point for actor {self.actor_id}")
                                raise ActorTimeoutError(
                                    message="Timeout waiting for initialization synchronization point",
                                    actor_id=self.actor_id,
                                    operation="start",
                                    timeout=timeout
                                )
                    except AttributeError as e:
                        logger.warning(f"Could not access synchronization points: {e}")
                        # Continue without waiting for the synchronization point

                # Start message processing
                self._is_running = True
                self._process_task = asyncio.create_task(self._process_messages())
                logger.info(f"Actor {self.actor_id} started message processing")

                # Start heartbeat if we have a supervisor
                if self._supervisor_id:
                    self._heartbeat_task = asyncio.create_task(self._send_heartbeats())
                    logger.info(f"Actor {self.actor_id} started heartbeat to supervisor {self._supervisor_id}")

                # Start metrics collection
                self._metrics_task = asyncio.create_task(self._collect_metrics())
                logger.info(f"Actor {self.actor_id} started metrics collection")

                # Call the actor-specific start method
                # This is a hook for subclasses to implement their own startup logic
                if hasattr(self, '_start') and callable(getattr(self, '_start')):
                    await self._start()

                # Process any pending messages
                if self._pending_messages:
                    logger.info(f"Actor {self.actor_id} processing {len(self._pending_messages)} pending messages")
                    for message_data in self._pending_messages:
                        await self._process_message(message_data, self.context_wave)
                    self._pending_messages = []

                # Transition to READY state
                await self.state_machine.transition_to(
                    ActorState.READY,
                    phase="start"
                )

                # Perform readiness check
                if not await self.check_readiness():
                    logger.warning(f"Actor {self.actor_id} failed readiness check after transitioning to READY state")
                    raise ActorInitializationError(
                        message="Actor failed readiness check after transitioning to READY state",
                        actor_id=self.actor_id,
                        phase="start",
                        state=ActorState.READY,
                        original_error=None
                    )

                # Update initializer state
                if initializer:
                    await initializer.set_actor_state(
                        self.actor_id,
                        ActorState.READY,
                        phase="start"
                    )

                logger.info(f"Actor {self.actor_id} started successfully")
                return True

            # Execute the startup with circuit breaker protection
            try:
                # Publish start start event
                await self._event_bus.publish_simple(
                    event_type=f"actor.start.start.{self.actor_id}",
                    data={
                        "actor_id": self.actor_id,
                        "timeout": timeout,
                        "timestamp": time.time()
                    },
                    source=self.actor_id
                )

                # Execute with circuit breaker and timeout
                try:
                    await asyncio.wait_for(
                        circuit_breaker.execute(do_start),
                        timeout=timeout
                    )

                    # Publish start success event
                    await self._event_bus.publish_simple(
                        event_type=f"actor.start.success.{self.actor_id}",
                        data={
                            "actor_id": self.actor_id,
                            "timestamp": time.time()
                        },
                        source=self.actor_id
                    )

                except asyncio.TimeoutError:
                    logger.error(f"Timeout starting actor {self.actor_id} after {timeout} seconds")

                    # Transition to FAILED state
                    await self.state_machine.transition_to(
                        ActorState.FAILED,
                        phase="start",
                        error=ActorTimeoutError(
                            message=f"Startup timed out after {timeout} seconds",
                            actor_id=self.actor_id,
                            operation="start",
                            timeout=timeout
                        )
                    )

                    # Update initializer state
                    if initializer:
                        await initializer.set_actor_state(
                            self.actor_id,
                            ActorState.FAILED,
                            error=ActorTimeoutError(
                                message=f"Startup timed out after {timeout} seconds",
                                actor_id=self.actor_id,
                                operation="start",
                                timeout=timeout
                            ),
                            phase="start"
                        )

                    # Publish start failure event
                    await self._event_bus.publish_simple(
                        event_type=f"actor.start.failure.{self.actor_id}",
                        data={
                            "actor_id": self.actor_id,
                            "error": f"Timeout after {timeout} seconds",
                            "timestamp": time.time()
                        },
                        source=self.actor_id,
                        priority=EventPriority.HIGH
                    )

                    # Clean up resources
                    await self._cleanup_resources()

                    # Raise the timeout exception
                    raise ActorTimeoutError(
                        message=f"Startup timed out after {timeout} seconds",
                        actor_id=self.actor_id,
                        operation="start",
                        timeout=timeout
                    )

            except CircuitBreakerOpenError as e:
                # The circuit breaker is open, too many startup failures
                logger.error(f"Circuit breaker open for actor startup: {e}")

                # Transition to FAILED state
                await self.state_machine.transition_to(
                    ActorState.FAILED,
                    phase="start",
                    error=e,
                    metadata={"circuit_breaker": "open"}
                )

                # Update initializer state
                if initializer:
                    await initializer.set_actor_state(
                        self.actor_id,
                        ActorState.FAILED,
                        error=e,
                        phase="start"
                    )

                # Publish start failure event
                await self._event_bus.publish_simple(
                    event_type=f"actor.start.failure.{self.actor_id}",
                    data={
                        "actor_id": self.actor_id,
                        "error": str(e),
                        "circuit_breaker_open": True,
                        "timestamp": time.time()
                    },
                    source=self.actor_id,
                    priority=EventPriority.HIGH
                )

                # Clean up resources
                await self._cleanup_resources()

                # Re-raise the exception
                raise

        except (ActorTimeoutError, CircuitBreakerOpenError):
            # These exceptions are already handled above
            raise
        except Exception as e:
            # Handle other exceptions
            logger.error(f"Error starting actor {self.actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Transition to FAILED state
            try:
                await self.state_machine.transition_to(
                    ActorState.FAILED,
                    phase="start",
                    error=e
                )
            except Exception as state_error:
                logger.error(f"Error transitioning to FAILED state: {state_error}")

            # Update initializer state
            if initializer:
                try:
                    await initializer.set_actor_state(
                        self.actor_id,
                        ActorState.FAILED,
                        error=e,
                        phase="start"
                    )
                except Exception as state_error:
                    logger.error(f"Error updating initializer state: {state_error}")

            # Publish start failure event
            try:
                await self._event_bus.publish_simple(
                    event_type=f"actor.start.failure.{self.actor_id}",
                    data={
                        "actor_id": self.actor_id,
                        "error": str(e),
                        "timestamp": time.time()
                    },
                    source=self.actor_id,
                    priority=EventPriority.HIGH
                )
            except Exception as event_error:
                logger.error(f"Error publishing start failure event: {event_error}")

            # Clean up resources
            try:
                await self._cleanup_resources()
            except Exception as cleanup_error:
                logger.error(f"Error cleaning up resources: {cleanup_error}")

            # Wrap the exception in our custom exception
            if not isinstance(e, ActorSystemError):
                raise ActorInitializationError(
                    message=str(e),
                    actor_id=self.actor_id,
                    phase="start",
                    state=ActorState.STARTING,
                    original_error=e
                ) from e
            else:
                raise
