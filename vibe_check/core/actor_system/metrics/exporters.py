"""
Metric Exporters Module
==================

This module defines exporters for metrics collected by the metrics system.
Exporters are responsible for formatting and exporting metrics to various
destinations, such as files, console, or external monitoring systems.

The following exporters are supported:
- JsonExporter: Exports metrics to JSON
- PrometheusExporter: Exports metrics in Prometheus format
- ConsoleExporter: Prints metrics to the console
- FileExporter: Writes metrics to a file
"""

import asyncio
import json
import logging
import os
import time
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union, cast

from .types import Counter, Gauge, Histogram, Metric, MetricType, Timer


class MetricsExporter(ABC):
    """
    Base class for all metrics exporters.

    This abstract class defines the interface that all metrics exporters must implement.
    """

    @abstractmethod
    async def export_metrics(self, metrics: Dict[str, Metric]) -> None:
        """
        Export metrics.

        Args:
            metrics: Dictionary of metrics to export
        """
        pass


class JsonExporter(MetricsExporter):
    """
    Exports metrics to JSON.

    This exporter formats metrics as JSON and exports them to a file or returns
    them as a string.
    """

    def __init__(self, output_path: Optional[Union[str, Path]] = None):
        """
        Initialize the JSON exporter.

        Args:
            output_path: Optional path to write JSON to (if None, export_metrics returns the JSON)
        """
        self.output_path = Path(output_path) if output_path else None
        self.logger = logging.getLogger("vibe_check_actor_system.metrics.exporters.json")

    async def export_metrics(self, metrics: Dict[str, Metric]) -> Optional[str]:
        """
        Export metrics as JSON.

        Args:
            metrics: Dictionary of metrics to export

        Returns:
            JSON string if output_path is None, otherwise None
        """
        # Convert metrics to a JSON-serializable format
        metrics_data = self._convert_metrics_to_dict(metrics)

        # Add timestamp
        metrics_data["timestamp"] = time.time()

        # Convert to JSON
        json_data = json.dumps(metrics_data, indent=2)

        # Write to file if output_path is specified
        if self.output_path:
            # Create directory if it doesn't exist
            os.makedirs(self.output_path.parent, exist_ok=True)

            # Write to file
            try:
                with open(self.output_path, "w") as f:
                    f.write(json_data)
                self.logger.info(f"Exported metrics to {self.output_path}")
            except Exception as e:
                self.logger.error(f"Failed to export metrics to {self.output_path}: {e}")
            return None
        else:
            return json_data

    def _convert_metrics_to_dict(self, metrics: Dict[str, Metric]) -> Dict[str, Any]:
        """
        Convert metrics to a JSON-serializable dictionary.

        Args:
            metrics: Dictionary of metrics to convert

        Returns:
            JSON-serializable dictionary
        """
        result: Dict[str, Any] = {
            "metrics": {}
        }

        for name, metric in metrics.items():
            metric_data = {
                "type": metric.get_type().name,
                "description": metric.description,
                "created_at": metric.created_at
            }

            # Add type-specific data
            if metric.get_type() == MetricType.COUNTER:
                counter = cast(Counter, metric)
                metric_data["values"] = counter.get_all_values()
                metric_data["timestamps"] = counter.get_all_timestamps()
            elif metric.get_type() == MetricType.GAUGE:
                gauge = cast(Gauge, metric)
                metric_data["values"] = gauge.get_all_values()
                metric_data["timestamps"] = gauge.get_all_timestamps()
            elif metric.get_type() == MetricType.HISTOGRAM:
                histogram = cast(Histogram, metric)
                metric_data["counts"] = histogram.get_all_counts()
                metric_data["sums"] = histogram.get_all_sums()
                metric_data["buckets"] = histogram.get_buckets()
                metric_data["bucket_counts"] = histogram.get_all_bucket_counts()
                metric_data["timestamps"] = histogram.get_all_timestamps()
            elif metric.get_type() == MetricType.TIMER:
                timer = cast(Timer, metric)
                metric_data["counts"] = timer.get_all_counts()
                metric_data["sums"] = timer.get_all_sums()
                metric_data["buckets"] = timer.get_buckets()
                metric_data["bucket_counts"] = timer.get_all_bucket_counts()
                metric_data["timestamps"] = timer.get_all_timestamps()

            result["metrics"][name] = metric_data

        return result


class PrometheusExporter(MetricsExporter):
    """
    Exports metrics in Prometheus format.

    This exporter formats metrics in the Prometheus text format and exports them
    to a file or returns them as a string.
    """

    def __init__(self, output_path: Optional[Union[str, Path]] = None):
        """
        Initialize the Prometheus exporter.

        Args:
            output_path: Optional path to write Prometheus metrics to
                        (if None, export_metrics returns the metrics)
        """
        self.output_path = Path(output_path) if output_path else None
        self.logger = logging.getLogger("vibe_check_actor_system.metrics.exporters.prometheus")

    async def export_metrics(self, metrics: Dict[str, Metric]) -> Optional[str]:
        """
        Export metrics in Prometheus format.

        Args:
            metrics: Dictionary of metrics to export

        Returns:
            Prometheus-formatted string if output_path is None, otherwise None
        """
        # Convert metrics to Prometheus format
        prometheus_data = self._convert_metrics_to_prometheus(metrics)

        # Write to file if output_path is specified
        if self.output_path:
            # Create directory if it doesn't exist
            os.makedirs(self.output_path.parent, exist_ok=True)

            # Write to file
            try:
                with open(self.output_path, "w") as f:
                    f.write(prometheus_data)
                self.logger.info(f"Exported metrics to {self.output_path}")
            except Exception as e:
                self.logger.error(f"Failed to export metrics to {self.output_path}: {e}")
            return None
        else:
            return prometheus_data

    def _convert_metrics_to_prometheus(self, metrics: Dict[str, Metric]) -> str:
        """
        Convert metrics to Prometheus format.

        Args:
            metrics: Dictionary of metrics to convert

        Returns:
            Prometheus-formatted string
        """
        lines = []

        for name, metric in metrics.items():
            # Add metric metadata
            lines.append(f"# HELP {name} {metric.description}")
            lines.append(f"# TYPE {name} {self._get_prometheus_type(metric.get_type())}")

            # Add metric values based on type
            if metric.get_type() == MetricType.COUNTER:
                counter = cast(Counter, metric)
                for labels, value in counter.get_all_values().items():
                    lines.append(f"{name}{self._format_labels(labels)} {value}")
            elif metric.get_type() == MetricType.GAUGE:
                gauge = cast(Gauge, metric)
                for labels, value in gauge.get_all_values().items():
                    lines.append(f"{name}{self._format_labels(labels)} {value}")
            elif metric.get_type() == MetricType.HISTOGRAM:
                histogram = cast(Histogram, metric)
                for labels, counts in histogram.get_all_bucket_counts().items():
                    # Add bucket values
                    buckets = histogram.get_buckets()
                    for i, bucket in enumerate(buckets):
                        bucket_labels = {**labels, "le": str(bucket)}
                        lines.append(f"{name}_bucket{self._format_labels(bucket_labels)} {counts[i]}")

                    # Add infinity bucket
                    inf_labels = {**labels, "le": "+Inf"}
                    lines.append(f"{name}_bucket{self._format_labels(inf_labels)} {histogram.get_count(labels)}")

                    # Add sum and count
                    lines.append(f"{name}_sum{self._format_labels(labels)} {histogram.get_sum(labels)}")
                    lines.append(f"{name}_count{self._format_labels(labels)} {histogram.get_count(labels)}")
            elif metric.get_type() == MetricType.TIMER:
                timer = cast(Timer, metric)
                for labels, counts in timer.get_all_bucket_counts().items():
                    # Add bucket values
                    buckets = timer.get_buckets()
                    for i, bucket in enumerate(buckets):
                        bucket_labels = {**labels, "le": str(bucket)}
                        lines.append(f"{name}_bucket{self._format_labels(bucket_labels)} {counts[i]}")

                    # Add infinity bucket
                    inf_labels = {**labels, "le": "+Inf"}
                    lines.append(f"{name}_bucket{self._format_labels(inf_labels)} {timer.get_count(labels)}")

                    # Add sum and count
                    lines.append(f"{name}_sum{self._format_labels(labels)} {timer.get_sum(labels)}")
                    lines.append(f"{name}_count{self._format_labels(labels)} {timer.get_count(labels)}")

        return "\n".join(lines)

    def _get_prometheus_type(self, metric_type: MetricType) -> str:
        """
        Get the Prometheus type for a metric type.

        Args:
            metric_type: The metric type

        Returns:
            The Prometheus type
        """
        if metric_type == MetricType.COUNTER:
            return "counter"
        elif metric_type == MetricType.GAUGE:
            return "gauge"
        elif metric_type == MetricType.HISTOGRAM or metric_type == MetricType.TIMER:
            return "histogram"
        else:
            return "untyped"

    def _format_labels(self, labels: Dict[str, str]) -> str:
        """
        Format labels for Prometheus.

        Args:
            labels: Dictionary of labels

        Returns:
            Prometheus-formatted labels string
        """
        if not labels:
            return ""

        label_parts = [f'{key}="{value}"' for key, value in sorted(labels.items())]
        return "{" + ",".join(label_parts) + "}"


class ConsoleExporter(MetricsExporter):
    """
    Prints metrics to the console.

    This exporter formats metrics in a human-readable format and prints them to
    the console.
    """

    def __init__(self, include_timestamps: bool = False, include_labels: bool = True):
        """
        Initialize the console exporter.

        Args:
            include_timestamps: Whether to include timestamps in the output
            include_labels: Whether to include labels in the output
        """
        self.include_timestamps = include_timestamps
        self.include_labels = include_labels
        self.logger = logging.getLogger("vibe_check_actor_system.metrics.exporters.console")

    async def export_metrics(self, metrics: Dict[str, Metric]) -> None:
        """
        Export metrics to the console.

        Args:
            metrics: Dictionary of metrics to export
        """
        lines = []
        lines.append("=== Metrics ===")
        lines.append(f"Timestamp: {time.time()}")
        lines.append("")

        for name, metric in sorted(metrics.items()):
            lines.append(f"{name} ({metric.get_type().name}):")
            lines.append(f"  Description: {metric.description}")

            if self.include_timestamps:
                lines.append(f"  Created: {metric.created_at}")

            # Add type-specific data
            if metric.get_type() == MetricType.COUNTER:
                counter = cast(Counter, metric)
                for labels, value in sorted(counter.get_all_values().items()):
                    label_str = f" {labels}" if self.include_labels and labels else ""
                    timestamp_str = f" @ {counter.get_all_timestamps().get(labels, 0):.3f}" if self.include_timestamps else ""
                    lines.append(f"  Value{label_str}: {value}{timestamp_str}")

            elif metric.get_type() == MetricType.GAUGE:
                gauge = cast(Gauge, metric)
                for labels, value in sorted(gauge.get_all_values().items()):
                    label_str = f" {labels}" if self.include_labels and labels else ""
                    timestamp_str = f" @ {gauge.get_all_timestamps().get(labels, 0):.3f}" if self.include_timestamps else ""
                    lines.append(f"  Value{label_str}: {value}{timestamp_str}")

            elif metric.get_type() == MetricType.HISTOGRAM:
                histogram = cast(Histogram, metric)
                for labels, count in sorted(histogram.get_all_counts().items()):
                    label_str = f" {labels}" if self.include_labels and labels else ""
                    timestamp_str = f" @ {histogram.get_all_timestamps().get(labels, 0):.3f}" if self.include_timestamps else ""
                    lines.append(f"  Count{label_str}: {count}{timestamp_str}")
                    lines.append(f"  Sum{label_str}: {histogram.get_all_sums().get(labels, 0):.3f}")
                    lines.append(f"  Avg{label_str}: {histogram.get_average(labels):.3f}")

                    # Add percentiles
                    lines.append(f"  Percentiles{label_str}:")
                    for p in [50, 90, 95, 99]:
                        lines.append(f"    p{p}: {histogram.get_percentile(p, labels):.3f}")

                    # Add buckets
                    if self.include_labels:
                        lines.append(f"  Buckets{label_str}:")
                        buckets = histogram.get_buckets()
                        bucket_counts = histogram.get_bucket_counts(labels)
                        for i, bucket in enumerate(buckets):
                            lines.append(f"    <= {bucket}: {bucket_counts[i]}")

            elif metric.get_type() == MetricType.TIMER:
                timer = cast(Timer, metric)
                for labels, count in sorted(timer.get_all_counts().items()):
                    label_str = f" {labels}" if self.include_labels and labels else ""
                    timestamp_str = f" @ {timer.get_all_timestamps().get(labels, 0):.3f}" if self.include_timestamps else ""
                    lines.append(f"  Count{label_str}: {count}{timestamp_str}")
                    lines.append(f"  Sum{label_str}: {timer.get_all_sums().get(labels, 0):.3f}s")
                    lines.append(f"  Avg{label_str}: {timer.get_average(labels):.3f}s")

                    # Add percentiles
                    lines.append(f"  Percentiles{label_str}:")
                    for p in [50, 90, 95, 99]:
                        lines.append(f"    p{p}: {timer.get_percentile(p, labels):.3f}s")

                    # Add buckets
                    if self.include_labels:
                        lines.append(f"  Buckets{label_str}:")
                        buckets = timer.get_buckets()
                        bucket_counts = timer.get_bucket_counts(labels)
                        for i, bucket in enumerate(buckets):
                            lines.append(f"    <= {bucket}s: {bucket_counts[i]}")

            lines.append("")

        # Print to console
        print("\n".join(lines))


class FileExporter(MetricsExporter):
    """
    Writes metrics to a file.

    This exporter formats metrics in a human-readable format and writes them to
    a file.
    """

    def __init__(self, output_path: Union[str, Path], include_timestamps: bool = True,
                include_labels: bool = True, append: bool = True):
        """
        Initialize the file exporter.

        Args:
            output_path: Path to write metrics to
            include_timestamps: Whether to include timestamps in the output
            include_labels: Whether to include labels in the output
            append: Whether to append to the file or overwrite it
        """
        self.output_path = Path(output_path)
        self.include_timestamps = include_timestamps
        self.include_labels = include_labels
        self.append = append
        self.logger = logging.getLogger("vibe_check_actor_system.metrics.exporters.file")

    async def export_metrics(self, metrics: Dict[str, Metric]) -> None:
        """
        Export metrics to a file.

        Args:
            metrics: Dictionary of metrics to export
        """
        # Create console exporter to format metrics
        console_exporter = ConsoleExporter(
            include_timestamps=self.include_timestamps,
            include_labels=self.include_labels
        )

        # Use StringIO to capture console output
        import io
        import sys

        original_stdout = sys.stdout
        string_io = io.StringIO()

        try:
            sys.stdout = string_io
            await console_exporter.export_metrics(metrics)
            metrics_text = string_io.getvalue()
        finally:
            sys.stdout = original_stdout

        # Create directory if it doesn't exist
        os.makedirs(self.output_path.parent, exist_ok=True)

        # Write to file
        try:
            mode = "a" if self.append else "w"
            with open(self.output_path, mode) as f:
                f.write(metrics_text)
                f.write("\n\n")
            self.logger.info(f"Exported metrics to {self.output_path}")
        except Exception as e:
            self.logger.error(f"Failed to export metrics to {self.output_path}: {e}")


class MultiExporter(MetricsExporter):
    """
    Exports metrics using multiple exporters.

    This exporter delegates to multiple other exporters, allowing metrics to be
    exported to multiple destinations simultaneously.
    """

    def __init__(self, exporters: List[MetricsExporter]):
        """
        Initialize the multi-exporter.

        Args:
            exporters: List of exporters to delegate to
        """
        self.exporters = exporters
        self.logger = logging.getLogger("vibe_check_actor_system.metrics.exporters.multi")

    async def export_metrics(self, metrics: Dict[str, Metric]) -> None:
        """
        Export metrics using all exporters.

        Args:
            metrics: Dictionary of metrics to export
        """
        # Export using all exporters
        for exporter in self.exporters:
            try:
                await exporter.export_metrics(metrics)
            except Exception as e:
                self.logger.error(f"Failed to export metrics using {exporter.__class__.__name__}: {e}")

    def add_exporter(self, exporter: MetricsExporter) -> None:
        """
        Add an exporter.

        Args:
            exporter: Exporter to add
        """
        self.exporters.append(exporter)

    def remove_exporter(self, exporter: MetricsExporter) -> None:
        """
        Remove an exporter.

        Args:
            exporter: Exporter to remove
        """
        if exporter in self.exporters:
            self.exporters.remove(exporter)
