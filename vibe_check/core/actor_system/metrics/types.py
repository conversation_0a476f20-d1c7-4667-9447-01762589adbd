"""
Metric Types Module
===============

This module defines the different types of metrics that can be collected by the
metrics system. Each metric type has different semantics and is appropriate for
different use cases.

The following metric types are supported:
- Counter: A cumulative metric that can only increase
- Gauge: A metric that can increase or decrease
- Histogram: A metric that samples observations and counts them in configurable buckets
- Timer: A specialized histogram for measuring durations
"""

import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union, cast


class MetricType(Enum):
    """Enum representing the type of a metric."""
    COUNTER = auto()
    GAUGE = auto()
    HISTOGRAM = auto()
    TIMER = auto()


@dataclass
class MetricValue:
    """
    A single value of a metric.

    Attributes:
        value: The value of the metric
        timestamp: The timestamp when the value was recorded
        labels: Optional labels for the value
    """
    value: Union[int, float]
    timestamp: float
    labels: Optional[Dict[str, str]] = None


class Metric(ABC):
    """
    Base class for all metrics.

    This abstract class defines the interface that all metric types must implement.
    """

    def __init__(self, name: str, description: str = "", labels: Optional[Dict[str, str]] = None):
        """
        Initialize the metric.

        Args:
            name: Name of the metric
            description: Description of the metric
            labels: Default labels for the metric
        """
        self.name = name
        self.description = description
        self.default_labels = labels or {}
        self.created_at = time.time()

    @abstractmethod
    def add(self, value: Union[int, float], labels: Optional[Dict[str, str]] = None) -> None:
        """
        Add a value to the metric.

        Args:
            value: The value to add
            labels: Optional labels for the value
        """
        pass

    @abstractmethod
    def get(self, labels: Optional[Dict[str, str]] = None) -> Union[int, float]:
        """
        Get the current value of the metric.

        Args:
            labels: Optional labels to filter by

        Returns:
            The current value of the metric
        """
        pass

    @abstractmethod
    def get_type(self) -> MetricType:
        """
        Get the type of the metric.

        Returns:
            The type of the metric
        """
        pass

    def get_metadata(self) -> Dict[str, Any]:
        """
        Get metadata about the metric.

        Returns:
            Dictionary of metadata
        """
        return {
            "name": self.name,
            "description": self.description,
            "type": self.get_type().name,
            "created_at": self.created_at,
            "default_labels": self.default_labels
        }

    def _merge_labels(self, labels: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        Merge default labels with provided labels.

        Args:
            labels: Optional labels to merge with default labels

        Returns:
            Merged labels
        """
        result = self.default_labels.copy()
        if labels:
            result.update(labels)
        return result

    def _labels_to_key(self, labels: Dict[str, str]) -> Tuple[Tuple[str, str], ...]:
        """
        Convert labels to a tuple of key-value pairs for use as a dictionary key.

        Args:
            labels: Labels to convert

        Returns:
            Tuple of key-value pairs
        """
        return tuple(sorted(labels.items()))


class Counter(Metric):
    """
    A cumulative metric that can only increase.

    Counters are typically used to count events or operations, such as the number
    of requests processed, errors encountered, or messages sent.
    """

    def __init__(self, name: str, description: str = "", labels: Optional[Dict[str, str]] = None):
        """
        Initialize the counter.

        Args:
            name: Name of the counter
            description: Description of the counter
            labels: Default labels for the counter
        """
        super().__init__(name, description, labels)
        self._values: Dict[Tuple[Tuple[str, str], ...], float] = {}
        self._timestamps: Dict[Tuple[Tuple[str, str], ...], float] = {}

    def add(self, value: Union[int, float], labels: Optional[Dict[str, str]] = None) -> None:
        """
        Add a value to the counter.

        Args:
            value: The value to add (must be non-negative)
            labels: Optional labels for the value

        Raises:
            ValueError: If the value is negative
        """
        if value < 0:
            raise ValueError("Counter values must be non-negative")

        merged_labels = self._merge_labels(labels)
        key = self._labels_to_key(merged_labels)

        if key not in self._values:
            self._values[key] = 0.0

        self._values[key] += value
        self._timestamps[key] = time.time()

    def get(self, labels: Optional[Dict[str, str]] = None) -> float:
        """
        Get the current value of the counter.

        Args:
            labels: Optional labels to filter by

        Returns:
            The current value of the counter
        """
        merged_labels = self._merge_labels(labels)
        key = self._labels_to_key(merged_labels)

        return self._values.get(key, 0.0)

    def get_type(self) -> MetricType:
        """
        Get the type of the metric.

        Returns:
            The type of the metric
        """
        return MetricType.COUNTER

    def get_all_values(self) -> Dict[Dict[str, str], float]:
        """
        Get all values of the counter.

        Returns:
            Dictionary mapping labels to values
        """
        return {dict(key): value for key, value in self._values.items()}

    def get_all_timestamps(self) -> Dict[Dict[str, str], float]:
        """
        Get all timestamps of the counter.

        Returns:
            Dictionary mapping labels to timestamps
        """
        return {dict(key): timestamp for key, timestamp in self._timestamps.items()}


class Gauge(Metric):
    """
    A metric that can increase or decrease.

    Gauges are typically used to measure values that can go up and down, such as
    memory usage, queue size, or temperature.
    """

    def __init__(self, name: str, description: str = "", labels: Optional[Dict[str, str]] = None):
        """
        Initialize the gauge.

        Args:
            name: Name of the gauge
            description: Description of the gauge
            labels: Default labels for the gauge
        """
        super().__init__(name, description, labels)
        self._values: Dict[Tuple[Tuple[str, str], ...], float] = {}
        self._timestamps: Dict[Tuple[Tuple[str, str], ...], float] = {}

    def add(self, value: Union[int, float], labels: Optional[Dict[str, str]] = None) -> None:
        """
        Set the value of the gauge.

        Args:
            value: The value to set
            labels: Optional labels for the value
        """
        merged_labels = self._merge_labels(labels)
        key = self._labels_to_key(merged_labels)

        self._values[key] = float(value)
        self._timestamps[key] = time.time()

    def increment(self, amount: float = 1.0, labels: Optional[Dict[str, str]] = None) -> None:
        """
        Increment the gauge by the given amount.

        Args:
            amount: The amount to increment by
            labels: Optional labels for the value
        """
        merged_labels = self._merge_labels(labels)
        key = self._labels_to_key(merged_labels)

        if key not in self._values:
            self._values[key] = 0.0

        self._values[key] += amount
        self._timestamps[key] = time.time()

    def decrement(self, amount: float = 1.0, labels: Optional[Dict[str, str]] = None) -> None:
        """
        Decrement the gauge by the given amount.

        Args:
            amount: The amount to decrement by
            labels: Optional labels for the value
        """
        self.increment(-amount, labels)

    def get(self, labels: Optional[Dict[str, str]] = None) -> float:
        """
        Get the current value of the gauge.

        Args:
            labels: Optional labels to filter by

        Returns:
            The current value of the gauge
        """
        merged_labels = self._merge_labels(labels)
        key = self._labels_to_key(merged_labels)

        return self._values.get(key, 0.0)

    def get_type(self) -> MetricType:
        """
        Get the type of the metric.

        Returns:
            The type of the metric
        """
        return MetricType.GAUGE

    def get_all_values(self) -> Dict[Dict[str, str], float]:
        """
        Get all values of the gauge.

        Returns:
            Dictionary mapping labels to values
        """
        return {dict(key): value for key, value in self._values.items()}

    def get_all_timestamps(self) -> Dict[Dict[str, str], float]:
        """
        Get all timestamps of the gauge.

        Returns:
            Dictionary mapping labels to timestamps
        """
        return {dict(key): timestamp for key, timestamp in self._timestamps.items()}


class Histogram(Metric):
    """
    A metric that samples observations and counts them in configurable buckets.

    Histograms are typically used to measure the distribution of values, such as
    request durations or response sizes.
    """

    def __init__(self, name: str, description: str = "", labels: Optional[Dict[str, str]] = None,
                 buckets: Optional[List[float]] = None):
        """
        Initialize the histogram.

        Args:
            name: Name of the histogram
            description: Description of the histogram
            labels: Default labels for the histogram
            buckets: Bucket boundaries (default: [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10])
        """
        super().__init__(name, description, labels)
        self._buckets = buckets or [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10]
        self._buckets.sort()  # Ensure buckets are sorted

        # Initialize data structures for each label combination
        self._values: Dict[Tuple[Tuple[str, str], ...], List[float]] = {}
        self._counts: Dict[Tuple[Tuple[str, str], ...], int] = {}
        self._sums: Dict[Tuple[Tuple[str, str], ...], float] = {}
        self._bucket_counts: Dict[Tuple[Tuple[str, str], ...], List[int]] = {}
        self._timestamps: Dict[Tuple[Tuple[str, str], ...], float] = {}

    def add(self, value: Union[int, float], labels: Optional[Dict[str, str]] = None) -> None:
        """
        Add a value to the histogram.

        Args:
            value: The value to add
            labels: Optional labels for the value
        """
        merged_labels = self._merge_labels(labels)
        key = self._labels_to_key(merged_labels)

        # Initialize data structures for this label combination if needed
        if key not in self._values:
            self._values[key] = []
            self._counts[key] = 0
            self._sums[key] = 0.0
            self._bucket_counts[key] = [0] * len(self._buckets)

        # Add the value
        self._values[key].append(float(value))
        self._counts[key] += 1
        self._sums[key] += float(value)

        # Update bucket counts
        for i, bucket in enumerate(self._buckets):
            if value <= bucket:
                self._bucket_counts[key][i] += 1

        self._timestamps[key] = time.time()

    def get(self, labels: Optional[Dict[str, str]] = None) -> float:
        """
        Get the sum of all values in the histogram.

        Args:
            labels: Optional labels to filter by

        Returns:
            The sum of all values in the histogram
        """
        merged_labels = self._merge_labels(labels)
        key = self._labels_to_key(merged_labels)

        return self._sums.get(key, 0.0)

    def get_count(self, labels: Optional[Dict[str, str]] = None) -> int:
        """
        Get the count of values in the histogram.

        Args:
            labels: Optional labels to filter by

        Returns:
            The count of values in the histogram
        """
        merged_labels = self._merge_labels(labels)
        key = self._labels_to_key(merged_labels)

        return self._counts.get(key, 0)

    def get_sum(self, labels: Optional[Dict[str, str]] = None) -> float:
        """
        Get the sum of all values in the histogram.

        Args:
            labels: Optional labels to filter by

        Returns:
            The sum of all values in the histogram
        """
        return self.get(labels)

    def get_average(self, labels: Optional[Dict[str, str]] = None) -> float:
        """
        Get the average of all values in the histogram.

        Args:
            labels: Optional labels to filter by

        Returns:
            The average of all values in the histogram
        """
        merged_labels = self._merge_labels(labels)
        key = self._labels_to_key(merged_labels)

        count = self._counts.get(key, 0)
        if count == 0:
            return 0.0

        return self._sums.get(key, 0.0) / count

    def get_bucket_counts(self, labels: Optional[Dict[str, str]] = None) -> List[int]:
        """
        Get the counts for each bucket in the histogram.

        Args:
            labels: Optional labels to filter by

        Returns:
            List of counts for each bucket
        """
        merged_labels = self._merge_labels(labels)
        key = self._labels_to_key(merged_labels)

        return self._bucket_counts.get(key, [0] * len(self._buckets))

    def get_buckets(self) -> List[float]:
        """
        Get the bucket boundaries of the histogram.

        Returns:
            List of bucket boundaries
        """
        return self._buckets.copy()

    def get_percentile(self, percentile: float, labels: Optional[Dict[str, str]] = None) -> float:
        """
        Get a percentile value from the histogram.

        Args:
            percentile: The percentile to get (0-100)
            labels: Optional labels to filter by

        Returns:
            The percentile value

        Raises:
            ValueError: If the percentile is not between 0 and 100
        """
        if percentile < 0 or percentile > 100:
            raise ValueError("Percentile must be between 0 and 100")

        merged_labels = self._merge_labels(labels)
        key = self._labels_to_key(merged_labels)

        values = self._values.get(key, [])
        if not values:
            return 0.0

        # Sort values if needed
        sorted_values = sorted(values)

        # Calculate the index
        index = (percentile / 100.0) * (len(sorted_values) - 1)

        # If index is an integer, return the value at that index
        if index.is_integer():
            return sorted_values[int(index)]

        # Otherwise, interpolate between the two nearest values
        lower_index = int(index)
        upper_index = lower_index + 1

        lower_value = sorted_values[lower_index]
        upper_value = sorted_values[upper_index]

        fraction = index - lower_index

        return lower_value + (upper_value - lower_value) * fraction

    def get_type(self) -> MetricType:
        """
        Get the type of the metric.

        Returns:
            The type of the metric
        """
        return MetricType.HISTOGRAM

    def get_all_values(self) -> Dict[Dict[str, str], List[float]]:
        """
        Get all values of the histogram.

        Returns:
            Dictionary mapping labels to lists of values
        """
        return {dict(key): values.copy() for key, values in self._values.items()}

    def get_all_counts(self) -> Dict[Dict[str, str], int]:
        """
        Get all counts of the histogram.

        Returns:
            Dictionary mapping labels to counts
        """
        return {dict(key): count for key, count in self._counts.items()}

    def get_all_sums(self) -> Dict[Dict[str, str], float]:
        """
        Get all sums of the histogram.

        Returns:
            Dictionary mapping labels to sums
        """
        return {dict(key): sum_value for key, sum_value in self._sums.items()}

    def get_all_bucket_counts(self) -> Dict[Dict[str, str], List[int]]:
        """
        Get all bucket counts of the histogram.

        Returns:
            Dictionary mapping labels to lists of bucket counts
        """
        return {dict(key): counts.copy() for key, counts in self._bucket_counts.items()}

    def get_all_timestamps(self) -> Dict[Dict[str, str], float]:
        """
        Get all timestamps of the histogram.

        Returns:
            Dictionary mapping labels to timestamps
        """
        return {dict(key): timestamp for key, timestamp in self._timestamps.items()}


class Timer(Histogram):
    """
    A specialized histogram for measuring durations.

    Timers are typically used to measure the duration of operations, such as
    request processing time or function execution time.
    """

    def __init__(self, name: str, description: str = "", labels: Optional[Dict[str, str]] = None,
                 buckets: Optional[List[float]] = None):
        """
        Initialize the timer.

        Args:
            name: Name of the timer
            description: Description of the timer
            labels: Default labels for the timer
            buckets: Bucket boundaries in seconds
                    (default: [0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10])
        """
        default_buckets = [0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10]
        super().__init__(name, description, labels, buckets or default_buckets)
        self._active_timers: Dict[Tuple[Tuple[str, str], ...], float] = {}

    def start(self, labels: Optional[Dict[str, str]] = None) -> None:
        """
        Start a timer.

        Args:
            labels: Optional labels for the timer
        """
        merged_labels = self._merge_labels(labels)
        key = self._labels_to_key(merged_labels)

        self._active_timers[key] = time.time()

    def stop(self, labels: Optional[Dict[str, str]] = None) -> float:
        """
        Stop a timer and record the duration.

        Args:
            labels: Optional labels for the timer

        Returns:
            The duration in seconds

        Raises:
            ValueError: If the timer was not started
        """
        merged_labels = self._merge_labels(labels)
        key = self._labels_to_key(merged_labels)

        if key not in self._active_timers:
            raise ValueError(f"Timer with labels {merged_labels} was not started")

        start_time = self._active_timers.pop(key)
        duration = time.time() - start_time

        self.add(duration, merged_labels)

        return duration

    def time_func(self, func: Callable, labels: Optional[Dict[str, str]] = None) -> Any:
        """
        Time a function call.

        Args:
            func: The function to time
            labels: Optional labels for the timer

        Returns:
            The result of the function call
        """
        self.start(labels)
        try:
            return func()
        finally:
            self.stop(labels)

    async def time_async_func(self, func: Callable, labels: Optional[Dict[str, str]] = None) -> Any:
        """
        Time an async function call.

        Args:
            func: The async function to time
            labels: Optional labels for the timer

        Returns:
            The result of the async function call
        """
        self.start(labels)
        try:
            return await func()
        finally:
            self.stop(labels)

    def get_type(self) -> MetricType:
        """
        Get the type of the metric.

        Returns:
            The type of the metric
        """
        return MetricType.TIMER
