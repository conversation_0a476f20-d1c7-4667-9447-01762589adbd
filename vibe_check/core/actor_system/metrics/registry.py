"""
Metrics Registry Module
===================

This module defines the MetricsRegistry class, which is responsible for
centralized metrics collection, aggregation, and querying across all actors
in the system.

The MetricsRegistry provides methods for registering metrics, querying metrics,
and aggregating metrics across actors.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Set, Tuple, Union, cast, TYPE_CHECKING

from ..message import Message, MessageType
from .types import Counter, Gauge, Histogram, Metric, MetricType, Timer
from .exporters import MetricsExporter, ConsoleExporter, JsonExporter

# Avoid circular imports
if TYPE_CHECKING:
    from ..actor_system import ActorSystem

# Forward declaration for type hints
_METRICS_REGISTRY: Optional['MetricsRegistry'] = None


class MetricsRegistry:
    """
    Centralized metrics collection, aggregation, and querying.

    This component is responsible for collecting, aggregating, and querying metrics
    across all actors in the system. It provides methods for registering metrics,
    querying metrics, and aggregating metrics across actors.

    Attributes:
        actor_system: The actor system that owns this component
        logger: Logger for this component
        metrics: Dictionary of metrics by actor ID
        exporters: List of exporters for metrics
    """

    def __init__(self, actor_system: Optional['ActorSystem'] = None) -> None:
        """
        Initialize the metrics registry.

        Args:
            actor_system: Optional actor system that owns this component
        """
        self.actor_system = actor_system
        self.logger = logging.getLogger("vibe_check_actor_system.metrics.registry")

        # Initialize metrics
        self._metrics: Dict[str, Dict[str, Any]] = {}  # actor_id -> metric_name -> metric_data
        self._last_update: Dict[str, float] = {}  # actor_id -> timestamp

        # Exporters
        self._exporters: List[MetricsExporter] = []

        # Add default console exporter
        self._exporters.append(ConsoleExporter())

        # Metrics collection settings
        self._metrics_interval: float = 60.0  # seconds
        self._metrics_task: Optional[asyncio.Task[None]] = None

        # Stream subscription
        self._stream_subscribed = False

    async def start(self) -> None:
        """
        Start the metrics registry.

        This method subscribes to the metrics stream and starts collecting metrics.
        """
        # Subscribe to metrics stream if actor system is available
        if self.actor_system and not self._stream_subscribed:
            try:
                await self.actor_system.subscribe_to_stream(
                    stream_id="metrics",
                    callback=self._handle_metrics_message
                )
                self._stream_subscribed = True
                self.logger.info("Subscribed to metrics stream")
            except Exception as e:
                self.logger.error(f"Failed to subscribe to metrics stream: {e}")

        # Start metrics collection task
        await self.start_collection()

    async def stop(self) -> None:
        """
        Stop the metrics registry.

        This method unsubscribes from the metrics stream and stops collecting metrics.
        """
        # Unsubscribe from metrics stream if actor system is available
        if self.actor_system and self._stream_subscribed:
            try:
                await self.actor_system.unsubscribe_from_stream("metrics")
                self._stream_subscribed = False
                self.logger.info("Unsubscribed from metrics stream")
            except Exception as e:
                self.logger.error(f"Failed to unsubscribe from metrics stream: {e}")

        # Stop metrics collection task
        await self.stop_collection()

    async def _handle_metrics_message(self, message: Message) -> None:
        """
        Handle a metrics message from the metrics stream.

        Args:
            message: The metrics message
        """
        try:
            # Extract metrics data from message
            payload = message.payload
            actor_id = payload.get("actor_id")
            timestamp = payload.get("timestamp", time.time())
            metrics_data = payload.get("metrics", {})

            if not actor_id:
                self.logger.warning("Received metrics message without actor_id")
                return

            # Update metrics
            self._metrics[actor_id] = metrics_data
            self._last_update[actor_id] = timestamp

            self.logger.debug(f"Updated metrics for actor {actor_id}")
        except Exception as e:
            self.logger.error(f"Failed to handle metrics message: {e}")

    def get_actor_metrics(self, actor_id: str) -> Dict[str, Any]:
        """
        Get metrics for a specific actor.

        Args:
            actor_id: ID of the actor

        Returns:
            Dictionary of metrics for the actor
        """
        return self._metrics.get(actor_id, {})

    def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """
        Get metrics for all actors.

        Returns:
            Dictionary of metrics by actor ID
        """
        return self._metrics.copy()

    def get_metric(self, metric_name: str, actor_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get a specific metric for one or all actors.

        Args:
            metric_name: Name of the metric
            actor_id: Optional ID of the actor (if None, get for all actors)

        Returns:
            Dictionary of metric values by actor ID
        """
        if actor_id:
            # Get metric for a specific actor
            actor_metrics = self._metrics.get(actor_id, {})
            return {actor_id: actor_metrics.get(metric_name)}
        else:
            # Get metric for all actors
            result = {}
            for aid, metrics in self._metrics.items():
                if metric_name in metrics:
                    result[aid] = metrics[metric_name]
            return result

    def get_aggregated_metric(self, metric_name: str, aggregation: str = "sum") -> Any:
        """
        Get an aggregated metric across all actors.

        Args:
            metric_name: Name of the metric
            aggregation: Type of aggregation (sum, avg, min, max)

        Returns:
            Aggregated metric value

        Raises:
            ValueError: If the aggregation type is not supported
        """
        # Get metric values for all actors
        values = []
        for metrics in self._metrics.values():
            if metric_name in metrics:
                metric_data = metrics[metric_name]
                if isinstance(metric_data, dict) and "value" in metric_data:
                    values.append(metric_data["value"])
                elif isinstance(metric_data, (int, float)):
                    values.append(metric_data)

        if not values:
            return None

        # Perform aggregation
        if aggregation == "sum":
            return sum(values)
        elif aggregation == "avg":
            return sum(values) / len(values)
        elif aggregation == "min":
            return min(values)
        elif aggregation == "max":
            return max(values)
        else:
            raise ValueError(f"Unsupported aggregation type: {aggregation}")

    def get_active_actors(self, max_age: float = 300.0) -> List[str]:
        """
        Get a list of active actors.

        Args:
            max_age: Maximum age of metrics in seconds for an actor to be considered active

        Returns:
            List of active actor IDs
        """
        now = time.time()
        return [
            actor_id for actor_id, timestamp in self._last_update.items()
            if now - timestamp <= max_age
        ]

    def add_exporter(self, exporter: MetricsExporter) -> None:
        """
        Add a metrics exporter.

        Args:
            exporter: The exporter to add
        """
        self._exporters.append(exporter)

    def remove_exporter(self, exporter: MetricsExporter) -> None:
        """
        Remove a metrics exporter.

        Args:
            exporter: The exporter to remove
        """
        if exporter in self._exporters:
            self._exporters.remove(exporter)

    def clear_exporters(self) -> None:
        """Clear all metrics exporters."""
        self._exporters.clear()

    @property
    def metrics_interval(self) -> float:
        """Get the interval between metrics collection in seconds."""
        return self._metrics_interval

    @metrics_interval.setter
    def metrics_interval(self, value: float) -> None:
        """
        Set the interval between metrics collection in seconds.

        Args:
            value: New interval in seconds

        Raises:
            ValueError: If the value is not positive
        """
        if value <= 0:
            raise ValueError("Metrics interval must be positive")
        self._metrics_interval = value

    async def start_collection(self) -> None:
        """
        Start collecting metrics periodically.

        Raises:
            RuntimeError: If metrics collection is already running
        """
        if self._metrics_task is not None and not self._metrics_task.done():
            raise RuntimeError("Metrics collection is already running")

        self._metrics_task = asyncio.create_task(self.collect_metrics())
        self.logger.info("Started metrics collection")

    async def stop_collection(self) -> None:
        """Stop collecting metrics periodically."""
        if self._metrics_task is not None and not self._metrics_task.done():
            self._metrics_task.cancel()
            try:
                await self._metrics_task
            except asyncio.CancelledError:
                pass
            self._metrics_task = None
            self.logger.info("Stopped metrics collection")

    async def collect_metrics(self) -> None:
        """
        Collect and report metrics periodically.

        This method runs in a loop, collecting and reporting metrics at the
        specified interval.
        """
        try:
            while True:
                try:
                    # Export metrics using all exporters
                    for exporter in self._exporters:
                        try:
                            # Convert metrics to a format suitable for exporting
                            export_metrics = {}
                            for actor_id, metrics in self._metrics.items():
                                for metric_name, metric_data in metrics.items():
                                    # Create a unique name for each actor's metric
                                    export_name = f"{actor_id}_{metric_name}"

                                    # Create a metric object based on the type
                                    if isinstance(metric_data, dict) and "type" in metric_data:
                                        metric_type = metric_data["type"]
                                        if metric_type == "counter":
                                            counter = Counter(export_name, f"Counter for {actor_id}.{metric_name}")
                                            counter.add(metric_data.get("value", 0))
                                            export_metrics[export_name] = counter
                                        elif metric_type == "gauge":
                                            gauge = Gauge(export_name, f"Gauge for {actor_id}.{metric_name}")
                                            gauge.add(metric_data.get("value", 0))
                                            export_metrics[export_name] = gauge
                                        elif metric_type == "histogram":
                                            histogram = Histogram(export_name, f"Histogram for {actor_id}.{metric_name}")
                                            # Add dummy value to create the histogram
                                            histogram.add(metric_data.get("avg", 0))
                                            export_metrics[export_name] = histogram
                                        elif metric_type == "timer":
                                            timer = Timer(export_name, f"Timer for {actor_id}.{metric_name}")
                                            # Add dummy value to create the timer
                                            timer.add(metric_data.get("avg", 0))
                                            export_metrics[export_name] = timer
                                    elif isinstance(metric_data, (int, float)):
                                        # Assume it's a gauge
                                        gauge = Gauge(export_name, f"Gauge for {actor_id}.{metric_name}")
                                        gauge.add(metric_data)
                                        export_metrics[export_name] = gauge

                            await exporter.export_metrics(export_metrics)
                        except Exception as e:
                            self.logger.error(f"Failed to export metrics using {exporter.__class__.__name__}: {e}")

                    self.logger.debug("Collected and exported metrics")
                except Exception as e:
                    self.logger.warning(f"Failed to collect metrics: {e}")

                # Wait for next metrics interval
                await asyncio.sleep(self._metrics_interval)
        except asyncio.CancelledError:
            self.logger.info("Metrics collection task cancelled")
        except Exception as e:
            self.logger.error(f"Error in metrics collection task: {e}")
            import traceback
            error_details = traceback.format_exc()
            self.logger.error(error_details)


def get_metrics_registry() -> 'MetricsRegistry':
    """
    Get the global metrics registry instance.

    Returns:
        The global metrics registry instance
    """
    global _METRICS_REGISTRY
    if _METRICS_REGISTRY is None:
        _METRICS_REGISTRY = MetricsRegistry()
    return _METRICS_REGISTRY


def reset_metrics_registry() -> None:
    """Reset the global metrics registry instance."""
    global _METRICS_REGISTRY
    _METRICS_REGISTRY = None
