"""
Metrics Collector Module
=====================

This module defines the MetricsCollector class, which is responsible for
collecting and reporting metrics about actor performance and behavior.

The MetricsCollector implements the MetricsCollectorProtocol and provides methods
for collecting, updating, and reporting metrics. It supports different metric types,
statistical analysis, and comprehensive metrics collection.
"""

import asyncio
import logging
import time
import traceback
from typing import Any, Dict, List, Optional, Set, Tuple, Union, cast, TYPE_CHECKING

from ..message import MessageType
from ..protocols import MetricsCollectorProtocol
from .types import Counter, Gauge, Histogram, Metric, MetricType, Timer
from .exporters import MetricsExporter, ConsoleExporter

# Avoid circular imports
if TYPE_CHECKING:
    from ..actor import Actor


class MetricsCollector(MetricsCollectorProtocol):
    """
    Collects and reports metrics about actor performance and behavior.

    This component is responsible for collecting, updating, and reporting metrics
    about actor performance and behavior. It implements the MetricsCollectorProtocol
    and supports different metric types, statistical analysis, and comprehensive
    metrics collection.

    Attributes:
        actor: The actor that owns this component
        logger: Logger for this component
        metrics: Dictionary of metrics
        metrics_interval: Interval between metrics collection in seconds
        metrics_task: Task for the metrics collection loop
        exporters: List of exporters for metrics
    """

    def __init__(self, actor: 'Actor') -> None:
        """
        Initialize the metrics collector.

        Args:
            actor: The actor that owns this component
        """
        self.actor = actor
        self.logger = logging.getLogger("vibe_check_actor_system.metrics")

        # Initialize metrics
        self._metrics: Dict[str, Metric] = {}

        # Initialize basic metrics
        self._init_basic_metrics()

        # Metrics collection settings
        self._metrics_interval: float = 30.0  # seconds
        self._metrics_task: Optional[asyncio.Task[None]] = None

        # Exporters
        self._exporters: List[MetricsExporter] = []

        # Add default console exporter
        self._exporters.append(ConsoleExporter())

        # For backward compatibility
        self._legacy_metrics: Dict[str, Any] = {
            "messages_received": 0,
            "messages_sent": 0,
            "messages_processed": 0,
            "errors": 0,
            "processing_time": 0.0,
            "avg_processing_time": 0.0,
            "last_activity": time.time(),
            "uptime": 0.0,
            "start_time": time.time(),
            "restarts": 0
        }

    def _init_basic_metrics(self) -> None:
        """Initialize basic metrics for the actor."""
        # Message metrics
        self.counter("messages_received", "Number of messages received by the actor")
        self.counter("messages_sent", "Number of messages sent by the actor")
        self.counter("messages_processed", "Number of messages processed by the actor")

        # Error metrics
        self.counter("errors", "Number of errors encountered by the actor")

        # Performance metrics
        self.timer("processing_time", "Time spent processing messages")

        # State metrics
        self.gauge("uptime", "Time since the actor was started")
        self.gauge("last_activity", "Time of the last activity").add(time.time())

        # Lifecycle metrics
        self.counter("restarts", "Number of times the actor has been restarted")
        self.gauge("start_time", "Time when the actor was started").add(time.time())

    def counter(self, name: str, description: str = "", labels: Optional[Dict[str, str]] = None) -> Counter:
        """
        Create or get a counter metric.

        Args:
            name: Name of the counter
            description: Description of the counter
            labels: Default labels for the counter

        Returns:
            The counter metric
        """
        if name not in self._metrics:
            self._metrics[name] = Counter(name, description, labels)

        return cast(Counter, self._metrics[name])

    def gauge(self, name: str, description: str = "", labels: Optional[Dict[str, str]] = None) -> Gauge:
        """
        Create or get a gauge metric.

        Args:
            name: Name of the gauge
            description: Description of the gauge
            labels: Default labels for the gauge

        Returns:
            The gauge metric
        """
        if name not in self._metrics:
            self._metrics[name] = Gauge(name, description, labels)

        return cast(Gauge, self._metrics[name])

    def histogram(self, name: str, description: str = "", labels: Optional[Dict[str, str]] = None,
                 buckets: Optional[List[float]] = None) -> Histogram:
        """
        Create or get a histogram metric.

        Args:
            name: Name of the histogram
            description: Description of the histogram
            labels: Default labels for the histogram
            buckets: Bucket boundaries

        Returns:
            The histogram metric
        """
        if name not in self._metrics:
            self._metrics[name] = Histogram(name, description, labels, buckets)

        return cast(Histogram, self._metrics[name])

    def timer(self, name: str, description: str = "", labels: Optional[Dict[str, str]] = None,
             buckets: Optional[List[float]] = None) -> Timer:
        """
        Create or get a timer metric.

        Args:
            name: Name of the timer
            description: Description of the timer
            labels: Default labels for the timer
            buckets: Bucket boundaries in seconds

        Returns:
            The timer metric
        """
        if name not in self._metrics:
            self._metrics[name] = Timer(name, description, labels, buckets)

        return cast(Timer, self._metrics[name])

    @property
    def metrics_interval(self) -> float:
        """Get the interval between metrics collection in seconds."""
        return self._metrics_interval

    @metrics_interval.setter
    def metrics_interval(self, value: float) -> None:
        """
        Set the interval between metrics collection in seconds.

        Args:
            value: New interval in seconds

        Raises:
            ValueError: If the value is not positive
        """
        if value <= 0:
            raise ValueError("Metrics interval must be positive")
        self._metrics_interval = value

    def update_metric(self, metric_name: str, value: Any) -> None:
        """
        Update a specific metric.

        Args:
            metric_name: Name of the metric to update
            value: New value for the metric

        Raises:
            KeyError: If the metric doesn't exist
        """
        # For backward compatibility
        if metric_name in self._legacy_metrics:
            self._legacy_metrics[metric_name] = value

            # Update the corresponding typed metric if it exists
            if metric_name in self._metrics:
                metric = self._metrics[metric_name]
                if isinstance(metric, (Counter, Gauge, Histogram, Timer)):
                    metric.add(value)

            self.logger.debug(f"Updated legacy metric {metric_name} to {value} for actor {self.actor.actor_id}")
            return

        # Handle typed metrics
        if metric_name not in self._metrics:
            raise KeyError(f"Unknown metric: {metric_name}")

        metric = self._metrics[metric_name]

        if isinstance(metric, (Counter, Gauge, Histogram, Timer)):
            metric.add(value)
        else:
            raise TypeError(f"Metric {metric_name} is not a supported type")

        self.logger.debug(f"Updated metric {metric_name} to {value} for actor {self.actor.actor_id}")

    def increment_metric(self, metric_name: str, increment: float = 1.0) -> None:
        """
        Increment a numeric metric.

        Args:
            metric_name: Name of the metric to increment
            increment: Amount to increment by (default: 1.0)

        Raises:
            KeyError: If the metric doesn't exist
            TypeError: If the metric is not a counter or gauge
        """
        # For backward compatibility
        if metric_name in self._legacy_metrics:
            if not isinstance(self._legacy_metrics[metric_name], (int, float)):
                raise TypeError(f"Legacy metric {metric_name} is not numeric")

            self._legacy_metrics[metric_name] += increment

            # Update the corresponding typed metric if it exists
            if metric_name in self._metrics:
                metric = self._metrics[metric_name]
                if isinstance(metric, Counter):
                    metric.add(increment)
                elif isinstance(metric, Gauge):
                    metric.increment(increment)

            self.logger.debug(f"Incremented legacy metric {metric_name} by {increment} for actor {self.actor.actor_id}")
            return

        # Handle typed metrics
        if metric_name not in self._metrics:
            raise KeyError(f"Unknown metric: {metric_name}")

        metric = self._metrics[metric_name]

        if isinstance(metric, Counter):
            metric.add(increment)
        elif isinstance(metric, Gauge):
            metric.increment(increment)
        else:
            raise TypeError(f"Metric {metric_name} is not a counter or gauge")

        self.logger.debug(f"Incremented metric {metric_name} by {increment} for actor {self.actor.actor_id}")

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get all metrics.

        Returns:
            Dictionary of metrics
        """
        # Update uptime before returning
        uptime_metric = self.gauge("uptime")
        uptime_metric.add(time.time() - self.gauge("start_time").get())

        # Update legacy metrics for backward compatibility
        for name, metric in self._metrics.items():
            if name in self._legacy_metrics:
                if isinstance(metric, Counter):
                    self._legacy_metrics[name] = metric.get()
                elif isinstance(metric, Gauge):
                    self._legacy_metrics[name] = metric.get()
                elif isinstance(metric, Histogram):
                    self._legacy_metrics[name] = metric.get_average()
                elif isinstance(metric, Timer):
                    self._legacy_metrics[name] = metric.get_average()

        # Return legacy metrics for backward compatibility
        return self._legacy_metrics.copy()

    def get_raw_metrics(self) -> Dict[str, Metric]:
        """
        Get all raw metrics.

        Returns:
            Dictionary of raw metrics
        """
        return self._metrics.copy()

    def reset_metrics(self) -> None:
        """Reset all metrics to their initial values."""
        # Re-initialize basic metrics
        self._metrics.clear()
        self._init_basic_metrics()

        # Reset legacy metrics for backward compatibility
        self._legacy_metrics = {
            "messages_received": 0,
            "messages_sent": 0,
            "messages_processed": 0,
            "errors": 0,
            "processing_time": 0.0,
            "avg_processing_time": 0.0,
            "last_activity": time.time(),
            "uptime": 0.0,
            "start_time": time.time(),
            "restarts": 0
        }

        self.logger.info(f"Reset metrics for actor {self.actor.actor_id}")

    def add_exporter(self, exporter: MetricsExporter) -> None:
        """
        Add a metrics exporter.

        Args:
            exporter: The exporter to add
        """
        self._exporters.append(exporter)

    def remove_exporter(self, exporter: MetricsExporter) -> None:
        """
        Remove a metrics exporter.

        Args:
            exporter: The exporter to remove
        """
        if exporter in self._exporters:
            self._exporters.remove(exporter)

    def clear_exporters(self) -> None:
        """Clear all metrics exporters."""
        self._exporters.clear()

    def set_metrics_interval(self, interval: float) -> None:
        """
        Set the interval between metrics collection.

        Args:
            interval: Interval in seconds
        """
        self._metrics_interval = interval

    async def start_collection(self) -> None:
        """
        Start collecting metrics periodically.

        Raises:
            RuntimeError: If metrics collection is already running
        """
        if self._metrics_task is not None and not self._metrics_task.done():
            raise RuntimeError("Metrics collection is already running")

        self._metrics_task = asyncio.create_task(self.collect_metrics())
        self.logger.info(f"Started metrics collection for actor {self.actor.actor_id}")

    async def stop_collection(self) -> None:
        """Stop collecting metrics periodically."""
        if self._metrics_task is not None and not self._metrics_task.done():
            self._metrics_task.cancel()
            try:
                await self._metrics_task
            except asyncio.CancelledError:
                pass
            self._metrics_task = None
            self.logger.info(f"Stopped metrics collection for actor {self.actor.actor_id}")

    async def collect_metrics(self) -> None:
        """
        Collect and report metrics periodically.

        This method runs in a loop, collecting and reporting metrics at the
        specified interval.
        """
        try:
            while self.actor.is_running:
                try:
                    # Update uptime metric
                    uptime_metric = self.gauge("uptime")
                    uptime_metric.add(time.time() - self.gauge("start_time").get())

                    # Export metrics using all exporters
                    for exporter in self._exporters:
                        try:
                            await exporter.export_metrics(self._metrics)
                        except Exception as e:
                            self.logger.error(f"Failed to export metrics using {exporter.__class__.__name__}: {e}")

                    # Publish metrics to registry if available
                    try:
                        from ..actor_registry import get_registry
                        registry = get_registry()
                        if registry is not None:
                            # Get metrics in a format suitable for publishing
                            metrics_data = {}
                            for name, metric in self._metrics.items():
                                if isinstance(metric, Counter):
                                    metrics_data[name] = {
                                        "type": "counter",
                                        "value": metric.get(),
                                        "values": metric.get_all_values()
                                    }
                                elif isinstance(metric, Gauge):
                                    metrics_data[name] = {
                                        "type": "gauge",
                                        "value": metric.get(),
                                        "values": metric.get_all_values()
                                    }
                                elif isinstance(metric, Histogram):
                                    metrics_data[name] = {
                                        "type": "histogram",
                                        "count": metric.get_count(),
                                        "sum": metric.get_sum(),
                                        "avg": metric.get_average(),
                                        "buckets": metric.get_buckets(),
                                        "bucket_counts": metric.get_bucket_counts()
                                    }
                                elif isinstance(metric, Timer):
                                    metrics_data[name] = {
                                        "type": "timer",
                                        "count": metric.get_count(),
                                        "sum": metric.get_sum(),
                                        "avg": metric.get_average(),
                                        "buckets": metric.get_buckets(),
                                        "bucket_counts": metric.get_bucket_counts()
                                    }

                            await registry.publish(
                                stream_id="metrics",
                                message_type=MessageType.METRICS,
                                payload={
                                    "actor_id": self.actor.actor_id,
                                    "timestamp": time.time(),
                                    "metrics": metrics_data
                                },
                                sender_id=self.actor.actor_id
                            )
                    except (ImportError, AttributeError) as e:
                        self.logger.debug(f"Failed to publish metrics to registry: {e}")

                    self.logger.debug(f"Actor {self.actor.actor_id} collected metrics")
                except Exception as e:
                    self.logger.warning(f"Failed to collect metrics: {e}")

                # Wait for next metrics interval
                await asyncio.sleep(self._metrics_interval)
        except asyncio.CancelledError:
            self.logger.info(f"Actor {self.actor.actor_id} metrics task cancelled")
        except Exception as e:
            self.logger.error(f"Error in metrics task for actor {self.actor.actor_id}: {e}")
            error_details = traceback.format_exc()
            self.logger.error(error_details)

    async def export_metrics(self, exporter: Optional[MetricsExporter] = None) -> None:
        """
        Export metrics using the specified exporter or all exporters.

        Args:
            exporter: Optional exporter to use (if None, use all exporters)
        """
        # Update uptime metric
        uptime_metric = self.gauge("uptime")
        uptime_metric.add(time.time() - self.gauge("start_time").get())

        if exporter:
            # Export using the specified exporter
            await exporter.export_metrics(self._metrics)
        else:
            # Export using all exporters
            for exp in self._exporters:
                try:
                    await exp.export_metrics(self._metrics)
                except Exception as e:
                    self.logger.error(f"Failed to export metrics using {exp.__class__.__name__}: {e}")

    def get_metric(self, name: str) -> Optional[Metric]:
        """
        Get a specific metric.

        Args:
            name: Name of the metric to get

        Returns:
            The metric, or None if it doesn't exist
        """
        return self._metrics.get(name)
