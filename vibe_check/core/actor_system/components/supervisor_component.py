"""
Supervisor component for the actor system.

This module provides a supervisor component that monitors the health of actors
and handles failures, including readiness failures.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Set, Tuple

from ..actor_state import ActorState
from ..exceptions import ActorInitializationError, ActorSystemError
from ..event_bus import EventBus, EventPriority, get_event_bus
from ..actor_registry import get_registry
from ..consolidated_initializer import get_initializer

logger = logging.getLogger(__name__)


class SupervisorComponent:
    """
    Supervisor component for the actor system.

    This component monitors the health of actors and handles failures,
    including readiness failures.
    """

    def __init__(self) -> None:
        """Initialize the supervisor component."""
        self._monitored_actors: Dict[str, Dict] = {}
        self._failed_actors: Set[str] = set()
        self._restart_attempts: Dict[str, int] = {}
        self._max_restart_attempts = 3
        self._restart_backoff_factor = 1.5
        self._base_restart_delay = 1.0  # seconds
        self._event_bus: Optional[EventBus] = None
        self._running = False
        self._monitor_task: Optional[asyncio.Task] = None

    async def start(self) -> None:
        """Start the supervisor component."""
        if self._running:
            return

        # Get the event bus
        self._event_bus = get_event_bus()

        # Subscribe to actor failure events
        if self._event_bus:
            await self._event_bus.subscribe(
                "actor.*.failure.*",
                self._handle_actor_failure
            )
            await self._event_bus.subscribe(
                "actor.start.failure.*",
                self._handle_start_failure
            )
            await self._event_bus.subscribe(
                "actor.initialize.failure.*",
                self._handle_initialization_failure
            )
            await self._event_bus.subscribe(
                "actor.readiness.failure.*",
                self._handle_readiness_failure
            )

        # Start the monitoring task
        self._running = True
        self._monitor_task = asyncio.create_task(self._monitor_actors())

    async def stop(self) -> None:
        """Stop the supervisor component."""
        if not self._running:
            return

        self._running = False

        # Cancel the monitoring task
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
            self._monitor_task = None

        # Unsubscribe from events
        if self._event_bus:
            await self._event_bus.unsubscribe("actor.*.failure.*", self._handle_actor_failure)
            await self._event_bus.unsubscribe("actor.start.failure.*", self._handle_start_failure)
            await self._event_bus.unsubscribe("actor.initialize.failure.*", self._handle_initialization_failure)
            await self._event_bus.unsubscribe("actor.readiness.failure.*", self._handle_readiness_failure)

    async def monitor_actor(self, actor_id: str, check_interval: float = 5.0) -> None:
        """
        Add an actor to the monitoring list.

        Args:
            actor_id: ID of the actor to monitor
            check_interval: Interval in seconds between health checks
        """
        self._monitored_actors[actor_id] = {
            "check_interval": check_interval,
            "last_check": time.time(),
            "health_status": "unknown"
        }

    async def _monitor_actors(self) -> None:
        """Monitor the health of actors."""
        try:
            while self._running:
                current_time = time.time()

                # Check each monitored actor
                for actor_id, info in list(self._monitored_actors.items()):
                    if current_time - info["last_check"] >= info["check_interval"]:
                        # Time to check this actor
                        await self._check_actor_health(actor_id)
                        info["last_check"] = current_time

                # Sleep for a short time
                await asyncio.sleep(1.0)
        except asyncio.CancelledError:
            # Task was cancelled, exit gracefully
            pass
        except Exception as e:
            logger.error(f"Error in actor monitoring task: {e}")

    async def _check_actor_health(self, actor_id: str) -> None:
        """
        Check the health of an actor.

        Args:
            actor_id: ID of the actor to check
        """
        # Get the actor from the registry
        registry = get_registry()

        if not registry:
            logger.warning(f"Cannot check health of actor {actor_id}: Registry not available")
            return

        actor = registry.get_actor(actor_id)

        if not actor:
            logger.warning(f"Cannot check health of actor {actor_id}: Actor not found in registry")
            self._monitored_actors[actor_id]["health_status"] = "not_found"
            return

        # Check if the actor has a check_readiness method
        if hasattr(actor, 'check_readiness') and callable(getattr(actor, 'check_readiness')):
            try:
                # Call the check_readiness method
                is_ready = await actor.check_readiness()

                if not is_ready:
                    logger.warning(f"Actor {actor_id} reports it is not ready")
                    self._monitored_actors[actor_id]["health_status"] = "not_ready"

                    # Publish a readiness failure event
                    if self._event_bus:
                        await self._event_bus.publish_simple(
                            event_type=f"actor.readiness.failure.{actor_id}",
                            data={
                                "actor_id": actor_id,
                                "timestamp": time.time()
                            },
                            source="supervisor",
                            priority=EventPriority.HIGH
                        )
                else:
                    self._monitored_actors[actor_id]["health_status"] = "healthy"
            except Exception as e:
                logger.error(f"Error checking readiness of actor {actor_id}: {e}")
                self._monitored_actors[actor_id]["health_status"] = "error"
        else:
            # If the actor doesn't have a check_readiness method, check its state
            try:
                # Get the actor's state
                if hasattr(actor, 'state_machine'):
                    state = actor.state_machine.get_state()

                    if state == ActorState.READY:
                        self._monitored_actors[actor_id]["health_status"] = "healthy"
                    elif state == ActorState.FAILED:
                        self._monitored_actors[actor_id]["health_status"] = "failed"

                        # Publish a failure event
                        if self._event_bus:
                            await self._event_bus.publish_simple(
                                event_type=f"actor.failure.{actor_id}",
                                data={
                                    "actor_id": actor_id,
                                    "state": state.value,
                                    "timestamp": time.time()
                                },
                                source="supervisor",
                                priority=EventPriority.HIGH
                            )
                    else:
                        self._monitored_actors[actor_id]["health_status"] = state.value
                else:
                    self._monitored_actors[actor_id]["health_status"] = "unknown"
            except Exception as e:
                logger.error(f"Error checking state of actor {actor_id}: {e}")
                self._monitored_actors[actor_id]["health_status"] = "error"

    async def _handle_actor_failure(self, event: Dict) -> None:
        """
        Handle an actor failure event.

        Args:
            event: The failure event
        """
        data = event.get("data", {})
        actor_id = data.get("actor_id")

        if not actor_id:
            logger.warning("Received actor failure event without actor_id")
            return

        logger.warning(f"Actor {actor_id} has failed")

        # Add to failed actors set
        self._failed_actors.add(actor_id)

        # Attempt to restart the actor
        await self._attempt_restart(actor_id)

    async def _handle_start_failure(self, event: Dict) -> None:
        """
        Handle an actor start failure event.

        Args:
            event: The start failure event
        """
        data = event.get("data", {})
        actor_id = data.get("actor_id")

        if not actor_id:
            logger.warning("Received actor start failure event without actor_id")
            return

        logger.warning(f"Actor {actor_id} failed to start")

        # Add to failed actors set
        self._failed_actors.add(actor_id)

        # Attempt to restart the actor
        await self._attempt_restart(actor_id)

    async def _handle_initialization_failure(self, event: Dict) -> None:
        """
        Handle an actor initialization failure event.

        Args:
            event: The initialization failure event
        """
        data = event.get("data", {})
        actor_id = data.get("actor_id")

        if not actor_id:
            logger.warning("Received actor initialization failure event without actor_id")
            return

        logger.warning(f"Actor {actor_id} failed to initialize")

        # Add to failed actors set
        self._failed_actors.add(actor_id)

        # Attempt to restart the actor
        await self._attempt_restart(actor_id)

    async def _handle_readiness_failure(self, event: Dict) -> None:
        """
        Handle an actor readiness failure event.

        Args:
            event: The readiness failure event
        """
        data = event.get("data", {})
        actor_id = data.get("actor_id")

        if not actor_id:
            logger.warning("Received actor readiness failure event without actor_id")
            return

        logger.warning(f"Actor {actor_id} failed readiness check")

        # Add to failed actors set
        self._failed_actors.add(actor_id)

        # Attempt to restart the actor
        await self._attempt_restart(actor_id)

    async def _attempt_restart(self, actor_id: str) -> None:
        """
        Attempt to restart a failed actor.

        Args:
            actor_id: ID of the actor to restart
        """
        # Check if we've exceeded the maximum restart attempts
        attempts = self._restart_attempts.get(actor_id, 0)

        if attempts >= self._max_restart_attempts:
            logger.error(f"Actor {actor_id} has exceeded the maximum restart attempts ({self._max_restart_attempts})")
            return

        # Increment the restart attempts
        self._restart_attempts[actor_id] = attempts + 1

        # Calculate the backoff delay
        delay = self._base_restart_delay * (self._restart_backoff_factor ** attempts)

        logger.info(f"Scheduling restart of actor {actor_id} in {delay:.2f} seconds (attempt {attempts + 1}/{self._max_restart_attempts})")

        # Schedule the restart
        asyncio.create_task(self._restart_actor(actor_id, delay))

    async def _restart_actor(self, actor_id: str, delay: float) -> None:
        """
        Restart an actor after a delay.

        Args:
            actor_id: ID of the actor to restart
            delay: Delay in seconds before restarting
        """
        # Wait for the delay
        await asyncio.sleep(delay)

        # Get the actor from the registry
        registry = get_registry()

        if not registry:
            logger.warning(f"Cannot restart actor {actor_id}: Registry not available")
            return

        actor = registry.get_actor(actor_id)

        if not actor:
            logger.warning(f"Cannot restart actor {actor_id}: Actor not found in registry")
            return

        # Get the initializer
        initializer = get_initializer()

        if not initializer:
            logger.warning(f"Cannot restart actor {actor_id}: Initializer not available")
            return

        logger.info(f"Restarting actor {actor_id}")

        try:
            # Stop the actor if it's running
            await actor.stop()

            # Reset the actor's state
            await initializer.set_actor_state(
                actor_id=actor_id,
                state=ActorState.CREATED,
                error=None,
                phase="restart"
            )

            # Initialize the actor
            # Get the actor from the registry again to ensure we have the latest instance
            actor = registry.get_actor(actor_id)
            if not actor:
                logger.warning(f"Cannot initialize actor {actor_id}: Actor not found in registry")
                return

            # Initialize the actor
            await initializer.initialize_actor(actor)

            # Start the actor
            try:
                # Try to start the actor
                await initializer.start_actor(actor)
                success = True
            except Exception as e:
                logger.error(f"Error starting actor {actor_id}: {e}")
                success = False

            if success:
                logger.info(f"Successfully restarted actor {actor_id}")

                # Remove from failed actors set
                self._failed_actors.discard(actor_id)

                # Reset restart attempts
                self._restart_attempts[actor_id] = 0

                # Publish a restart success event
                if self._event_bus:
                    await self._event_bus.publish_simple(
                        event_type=f"actor.restart.success.{actor_id}",
                        data={
                            "actor_id": actor_id,
                            "timestamp": time.time()
                        },
                        source="supervisor"
                    )
            else:
                logger.warning(f"Failed to restart actor {actor_id}")

                # Publish a restart failure event
                if self._event_bus:
                    await self._event_bus.publish_simple(
                        event_type=f"actor.restart.failure.{actor_id}",
                        data={
                            "actor_id": actor_id,
                            "timestamp": time.time()
                        },
                        source="supervisor",
                        priority=EventPriority.HIGH
                    )
        except Exception as e:
            logger.error(f"Error restarting actor {actor_id}: {e}")

            # Publish a restart failure event
            if self._event_bus:
                await self._event_bus.publish_simple(
                    event_type=f"actor.restart.failure.{actor_id}",
                    data={
                        "actor_id": actor_id,
                        "error": str(e),
                        "timestamp": time.time()
                    },
                    source="supervisor",
                    priority=EventPriority.HIGH
                )
