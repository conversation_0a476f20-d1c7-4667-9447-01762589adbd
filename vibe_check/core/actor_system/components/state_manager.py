"""
State Manager component for the actor system.

This module provides a state manager component that handles actor state
persistence, recovery, and migration.
"""

import asyncio
import json
import logging
import os
import pickle
import time
from abc import ABC, abstractmethod
from enum import Enum, auto
from pathlib import Path
from typing import Any, Dict, Generic, List, Optional, Set, Type, TypeVar, Union

from ..event_bus import EventBus, EventPriority, get_event_bus

logger = logging.getLogger(__name__)

# Type variable for generic state types
T = TypeVar('T')


class StateFormat(Enum):
    """Enum representing the format of serialized state."""
    JSON = auto()
    PICKLE = auto()
    CUSTOM = auto()


class StateError(Exception):
    """Base exception for state-related errors."""
    pass


class StateSerializationError(StateError):
    """Exception raised when state serialization fails."""
    pass


class StateDeserializationError(StateError):
    """Exception raised when state deserialization fails."""
    pass


class StateNotFoundError(StateError):
    """Exception raised when state is not found."""
    pass


class StateSerializer(Generic[T], ABC):
    """
    Abstract base class for state serializers.

    A state serializer converts between state objects and serialized
    representations for persistence.
    """

    @abstractmethod
    def serialize(self, state: T) -> bytes:
        """
        Serialize a state object to bytes.

        Args:
            state: The state object to serialize

        Returns:
            The serialized state as bytes

        Raises:
            StateSerializationError: If serialization fails
        """
        pass

    @abstractmethod
    def deserialize(self, data: bytes) -> T:
        """
        Deserialize bytes to a state object.

        Args:
            data: The serialized state as bytes

        Returns:
            The deserialized state object

        Raises:
            StateDeserializationError: If deserialization fails
        """
        pass


class JsonSerializer(StateSerializer[Dict[str, Any]]):
    """
    Serializer for JSON-compatible state objects.

    This serializer converts between dictionaries and JSON-encoded bytes.
    """

    def serialize(self, state: Dict[str, Any]) -> bytes:
        """
        Serialize a dictionary to JSON-encoded bytes.

        Args:
            state: The dictionary to serialize

        Returns:
            The serialized state as bytes

        Raises:
            StateSerializationError: If serialization fails
        """
        try:
            return json.dumps(state, ensure_ascii=False).encode('utf-8')
        except Exception as e:
            raise StateSerializationError(f"Failed to serialize state to JSON: {e}")

    def deserialize(self, data: bytes) -> Dict[str, Any]:
        """
        Deserialize JSON-encoded bytes to a dictionary.

        Args:
            data: The serialized state as bytes

        Returns:
            The deserialized dictionary

        Raises:
            StateDeserializationError: If deserialization fails
        """
        try:
            return json.loads(data.decode('utf-8'))
        except Exception as e:
            raise StateDeserializationError(f"Failed to deserialize JSON state: {e}")


class PickleSerializer(StateSerializer[Any]):
    """
    Serializer for arbitrary Python objects using pickle.

    This serializer converts between Python objects and pickle-encoded bytes.
    """

    def serialize(self, state: Any) -> bytes:
        """
        Serialize an object to pickle-encoded bytes.

        Args:
            state: The object to serialize

        Returns:
            The serialized state as bytes

        Raises:
            StateSerializationError: If serialization fails
        """
        try:
            return pickle.dumps(state)
        except Exception as e:
            raise StateSerializationError(f"Failed to serialize state with pickle: {e}")

    def deserialize(self, data: bytes) -> Any:
        """
        Deserialize pickle-encoded bytes to an object.

        Args:
            data: The serialized state as bytes

        Returns:
            The deserialized object

        Raises:
            StateDeserializationError: If deserialization fails
        """
        try:
            return pickle.loads(data)
        except Exception as e:
            raise StateDeserializationError(f"Failed to deserialize pickle state: {e}")


class StateStorage(ABC):
    """
    Abstract base class for state storage backends.

    A state storage backend handles the actual persistence of serialized state.
    """

    @abstractmethod
    async def save(self, key: str, data: bytes) -> None:
        """
        Save serialized state.

        Args:
            key: The key to save the state under
            data: The serialized state as bytes

        Raises:
            StateError: If saving fails
        """
        pass

    @abstractmethod
    async def load(self, key: str) -> bytes:
        """
        Load serialized state.

        Args:
            key: The key to load the state from

        Returns:
            The serialized state as bytes

        Raises:
            StateNotFoundError: If the state is not found
            StateError: If loading fails
        """
        pass

    @abstractmethod
    async def delete(self, key: str) -> None:
        """
        Delete serialized state.

        Args:
            key: The key to delete the state for

        Raises:
            StateError: If deletion fails
        """
        pass

    @abstractmethod
    async def exists(self, key: str) -> bool:
        """
        Check if state exists.

        Args:
            key: The key to check

        Returns:
            True if the state exists, False otherwise
        """
        pass

    @abstractmethod
    async def list_keys(self, prefix: str = "") -> List[str]:
        """
        List all keys with a given prefix.

        Args:
            prefix: Optional prefix to filter keys by

        Returns:
            List of keys
        """
        pass


class FileStorage(StateStorage):
    """
    File-based state storage backend.

    This storage backend saves state to files in a directory.
    """

    def __init__(self, directory: Union[str, Path]) -> None:
        """
        Initialize the file storage.

        Args:
            directory: Directory to store state files in
        """
        self.directory = Path(directory)
        self.directory.mkdir(parents=True, exist_ok=True)

    def _get_path(self, key: str) -> Path:
        """
        Get the file path for a key.

        Args:
            key: The key to get the path for

        Returns:
            The file path
        """
        # Replace any characters that are invalid in filenames
        safe_key = key.replace('/', '_').replace('\\', '_')
        return self.directory / safe_key

    async def save(self, key: str, data: bytes) -> None:
        """
        Save serialized state to a file.

        Args:
            key: The key to save the state under
            data: The serialized state as bytes

        Raises:
            StateError: If saving fails
        """
        path = self._get_path(key)
        try:
            # Use a temporary file to ensure atomic writes
            temp_path = path.with_suffix('.tmp')
            with open(temp_path, 'wb') as f:
                f.write(data)
            # Rename the temporary file to the final path
            temp_path.rename(path)
        except Exception as e:
            raise StateError(f"Failed to save state to file {path}: {e}")

    async def load(self, key: str) -> bytes:
        """
        Load serialized state from a file.

        Args:
            key: The key to load the state from

        Returns:
            The serialized state as bytes

        Raises:
            StateNotFoundError: If the file does not exist
            StateError: If loading fails
        """
        path = self._get_path(key)
        if not path.exists():
            raise StateNotFoundError(f"State file not found: {path}")
        try:
            with open(path, 'rb') as f:
                return f.read()
        except Exception as e:
            raise StateError(f"Failed to load state from file {path}: {e}")

    async def delete(self, key: str) -> None:
        """
        Delete a state file.

        Args:
            key: The key to delete the state for

        Raises:
            StateError: If deletion fails
        """
        path = self._get_path(key)
        try:
            if path.exists():
                path.unlink()
        except Exception as e:
            raise StateError(f"Failed to delete state file {path}: {e}")

    async def exists(self, key: str) -> bool:
        """
        Check if a state file exists.

        Args:
            key: The key to check

        Returns:
            True if the file exists, False otherwise
        """
        path = self._get_path(key)
        return path.exists()

    async def list_keys(self, prefix: str = "") -> List[str]:
        """
        List all keys with a given prefix.

        Args:
            prefix: Optional prefix to filter keys by

        Returns:
            List of keys
        """
        keys = []
        for path in self.directory.iterdir():
            if path.is_file() and not path.name.endswith('.tmp'):
                key = path.name
                if key.startswith(prefix):
                    keys.append(key)
        return keys


class StateManager:
    """
    Manager for actor state persistence.

    The StateManager handles saving, loading, and managing actor state
    using configurable serializers and storage backends.
    """

    def __init__(self, storage: Optional[StateStorage] = None) -> None:
        """
        Initialize the state manager.

        Args:
            storage: Optional storage backend to use
        """
        self._storage = storage
        self._serializers: Dict[StateFormat, StateSerializer] = {
            StateFormat.JSON: JsonSerializer(),
            StateFormat.PICKLE: PickleSerializer()
        }
        self._custom_serializers: Dict[str, StateSerializer] = {}
        self._actor_formats: Dict[str, StateFormat] = {}
        self._event_bus: Optional[EventBus] = None
        self._running = False

    async def start(self) -> None:
        """Start the state manager."""
        if self._running:
            return

        # Create default storage if none was provided
        if not self._storage:
            # Use a directory in the current working directory
            storage_dir = Path.cwd() / 'actor_state'
            self._storage = FileStorage(storage_dir)

        # Get the event bus
        self._event_bus = get_event_bus()

        # Subscribe to actor lifecycle events
        if self._event_bus:
            await self._event_bus.subscribe(
                "actor.stop.*",
                self._handle_actor_stop
            )

        self._running = True
        logger.info("State manager started")

    async def stop(self) -> None:
        """Stop the state manager."""
        if not self._running:
            return

        self._running = False

        # Unsubscribe from events
        if self._event_bus:
            await self._event_bus.unsubscribe("actor.stop.*", self._handle_actor_stop)

        logger.info("State manager stopped")

    def register_serializer(self, format_type: StateFormat, serializer: StateSerializer) -> None:
        """
        Register a serializer for a format type.

        Args:
            format_type: The format type to register the serializer for
            serializer: The serializer to register
        """
        self._serializers[format_type] = serializer

    def register_custom_serializer(self, actor_type: str, serializer: StateSerializer) -> None:
        """
        Register a custom serializer for an actor type.

        Args:
            actor_type: The actor type to register the serializer for
            serializer: The serializer to register
        """
        self._custom_serializers[actor_type] = serializer

    def set_actor_format(self, actor_id: str, format_type: StateFormat) -> None:
        """
        Set the format type for an actor's state.

        Args:
            actor_id: The ID of the actor
            format_type: The format type to use
        """
        self._actor_formats[actor_id] = format_type

    def _get_serializer(self, actor_id: str, actor_type: Optional[str] = None) -> StateSerializer:
        """
        Get the serializer for an actor.

        Args:
            actor_id: The ID of the actor
            actor_type: Optional type of the actor

        Returns:
            The serializer to use

        Raises:
            StateError: If no suitable serializer is found
        """
        # Check for a custom serializer for the actor type
        if actor_type and actor_type in self._custom_serializers:
            return self._custom_serializers[actor_type]

        # Check for a format type set for the actor
        if actor_id in self._actor_formats:
            format_type = self._actor_formats[actor_id]
            if format_type in self._serializers:
                return self._serializers[format_type]

        # Default to JSON serializer
        return self._serializers[StateFormat.JSON]

    async def save_state(self, actor_id: str, state: Any, actor_type: Optional[str] = None) -> None:
        """
        Save an actor's state.

        Args:
            actor_id: The ID of the actor
            state: The state to save
            actor_type: Optional type of the actor

        Raises:
            StateError: If saving fails
        """
        if not self._running:
            raise StateError("State manager is not running")

        serializer = self._get_serializer(actor_id, actor_type)

        try:
            # Serialize the state
            data = serializer.serialize(state)

            # Save the serialized state
            await self._storage.save(actor_id, data)

            logger.debug(f"Saved state for actor {actor_id}")
        except Exception as e:
            raise StateError(f"Failed to save state for actor {actor_id}: {e}")

    async def load_state(self, actor_id: str, actor_type: Optional[str] = None) -> Any:
        """
        Load an actor's state.

        Args:
            actor_id: The ID of the actor
            actor_type: Optional type of the actor

        Returns:
            The loaded state

        Raises:
            StateNotFoundError: If the state is not found
            StateError: If loading fails
        """
        if not self._running:
            raise StateError("State manager is not running")

        serializer = self._get_serializer(actor_id, actor_type)

        try:
            # Load the serialized state
            data = await self._storage.load(actor_id)

            # Deserialize the state
            state = serializer.deserialize(data)

            logger.debug(f"Loaded state for actor {actor_id}")
            return state
        except StateNotFoundError:
            raise
        except Exception as e:
            raise StateError(f"Failed to load state for actor {actor_id}: {e}")

    async def delete_state(self, actor_id: str) -> None:
        """
        Delete an actor's state.

        Args:
            actor_id: The ID of the actor

        Raises:
            StateError: If deletion fails
        """
        if not self._running:
            raise StateError("State manager is not running")

        try:
            await self._storage.delete(actor_id)
            logger.debug(f"Deleted state for actor {actor_id}")
        except Exception as e:
            raise StateError(f"Failed to delete state for actor {actor_id}: {e}")

    async def has_state(self, actor_id: str) -> bool:
        """
        Check if an actor has saved state.

        Args:
            actor_id: The ID of the actor

        Returns:
            True if the actor has saved state, False otherwise
        """
        if not self._running:
            return False

        try:
            return await self._storage.exists(actor_id)
        except Exception as e:
            logger.error(f"Error checking state for actor {actor_id}: {e}")
            return False

    async def list_actors_with_state(self, prefix: str = "") -> List[str]:
        """
        List all actors with saved state.

        Args:
            prefix: Optional prefix to filter actor IDs by

        Returns:
            List of actor IDs
        """
        if not self._running:
            return []

        try:
            return await self._storage.list_keys(prefix)
        except Exception as e:
            logger.error(f"Error listing actors with state: {e}")
            return []

    async def _handle_actor_stop(self, event: Dict) -> None:
        """
        Handle an actor stop event.

        This method is called when an actor is stopped. It can be used to
        automatically save the actor's state if needed.

        Args:
            event: The stop event
        """
        # This is a placeholder for automatic state saving on actor stop.
        # In a real implementation, you might want to check if the actor
        # has requested automatic state saving and handle it accordingly.
        pass
