"""
Metrics Collector component for the actor system.

This module provides a metrics collector component that monitors and records
performance metrics for the actor system and individual actors.
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

from ..event_bus import EventBus, EventPriority, get_event_bus

logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Enum representing the type of a metric."""
    COUNTER = auto()
    GAUGE = auto()
    HISTOGRAM = auto()
    TIMER = auto()


@dataclass
class MetricValue:
    """Class representing a metric value with metadata."""
    value: Union[int, float]
    timestamp: float
    labels: Dict[str, str] = field(default_factory=dict)


class MetricSeries:
    """
    Class representing a series of metric values over time.

    This class stores a time series of metric values and provides
    methods for adding values and computing statistics.
    """

    def __init__(self, name: str, metric_type: MetricType, max_samples: int = 1000) -> None:
        """
        Initialize the metric series.

        Args:
            name: Name of the metric
            metric_type: Type of the metric
            max_samples: Maximum number of samples to store
        """
        self.name = name
        self.metric_type = metric_type
        self.max_samples = max_samples
        self.values: Dict[Tuple[str, ...], deque] = defaultdict(lambda: deque(maxlen=max_samples))

    def add(self, value: Union[int, float], labels: Optional[Dict[str, str]] = None) -> None:
        """
        Add a value to the series.

        Args:
            value: The value to add
            labels: Optional labels for the value
        """
        if labels is None:
            labels = {}

        # Convert labels to a tuple of key-value pairs for use as a dictionary key
        labels_key = tuple(sorted(labels.items()))

        # Add the value with the current timestamp
        self.values[labels_key].append(MetricValue(
            value=value,
            timestamp=time.time(),
            labels=labels
        ))

    def get_values(self, labels: Optional[Dict[str, str]] = None) -> List[MetricValue]:
        """
        Get all values with matching labels.

        Args:
            labels: Optional labels to filter by

        Returns:
            List of matching metric values
        """
        if labels is None:
            labels = {}

        # Convert labels to a tuple of key-value pairs
        labels_key = tuple(sorted(labels.items()))

        # Return the values for the labels
        return list(self.values.get(labels_key, []))

    def get_latest(self, labels: Optional[Dict[str, str]] = None) -> Optional[MetricValue]:
        """
        Get the latest value with matching labels.

        Args:
            labels: Optional labels to filter by

        Returns:
            The latest metric value, or None if no values match
        """
        values = self.get_values(labels)
        if not values:
            return None
        return values[-1]

    def get_count(self, labels: Optional[Dict[str, str]] = None) -> int:
        """
        Get the number of values with matching labels.

        Args:
            labels: Optional labels to filter by

        Returns:
            The number of matching values
        """
        return len(self.get_values(labels))

    def get_sum(self, labels: Optional[Dict[str, str]] = None) -> Union[int, float]:
        """
        Get the sum of values with matching labels.

        Args:
            labels: Optional labels to filter by

        Returns:
            The sum of matching values
        """
        values = self.get_values(labels)
        if not values:
            return 0
        return sum(v.value for v in values)

    def get_average(self, labels: Optional[Dict[str, str]] = None) -> Optional[float]:
        """
        Get the average of values with matching labels.

        Args:
            labels: Optional labels to filter by

        Returns:
            The average of matching values, or None if no values match
        """
        values = self.get_values(labels)
        if not values:
            return None
        return sum(v.value for v in values) / len(values)

    def get_min(self, labels: Optional[Dict[str, str]] = None) -> Optional[Union[int, float]]:
        """
        Get the minimum of values with matching labels.

        Args:
            labels: Optional labels to filter by

        Returns:
            The minimum of matching values, or None if no values match
        """
        values = self.get_values(labels)
        if not values:
            return None
        return min(v.value for v in values)

    def get_max(self, labels: Optional[Dict[str, str]] = None) -> Optional[Union[int, float]]:
        """
        Get the maximum of values with matching labels.

        Args:
            labels: Optional labels to filter by

        Returns:
            The maximum of matching values, or None if no values match
        """
        values = self.get_values(labels)
        if not values:
            return None
        return max(v.value for v in values)

    def get_percentile(self, percentile: float, labels: Optional[Dict[str, str]] = None) -> Optional[float]:
        """
        Get a percentile of values with matching labels.

        Args:
            percentile: The percentile to compute (0-100)
            labels: Optional labels to filter by

        Returns:
            The percentile of matching values, or None if no values match
        """
        values = self.get_values(labels)
        if not values:
            return None

        # Sort values
        sorted_values = sorted(v.value for v in values)

        # Compute the index
        index = (percentile / 100.0) * (len(sorted_values) - 1)

        # Handle the case where the index is not an integer
        if index.is_integer():
            return sorted_values[int(index)]
        else:
            # Interpolate between the two nearest values
            lower_index = int(index)
            upper_index = lower_index + 1
            lower_value = sorted_values[lower_index]
            upper_value = sorted_values[upper_index]
            fraction = index - lower_index
            return lower_value + fraction * (upper_value - lower_value)


class MetricsExporter(ABC):
    """
    Abstract base class for metrics exporters.

    A metrics exporter exports metrics to an external system.
    """

    @abstractmethod
    async def export_metrics(self, metrics: Dict[str, MetricSeries]) -> None:
        """
        Export metrics to an external system.

        Args:
            metrics: Dictionary mapping metric names to series
        """
        pass


class ConsoleExporter(MetricsExporter):
    """
    Metrics exporter that logs metrics to the console.

    This exporter is useful for debugging and development.
    """

    async def export_metrics(self, metrics: Dict[str, MetricSeries]) -> None:
        """
        Export metrics by logging them to the console.

        Args:
            metrics: Dictionary mapping metric names to series
        """
        logger.info("=== Metrics Export ===")

        for name, series in sorted(metrics.items()):
            logger.info(f"Metric: {name} ({series.metric_type.name})")

            # Group values by labels
            by_labels = defaultdict(list)
            for labels_key, values in series.values.items():
                if not values:
                    continue

                # Convert the labels key back to a dictionary for display
                labels_dict = dict(labels_key)

                # Format the labels for display
                labels_str = ", ".join(f"{k}={v}" for k, v in labels_dict.items())
                if labels_str:
                    labels_str = f" [{labels_str}]"

                # Compute statistics based on the metric type
                if series.metric_type == MetricType.COUNTER:
                    value = series.get_sum(labels_dict)
                    logger.info(f"  Count{labels_str}: {value}")
                elif series.metric_type == MetricType.GAUGE:
                    latest = series.get_latest(labels_dict)
                    if latest:
                        logger.info(f"  Value{labels_str}: {latest.value}")
                elif series.metric_type == MetricType.HISTOGRAM:
                    count = series.get_count(labels_dict)
                    avg = series.get_average(labels_dict)
                    min_val = series.get_min(labels_dict)
                    max_val = series.get_max(labels_dict)
                    p50 = series.get_percentile(50, labels_dict)
                    p95 = series.get_percentile(95, labels_dict)
                    p99 = series.get_percentile(99, labels_dict)

                    logger.info(f"  Histogram{labels_str}:")
                    logger.info(f"    Count: {count}")
                    logger.info(f"    Avg: {avg:.2f}")
                    logger.info(f"    Min: {min_val:.2f}")
                    logger.info(f"    Max: {max_val:.2f}")
                    logger.info(f"    p50: {p50:.2f}")
                    logger.info(f"    p95: {p95:.2f}")
                    logger.info(f"    p99: {p99:.2f}")
                elif series.metric_type == MetricType.TIMER:
                    count = series.get_count(labels_dict)
                    avg = series.get_average(labels_dict)
                    min_val = series.get_min(labels_dict)
                    max_val = series.get_max(labels_dict)
                    p50 = series.get_percentile(50, labels_dict)
                    p95 = series.get_percentile(95, labels_dict)
                    p99 = series.get_percentile(99, labels_dict)

                    logger.info(f"  Timer{labels_str}:")
                    logger.info(f"    Count: {count}")
                    logger.info(f"    Avg: {avg:.6f}s")
                    logger.info(f"    Min: {min_val:.6f}s")
                    logger.info(f"    Max: {max_val:.6f}s")
                    logger.info(f"    p50: {p50:.6f}s")
                    logger.info(f"    p95: {p95:.6f}s")
                    logger.info(f"    p99: {p99:.6f}s")

        logger.info("=====================")


class MetricsCollector:
    """
    Collector for actor system metrics.

    The MetricsCollector monitors and records performance metrics for the
    actor system and individual actors.
    """

    def __init__(self) -> None:
        """Initialize the metrics collector."""
        self._metrics: Dict[str, MetricSeries] = {}
        self._exporters: List[MetricsExporter] = []
        self._export_interval: float = 60.0  # seconds
        self._export_task: Optional[asyncio.Task] = None
        self._event_bus: Optional[EventBus] = None
        self._running = False

    async def start(self) -> None:
        """Start the metrics collector."""
        if self._running:
            return

        # Get the event bus
        self._event_bus = get_event_bus()

        # Subscribe to actor lifecycle events
        if self._event_bus:
            await self._event_bus.subscribe(
                "actor.*.success.*",
                self._handle_actor_success
            )
            await self._event_bus.subscribe(
                "actor.*.failure.*",
                self._handle_actor_failure
            )
            await self._event_bus.subscribe(
                "actor.message.*",
                self._handle_actor_message
            )

        # Add a default console exporter if none are configured
        if not self._exporters:
            self._exporters.append(ConsoleExporter())

        # Start the export task
        self._running = True
        self._export_task = asyncio.create_task(self._export_metrics_periodically())

        logger.info("Metrics collector started")

    async def stop(self) -> None:
        """Stop the metrics collector."""
        if not self._running:
            return

        self._running = False

        # Cancel the export task
        if self._export_task:
            self._export_task.cancel()
            try:
                await self._export_task
            except asyncio.CancelledError:
                pass
            self._export_task = None

        # Unsubscribe from events
        if self._event_bus:
            await self._event_bus.unsubscribe("actor.*.success.*", self._handle_actor_success)
            await self._event_bus.unsubscribe("actor.*.failure.*", self._handle_actor_failure)
            await self._event_bus.unsubscribe("actor.message.*", self._handle_actor_message)

        logger.info("Metrics collector stopped")

    def add_exporter(self, exporter: MetricsExporter) -> None:
        """
        Add a metrics exporter.

        Args:
            exporter: The exporter to add
        """
        self._exporters.append(exporter)

    def set_export_interval(self, interval: float) -> None:
        """
        Set the interval for exporting metrics.

        Args:
            interval: The interval in seconds
        """
        self._export_interval = interval

    def _get_or_create_metric(self, name: str, metric_type: MetricType) -> MetricSeries:
        """
        Get or create a metric series.

        Args:
            name: Name of the metric
            metric_type: Type of the metric

        Returns:
            The metric series
        """
        if name not in self._metrics:
            self._metrics[name] = MetricSeries(name, metric_type)
        return self._metrics[name]

    def counter(self, name: str, value: int = 1, labels: Optional[Dict[str, str]] = None) -> None:
        """
        Increment a counter metric.

        Args:
            name: Name of the metric
            value: Value to increment by
            labels: Optional labels for the value
        """
        metric = self._get_or_create_metric(name, MetricType.COUNTER)
        metric.add(value, labels)

    def gauge(self, name: str, value: Union[int, float], labels: Optional[Dict[str, str]] = None) -> None:
        """
        Set a gauge metric.

        Args:
            name: Name of the metric
            value: Value to set
            labels: Optional labels for the value
        """
        metric = self._get_or_create_metric(name, MetricType.GAUGE)
        metric.add(value, labels)

    def histogram(self, name: str, value: Union[int, float], labels: Optional[Dict[str, str]] = None) -> None:
        """
        Add a value to a histogram metric.

        Args:
            name: Name of the metric
            value: Value to add
            labels: Optional labels for the value
        """
        metric = self._get_or_create_metric(name, MetricType.HISTOGRAM)
        metric.add(value, labels)

    def timer(self, name: str, value: float, labels: Optional[Dict[str, str]] = None) -> None:
        """
        Add a timing value to a timer metric.

        Args:
            name: Name of the metric
            value: Timing value in seconds
            labels: Optional labels for the value
        """
        metric = self._get_or_create_metric(name, MetricType.TIMER)
        metric.add(value, labels)

    async def _export_metrics_periodically(self) -> None:
        """Export metrics periodically."""
        try:
            while self._running:
                # Wait for the export interval
                await asyncio.sleep(self._export_interval)

                # Export metrics
                await self._export_metrics()
        except asyncio.CancelledError:
            # Task was cancelled, exit gracefully
            pass
        except Exception as e:
            logger.error(f"Error in metrics export task: {e}")

    async def _export_metrics(self) -> None:
        """Export metrics to all exporters."""
        if not self._running or not self._metrics:
            return

        try:
            # Export metrics to all exporters
            for exporter in self._exporters:
                await exporter.export_metrics(self._metrics)
        except Exception as e:
            logger.error(f"Error exporting metrics: {e}")

    async def _handle_actor_success(self, event: Dict) -> None:
        """
        Handle an actor success event.

        Args:
            event: The success event
        """
        data = event.get("data", {})
        actor_id = data.get("actor_id")
        event_type = event.get("event_type", "")
        parts = event_type.split(".")
        phase = parts[1] if len(parts) > 1 else ""  # Extract the phase from the event type

        if not actor_id or not phase:
            return

        # Increment the success counter
        self.counter("actor_success_total", labels={"actor_id": actor_id, "phase": phase})

    async def _handle_actor_failure(self, event: Dict) -> None:
        """
        Handle an actor failure event.

        Args:
            event: The failure event
        """
        data = event.get("data", {})
        actor_id = data.get("actor_id")
        event_type = event.get("event_type", "")
        parts = event_type.split(".")
        phase = parts[1] if len(parts) > 1 else ""  # Extract the phase from the event type
        error = data.get("error", "unknown")

        if not actor_id or not phase:
            return

        # Increment the failure counter
        self.counter("actor_failure_total", labels={"actor_id": actor_id, "phase": phase, "error": error})

    async def _handle_actor_message(self, event: Dict) -> None:
        """
        Handle an actor message event.

        Args:
            event: The message event
        """
        data = event.get("data", {})
        sender_id = data.get("sender_id")
        recipient_id = data.get("recipient_id")
        message_type = data.get("message_type")
        processing_time = data.get("processing_time")

        if not sender_id or not recipient_id or not message_type:
            return

        # Increment the message counter
        self.counter("actor_messages_total", labels={
            "sender_id": sender_id,
            "recipient_id": recipient_id,
            "message_type": message_type
        })

        # Record the processing time if available
        if processing_time is not None:
            self.timer("actor_message_processing_time", processing_time, labels={
                "sender_id": sender_id,
                "recipient_id": recipient_id,
                "message_type": message_type
            })
