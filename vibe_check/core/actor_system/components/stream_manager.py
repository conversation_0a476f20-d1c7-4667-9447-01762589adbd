"""
Stream Manager component for the actor system.

This module provides a stream manager component that facilitates efficient
data streaming between actors, supporting backpressure, buffering, and
transformation of data streams.
"""

import asyncio
import logging
import time
import uuid
from abc import ABC, abstractmethod
from enum import Enum, auto
from typing import Any, AsyncIterator, Callable, Dict, Generic, List, Optional, Set, TypeVar, Union

from ..event_bus import EventBus, EventPriority, get_event_bus

logger = logging.getLogger(__name__)

# Type variables for generic stream types
T = TypeVar('T')
U = TypeVar('U')


class StreamState(Enum):
    """Enum representing the state of a stream."""
    CREATED = auto()
    OPEN = auto()
    CLOSED = auto()
    ERROR = auto()


class StreamError(Exception):
    """Base exception for stream-related errors."""
    pass


class StreamClosedError(StreamError):
    """Exception raised when attempting to use a closed stream."""
    pass


class StreamBackpressureError(StreamError):
    """Exception raised when a stream's buffer is full."""
    pass


class Stream(Generic[T], ABC):
    """
    Abstract base class for data streams.

    A stream is a sequence of data elements that can be read from or written to.
    Streams support asynchronous iteration and can be closed when no longer needed.
    """

    @abstractmethod
    async def write(self, item: T) -> None:
        """
        Write an item to the stream.

        Args:
            item: The item to write

        Raises:
            StreamClosedError: If the stream is closed
            StreamBackpressureError: If the stream's buffer is full
        """
        pass

    @abstractmethod
    async def read(self) -> T:
        """
        Read an item from the stream.

        Returns:
            The next item from the stream

        Raises:
            StreamClosedError: If the stream is closed and no more items are available
        """
        pass

    @abstractmethod
    async def close(self) -> None:
        """
        Close the stream.

        After a stream is closed, no more items can be written to it,
        but existing items can still be read until the stream is empty.
        """
        pass

    @abstractmethod
    def is_closed(self) -> bool:
        """
        Check if the stream is closed.

        Returns:
            True if the stream is closed, False otherwise
        """
        pass

    @abstractmethod
    async def __aiter__(self) -> AsyncIterator[T]:
        """
        Return an async iterator for the stream.

        This allows using the stream in an async for loop.
        """
        pass


class AsyncStream(Stream[T]):
    """
    Implementation of a stream using asyncio queues.

    This stream implementation uses an asyncio queue to buffer items
    and supports backpressure by limiting the queue size.
    """

    def __init__(self, name: str = None, max_size: int = 100) -> None:
        """
        Initialize the stream.

        Args:
            name: Optional name for the stream
            max_size: Maximum number of items that can be buffered
        """
        self.name = name or f"stream-{uuid.uuid4()}"
        self.queue: asyncio.Queue[T] = asyncio.Queue(maxsize=max_size)
        self.state = StreamState.CREATED
        self._closed = False
        self._eof_marker = object()  # Special marker for end of stream

    async def write(self, item: T) -> None:
        """
        Write an item to the stream.

        Args:
            item: The item to write

        Raises:
            StreamClosedError: If the stream is closed
            StreamBackpressureError: If the stream's buffer is full and would block
        """
        if self._closed:
            raise StreamClosedError(f"Cannot write to closed stream {self.name}")

        if self.state != StreamState.OPEN:
            self.state = StreamState.OPEN

        try:
            # Try to put the item in the queue without blocking
            self.queue.put_nowait(item)
        except asyncio.QueueFull:
            # If the queue is full, raise a backpressure error
            raise StreamBackpressureError(f"Stream {self.name} buffer is full")

    async def read(self) -> T:
        """
        Read an item from the stream.

        Returns:
            The next item from the stream

        Raises:
            StreamClosedError: If the stream is closed and no more items are available
        """
        if self._closed and self.queue.empty():
            raise StreamClosedError(f"Stream {self.name} is closed and empty")

        # Get the next item from the queue
        item = await self.queue.get()

        # Check if it's the EOF marker
        if item is self._eof_marker:
            # If we got the EOF marker, put it back for other readers
            # and raise StreamClosedError
            await self.queue.put(self._eof_marker)
            raise StreamClosedError(f"Stream {self.name} is closed and empty")

        # Mark the item as processed
        self.queue.task_done()

        return item

    async def close(self) -> None:
        """
        Close the stream.

        After a stream is closed, no more items can be written to it,
        but existing items can still be read until the stream is empty.
        """
        if self._closed:
            return

        self._closed = True
        self.state = StreamState.CLOSED

        # Put an EOF marker in the queue
        await self.queue.put(self._eof_marker)

    def is_closed(self) -> bool:
        """
        Check if the stream is closed.

        Returns:
            True if the stream is closed, False otherwise
        """
        return self._closed

    async def __aiter__(self) -> AsyncIterator[T]:
        """
        Return an async iterator for the stream.

        This allows using the stream in an async for loop.
        """
        while True:
            try:
                yield await self.read()
            except StreamClosedError:
                break


class StreamProcessor(Generic[T, U], ABC):
    """
    Abstract base class for stream processors.

    A stream processor transforms items from an input stream and
    writes the results to an output stream.
    """

    @abstractmethod
    async def process(self, input_stream: Stream[T], output_stream: Stream[U]) -> None:
        """
        Process items from the input stream and write results to the output stream.

        Args:
            input_stream: The stream to read items from
            output_stream: The stream to write processed items to
        """
        pass


class StreamTransformer(StreamProcessor[T, U]):
    """
    A stream processor that applies a transformation function to each item.
    """

    def __init__(self, transform_func: Callable[[T], U]) -> None:
        """
        Initialize the transformer.

        Args:
            transform_func: Function to apply to each item
        """
        self.transform_func = transform_func

    async def process(self, input_stream: Stream[T], output_stream: Stream[U]) -> None:
        """
        Process items from the input stream and write results to the output stream.

        Args:
            input_stream: The stream to read items from
            output_stream: The stream to write processed items to
        """
        try:
            async for item in input_stream:
                transformed_item = self.transform_func(item)
                await output_stream.write(transformed_item)
        except StreamClosedError:
            # Input stream is closed, close the output stream
            await output_stream.close()
        except Exception as e:
            logger.error(f"Error in stream transformer: {e}")
            # Close the output stream with an error
            await output_stream.close()


class StreamManager:
    """
    Manager for streams in the actor system.

    The StreamManager creates, connects, and manages streams between actors.
    It supports stream creation, connection, and lifecycle management.
    """

    def __init__(self) -> None:
        """Initialize the stream manager."""
        self._streams: Dict[str, Stream] = {}
        self._stream_connections: Dict[str, Set[str]] = {}  # source_actor_id -> {target_actor_id}
        self._actor_streams: Dict[str, Set[str]] = {}  # actor_id -> {stream_id}
        self._processors: Dict[str, asyncio.Task] = {}
        self._event_bus: Optional[EventBus] = None
        self._running = False

    async def start(self) -> None:
        """Start the stream manager."""
        if self._running:
            return

        # Get the event bus
        self._event_bus = get_event_bus()

        # Subscribe to actor lifecycle events
        if self._event_bus:
            await self._event_bus.subscribe(
                "actor.stop.*",
                self._handle_actor_stop
            )

        self._running = True
        logger.info("Stream manager started")

    async def stop(self) -> None:
        """Stop the stream manager."""
        if not self._running:
            return

        self._running = False

        # Close all streams
        for stream_id, stream in list(self._streams.items()):
            try:
                await stream.close()
                logger.debug(f"Closed stream {stream_id}")
            except Exception as e:
                logger.error(f"Error closing stream {stream_id}: {e}")

        # Cancel all processor tasks
        for processor_id, task in list(self._processors.items()):
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                except Exception as e:
                    logger.error(f"Error cancelling processor {processor_id}: {e}")

        # Unsubscribe from events
        if self._event_bus:
            await self._event_bus.unsubscribe("actor.stop.*", self._handle_actor_stop)

        logger.info("Stream manager stopped")

    async def create_stream(self, name: Optional[str] = None,
                           max_size: int = 100) -> Stream:
        """
        Create a new stream.

        Args:
            name: Optional name for the stream
            max_size: Maximum number of items that can be buffered

        Returns:
            The created stream
        """
        stream = AsyncStream(name=name, max_size=max_size)
        stream_id = stream.name
        self._streams[stream_id] = stream
        logger.debug(f"Created stream {stream_id}")
        return stream

    async def register_stream(self, stream: Stream,
                             owner_actor_id: str) -> None:
        """
        Register an existing stream with an actor.

        Args:
            stream: The stream to register
            owner_actor_id: ID of the actor that owns the stream
        """
        stream_id = getattr(stream, 'name', str(id(stream)))
        self._streams[stream_id] = stream

        if owner_actor_id not in self._actor_streams:
            self._actor_streams[owner_actor_id] = set()

        self._actor_streams[owner_actor_id].add(stream_id)
        logger.debug(f"Registered stream {stream_id} with actor {owner_actor_id}")

    async def connect_streams(self, source_stream: Stream, target_stream: Stream,
                             processor: Optional[StreamProcessor] = None) -> str:
        """
        Connect two streams, optionally with a processor.

        Args:
            source_stream: The stream to read from
            target_stream: The stream to write to
            processor: Optional processor to transform items

        Returns:
            ID of the processor task
        """
        # Generate a processor ID
        processor_id = f"processor-{uuid.uuid4()}"

        # Create a task to process the streams
        if processor:
            task = asyncio.create_task(
                processor.process(source_stream, target_stream)
            )
        else:
            # If no processor is provided, just copy items
            async def copy_items():
                try:
                    async for item in source_stream:
                        await target_stream.write(item)
                except StreamClosedError:
                    # Source stream is closed, close the target stream
                    await target_stream.close()
                except Exception as e:
                    logger.error(f"Error in stream connector: {e}")
                    # Close the target stream with an error
                    await target_stream.close()

            task = asyncio.create_task(copy_items())

        # Store the task
        self._processors[processor_id] = task

        # Set up a callback to remove the task when it's done
        def on_task_done(task):
            try:
                # Check if the task raised an exception
                if task.exception():
                    logger.error(f"Processor {processor_id} failed: {task.exception()}")
            except asyncio.CancelledError:
                # Task was cancelled, this is expected during shutdown
                pass
            except Exception as e:
                logger.error(f"Error handling processor completion: {e}")
            finally:
                # Remove the task from the processors dict
                self._processors.pop(processor_id, None)

        task.add_done_callback(on_task_done)

        logger.debug(f"Connected streams with processor {processor_id}")
        return processor_id

    async def disconnect_streams(self, processor_id: str) -> None:
        """
        Disconnect streams by cancelling the processor task.

        Args:
            processor_id: ID of the processor task to cancel
        """
        task = self._processors.get(processor_id)
        if not task:
            logger.warning(f"Processor {processor_id} not found")
            return

        if not task.done():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            except Exception as e:
                logger.error(f"Error cancelling processor {processor_id}: {e}")

        self._processors.pop(processor_id, None)
        logger.debug(f"Disconnected streams with processor {processor_id}")

    async def close_stream(self, stream_id: str) -> None:
        """
        Close a stream.

        Args:
            stream_id: ID of the stream to close
        """
        stream = self._streams.get(stream_id)
        if not stream:
            logger.warning(f"Stream {stream_id} not found")
            return

        await stream.close()
        self._streams.pop(stream_id, None)

        # Remove the stream from actor_streams
        for actor_id, streams in list(self._actor_streams.items()):
            if stream_id in streams:
                streams.remove(stream_id)
                if not streams:
                    self._actor_streams.pop(actor_id, None)

        logger.debug(f"Closed stream {stream_id}")

    async def _handle_actor_stop(self, event: Dict) -> None:
        """
        Handle an actor stop event.

        Args:
            event: The stop event
        """
        data = event.get("data", {})
        actor_id = data.get("actor_id")

        if not actor_id:
            return

        # Close all streams owned by the actor
        streams = self._actor_streams.get(actor_id, set())
        for stream_id in list(streams):
            await self.close_stream(stream_id)

        # Remove the actor from actor_streams
        self._actor_streams.pop(actor_id, None)

        logger.debug(f"Cleaned up streams for actor {actor_id}")
