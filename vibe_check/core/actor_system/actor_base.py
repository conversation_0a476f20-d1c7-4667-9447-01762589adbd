"""
Actor Base Module
================

This module defines the abstract base class for actors in the system.
It establishes the core interface that all actors must implement.

The ActorBase class defines the fundamental operations that all actors
must support, including initialization, starting, stopping, and message
passing.
"""

from abc import ABC, abstractmethod
from typing import Any, Awaitable, Dict, Optional, Set

from .context_wave import ContextWave
from .message import Message, MessageType


class ActorBase(ABC):
    """
    Abstract base class for actors in the system.
    
    This class defines the core interface that all actors must implement.
    It establishes the contract for the two-phase initialization process,
    message passing, and lifecycle management.
    
    Attributes:
        actor_id: Unique identifier for the actor
        actor_type: Type of the actor for discovery
        tags: Set of tags for discovery
        capabilities: Set of capabilities for discovery
        supervisor_id: ID of the supervisor actor (if any)
        is_running: Flag indicating if the actor is running
    """
    
    @property
    @abstractmethod
    def actor_id(self) -> str:
        """Get the actor's unique identifier."""
        pass
    
    @property
    @abstractmethod
    def actor_type(self) -> str:
        """Get the actor's type."""
        pass
    
    @property
    @abstractmethod
    def tags(self) -> Set[str]:
        """Get the actor's tags."""
        pass
    
    @property
    @abstractmethod
    def capabilities(self) -> Set[str]:
        """Get the actor's capabilities."""
        pass
    
    @property
    @abstractmethod
    def supervisor_id(self) -> Optional[str]:
        """Get the actor's supervisor ID (if any)."""
        pass
    
    @property
    @abstractmethod
    def is_running(self) -> bool:
        """Check if the actor is running."""
        pass
    
    @abstractmethod
    async def initialize(self) -> None:
        """
        Initialize the actor (first phase of two-phase initialization).
        
        This method prepares the actor for starting but doesn't begin processing messages.
        It's part of the two-phase initialization process to ensure all actors are
        properly initialized before they start communicating.
        
        Raises:
            ActorInitializationError: If initialization fails
        """
        pass
    
    @abstractmethod
    async def start(self) -> None:
        """
        Start the actor (second phase of two-phase initialization).
        
        This method starts the actor's message processing loop and other background tasks.
        It should only be called after initialize() has completed successfully.
        
        Raises:
            ActorInitializationError: If starting fails or if initialize() was not called
        """
        pass
    
    @abstractmethod
    async def stop(self) -> None:
        """
        Stop the actor.
        
        This method stops the actor's message processing loop and other background tasks.
        It also performs cleanup operations such as saving state and unregistering from
        the registry.
        """
        pass
    
    @abstractmethod
    async def receive(self, message: Message) -> None:
        """
        Receive a message from another actor.
        
        Args:
            message: The message to receive
        """
        pass
    
    @abstractmethod
    async def send(self, recipient_id: str, msg_type: MessageType,
                  payload: Dict[str, Any], context: Optional[ContextWave] = None,
                  priority: int = 0, ttl: Optional[int] = None) -> None:
        """
        Send a message to another actor.
        
        Args:
            recipient_id: ID of the recipient actor
            msg_type: Type of the message
            payload: Message payload
            context: Optional context to propagate
            priority: Optional priority for the message (0-9, higher is more important)
            ttl: Optional time-to-live in seconds
        """
        pass
    
    @abstractmethod
    async def subscribe(self, stream_id: str) -> None:
        """
        Subscribe to a stream.
        
        Args:
            stream_id: ID of the stream to subscribe to
        """
        pass
    
    @abstractmethod
    async def unsubscribe(self, stream_id: str) -> None:
        """
        Unsubscribe from a stream.
        
        Args:
            stream_id: ID of the stream to unsubscribe from
        """
        pass
    
    @abstractmethod
    async def publish(self, stream_id: str, message_type: MessageType, 
                     payload: Dict[str, Any]) -> None:
        """
        Publish a message to a stream.
        
        Args:
            stream_id: ID of the stream to publish to
            message_type: Type of the message to publish
            payload: Message payload
        """
        pass
