"""
Actor State Machine for the Vibe Check Actor System.

This module provides a formal state machine implementation for managing actor lifecycle
states and transitions. It ensures that actors follow valid state transitions and
provides hooks for state-specific actions.

The state machine is a core component of the improved actor system, addressing issues
with improper state transitions and providing a more robust foundation for actor
lifecycle management.
"""

import asyncio
import logging
import time
from enum import Enum
from typing import Any, Callable, Dict, Optional, Set, Tuple, Union, cast

from vibe_check.core.actor_system.actor_state import ActorState

# Configure logging
logger = logging.getLogger(__name__)


class InvalidStateTransitionError(Exception):
    """Exception raised when an invalid state transition is attempted."""

    def __init__(self, current_state: ActorState, target_state: ActorState, actor_id: str):
        """
        Initialize the exception.

        Args:
            current_state: The current state of the actor
            target_state: The target state that was attempted
            actor_id: The ID of the actor
        """
        self.current_state = current_state
        self.target_state = target_state
        self.actor_id = actor_id
        message = (
            f"Invalid state transition for actor {actor_id}: "
            f"{current_state.value} -> {target_state.value}"
        )
        super().__init__(message)


class StateTransitionContext:
    """Context information for a state transition."""

    def __init__(
        self,
        actor_id: str,
        old_state: ActorState,
        new_state: ActorState,
        phase: Optional[str] = None,
        error: Optional[Exception] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """
        Initialize the state transition context.

        Args:
            actor_id: The ID of the actor
            old_state: The previous state of the actor
            new_state: The new state of the actor
            phase: Optional phase name (e.g., "initialize", "start")
            error: Optional exception that caused a state change to FAILED
            metadata: Optional additional metadata for the transition
        """
        self.actor_id = actor_id
        self.old_state = old_state
        self.new_state = new_state
        self.phase = phase or "unknown"
        self.error = error
        self.metadata = metadata or {}
        self.timestamp = time.time()


class ActorStateMachine:
    """
    A formal state machine for managing actor lifecycle states and transitions.

    This class ensures that actors follow valid state transitions and provides
    hooks for state-specific actions. It is a core component of the improved
    actor system, addressing issues with improper state transitions.

    Attributes:
        actor_id: The ID of the actor this state machine belongs to
        state: The current state of the actor
        transitions: A dictionary mapping current states to sets of valid target states
        state_entry_hooks: A dictionary mapping states to callbacks executed when entering that state
        state_exit_hooks: A dictionary mapping states to callbacks executed when exiting that state
        transition_hooks: A dictionary mapping (old_state, new_state) tuples to transition callbacks
        last_transition_time: The timestamp of the last state transition
        last_transition_context: The context of the last state transition
    """

    def __init__(self, actor_id: str, initial_state: ActorState = ActorState.CREATED):
        """
        Initialize the actor state machine.

        Args:
            actor_id: The ID of the actor this state machine belongs to
            initial_state: The initial state of the actor (default: CREATED)
        """
        self.actor_id = actor_id
        self.state = initial_state
        self.last_transition_time: Optional[float] = None
        self.last_transition_context: Optional[StateTransitionContext] = None
        
        # Define valid state transitions
        self.transitions: Dict[ActorState, Set[ActorState]] = {
            ActorState.CREATED: {ActorState.INITIALIZING, ActorState.FAILED, ActorState.STOPPED},
            ActorState.INITIALIZING: {ActorState.INITIALIZED, ActorState.FAILED, ActorState.ROLLBACK, ActorState.STOPPED},
            ActorState.INITIALIZED: {ActorState.STARTING, ActorState.FAILED, ActorState.ROLLBACK, ActorState.STOPPING, ActorState.STOPPED},
            ActorState.STARTING: {ActorState.READY, ActorState.FAILED, ActorState.ROLLBACK, ActorState.STOPPING, ActorState.STOPPED},
            ActorState.READY: {ActorState.FAILED, ActorState.STOPPING, ActorState.STOPPED},
            ActorState.STOPPING: {ActorState.STOPPED, ActorState.FAILED},
            ActorState.STOPPED: set(),  # Terminal state
            ActorState.FAILED: {ActorState.ROLLBACK, ActorState.STOPPED},  # Can only go to ROLLBACK or STOPPED
            ActorState.ROLLBACK: {ActorState.CREATED, ActorState.FAILED, ActorState.STOPPED},  # Can restart or fail
        }
        
        # Initialize hooks
        self.state_entry_hooks: Dict[ActorState, Set[Callable[[StateTransitionContext], Any]]] = {
            state: set() for state in ActorState
        }
        self.state_exit_hooks: Dict[ActorState, Set[Callable[[StateTransitionContext], Any]]] = {
            state: set() for state in ActorState
        }
        self.transition_hooks: Dict[Tuple[ActorState, ActorState], Set[Callable[[StateTransitionContext], Any]]] = {}
        
        logger.debug(f"Initialized state machine for actor {actor_id} in state {initial_state.value}")

    def add_state_entry_hook(self, state: ActorState, hook: Callable[[StateTransitionContext], Any]) -> None:
        """
        Add a hook to be called when entering a specific state.

        Args:
            state: The state to hook into
            hook: The callback function to call when entering the state
        """
        self.state_entry_hooks[state].add(hook)
        
    def add_state_exit_hook(self, state: ActorState, hook: Callable[[StateTransitionContext], Any]) -> None:
        """
        Add a hook to be called when exiting a specific state.

        Args:
            state: The state to hook into
            hook: The callback function to call when exiting the state
        """
        self.state_exit_hooks[state].add(hook)
        
    def add_transition_hook(
        self, 
        from_state: ActorState, 
        to_state: ActorState, 
        hook: Callable[[StateTransitionContext], Any]
    ) -> None:
        """
        Add a hook to be called during a specific state transition.

        Args:
            from_state: The source state of the transition
            to_state: The target state of the transition
            hook: The callback function to call during the transition
        """
        transition_key = (from_state, to_state)
        if transition_key not in self.transition_hooks:
            self.transition_hooks[transition_key] = set()
        self.transition_hooks[transition_key].add(hook)

    def can_transition_to(self, target_state: ActorState) -> bool:
        """
        Check if a transition to the target state is valid from the current state.

        Args:
            target_state: The target state to check

        Returns:
            True if the transition is valid, False otherwise
        """
        # Always allow transitions to FAILED or STOPPED for error handling
        if target_state in {ActorState.FAILED, ActorState.STOPPED}:
            return True
            
        return target_state in self.transitions.get(self.state, set())

    async def transition_to(
        self, 
        target_state: ActorState, 
        phase: Optional[str] = None,
        error: Optional[Exception] = None,
        metadata: Optional[Dict[str, Any]] = None,
        force: bool = False
    ) -> bool:
        """
        Transition the actor to a new state.

        Args:
            target_state: The target state to transition to
            phase: Optional phase name (e.g., "initialize", "start")
            error: Optional exception that caused a state change to FAILED
            metadata: Optional additional metadata for the transition
            force: If True, allow invalid transitions (use with caution)

        Returns:
            True if the transition was successful, False otherwise

        Raises:
            InvalidStateTransitionError: If the transition is invalid and not forced
        """
        # Check if we're already in the target state
        if self.state == target_state:
            logger.debug(f"Actor {self.actor_id} is already in state {target_state.value}, no transition needed")
            return True
            
        # Check if the transition is valid
        if not self.can_transition_to(target_state) and not force:
            error_msg = f"Invalid state transition for actor {self.actor_id}: {self.state.value} -> {target_state.value}"
            logger.error(error_msg)
            raise InvalidStateTransitionError(self.state, target_state, self.actor_id)
            
        # Create transition context
        old_state = self.state
        context = StateTransitionContext(
            actor_id=self.actor_id,
            old_state=old_state,
            new_state=target_state,
            phase=phase,
            error=error,
            metadata=metadata or {}
        )
        
        # Execute exit hooks for the current state
        for hook in self.state_exit_hooks.get(old_state, set()):
            try:
                result = hook(context)
                if asyncio.iscoroutine(result):
                    await result
            except Exception as e:
                logger.error(f"Error in exit hook for state {old_state.value}: {e}")
                
        # Execute transition hooks
        transition_key = (old_state, target_state)
        for hook in self.transition_hooks.get(transition_key, set()):
            try:
                result = hook(context)
                if asyncio.iscoroutine(result):
                    await result
            except Exception as e:
                logger.error(f"Error in transition hook for {old_state.value} -> {target_state.value}: {e}")
                
        # Update state
        self.state = target_state
        self.last_transition_time = time.time()
        self.last_transition_context = context
        
        logger.info(f"Actor {self.actor_id} transitioned from {old_state.value} to {target_state.value}")
        
        # Execute entry hooks for the new state
        for hook in self.state_entry_hooks.get(target_state, set()):
            try:
                result = hook(context)
                if asyncio.iscoroutine(result):
                    await result
            except Exception as e:
                logger.error(f"Error in entry hook for state {target_state.value}: {e}")
                
        return True
        
    def get_state(self) -> ActorState:
        """
        Get the current state of the actor.

        Returns:
            The current actor state
        """
        return self.state
        
    def get_last_transition_time(self) -> Optional[float]:
        """
        Get the timestamp of the last state transition.

        Returns:
            The timestamp of the last transition, or None if no transition has occurred
        """
        return self.last_transition_time
        
    def get_last_transition_context(self) -> Optional[StateTransitionContext]:
        """
        Get the context of the last state transition.

        Returns:
            The context of the last transition, or None if no transition has occurred
        """
        return self.last_transition_context
        
    def is_in_terminal_state(self) -> bool:
        """
        Check if the actor is in a terminal state (STOPPED or FAILED).

        Returns:
            True if the actor is in a terminal state, False otherwise
        """
        return self.state in {ActorState.STOPPED, ActorState.FAILED}
        
    def is_active(self) -> bool:
        """
        Check if the actor is in an active state (READY).

        Returns:
            True if the actor is active, False otherwise
        """
        return self.state == ActorState.READY
