"""
Actor State Module
=================

This module defines the ActorState enum, which represents the possible states
of an actor in the actor system.
"""

import enum
from typing import Set


class ActorState(enum.Enum):
    """
    Enum representing the possible states of an actor.

    States:
        CREATED: Actor has been created but not initialized
        INITIALIZING: Actor is being initialized
        INITIALIZED: Actor has been initialized but not started
        STARTING: Actor is being started
        READY: Actor is ready to receive messages
        STOPPING: Actor is being stopped
        STOPPED: Actor has been stopped
        FAILED: Actor has failed to initialize or start
        ROLLBACK: Actor is in the process of rolling back initialization
    """
    CREATED = "created"
    INITIALIZING = "initializing"
    INITIALIZED = "initialized"
    STARTING = "starting"
    READY = "ready"
    STOPPING = "stopping"
    STOPPED = "stopped"
    FAILED = "failed"
    ROLLBACK = "rollback"


# Define valid state transitions
VALID_STATE_TRANSITIONS = {
    ActorState.CREATED: {ActorState.INITIALIZING, ActorState.FAILED, ActorState.ROLLBACK, ActorState.STOPPING},
    ActorState.INITIALIZING: {ActorState.INITIALIZED, ActorState.FAILED, ActorState.ROLLBACK, ActorState.STOPPING},
    ActorState.INITIALIZED: {ActorState.STARTING, ActorState.FAILED, ActorState.ROLLBACK, ActorState.STOPPING},
    ActorState.STARTING: {ActorState.READY, ActorState.FAILED, ActorState.ROLLBACK, ActorState.STOPPING},
    ActorState.READY: {ActorState.STOPPING, ActorState.FAILED},
    ActorState.STOPPING: {ActorState.STOPPED, ActorState.FAILED},
    ActorState.STOPPED: {ActorState.CREATED, ActorState.INITIALIZING},  # Allow restart or direct initialization
    ActorState.FAILED: {ActorState.ROLLBACK, ActorState.STOPPED, ActorState.CREATED},  # Allow direct reset
    ActorState.ROLLBACK: {ActorState.STOPPED, ActorState.CREATED}  # Allow direct reset
}


def get_valid_transitions(state: ActorState) -> Set[ActorState]:
    """
    Get the valid state transitions for a given state.

    Args:
        state: Current actor state

    Returns:
        Set of valid next states
    """
    return VALID_STATE_TRANSITIONS.get(state, set())
