"""
Actor Stream Management Module
=============================

This module provides stream management functionality for actors,
including subscription management, publishing, and stream data handling.
"""

import logging
import time
from typing import Any, Dict, Optional, Set, TYPE_CHECKING

from ..context_wave import ContextWave
from ..message import MessageType

if TYPE_CHECKING:
    from ..actor import Actor

logger = logging.getLogger("vibe_check_actor_streaming")


class ActorStreamManager:
    """
    Manages stream subscriptions and publishing for actors.
    
    This class handles stream subscriptions, publishing messages to streams,
    and processing stream data.
    """

    def __init__(self, actor: 'Actor'):
        """
        Initialize the actor stream manager.

        Args:
            actor: The actor instance that owns this stream manager
        """
        self.actor = actor
        self._subscriptions: Set[str] = set()

    async def subscribe(self, stream_id: str) -> None:
        """
        Subscribe to a stream.

        Args:
            stream_id: ID of the stream to subscribe to
        """
        try:
            # Use the stream bus to subscribe
            from ..stream_bus import get_stream_bus
            stream_bus = get_stream_bus()
            await stream_bus.subscribe(self.actor.actor_id, stream_id)

            # Add to local subscriptions
            self._subscriptions.add(stream_id)
            logger.info(f"Actor {self.actor.actor_id} subscribed to stream {stream_id}")
        except (ImportError, AttributeError) as e:
            logger.error(f"Failed to subscribe to stream {stream_id}: {e}")

    async def unsubscribe(self, stream_id: str) -> None:
        """
        Unsubscribe from a stream.

        Args:
            stream_id: ID of the stream to unsubscribe from
        """
        try:
            # Use the stream bus to unsubscribe
            from ..stream_bus import get_stream_bus
            stream_bus = get_stream_bus()
            await stream_bus.unsubscribe(self.actor.actor_id, stream_id)

            # Remove from local subscriptions
            self._subscriptions.discard(stream_id)
            logger.info(f"Actor {self.actor.actor_id} unsubscribed from stream {stream_id}")
        except (ImportError, AttributeError) as e:
            logger.error(f"Failed to unsubscribe from stream {stream_id}: {e}")

    async def publish(self, stream_id: str, message_type: MessageType, payload: Optional[Dict[str, Any]] = None) -> None:
        """
        Publish a message to a stream.

        Args:
            stream_id: ID of the stream to publish to
            message_type: Type of the message
            payload: Optional message payload
        """
        try:
            # Use the stream bus to publish
            from ..stream_bus import get_stream_bus
            stream_bus = get_stream_bus()

            # Create stream message
            stream_message = {
                "sender_id": self.actor.actor_id,
                "message_type": message_type.name,
                "payload": payload or {},
                "timestamp": time.time()
            }

            await stream_bus.publish(stream_id, stream_message)
            logger.debug(f"Actor {self.actor.actor_id} published {message_type.name} to stream {stream_id}")
        except (ImportError, AttributeError) as e:
            logger.error(f"Failed to publish to stream {stream_id}: {e}")
        except Exception as e:
            logger.error(f"Unexpected error publishing to stream {stream_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def handle_stream_data(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle data from a stream.

        Args:
            payload: Stream data payload
            context: Message context
        """
        stream_id = payload.get("stream_id")
        if not stream_id:
            logger.warning("Received stream data without stream_id")
            return

        # Extract stream data
        stream_data = payload.get("data", {})
        sender_id = payload.get("sender_id", "unknown")
        message_type = payload.get("message_type", "unknown")
        timestamp = payload.get("timestamp", 0)

        logger.debug(f"Actor {self.actor.actor_id} received data from stream {stream_id}")

        # Process stream data based on stream_id
        # Override this method in subclasses to handle specific streams

    async def handle_subscribe(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a subscription request.

        Args:
            payload: Subscription payload
            context: Message context
        """
        stream_id = payload.get("stream_id")
        if not stream_id:
            logger.warning("Received subscribe message without stream_id")
            return

        await self.subscribe(stream_id)

    async def handle_unsubscribe(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle an unsubscription request.

        Args:
            payload: Unsubscription payload
            context: Message context
        """
        stream_id = payload.get("stream_id")
        if not stream_id:
            logger.warning("Received unsubscribe message without stream_id")
            return

        await self.unsubscribe(stream_id)

    async def handle_publish(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a publish request.

        Args:
            payload: Publish payload
            context: Message context
        """
        stream_id = payload.get("stream_id")
        if not stream_id:
            logger.warning("Received publish message without stream_id")
            return

        # Extract message type and payload
        message_type_name = payload.get("message_type", "DATA")
        stream_payload = payload.get("payload", {})

        # Convert message type name to MessageType enum
        try:
            message_type = MessageType[message_type_name.upper()]
        except KeyError:
            logger.warning(f"Unknown message type: {message_type_name}")
            return

        await self.publish(stream_id, message_type, stream_payload)

    async def cleanup_streams(self) -> None:
        """Clean up all stream subscriptions."""
        try:
            # Unsubscribe from all streams
            for stream_id in self._subscriptions.copy():
                await self.unsubscribe(stream_id)

            logger.debug(f"Cleaned up stream subscriptions for actor {self.actor.actor_id}")
        except Exception as e:
            logger.error(f"Error cleaning up streams for actor {self.actor.actor_id}: {e}")

    @property
    def subscriptions(self) -> Set[str]:
        """Get the set of stream subscriptions."""
        return self._subscriptions.copy()

    @property
    def is_subscribed_to_streams(self) -> bool:
        """Check if this actor is subscribed to any streams."""
        return len(self._subscriptions) > 0

    def is_subscribed_to(self, stream_id: str) -> bool:
        """
        Check if this actor is subscribed to a specific stream.

        Args:
            stream_id: ID of the stream to check

        Returns:
            True if subscribed to the stream, False otherwise
        """
        return stream_id in self._subscriptions
