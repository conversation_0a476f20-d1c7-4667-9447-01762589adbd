"""
Error Handler Module
==================

This module defines the ErrorHandler class, which is responsible for
handling errors that occur during message processing in the actor system.
"""

import logging
import time
import traceback
from typing import Any, Callable, Dict

from ..context_wave import ContextWave
from ..message import MessageType

# Configure logging
logger = logging.getLogger("vibe_check_actor_system")


class ErrorHandler:
    """
    Handles errors during message processing.
    
    This class encapsulates the error handling logic that was previously
    in the Actor._handle_message method. It is responsible for:
    - Updating error metrics
    - Logging error details
    - Sending error messages to the sender
    - Notifying the supervisor of errors
    
    Implementation:
        This class uses composition to break down the complex error handling
        logic into smaller, more focused methods. It provides a clean interface
        for handling different types of errors during message processing.
    """
    
    def __init__(self, actor: Any):
        """
        Initialize the error handler.
        
        Args:
            actor: The actor that owns this handler
        """
        self.actor = actor
        
    async def handle_handler_exception(self, exception: Exception, handler: Callable, 
                                      message_type: MessageType, context: ContextWave,
                                      start_time: float) -> None:
        """
        Handle an exception that occurred during message processing.
        
        Args:
            exception: The exception that was raised
            handler: The handler that raised the exception
            message_type: The type of message being processed
            context: The message context
            start_time: The time when message processing started
        """
        # Update error metrics
        self.actor._metrics["errors"] += 1
        # Calculate processing time for logging purposes
        _ = time.time() - start_time  # Time spent before the error occurred

        # Log the error with more details
        error_details = traceback.format_exc()
        logger.error(f"Error in {self.actor.actor_id}.{handler.__name__}: {exception}\n{error_details}")

        # Get sender ID from context if available
        sender_id = context.metadata.get("sender_id")
        
        # Send error message to sender if possible
        if sender_id:
            await self._send_error_to_sender(exception, error_details, message_type, handler, sender_id, context)
            
        # Notify supervisor if available and different from sender
        if self.actor.supervisor_id and self.actor.supervisor_id != sender_id:
            await self._notify_supervisor_of_error(exception, error_details, message_type, handler)
            
    async def _send_error_to_sender(self, exception: Exception, error_details: str, 
                                   message_type: MessageType, handler: Callable,
                                   sender_id: str, original_context: ContextWave) -> None:
        """
        Send an error message back to the sender.
        
        Args:
            exception: The exception that was raised
            error_details: The formatted traceback
            message_type: The type of message being processed
            handler: The handler that raised the exception
            sender_id: The ID of the sender
            original_context: The original message context
        """
        try:
            # Create a new context by propagating the original
            error_context = original_context.propagate()
            
            error_payload = {
                "error": str(exception),
                "error_details": error_details,
                "original_message_type": message_type.name,
                "handler": handler.__name__,
                "actor_id": self.actor.actor_id,
                "timestamp": time.time()
            }
            await self.actor.send(sender_id, MessageType.ERROR, error_payload, error_context)
        except Exception as send_error:
            logger.error(f"Failed to send error message: {send_error}")
            
    async def _notify_supervisor_of_error(self, exception: Exception, error_details: str,
                                         message_type: MessageType, handler: Callable) -> None:
        """
        Notify the supervisor of an error.
        
        Args:
            exception: The exception that was raised
            error_details: The formatted traceback
            message_type: The type of message being processed
            handler: The handler that raised the exception
        """
        try:
            supervisor_context = ContextWave()
            supervisor_context.metadata["sender_id"] = self.actor.actor_id
            
            error_payload = {
                "error": str(exception),
                "error_details": error_details,
                "original_message_type": message_type.name,
                "handler": handler.__name__,
                "actor_id": self.actor.actor_id,
                "timestamp": time.time(),
                "fatal": False  # Most errors are non-fatal
            }
            await self.actor.send(self.actor.supervisor_id, MessageType.ERROR, error_payload, supervisor_context)
        except Exception as send_error:
            logger.error(f"Failed to notify supervisor of error: {send_error}")
