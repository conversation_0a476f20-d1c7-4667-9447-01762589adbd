"""
Unknown Message Handler Module
===========================

This module defines the UnknownMessageHandler class, which is responsible for
handling messages with unknown types in the actor system.
"""

import logging
import time
from typing import Any, Dict

from ..context_wave import ContextWave
from ..message import Message, MessageType

# Configure logging
logger = logging.getLogger("vibe_check_actor_system")


class UnknownMessageHandler:
    """
    Handles messages with unknown types.
    
    This class encapsulates the logic for handling messages with unknown types
    that was previously in the Actor._handle_message method. It is responsible for:
    - Handling messages explicitly marked as unknown
    - Handling known message types that don't have an implemented handler
    - Delegating to default handlers for common message types
    
    Implementation:
        This class uses composition to break down the complex unknown message handling
        logic into smaller, more focused methods. It provides a clean interface
        for handling different types of unknown messages.
    """
    
    def __init__(self, actor: Any):
        """
        Initialize the unknown message handler.
        
        Args:
            actor: The actor that owns this handler
        """
        self.actor = actor
        
        # Import default handlers
        from .default_handlers import DefaultHandlers
        
        # Create default handlers
        self.default_handlers = DefaultHandlers(actor)
        
    async def handle_unknown_message_type(self, message: Message, message_type: MessageType, 
                                         context: ContextWave) -> None:
        """
        Handle a message with an unknown type.
        
        Args:
            message: The original message
            message_type: The type of message
            context: The message context
        """
        if message_type == MessageType.UNKNOWN:
            await self._handle_explicit_unknown_message(message)
        else:
            await self._handle_unimplemented_message_type(message, message_type, context)
            
    async def _handle_explicit_unknown_message(self, message: Message) -> None:
        """
        Handle a message explicitly marked as unknown.
        
        Args:
            message: The original message
        """
        # This is an unknown message type, log an error
        logger.error(f"Actor {self.actor.actor_id} received error: Unknown message type (from message type: Unknown)")

        # Try to extract original message type from payload for debugging
        original_type = self._extract_original_message_type(message.payload)
        logger.error(f"Original message type might be: {original_type}")

        # Notify supervisor with more details
        if self.actor.supervisor_id:
            await self._notify_supervisor_of_unknown_message(original_type, message.payload)
            
    def _extract_original_message_type(self, payload: Any) -> str:
        """
        Extract the original message type from a payload.
        
        Args:
            payload: The message payload
            
        Returns:
            The extracted message type or "Unknown"
        """
        original_type = "Unknown"
        if isinstance(payload, dict):
            if "original_message_type" in payload:
                original_type = payload["original_message_type"]
            elif "type" in payload:
                original_type = payload["type"]
        return original_type
        
    async def _notify_supervisor_of_unknown_message(self, original_type: str, payload: Any) -> None:
        """
        Notify the supervisor of an unknown message.
        
        Args:
            original_type: The extracted original message type
            payload: The message payload
        """
        try:
            error_context = ContextWave()
            error_context.metadata["sender_id"] = self.actor.actor_id
            
            error_payload = {
                "error": f"Unknown message type: {original_type}",
                "actor_id": self.actor.actor_id,
                "message_payload": str(payload)[:200],  # Truncate for log size
                "timestamp": time.time()
            }
            await self.actor.send(self.actor.supervisor_id, MessageType.ERROR, error_payload, error_context)
        except Exception as send_error:
            logger.error(f"Failed to notify supervisor of error: {send_error}")
            
    async def _handle_unimplemented_message_type(self, message: Message, message_type: MessageType,
                                               context: ContextWave) -> None:
        """
        Handle a known message type that doesn't have an implemented handler.
        
        Args:
            message: The original message
            message_type: The type of message
            context: The message context
        """
        # This is a known message type but we don't have a handler
        logger.warning(f"Actor {self.actor.actor_id} received unknown message type: {message_type.name}")

        # Handle common message types with default behavior
        if message_type == MessageType.HEARTBEAT:
            await self.default_handlers.handle_default_heartbeat(message, context)
        elif message_type == MessageType.METRICS:
            await self.default_handlers.handle_default_metrics(message, context)
        else:
            # Fall back to handle_unknown for other message types
            await self.default_handlers.handle_default_unknown(message, message_type, context)
