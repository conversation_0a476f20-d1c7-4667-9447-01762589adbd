"""
Message Handling Package
=======================

This package contains modules for handling different aspects of message processing
in the actor system. It implements the CAW principle of adaptive message handling
based on context.

Modules:
- handler.py: Base message handler functionality
- unknown_message_handler.py: Handling for unknown message types
- error_handler.py: Error handling during message processing
- default_handlers.py: Default handlers for common message types
"""

from .default_handlers import DefaultHandlers
from .error_handler import <PERSON>rror<PERSON>andler
from .handler import MessageHandler
from .unknown_message_handler import UnknownMessageHandler

__all__ = [
    'MessageHandler',
    'UnknownMessageHandler',
    '<PERSON><PERSON>r<PERSON>and<PERSON>',
    'DefaultHandlers',
]
