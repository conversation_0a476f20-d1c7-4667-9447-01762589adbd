"""
Message Handler Module
====================

This module defines the MessageHandler class, which is responsible for
handling messages in the actor system. It implements the core message
handling logic that was previously in the Actor._handle_message method.

The MessageHandler implements the MessageHandlerProtocol and provides methods
for handling messages, executing handlers, and updating metrics.
"""

import logging
import time
import traceback
from typing import Any, Awaitable, Callable, Dict, Optional, TYPE_CHECKING

from ..context_wave import ContextWave
from ..message import Message, MessageType
from ..protocols import MessageHandlerProtocol

# Avoid circular imports
if TYPE_CHECKING:
    from ..actor import Actor
    from .unknown_message_handler import Unknown<PERSON>essage<PERSON>and<PERSON>
    from .error_handler import Error<PERSON>andler
    from .default_handlers import Default<PERSON>andlers

# Configure logging
logger = logging.getLogger("vibe_check_actor_system")


class MessageHandler(MessageHandlerProtocol):
    """
    Handles message processing for an actor.

    This class encapsulates the message handling logic that was previously
    in the Actor._handle_message method. It is responsible for:
    - Executing the appropriate handler for a message
    - Updating metrics for message processing
    - Delegating to specialized handlers for unknown message types

    Attributes:
        actor: The actor that owns this handler
        logger: Logger for this handler
        unknown_message_handler: Handler for unknown message types
        error_handler: Handler for errors during message processing
        default_handlers: Default handlers for common message types

    Implementation:
        This class uses composition to break down the complex message handling
        logic into smaller, more focused components. It delegates to specialized
        handlers for different aspects of message handling.
    """

    def __init__(self, actor: 'Actor') -> None:
        """
        Initialize the message handler.

        Args:
            actor: The actor that owns this handler
        """
        self.actor = actor
        self.logger = logging.getLogger("vibe_check_actor_system.message_handler")

        # Import specialized handlers
        from .unknown_message_handler import UnknownMessageHandler
        from .error_handler import ErrorHandler
        from .default_handlers import DefaultHandlers

        # Create specialized handlers
        self.unknown_message_handler: 'UnknownMessageHandler' = UnknownMessageHandler(actor)
        self.error_handler: 'ErrorHandler' = ErrorHandler(actor)
        self.default_handlers: 'DefaultHandlers' = DefaultHandlers(actor)

    async def handle_message(self, message: Message) -> None:
        """
        Handle an incoming message.

        This method is the entry point for message handling. It:
        1. Determines the appropriate handler for the message
        2. Executes the handler
        3. Updates metrics
        4. Handles any errors that occur

        Args:
            message: The message to handle

        Raises:
            Exception: If an unexpected error occurs during message handling
        """
        # Get the message type and handler
        message_type = message.type
        handler = self.actor._message_handlers.get(message_type)

        # Start timing for metrics
        start_time = time.time()

        # Ensure context is not None
        context = message.context or ContextWave()

        try:
            if handler:
                # We have a handler, call it
                self.logger.debug(
                    f"Actor {self.actor.actor_id} handling message of type {message_type.name} "
                    f"from {message.sender_id or 'unknown'}"
                )
                await self._execute_handler(handler, message.payload, context, message_type, start_time)
            else:
                # No handler for this message type
                self.logger.warning(
                    f"Actor {self.actor.actor_id} has no handler for message type {message_type.name} "
                    f"from {message.sender_id or 'unknown'}"
                )
                await self.unknown_message_handler.handle_unknown_message_type(message, message_type, context)

        except Exception as e:
            # This is a catch-all for any unexpected errors in the message handling process
            self.logger.error(f"Unexpected error in handle_message for actor {self.actor.actor_id}: {e}")
            error_details = traceback.format_exc()
            self.logger.error(error_details)

            # Update error metrics
            self.actor._metrics["errors"] += 1

            # Try to notify supervisor if available
            if self.actor.supervisor_id:
                try:
                    error_context = ContextWave()
                    error_context.metadata["sender_id"] = self.actor.actor_id
                    error_payload = {
                        "error": str(e),
                        "error_details": error_details,
                        "actor_id": self.actor.actor_id,
                        "message_type": message_type.name,
                        "timestamp": time.time(),
                        "fatal": False
                    }
                    await self.actor.send(
                        self.actor.supervisor_id,
                        MessageType.ERROR,
                        error_payload,
                        error_context
                    )
                except Exception as send_error:
                    self.logger.error(f"Failed to notify supervisor of error: {send_error}")

            # Re-raise the exception for the caller to handle
            raise

    async def _execute_handler(
        self,
        handler: Callable[[Dict[str, Any], ContextWave], Awaitable[None]],
        payload: Dict[str, Any],
        context: ContextWave,
        message_type: MessageType,
        start_time: float
    ) -> None:
        """
        Execute a message handler and handle any exceptions.

        Args:
            handler: The handler function to call
            payload: The message payload
            context: The message context
            message_type: The type of message being handled
            start_time: The time when message processing started

        Raises:
            Exception: If an error occurs during handler execution
        """
        try:
            # Call the handler
            await handler(payload, context)

            # Update metrics for successful processing
            await self._update_success_metrics(start_time, message_type)

        except Exception as e:
            # Handle exception in message processing
            self.logger.error(
                f"Error in handler for message type {message_type.name} in actor {self.actor.actor_id}: {e}"
            )
            await self.error_handler.handle_handler_exception(e, handler, message_type, context, start_time)

            # Re-raise the exception for the caller to handle
            raise

    async def _update_success_metrics(self, start_time: float, message_type: MessageType) -> None:
        """
        Update metrics after successful message processing.

        Args:
            start_time: The time when message processing started
            message_type: The type of message that was processed
        """
        processing_time = time.time() - start_time
        self.actor._metrics["processing_time"] += processing_time
        self.actor._metrics["messages_processed"] += 1

        if self.actor._metrics["messages_processed"] > 0:
            self.actor._metrics["avg_processing_time"] = (
                self.actor._metrics["processing_time"] / self.actor._metrics["messages_processed"]
            )

        # Log processing time for performance monitoring
        self.logger.debug(
            f"Actor {self.actor.actor_id} processed {message_type.name} in {processing_time:.6f}s "
            f"(avg: {self.actor._metrics['avg_processing_time']:.6f}s)"
        )
