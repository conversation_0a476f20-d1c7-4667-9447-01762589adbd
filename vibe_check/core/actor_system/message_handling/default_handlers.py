"""
Default Handlers Module
=====================

This module defines the DefaultHandlers class, which provides default
handlers for common message types in the actor system.
"""

import logging
import time
import traceback
from typing import Any

from ..context_wave import ContextWave
from ..message import Message, MessageType

# Configure logging
logger = logging.getLogger("vibe_check_actor_system")


class DefaultHandlers:
    """
    Provides default handlers for common message types.

    This class encapsulates the default handling logic for common message types
    that was previously in the Actor._handle_message method. It is responsible for:
    - Handling heartbeat messages
    - Handling metrics messages
    - Handling unknown message types with the default unknown handler

    Implementation:
        This class uses composition to break down the complex default handling
        logic into smaller, more focused methods. It provides a clean interface
        for handling different types of common messages.
    """

    def __init__(self, actor: Any):
        """
        Initialize the default handlers.

        Args:
            actor: The actor that owns these handlers
        """
        self.actor = actor

    async def handle_default_heartbeat(self, message: Message, context: ContextWave) -> None:
        """
        Handle a heartbeat message with default behavior.

        Args:
            message: The original message
            context: The message context
        """
        # Just acknowledge heartbeats
        if message.sender_id:
            try:
                await self.actor.send(message.sender_id, MessageType.HEARTBEAT_ACK, {  # type: ignore[attr-defined]
                    "actor_id": self.actor.actor_id,
                    "timestamp": time.time()
                }, context)
            except Exception as e:
                logger.error(f"Error sending heartbeat acknowledgment: {e}")

    async def handle_default_metrics(self, message: Message, context: ContextWave) -> None:
        """
        Handle a metrics message with default behavior.

        Args:
            message: The original message
            context: The message context
        """
        # Just acknowledge metrics requests
        if message.sender_id:
            try:
                await self.actor.send(message.sender_id, MessageType.METRICS, self.actor._metrics, context)
            except Exception as e:
                logger.error(f"Error sending metrics: {e}")

    async def handle_default_unknown(self, message: Message, message_type: MessageType, 
                                    context: ContextWave) -> None:
        """
        Handle any other unknown message type with the default unknown handler.

        Args:
            message: The original message
            message_type: The type of message
            context: The message context
        """
        try:
            await self.actor.handle_unknown(message.payload, context)
        except Exception as e:
            logger.error(f"Error in handle_unknown for message type {message_type.name}: {e}")
            logger.error(traceback.format_exc())
