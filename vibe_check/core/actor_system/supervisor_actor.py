"""
Supervisor Actor Module
====================

This module defines the SupervisorActor class, which monitors and manages
other actors in the system. It implements the CAW principle of resilience
through supervision and recovery.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional

from .actor import Actor
from .context_wave import ContextWave
from .message import MessageType

logger = logging.getLogger("vibe_check_supervisor")


class SupervisorActor(Actor):
    """
    Supervisor actor for monitoring and managing other actors.

    Implements the CAW principle of resilience through supervision and recovery.
    """

    def __init__(self, actor_id: str, state_dir: Optional[str] = None, actor_type: str = "supervisor"):
        """
        Initialize the supervisor actor.

        Args:
            actor_id: Unique ID for this actor
            state_dir: Optional directory for state persistence
            actor_type: Type of the actor (default: "supervisor")
        """
        super().__init__(
            actor_id=actor_id,
            actor_type=actor_type,
            tags={"supervisor", "system"},
            capabilities={"supervision", "monitoring", "recovery"},
            state_dir=state_dir
        )

        # Supervision state
        # Override the _supervised_actors from the base class
        self._supervised_actors = {}  # type: Dict[str, Dict[str, Any]]
        self._actor_heartbeats: Dict[str, float] = {}
        self._actor_metrics: Dict[str, Dict[str, Any]] = {}
        self._actor_errors: Dict[str, List[Dict[str, Any]]] = {}
        self._actor_restarts: Dict[str, List[float]] = {}

        # Monitoring state
        self._monitoring_task = None  # type: Optional[asyncio.Task]
        self._monitoring_interval = 5.0  # seconds
        self._heartbeat_timeout = 30.0  # seconds
        self._max_restarts = 3  # Maximum number of restarts in restart window
        self._restart_window = 60.0  # seconds

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the supervisor actor (actor-specific initialization).

        Args:
            config: Configuration dictionary for the actor
        """
        # No special initialization needed for supervisor actor
        pass

    async def start(self) -> None:
        """Start the supervisor actor."""
        await super().start()

        # Start monitoring task
        self._monitoring_task = asyncio.create_task(self._monitor_actors())
        self._monitoring_task.add_done_callback(
            lambda t: logger.info(f"Supervisor {self.actor_id} monitoring task completed")
            if not t.cancelled() else
            logger.info(f"Supervisor {self.actor_id} monitoring task was cancelled")
        )

        logger.info(f"Supervisor {self.actor_id} started monitoring")

    async def stop(self) -> None:
        """Stop the supervisor actor."""
        # Cancel monitoring task
        if self._monitoring_task is not None:
            if not self._monitoring_task.done():
                self._monitoring_task.cancel()
                try:
                    await asyncio.wait_for(asyncio.shield(self._monitoring_task), timeout=0.5)
                except (asyncio.TimeoutError, asyncio.CancelledError):
                    pass

        # Stop all supervised actors
        stop_tasks = []
        for actor_id in self._supervised_actors:
            if actor_id in self._known_actors:
                stop_payload = {
                    "supervisor_id": self.actor_id,
                    "timestamp": time.time(),
                    "reason": "supervisor_shutdown"
                }
                stop_tasks.append(self.send(actor_id, MessageType.STOP, stop_payload))

        if stop_tasks:
            await asyncio.gather(*stop_tasks)

        await super().stop()

    async def supervise(self, actor_id: str, actor_type: Optional[str] = None,
                       restart_on_failure: bool = True) -> None:
        """
        Start supervising an actor.

        Enhanced with more comprehensive actor tracking and initialization.

        Args:
            actor_id: ID of the actor to supervise
            actor_type: Optional type of the actor
            restart_on_failure: Whether to restart the actor on failure
        """
        if actor_id in self._supervised_actors:
            logger.warning(f"Already supervising actor {actor_id}")
            return

        # Add to supervised actors with enhanced tracking
        current_time = time.time()
        self._supervised_actors[actor_id] = {
            "actor_id": actor_id,
            "actor_type": actor_type,
            "restart_on_failure": restart_on_failure,
            "supervised_since": current_time,
            "last_heartbeat": current_time,
            "status": "unknown",
            "consecutive_failures": 0,
            "total_restarts": 0,
            "last_restart_time": 0,
            "restart_backoff": 1.0,  # Initial backoff in seconds
            "error_categories": {},   # Track error categories for adaptive strategy
            "last_error": None,
            "last_error_time": 0
        }

        # Initialize heartbeat and metrics
        self._actor_heartbeats[actor_id] = current_time
        self._actor_metrics[actor_id] = {}
        self._actor_errors[actor_id] = []
        self._actor_restarts[actor_id] = []

        # Send supervise message to the actor
        try:
            supervise_payload = {
                "supervisor_id": self.actor_id,
                "timestamp": current_time,
                "heartbeat_interval": self._monitoring_interval,  # Tell actor how often to send heartbeats
                "max_missed_heartbeats": 3  # Tell actor how many heartbeats it can miss before being considered unresponsive
            }
            await self.send(actor_id, MessageType.SUPERVISE, supervise_payload)
            logger.info(f"Started supervising actor {actor_id}")

            # Try to get actor from registry to update its type if needed
            try:
                from .actor_registry import get_registry
                registry = get_registry()
                actor = registry.get_actor(actor_id)
                if actor and not actor_type:
                    self._supervised_actors[actor_id]["actor_type"] = getattr(actor, "actor_type", None)
            except (ImportError, AttributeError) as e:
                logger.debug(f"Could not get actor type from registry: {e}")

        except Exception as e:
            logger.error(f"Failed to start supervising actor {actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def unsupervise(self, actor_id: str) -> None:
        """
        Stop supervising an actor.

        Args:
            actor_id: ID of the actor to stop supervising
        """
        if actor_id not in self._supervised_actors:
            logger.warning(f"Not supervising actor {actor_id}")
            return

        # Remove from supervised actors
        self._supervised_actors.pop(actor_id)
        self._actor_heartbeats.pop(actor_id, None)
        self._actor_metrics.pop(actor_id, None)
        self._actor_errors.pop(actor_id, None)
        self._actor_restarts.pop(actor_id, None)

        # Send message to the actor to notify it's no longer supervised
        try:
            unsupervise_payload = {
                "supervisor_id": self.actor_id,
                "timestamp": time.time(),
                "action": "unsupervise"
            }
            # Use a standard message type since UNSUPERVISE doesn't exist
            await self.send(actor_id, MessageType.SUPERVISE, unsupervise_payload)
            logger.info(f"Stopped supervising actor {actor_id}")
        except Exception as e:
            logger.error(f"Failed to stop supervising actor {actor_id}: {e}")

    async def _monitor_actors(self) -> None:
        """
        Monitor supervised actors.

        Enhanced with more comprehensive heartbeat monitoring and actor health checks.
        """
        try:
            while self.is_running:
                try:
                    # Check heartbeats and actor health
                    current_time = time.time()

                    # Process each supervised actor
                    for actor_id, actor_info in list(self._supervised_actors.items()):
                        # Skip actors that are already in failed state
                        if actor_info["status"] == "failed":
                            continue

                        # Get last heartbeat time
                        last_heartbeat = self._actor_heartbeats.get(actor_id, 0)

                        # Check if heartbeat has timed out
                        heartbeat_age = current_time - last_heartbeat
                        heartbeat_timeout = heartbeat_age > self._heartbeat_timeout

                        if heartbeat_timeout:
                            # Log with actor type if available
                            actor_type = actor_info.get("actor_type", "unknown")
                            logger.warning(f"Actor {actor_id} ({actor_type}) heartbeat timed out "
                                          f"(last heartbeat: {heartbeat_age:.1f}s ago)")

                            # Update status
                            actor_info["status"] = "unresponsive"

                            # Try to restart the actor if configured to do so
                            if actor_info["restart_on_failure"]:
                                # Check if we should try to ping the actor first
                                if actor_info.get("consecutive_failures", 0) == 0:
                                    # First failure, try to ping the actor
                                    try:
                                        # Send a ping message with a short timeout
                                        ping_context = ContextWave()
                                        ping_context.metadata["sender_id"] = self.actor_id
                                        ping_context.metadata["timeout"] = 5.0  # Short timeout

                                        ping_payload = {
                                            "supervisor_id": self.actor_id,
                                            "timestamp": current_time,
                                            "action": "ping"
                                        }

                                        # Use a task with timeout to avoid blocking
                                        ping_task = asyncio.create_task(
                                            self.send(actor_id, MessageType.HEARTBEAT, ping_payload, ping_context)
                                        )

                                        # Wait for ping response with timeout
                                        try:
                                            await asyncio.wait_for(ping_task, timeout=5.0)
                                            logger.info(f"Successfully pinged actor {actor_id}, waiting for response")

                                            # Skip restart for now, we'll check again next cycle
                                            continue
                                        except asyncio.TimeoutError:
                                            logger.warning(f"Ping to actor {actor_id} timed out")
                                            # Fall through to restart
                                    except Exception as ping_error:
                                        logger.warning(f"Error pinging actor {actor_id}: {ping_error}")
                                        # Fall through to restart

                                # Restart the actor
                                await self._restart_actor(actor_id, "heartbeat_timeout")
                        elif actor_info["status"] == "unresponsive":
                            # Actor was unresponsive but has sent a heartbeat
                            logger.info(f"Actor {actor_id} is responsive again")
                            actor_info["status"] = "running"

                    # Wait before checking again
                    await asyncio.sleep(self._monitoring_interval)
                except Exception as e:
                    logger.error(f"Error monitoring actors: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    await asyncio.sleep(self._monitoring_interval)
        except asyncio.CancelledError:
            logger.info(f"Supervisor {self.actor_id} monitoring task cancelled")
        except Exception as e:
            logger.error(f"Fatal error in supervisor {self.actor_id} monitoring task: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def _restart_actor(self, actor_id: str, reason: str) -> None:
        """
        Restart a supervised actor.

        Enhanced with adaptive restart strategy based on failure patterns.

        Args:
            actor_id: ID of the actor to restart
            reason: Reason for restarting
        """
        if actor_id not in self._supervised_actors:
            logger.warning(f"Cannot restart unsupervised actor {actor_id}")
            return

        # Get actor info
        actor_info = self._supervised_actors[actor_id]

        # Update restart tracking
        current_time = time.time()

        # Initialize restart tracking if not present
        if "consecutive_failures" not in actor_info:
            actor_info["consecutive_failures"] = 0
        if "total_restarts" not in actor_info:
            actor_info["total_restarts"] = 0
        if "last_restart_time" not in actor_info:
            actor_info["last_restart_time"] = 0
        if "restart_backoff" not in actor_info:
            actor_info["restart_backoff"] = 1.0  # Initial backoff in seconds
        if "error_categories" not in actor_info:
            actor_info["error_categories"] = {}

        # Track error categories for adaptive strategy
        error_category = reason.split("_")[0] if "_" in reason else reason
        actor_info["error_categories"][error_category] = actor_info["error_categories"].get(error_category, 0) + 1

        # Check if we're in the restart window
        if current_time - actor_info["last_restart_time"] > self._restart_window:
            # Reset consecutive failures if outside window
            actor_info["consecutive_failures"] = 1
            actor_info["restart_backoff"] = 1.0  # Reset backoff
        else:
            # Increment consecutive failures
            actor_info["consecutive_failures"] += 1

            # Exponential backoff with a maximum of 30 seconds
            actor_info["restart_backoff"] = min(30.0, actor_info["restart_backoff"] * 2.0)

        # Update restart tracking
        actor_info["total_restarts"] += 1
        actor_info["last_restart_time"] = current_time

        # Add to restart history
        self._actor_restarts[actor_id].append(current_time)

        # Remove restarts outside the window
        self._actor_restarts[actor_id] = [
            t for t in self._actor_restarts[actor_id]
            if current_time - t <= self._restart_window
        ]

        # Check if we've exceeded the maximum number of restarts
        if actor_info["consecutive_failures"] > self._max_restarts:
            logger.error(
                f"Actor {actor_id} has been restarted {actor_info['consecutive_failures']} times "
                f"in {self._restart_window}s, giving up"
            )
            actor_info["status"] = "failed"
            return

        # Update status
        actor_info["status"] = "restarting"

        # Apply backoff delay if this is not the first restart
        if actor_info["consecutive_failures"] > 1:
            backoff_time = actor_info["restart_backoff"]
            logger.info(f"Applying restart backoff of {backoff_time:.2f}s for actor {actor_id}")
            await asyncio.sleep(backoff_time)

        try:
            # Get the actor from the registry
            from .actor_registry import get_registry
            registry = get_registry()
            actor = registry.get_actor(actor_id)

            if actor is None:
                logger.error(f"Cannot restart actor {actor_id}: not found in registry")
                actor_info["status"] = "failed"
                return

            # We're directly restarting the actor instead of sending a message
            # This is more reliable than the message-based approach

            # First save the actor's state
            try:
                await actor._save_state()
            except Exception as save_error:
                logger.warning(f"Failed to save state for actor {actor_id}: {save_error}")
                # Continue with restart anyway

            # Stop the actor with a timeout
            try:
                stop_task = asyncio.create_task(actor.stop())
                await asyncio.wait_for(stop_task, timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning(f"Timeout stopping actor {actor_id}, continuing with restart")
            except Exception as stop_error:
                logger.warning(f"Error stopping actor {actor_id}: {stop_error}")
                # Continue with restart anyway

            # Wait a moment for cleanup
            await asyncio.sleep(0.5)

            # Start the actor again with a timeout
            try:
                start_task = asyncio.create_task(actor.start())
                await asyncio.wait_for(start_task, timeout=10.0)

                # Update status
                actor_info["status"] = "running"
                logger.info(f"Successfully restarted actor {actor_id}")

                # Reset heartbeat time
                self._actor_heartbeats[actor_id] = time.time()

            except asyncio.TimeoutError:
                logger.error(f"Timeout starting actor {actor_id}")
                actor_info["status"] = "failed"
            except Exception as start_error:
                logger.error(f"Failed to start actor {actor_id}: {start_error}")
                import traceback
                logger.error(traceback.format_exc())
                actor_info["status"] = "failed"

        except Exception as e:
            logger.error(f"Failed to restart actor {actor_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            actor_info["status"] = "failed"

    async def handle_heartbeat(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a heartbeat message from a supervised actor.

        Args:
            payload: Heartbeat payload
            context: Message context
        """
        actor_id = payload.get("actor_id")
        timestamp = payload.get("timestamp", time.time())
        metrics = payload.get("metrics", {})

        if not actor_id:
            logger.warning("Received heartbeat without actor_id")
            return

        if actor_id not in self._supervised_actors:
            logger.info(f"Received heartbeat from unsupervised actor {actor_id}, automatically supervising")

            # Try to get actor type from registry
            actor_type = None
            try:
                from .actor_registry import get_registry
                registry = get_registry()
                actor = registry.get_actor(actor_id)
                if actor:
                    actor_type = getattr(actor, "actor_type", None)
            except Exception as e:
                logger.debug(f"Could not get actor type from registry: {e}")

            # Start supervising this actor
            await self.supervise(actor_id, actor_type)

            # Continue processing the heartbeat

        # Update heartbeat time
        self._actor_heartbeats[actor_id] = time.time()

        # Update metrics
        if metrics:
            self._actor_metrics[actor_id] = metrics

        # Update status
        self._supervised_actors[actor_id]["status"] = "healthy"

        logger.debug(f"Received heartbeat from actor {actor_id} at {timestamp}")

    async def handle_error(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle an error message from a supervised actor.

        Args:
            payload: Error payload
            context: Message context
        """
        actor_id = payload.get("actor_id")
        error = payload.get("error", "Unknown error")
        fatal = payload.get("fatal", False)
        timestamp = payload.get("timestamp", time.time())

        if not actor_id:
            # This is an error for this actor
            await super().handle_error(payload, context)
            return

        if actor_id not in self._supervised_actors:
            logger.warning(f"Received error from unsupervised actor {actor_id}")
            return

        # Add to errors
        self._actor_errors[actor_id].append({
            "error": error,
            "fatal": fatal,
            "timestamp": timestamp
        })

        # Update status
        self._supervised_actors[actor_id]["status"] = "error"

        logger.error(f"Actor {actor_id} reported error: {error}")

        # Restart if fatal
        if fatal and self._supervised_actors[actor_id]["restart_on_failure"]:
            await self._restart_actor(actor_id, "fatal_error")

    async def handle_start(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a start message from a supervised actor.

        Args:
            payload: Start payload
            context: Message context
        """
        actor_id = payload.get("actor_id")
        timestamp = payload.get("timestamp", time.time())
        actor_type = payload.get("actor_type")

        if not actor_id:
            logger.warning("Received start message without actor_id")
            return

        if actor_id not in self._supervised_actors:
            logger.info(f"Actor {actor_id} started but is not supervised")
            return

        # Update status
        self._supervised_actors[actor_id]["status"] = "running"

        # Update actor type if provided
        if actor_type:
            self._supervised_actors[actor_id]["actor_type"] = actor_type

        # Update heartbeat time
        self._actor_heartbeats[actor_id] = time.time()

        logger.info(f"Actor {actor_id} started at {timestamp}")

    async def handle_stop(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a stop message from a supervised actor.

        Args:
            payload: Stop payload
            context: Message context
        """
        actor_id = payload.get("actor_id")
        timestamp = payload.get("timestamp", time.time())
        reason = payload.get("reason", "normal_shutdown")

        if not actor_id:
            logger.warning("Received stop message without actor_id")
            return

        if actor_id not in self._supervised_actors:
            logger.info(f"Actor {actor_id} stopped but is not supervised")
            return

        # Update status
        self._supervised_actors[actor_id]["status"] = "stopped"

        logger.info(f"Actor {actor_id} stopped at {timestamp} (reason: {reason})")

    async def handle_metrics(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a metrics message from a supervised actor.

        Args:
            payload: Metrics payload
            context: Message context
        """
        actor_id = payload.get("actor_id")
        timestamp = payload.get("timestamp", time.time())
        metrics = payload.get("metrics", {})

        if not actor_id:
            logger.warning("Received metrics without actor_id")
            return

        if actor_id not in self._supervised_actors:
            logger.debug(f"Received metrics from unsupervised actor {actor_id}")
            return

        # Update metrics
        self._actor_metrics[actor_id] = metrics

        logger.debug(f"Received metrics from actor {actor_id} at {timestamp}")

    def get_actor_status(self, actor_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the status of a supervised actor.

        Args:
            actor_id: ID of the actor to get status for

        Returns:
            Status information or None if not supervised
        """
        if actor_id not in self._supervised_actors:
            return None

        status = self._supervised_actors[actor_id].copy()
        status["metrics"] = self._actor_metrics.get(actor_id, {})
        status["errors"] = self._actor_errors.get(actor_id, [])
        status["restarts"] = len(self._actor_restarts.get(actor_id, []))

        return status

    def get_all_actor_statuses(self) -> Dict[str, Dict[str, Any]]:
        """
        Get the status of all supervised actors.

        Returns:
            Dictionary of actor IDs to status information
        """
        result = {}
        for actor_id in self._supervised_actors:
            status = self.get_actor_status(actor_id)
            if status is not None:
                result[actor_id] = status
        return result

    async def handle_status(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a status request.

        Args:
            payload: Status request payload
            context: Message context
        """
        actor_id = payload.get("actor_id")

        if actor_id:
            # Get status for a specific actor
            status = self.get_actor_status(actor_id)

            # Send response
            response_payload = {
                "actor_id": actor_id,
                "status": status
            }

            # Get sender ID from context
            sender_id = context.metadata.get("sender_id")
            if sender_id:
                await self.send(sender_id, MessageType.STATUS, response_payload, context)
        else:
            # Get status for all actors
            statuses = self.get_all_actor_statuses()

            # Send response
            response_payload = {
                "statuses": statuses
            }

            # Get sender ID from context
            sender_id = context.metadata.get("sender_id")
            if sender_id:
                await self.send(sender_id, MessageType.STATUS, response_payload, context)

    def _get_state(self) -> Dict[str, Any]:
        """
        Get actor state for persistence.

        Returns:
            Dictionary of state to save
        """
        state = super()._get_state()

        # Add supervision state
        state["supervised_actors"] = self._supervised_actors
        state["actor_heartbeats"] = self._actor_heartbeats
        state["actor_metrics"] = self._actor_metrics
        state["actor_errors"] = self._actor_errors
        state["actor_restarts"] = self._actor_restarts

        return state

    def _set_state(self, state: Dict[str, Any]) -> None:
        """
        Set actor state from persistence.

        Args:
            state: Dictionary of state to restore
        """
        super()._set_state(state)

        # Restore supervision state
        if "supervised_actors" in state:
            self._supervised_actors = state["supervised_actors"]

        if "actor_heartbeats" in state:
            self._actor_heartbeats = state["actor_heartbeats"]

        if "actor_metrics" in state:
            self._actor_metrics = state["actor_metrics"]

        if "actor_errors" in state:
            self._actor_errors = state["actor_errors"]

        if "actor_restarts" in state:
            self._actor_restarts = state["actor_restarts"]
