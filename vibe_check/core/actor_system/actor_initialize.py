"""
Actor Initialize Method
======================

This module contains the implementation of the initialize method for the Actor class.
It has been extracted to a separate file to make it easier to maintain and test.

Enhanced with comprehensive debugging capabilities for diagnosing and resolving
initialization issues, including detailed logging, timing information, and error tracking.
"""

import asyncio
import logging
import time
import traceback
from typing import Any, Dict, List, Optional, Set, Union, TYPE_CHECKING

from .actor_state import ActorState
from .exceptions import (
    ActorInitializationError,
    ActorDependencyError,
    ActorTimeoutError
)
from .consolidated_initializer import get_initializer
from .diagnostics import get_tracker, InitializationStep
from .logging.initialization_debug import (
    InitializationStep as DebugStep,
    log_init_event,
    init_step_timing,
    time_operation,
    init_debug_async_decorator,
    is_init_debugging_enabled
)

if TYPE_CHECKING:
    from .actor import Actor

# Configure logging
logger = logging.getLogger("vibe_check_actor_system")


@init_debug_async_decorator(DebugStep.INITIALIZE)
async def initialize_actor(
    self: 'Actor',  # Added type annotation for self
    config: Optional[Dict[str, Any]] = None,
    timeout: float = 120.0,  # Increased default timeout
    dependencies: Optional[List[str]] = None,
    optional_dependencies: Optional[List[str]] = None
) -> None:
    """
    Initialize the actor (first phase of two-phase initialization).

    This method prepares the actor for starting but doesn't begin processing messages.
    It's part of the two-phase initialization process to ensure all actors are
    properly initialized before they start communicating.

    Args:
        self: The actor instance
        config: Optional configuration dictionary for the actor
        timeout: Maximum time in seconds to wait for initialization to complete
        dependencies: Optional list of actor IDs that this actor depends on
        optional_dependencies: Optional list of actor IDs that this actor can
                             function without

    Raises:
        ActorInitializationError: If initialization fails
        ActorTimeoutError: If initialization times out
        ActorDependencyError: If a dependency fails to initialize

    Enhanced with robust error handling, dependency management, timeout handling,
    and cleanup on failure. Now uses the new ActorInitializationManager.
    """
    # First, ensure we're registered with both initializers
    try:
        # Log the registration attempt
        if is_init_debugging_enabled():
            log_init_event(
                actor_id=self.actor_id,
                step=DebugStep.REGISTRATION,
                message="Ensuring actor is registered with initializers",
                details={
                    "timeout": timeout,
                    "has_dependencies": bool(dependencies),
                    "has_optional_dependencies": bool(optional_dependencies)
                }
            )

        # Time the registration operation
        if is_init_debugging_enabled():
            with time_operation(self.actor_id, "ensure_registered_with_initializer", {
                "timeout": timeout,
                "has_dependencies": bool(dependencies),
                "has_optional_dependencies": bool(optional_dependencies)
            }):
                await self._ensure_registered_with_initializer()
        else:
            await self._ensure_registered_with_initializer()

        # Log successful registration
        if is_init_debugging_enabled():
            log_init_event(
                actor_id=self.actor_id,
                step=DebugStep.REGISTRATION,
                message="Actor successfully registered with initializers"
            )
    except Exception as e:
        logger.error(f"Error ensuring actor {self.actor_id} is registered with initializers: {e}")
        import traceback
        logger.error(traceback.format_exc())

        # Log the registration failure with detailed error information
        if is_init_debugging_enabled():
            log_init_event(
                actor_id=self.actor_id,
                step=DebugStep.FAILED,
                message=f"Failed to register with initializers: {str(e)}",
                error=e,
                stack_trace=True,
                details={"phase": "registration", "state": ActorState.CREATED.value}
            )

        raise ActorInitializationError(
            message=f"Failed to register with initializers: {str(e)}",
            actor_id=self.actor_id,
            phase="registration",
            state=ActorState.CREATED,
            original_error=e
        )

    # Get the ConsolidatedActorInitializer
    initializer = get_initializer()

    # Create a timeout for the entire initialization process
    try:
        # Use wait_for instead of asyncio.timeout for compatibility with older Python versions
        async def _initialize_with_timeout() -> None:
            try:
                # Set actor state to INITIALIZING in the ConsolidatedActorInitializer
                if initializer:
                    await initializer.set_actor_state(
                        actor_id=self.actor_id,
                        state=ActorState.INITIALIZING,
                        error=None,
                        phase="initialize"
                    )

                # No legacy components to update

                # Record initialization start with the tracker
                tracker = get_tracker()
                if tracker:
                    await tracker.record_event(
                        actor_id=self.actor_id,
                        step=InitializationStep.INITIALIZATION_START,
                        details={"state": ActorState.INITIALIZING.value}
                    )

                # Register dependencies with the ConsolidatedActorInitializer
                if dependencies and initializer:
                    logger.info(f"Actor {self.actor_id} registering dependencies: {dependencies}")

                    # Time the dependency registration operation
                    if is_init_debugging_enabled():
                        with time_operation(self.actor_id, "register_dependencies", {
                            "dependencies": dependencies,
                            "optional": False
                        }):
                            for dep_id in dependencies:
                                await initializer.register_dependency(self.actor_id, dep_id, optional=False)
                    else:
                        for dep_id in dependencies:
                            await initializer.register_dependency(self.actor_id, dep_id, optional=False)

                    # No legacy components to update

                # Register optional dependencies with the ConsolidatedActorInitializer
                if optional_dependencies and initializer:
                    logger.info(f"Actor {self.actor_id} registering optional dependencies: {optional_dependencies}")

                    # Time the optional dependency registration operation
                    if is_init_debugging_enabled():
                        with time_operation(self.actor_id, "register_optional_dependencies", {
                            "dependencies": optional_dependencies,
                            "optional": True
                        }):
                            for dep_id in optional_dependencies:
                                await initializer.register_dependency(self.actor_id, dep_id, optional=True)
                    else:
                        for dep_id in optional_dependencies:
                            await initializer.register_dependency(self.actor_id, dep_id, optional=True)

                    # No legacy components to update

                # Resolve dependencies with more robust error handling
                if dependencies or optional_dependencies:
                    logger.info(f"Actor {self.actor_id} resolving dependencies")

                    # Log dependency resolution start
                    if is_init_debugging_enabled():
                        log_init_event(
                            actor_id=self.actor_id,
                            step=DebugStep.DEPENDENCY_RESOLUTION,
                            message="Starting dependency resolution",
                            details={
                                "dependencies": dependencies,
                                "optional_dependencies": optional_dependencies
                            }
                        )

                    try:
                        # Use a slightly shorter timeout for dependencies to leave time for initialization
                        dependency_timeout = max(timeout * 0.8, 30.0)  # At least 30 seconds

                        # Resolve dependencies with the ConsolidatedActorInitializer
                        if initializer:
                            # Use timing context for dependency resolution
                            if is_init_debugging_enabled():
                                with init_step_timing(self.actor_id, DebugStep.DEPENDENCY_RESOLUTION):
                                    success = await initializer.wait_for_dependencies(self.actor_id, dependency_timeout)
                            else:
                                success = await initializer.wait_for_dependencies(self.actor_id, dependency_timeout)

                            if not success:
                                # Get the error from the initializer
                                error = None
                                with initializer._state_lock:
                                    error = initializer._initialization_errors.get(self.actor_id)

                                if error:
                                    logger.warning(f"Dependency resolution error for actor {self.actor_id}: {error}")

                                    # Log dependency resolution error
                                    if is_init_debugging_enabled():
                                        log_init_event(
                                            actor_id=self.actor_id,
                                            step=DebugStep.DEPENDENCY_RESOLUTION,
                                            message=f"Dependency resolution error: {error}",
                                            error=error,
                                            details={
                                                "dependencies": dependencies,
                                                "optional_dependencies": optional_dependencies,
                                                "will_continue": bool(optional_dependencies) and (not dependencies or set(dependencies or []).issubset(set(optional_dependencies or [])))
                                            }
                                        )

                                    # Continue with initialization despite dependency errors if they're optional
                                    if not optional_dependencies or (dependencies and set(dependencies or []) - set(optional_dependencies or [])):
                                        # If there are non-optional dependencies, raise the error
                                        raise error
                                    else:
                                        logger.warning(f"Continuing with initialization despite dependency errors (all dependencies are optional)")
                                else:
                                    logger.warning(f"Failed to resolve dependencies for actor {self.actor_id}, but continuing anyway")

                                    # Log dependency resolution warning
                                    if is_init_debugging_enabled():
                                        log_init_event(
                                            actor_id=self.actor_id,
                                            step=DebugStep.DEPENDENCY_RESOLUTION,
                                            message="Failed to resolve dependencies, but continuing anyway",
                                            details={
                                                "dependencies": dependencies,
                                                "optional_dependencies": optional_dependencies
                                            }
                                        )
                        else:
                            # ConsolidatedActorInitializer not available - this should never happen
                            logger.error(f"ConsolidatedActorInitializer not available for actor {self.actor_id}")
                            raise ActorInitializationError(
                                message=f"ConsolidatedActorInitializer not available for actor {self.actor_id}",
                                actor_id=self.actor_id,
                                phase="dependency_resolution",
                                state=ActorState.INITIALIZING
                            )
                    except asyncio.TimeoutError:
                        logger.warning(f"Timeout resolving dependencies for actor {self.actor_id} after {dependency_timeout} seconds")
                        # Continue with initialization despite timeout if all dependencies are optional
                        if not optional_dependencies or (dependencies and set(dependencies or []) - set(optional_dependencies or [])):
                            # If there are non-optional dependencies, raise the error
                            raise ActorDependencyError(
                                message=f"Timeout resolving dependencies for actor {self.actor_id}",
                                actor_id=self.actor_id,
                                dependencies={}
                            )
                        else:
                            logger.warning(f"Continuing with initialization despite dependency timeout (all dependencies are optional)")
                    except Exception as e:
                        logger.error(f"Error resolving dependencies for actor {self.actor_id}: {e}")
                        # Continue with initialization despite errors if all dependencies are optional
                        if not optional_dependencies or (dependencies and set(dependencies or []) - set(optional_dependencies or [])):
                            # If there are non-optional dependencies, raise the error
                            raise ActorDependencyError(
                                message=f"Failed to resolve dependencies for actor {self.actor_id}: {str(e)}",
                                actor_id=self.actor_id,
                                dependencies={}
                            )
                        else:
                            logger.warning(f"Continuing with initialization despite dependency errors (all dependencies are optional)")

                # Reset metrics for a clean start
                self._metrics["start_time"] = time.time()
                self._metrics["uptime"] = 0.0
                self._metrics["messages_received"] = 0
                self._metrics["messages_sent"] = 0
                self._metrics["messages_processed"] = 0
                self._metrics["errors"] = 0
                self._metrics["processing_time"] = 0.0
                self._metrics["avg_processing_time"] = 0.0
                self._metrics["last_activity"] = time.time()

                # Load state if available
                try:
                    # Time the state loading operation
                    if is_init_debugging_enabled():
                        with time_operation(self.actor_id, "load_state"):
                            await self._load_state()
                    else:
                        await self._load_state()
                except Exception as state_error:
                    logger.error(f"Error loading state for actor {self.actor_id}: {state_error}")
                    import traceback
                    logger.error(traceback.format_exc())

                    # Log the state loading error
                    if is_init_debugging_enabled():
                        log_init_event(
                            actor_id=self.actor_id,
                            step=DebugStep.INITIALIZE,
                            message=f"Error loading state: {state_error}",
                            error=state_error,
                            stack_trace=True
                        )

                    # Continue with initialization despite state loading error

                # Initialize message handlers
                self._initialize_handlers()

                # Initialize the message processor
                self._message_processor = self._message_processor.__class__(self)

                # Initialize the message router
                self._message_router = self._message_router.__class__(self)

                # Initialize the task manager
                self._task_manager = self._task_manager.__class__(self)

                # Initialize the actor starter
                self._starter = self._starter.__class__(self)

                # Initialize the actor terminator
                self._terminator = self._terminator.__class__(self)

                # Call the actor-specific initialization
                # This is a hook for subclasses to implement their own initialization logic
                if hasattr(self, '_initialize') and callable(getattr(self, '_initialize')):
                    # Time the actor-specific initialization
                    if is_init_debugging_enabled():
                        with time_operation(self.actor_id, "actor_specific_initialize", {
                            "has_config": bool(config)
                        }):
                            await self._initialize(config or {})
                    else:
                        await self._initialize(config or {})
                else:
                    # If the method doesn't exist, log a debug message
                    logger.debug(f"Actor {self.actor_id} has no _initialize method")

                # Mark initialization as complete
                self._initialization_complete = True

                # Set actor state to INITIALIZED in the ConsolidatedActorInitializer
                if initializer:
                    await initializer.set_actor_state(
                        actor_id=self.actor_id,
                        state=ActorState.INITIALIZED,
                        error=None,
                        phase="initialize"
                    )

                # No legacy components to update

                # Record initialization complete with the tracker
                if tracker:
                    await tracker.record_event(
                        actor_id=self.actor_id,
                        step=InitializationStep.INITIALIZATION_COMPLETE,
                        details={"state": ActorState.INITIALIZED.value}
                    )

                logger.info(f"Actor {self.actor_id} initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing actor {self.actor_id}: {e}")
                import traceback
                logger.error(traceback.format_exc())

                # Mark initialization as failed
                self._initialization_complete = False

                # Set actor state to FAILED in the ConsolidatedActorInitializer
                try:
                    # Set state in the ConsolidatedActorInitializer
                    if initializer:
                        await initializer.set_actor_state(
                            actor_id=self.actor_id,
                            state=ActorState.FAILED,
                            error=e,
                            phase="initialize"
                        )

                        # Clean up resources in the ConsolidatedActorInitializer
                        await initializer.cleanup_actor(self.actor_id)

                    # No legacy components to update
                except Exception as state_error:
                    logger.error(f"Error setting actor {self.actor_id} state to FAILED: {state_error}")

                # Record failure with the tracker
                tracker = get_tracker()
                if tracker:
                    await tracker.record_event(
                        actor_id=self.actor_id,
                        step=InitializationStep.FAILED,
                        details={"phase": "initialize", "error": str(e)},
                        error=e
                    )

                # Re-raise the exception
                raise

        # Execute the initialization with a timeout
        try:
            # Log initialization timeout start
            if is_init_debugging_enabled():
                log_init_event(
                    actor_id=self.actor_id,
                    step=DebugStep.INITIALIZE,
                    message=f"Starting initialization with timeout of {timeout} seconds",
                    details={"timeout": timeout}
                )

            # Start timing the initialization
            start_time = time.time()
            await asyncio.wait_for(_initialize_with_timeout(), timeout=timeout)

            # Log successful initialization with timing
            if is_init_debugging_enabled():
                duration = time.time() - start_time
                log_init_event(
                    actor_id=self.actor_id,
                    step=DebugStep.INITIALIZE,
                    message=f"Initialization completed successfully in {duration:.4f} seconds",
                    details={"duration": duration, "timeout": timeout}
                )

        except asyncio.TimeoutError:
            # Calculate actual duration before timeout
            duration = time.time() - start_time
            logger.error(f"Timeout initializing actor {self.actor_id} after {timeout} seconds (actual duration: {duration:.4f}s)")

            # Mark initialization as failed
            self._initialization_complete = False

            # Create a timeout exception
            timeout_error = ActorTimeoutError(
                message=f"Initialization timed out after {timeout} seconds",
                actor_id=self.actor_id,
                operation="initialize",
                timeout=timeout
            )

            # Log timeout error with detailed information
            if is_init_debugging_enabled():
                log_init_event(
                    actor_id=self.actor_id,
                    step=DebugStep.TIMEOUT,
                    message=f"Initialization timed out after {timeout} seconds",
                    error=timeout_error,
                    details={
                        "timeout": timeout,
                        "actual_duration": duration,
                        "operation": "initialize"
                    }
                )

            # Set actor state to FAILED in the initializer
            try:
                # Set state in the initializer
                await initializer.set_actor_state(
                    actor_id=self.actor_id,
                    state=ActorState.FAILED,
                    error=timeout_error,
                    phase="initialize"
                )

                # Clean up resources in the initializer
                await initializer.cleanup_actor(self.actor_id)
            except Exception as state_error:
                logger.error(f"Error setting actor {self.actor_id} state to FAILED: {state_error}")

            # Clean up resources
            await self._cleanup_resources()

            # Raise the timeout exception
            raise timeout_error
    except Exception as e:
        # This catches any exceptions from the timeout context
        logger.error(f"Error in initialization process for actor {self.actor_id}: {e}")

        # Mark initialization as failed
        self._initialization_complete = False

        # Clean up resources
        await self._cleanup_resources()

        # Re-raise the exception
        raise
