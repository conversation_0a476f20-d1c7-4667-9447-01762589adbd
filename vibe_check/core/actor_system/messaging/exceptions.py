"""
Messaging Exceptions Module
========================

This module defines exceptions related to the messaging system in the actor system.
These exceptions are used to signal various error conditions in message handling,
routing, and queuing.
"""

from typing import Any, Dict, Optional


class MessagingError(Exception):
    """Base class for all messaging-related exceptions."""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        """
        Initialize the exception.

        Args:
            message: Error message
            details: Optional additional details about the error
        """
        self.details = details or {}
        super().__init__(message)


class QueueError(MessagingError):
    """Base class for queue-related exceptions."""
    pass


class QueueFullError(QueueError):
    """
    Exception raised when a queue is full and cannot accept more messages.

    This exception is raised when a message cannot be added to a queue because
    the queue has reached its maximum capacity and the backpressure policy is
    set to raise exceptions.
    """

    def __init__(self, message: str, queue_name: str, queue_size: int, 
                max_size: int, details: Optional[Dict[str, Any]] = None):
        """
        Initialize the exception.

        Args:
            message: Error message
            queue_name: Name of the queue that is full
            queue_size: Current size of the queue
            max_size: Maximum size of the queue
            details: Optional additional details about the error
        """
        self.queue_name = queue_name
        self.queue_size = queue_size
        self.max_size = max_size
        super().__init__(message, details)


class InvalidStateError(QueueError):
    """
    Exception raised when a message cannot be processed due to actor state.

    This exception is raised when a message is sent to an actor that is not in
    a state that can process the message (e.g., sending a normal message to an
    actor that is not READY).
    """

    def __init__(self, message: str, current_state: Any, required_state: Any,
                details: Optional[Dict[str, Any]] = None):
        """
        Initialize the exception.

        Args:
            message: Error message
            current_state: Current state of the actor
            required_state: State required to process the message
            details: Optional additional details about the error
        """
        self.current_state = current_state
        self.required_state = required_state
        super().__init__(message, details)


class MessageExpiredError(MessagingError):
    """
    Exception raised when a message has expired.

    This exception is raised when a message's TTL has been exceeded and the
    message can no longer be processed.
    """

    def __init__(self, message: str, message_id: str, ttl: Optional[int],
                age: float, details: Optional[Dict[str, Any]] = None):
        """
        Initialize the exception.

        Args:
            message: Error message
            message_id: ID of the expired message
            ttl: Time-to-live of the message in seconds
            age: Age of the message in seconds
            details: Optional additional details about the error
        """
        self.message_id = message_id
        self.ttl = ttl
        self.age = age
        super().__init__(message, details)


class RoutingError(MessagingError):
    """
    Exception raised when a message cannot be routed to its recipient.

    This exception is raised when a message cannot be delivered to its intended
    recipient, either because the recipient does not exist or is not available.
    """

    def __init__(self, message: str, recipient_id: str, 
                details: Optional[Dict[str, Any]] = None):
        """
        Initialize the exception.

        Args:
            message: Error message
            recipient_id: ID of the intended recipient
            details: Optional additional details about the error
        """
        self.recipient_id = recipient_id
        super().__init__(message, details)


class CircuitBreakerOpenError(RoutingError):
    """
    Exception raised when a circuit breaker is open.

    This exception is raised when a message cannot be delivered to a recipient
    because the circuit breaker for that recipient is open due to previous
    failures.
    """

    def __init__(self, message: str, recipient_id: str, failure_count: int,
                threshold: int, reset_timeout: float,
                details: Optional[Dict[str, Any]] = None):
        """
        Initialize the exception.

        Args:
            message: Error message
            recipient_id: ID of the intended recipient
            failure_count: Number of consecutive failures
            threshold: Failure threshold for opening the circuit breaker
            reset_timeout: Time in seconds until the circuit breaker resets
            details: Optional additional details about the error
        """
        self.failure_count = failure_count
        self.threshold = threshold
        self.reset_timeout = reset_timeout
        super().__init__(message, recipient_id, details)
