"""
Messaging package for the actor system.

This package contains components related to message handling, processing,
and sending between actors in the system.

Components:
- MessageProcessor: Handles message processing for actors
- StreamManager: Handles stream-related functionality for actors
"""

# Import key classes for easier access
from .processor import MessageProcessor
from .streams import StreamManager

__all__ = ["MessageProcessor", "StreamManager"]
