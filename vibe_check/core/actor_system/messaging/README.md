# Messaging Components

This directory contains the messaging components for the actor system. These components are responsible for handling message processing, routing, stream management, and state persistence.

## Components

### MessageProcessor

The `MessageProcessor` class is responsible for processing messages from an actor's mailbox. It implements the core message processing loop, handling message retrieval, processing, error handling, and heartbeat sending.

```python
# Example usage
processor = MessageProcessor(actor)
await processor.process_messages()
```

### MessageQueue

The `MessageQueue` class is responsible for storing and prioritizing messages for actors. It implements the `MessageQueueProtocol` and provides methods for adding messages to the queue, retrieving messages based on priority, and managing backpressure.

Enhanced with:

- Priority-based message queuing
- Configurable backpressure mechanisms
- State-aware message acceptance
- Message buffering for actors not yet ready
- Comprehensive metrics collection

```python
# Example usage
queue = MessageQueue(actor_id="actor1", max_size=1000)

# Set actor state
queue.set_actor_state(ActorState.READY)

# Configure backpressure policy
queue.set_backpressure_policy(BackpressurePolicy.BLOCK)

# Add a message to the queue
await queue.put(message)

# Get the next message from the queue
message = await queue.get()

# Mark the message as processed
queue.task_done()

# Process buffered messages
await queue.process_buffered_messages()

# Get queue metrics
metrics = queue.get_metrics()
```

### MessageRouter

The `MessageRouter` class is responsible for routing messages between actors, handling dynamic discovery via the registry, and managing message delivery. It implements the `MessageRouterProtocol` and provides methods for sending messages to other actors, handling message delivery failures, and notifying supervisors of errors.

Enhanced with:

- State-based message routing
- Circuit breaker pattern for unavailable actors
- Dead letter queue for undeliverable messages
- Fallback handlers for messages to failed actors

```python
# Example usage
router = MessageRouter(actor)

# Set dead letter queue
router.set_dead_letter_queue(dead_letter_queue)

# Add fallback handler for a message type
router.add_fallback_handler(MessageType.TEST, "fallback_actor_id")

# Send a message
await router.send(
    recipient_id="recipient_actor",
    msg_type=MessageType.INITIALIZE,
    payload={"test": "data"},
    context=None,
    priority=MessagePriority.NORMAL,
    ttl=None
)

# Get router metrics
metrics = router.get_metrics()
```

### DeadLetterQueue

The `DeadLetterQueue` class is responsible for storing messages that could not be delivered to their intended recipients. It implements the `DeadLetterQueueProtocol` and provides methods for adding messages to the queue, retrieving messages, and retrying delivery.

Enhanced with:

- Message persistence to disk
- Retry delivery mechanisms
- Comprehensive metrics collection

```python
# Example usage
dead_letter_queue = DeadLetterQueue(registry, max_size=1000, persistence_enabled=True)

# Add a message to the queue
await dead_letter_queue.add(message, "Recipient not found")

# Get messages from the queue
messages = await dead_letter_queue.get_messages(count=10)

# Retry delivery of a message
success = await dead_letter_queue.retry_delivery(message_id)

# Clear the queue
await dead_letter_queue.clear()

# Get queue metrics
metrics = dead_letter_queue.get_metrics()

# Load persisted messages
loaded_count = await dead_letter_queue.load_persisted_messages()
```

### StateManager

The `StateManager` class is responsible for saving and loading actor state, enabling persistence across restarts. It implements the `StateManagerProtocol` and provides methods for saving and loading actor state, as well as getting and setting state.

Enhanced with:

- Multiple serialization formats (JSON, Pickle, Custom)
- Automatic state saving
- Comprehensive metrics collection
- Pluggable storage backends

```python
# Example usage
state_manager = StateManager(actor, state_format=StateFormat.JSON)

# Save state
await state_manager.save_state()

# Load state
await state_manager.load_state()

# Get state
state = state_manager.get_state()

# Set state
state_manager.set_state({"key": "value"})

# Start automatic state saving
await state_manager.start_auto_save(interval=60.0)

# Stop automatic state saving
await state_manager.stop_auto_save()

# Delete state
await state_manager.delete_state()

# Get metrics
metrics = state_manager.get_metrics()

# Using different serialization formats
json_state_manager = StateManager(actor, state_format=StateFormat.JSON)
pickle_state_manager = StateManager(actor, state_format=StateFormat.PICKLE)

# Using custom serializer
custom_serializer = MyCustomSerializer()
custom_state_manager = StateManager(
    actor,
    state_format=StateFormat.CUSTOM,
    custom_serializer=custom_serializer
)

# Using custom storage
custom_storage = MyCustomStorage()
custom_storage_manager = StateManager(
    actor,
    custom_storage=custom_storage
)
```

### StreamManager

The `StreamManager` class is responsible for handling publish-subscribe patterns, allowing actors to subscribe to streams and publish messages to them. It implements the `StreamManagerProtocol` and provides methods for subscribing to streams, unsubscribing from streams, and publishing messages to streams.

Enhanced with:

- Support for stream filtering and transformation
- Priority-based message delivery
- Comprehensive metrics collection
- Configurable backpressure handling

```python
# Example usage
stream_manager = StreamManager(actor)

# Subscribe to a stream
await stream_manager.subscribe("test_stream", priority=MessagePriority.HIGH, backpressure_limit=100)

# Publish a message to a stream
await stream_manager.publish(
    stream_id="test_stream",
    message_type=MessageType.TEST,
    payload={"test": "data"},
    priority=MessagePriority.NORMAL,
    ttl=60
)

# Unsubscribe from a stream
await stream_manager.unsubscribe("test_stream")

# Add a filter
def filter_func(message):
    return message.payload.get("importance") == "high"

stream_manager.add_filter("test_stream", "important_only", filter_func)

# Remove a filter
stream_manager.remove_filter("test_stream", "important_only")

# Add a transformer
def transform_func(message):
    message.payload["processed_at"] = time.time()
    return message

stream_manager.add_transformer("test_stream", "add_timestamp", transform_func)

# Remove a transformer
stream_manager.remove_transformer("test_stream", "add_timestamp")

# Get metrics
metrics = stream_manager.get_metrics()
```

## Protocols

These components implement the following protocols:

- `MessageProcessorProtocol`: Defines the interface for message processors
- `MessageQueueProtocol`: Defines the interface for message queues
- `MessageRouterProtocol`: Defines the interface for message routers
- `DeadLetterQueueProtocol`: Defines the interface for dead letter queues
- `StreamManagerProtocol`: Defines the interface for stream managers
- `StateManagerProtocol`: Defines the interface for state managers

## Integration with Actor System

These components are used by the `Actor` class to handle message processing, routing, and stream management. The `Actor` class delegates to these components to reduce its complexity and make it easier to test and maintain.

```python
# Example integration in Actor class
class Actor:
    def __init__(self, actor_id, ...):
        # ...
        self.mailbox = MessageQueue(actor_id, max_size=1000)
        self._message_processor = MessageProcessor(self)
        self._message_router = MessageRouter(self)
        self._stream_manager = StreamManager(self)
        self._state_manager = StateManager(self)
        # ...

        # Set up dead letter queue
        from ..actor_registry import get_registry
        registry = get_registry()
        self._dead_letter_queue = DeadLetterQueue(registry, max_size=1000)
        self._message_router.set_dead_letter_queue(self._dead_letter_queue)

        # Update mailbox with actor state
        self.mailbox.set_actor_state(ActorState.CREATED)

        # Start automatic state saving
        asyncio.create_task(self._state_manager.start_auto_save(interval=60.0))
        # ...

    async def send(self, recipient_id, msg_type, payload, context=None, priority=MessagePriority.NORMAL, ttl=None):
        await self._message_router.send(
            recipient_id=recipient_id,
            msg_type=msg_type,
            payload=payload,
            context=context,
            priority=priority,
            ttl=ttl
        )

    async def receive(self, message):
        # Add to mailbox with state-aware acceptance
        await self.mailbox.put(message)

    async def process_messages(self):
        await self._message_processor.process_messages()

    async def _update_state(self, state):
        # Update mailbox with new state
        self.mailbox.set_actor_state(state)

        # Process any buffered messages if transitioning to READY
        if state == ActorState.READY:
            await self.mailbox.process_buffered_messages()

        # Save state if transitioning to a stable state
        if state in {ActorState.READY, ActorState.STOPPED}:
            await self._state_manager.save_state()

    async def subscribe(self, stream_id, priority=None, backpressure_limit=None):
        await self._stream_manager.subscribe(stream_id, priority, backpressure_limit)

    async def publish(self, stream_id, message_type, payload, priority=None, ttl=None):
        await self._stream_manager.publish(stream_id, message_type, payload, priority, ttl)

    async def unsubscribe(self, stream_id):
        await self._stream_manager.unsubscribe(stream_id)

    def add_filter(self, stream_id, filter_name, filter_func):
        self._stream_manager.add_filter(stream_id, filter_name, filter_func)

    def remove_filter(self, stream_id, filter_name):
        self._stream_manager.remove_filter(stream_id, filter_name)

    def add_transformer(self, stream_id, transformer_name, transform_func):
        self._stream_manager.add_transformer(stream_id, transformer_name, transform_func)

    def remove_transformer(self, stream_id, transformer_name):
        self._stream_manager.remove_transformer(stream_id, transformer_name)

    def add_fallback_handler(self, message_type, handler_id):
        self._message_router.add_fallback_handler(message_type, handler_id)

    def get_state(self) -> Dict[str, Any]:
        """Get actor state for persistence."""
        return {
            "actor_id": self.actor_id,
            "actor_type": self.actor_type,
            "tags": list(self.tags),
            "capabilities": list(self.capabilities),
            "supervisor_id": self.supervisor_id,
            "custom_state": self._custom_state,
            # Add other state properties as needed
        }

    def set_state(self, state: Dict[str, Any]) -> None:
        """Set actor state from persistence."""
        if "actor_id" in state and state["actor_id"] != self.actor_id:
            self.logger.warning(
                f"State actor_id {state['actor_id']} does not match actor {self.actor_id}"
            )

        if "actor_type" in state:
            self.actor_type = state["actor_type"]

        if "tags" in state:
            self.tags = set(state["tags"])

        if "capabilities" in state:
            self.capabilities = set(state["capabilities"])

        if "supervisor_id" in state:
            self.supervisor_id = state["supervisor_id"]

        if "custom_state" in state:
            self._custom_state = state["custom_state"]

        # Restore other state properties as needed

    async def initialize(self) -> None:
        """Initialize the actor."""
        # Load state from disk
        await self._state_manager.load_state()

        # Continue with initialization
        # ...

    async def shutdown(self) -> None:
        """Shutdown the actor."""
        # Save state before shutting down
        await self._state_manager.save_state()

        # Stop automatic state saving
        await self._state_manager.stop_auto_save()

        # Continue with shutdown
        # ...
```
