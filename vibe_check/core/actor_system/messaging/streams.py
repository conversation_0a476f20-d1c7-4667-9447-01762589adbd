"""
Stream Manager Module
==================

This module defines the StreamManager class, which is responsible for
handling stream-related functionality for actors.

The StreamManager implements the StreamManagerProtocol and provides methods
for subscribing to streams, unsubscribing from streams, and publishing
messages to streams.

Enhanced with:
- Support for stream filtering and transformation
- Stream backpressure handling
- Stream prioritization
- Enhanced error handling and recovery mechanisms
- Stream metrics and monitoring
- Stream persistence for reliable delivery
"""

import asyncio
import enum
import json
import logging
import os
import time
import traceback
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union, TYPE_CHECKING

from ..context_wave import ContextWave
from ..message import Message, MessageType
from ..protocols import StreamManagerProtocol

# Avoid circular imports
if TYPE_CHECKING:
    from ..actor import Actor


class StreamPriority(enum.IntEnum):
    """
    Enumeration of stream priorities.

    These priorities determine the order in which messages are processed
    when multiple streams have pending messages.
    """

    # Lowest priority
    LOW = 0

    # Normal priority (default)
    NORMAL = 5

    # High priority
    HIGH = 7

    # Highest priority (critical)
    CRITICAL = 9


class StreamFilter:
    """
    Filter for stream messages.

    Filters can be applied to streams to control which messages are received
    by subscribers.

    Attributes:
        name: Name of the filter
        predicate: Function that determines whether a message passes the filter
    """

    def __init__(self, name: str, predicate: Callable[[Dict[str, Any]], bool]) -> None:
        """
        Initialize a stream filter.

        Args:
            name: Name of the filter
            predicate: Function that takes a message payload and returns True if
                      the message should be passed, False otherwise
        """
        self.name = name
        self.predicate = predicate

    def apply(self, payload: Dict[str, Any]) -> bool:
        """
        Apply the filter to a message payload.

        Args:
            payload: Message payload to filter

        Returns:
            True if the message passes the filter, False otherwise
        """
        try:
            return self.predicate(payload)
        except Exception as e:
            logging.getLogger("vibe_check_actor_system.stream_filter").error(
                f"Error applying filter {self.name}: {e}"
            )
            # Default to passing the message if the filter fails
            return True


class StreamTransformer:
    """
    Transformer for stream messages.

    Transformers can be applied to streams to modify messages before they are
    received by subscribers.

    Attributes:
        name: Name of the transformer
        transform_func: Function that transforms a message payload
    """

    def __init__(self, name: str,
                transform_func: Callable[[Dict[str, Any]], Dict[str, Any]]) -> None:
        """
        Initialize a stream transformer.

        Args:
            name: Name of the transformer
            transform_func: Function that takes a message payload and returns a
                           transformed payload
        """
        self.name = name
        self.transform_func = transform_func

    def apply(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply the transformer to a message payload.

        Args:
            payload: Message payload to transform

        Returns:
            Transformed message payload
        """
        try:
            return self.transform_func(payload)
        except Exception as e:
            logging.getLogger("vibe_check_actor_system.stream_transformer").error(
                f"Error applying transformer {self.name}: {e}"
            )
            # Return the original payload if the transformer fails
            return payload


class StreamManager(StreamManagerProtocol):
    """
    Handles stream-related functionality for actors.

    This component is responsible for subscribing to streams, unsubscribing
    from streams, and publishing messages to streams. It implements the
    StreamManagerProtocol.

    Enhanced with support for stream filtering and transformation, backpressure
    handling, stream prioritization, error handling and recovery mechanisms,
    stream metrics and monitoring, and stream persistence for reliable delivery.

    Attributes:
        actor: The actor that owns this component
        logger: Logger for this component
        subscriptions: Set of stream IDs the actor is subscribed to
        stream_filters: Dictionary mapping stream IDs to filters
        stream_transformers: Dictionary mapping stream IDs to transformers
        stream_priorities: Dictionary mapping stream IDs to priorities
        stream_metrics: Dictionary of stream-related metrics
        stream_backpressure_limits: Dictionary mapping stream IDs to backpressure limits
        stream_persistence_enabled: Whether stream persistence is enabled
        stream_persistence_dir: Directory for stream persistence
    """

    def __init__(self, actor: 'Actor') -> None:
        """
        Initialize the stream manager.

        Args:
            actor: The actor that owns this component
        """
        self.actor = actor
        self.logger = logging.getLogger("vibe_check_actor_system.streams")
        self._subscriptions: Set[str] = set()

        # Stream filtering and transformation
        self._stream_filters: Dict[str, List[StreamFilter]] = {}
        self._stream_transformers: Dict[str, List[StreamTransformer]] = {}

        # Stream prioritization
        self._stream_priorities: Dict[str, StreamPriority] = {}
        self._default_priority = StreamPriority.NORMAL

        # Stream metrics
        self._stream_metrics: Dict[str, Dict[str, Any]] = {}
        self._metrics_enabled = True
        self._metrics_collection_interval = 60.0  # seconds
        self._metrics_collection_task: Optional[asyncio.Task] = None

        # Backpressure handling
        self._stream_backpressure_limits: Dict[str, int] = {}
        self._default_backpressure_limit = 1000  # messages
        self._backpressure_enabled = True

        # Stream persistence
        self._stream_persistence_enabled = False
        self._stream_persistence_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            "data", "streams"
        )
        self._pending_messages: Dict[str, List[Dict[str, Any]]] = {}
        self._max_pending_messages = 100  # per stream

        # Initialize metrics for all streams
        self._initialize_metrics()

    @property
    def subscriptions(self) -> Set[str]:
        """Get the set of stream IDs the actor is subscribed to."""
        return self._subscriptions

    @property
    def stream_filters(self) -> Dict[str, List[StreamFilter]]:
        """Get the dictionary of stream filters."""
        return self._stream_filters

    @property
    def stream_transformers(self) -> Dict[str, List[StreamTransformer]]:
        """Get the dictionary of stream transformers."""
        return self._stream_transformers

    @property
    def stream_priorities(self) -> Dict[str, StreamPriority]:
        """Get the dictionary of stream priorities."""
        return self._stream_priorities

    @property
    def stream_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get the dictionary of stream metrics."""
        return self._stream_metrics

    @property
    def stream_persistence_enabled(self) -> bool:
        """Check if stream persistence is enabled."""
        return self._stream_persistence_enabled

    @stream_persistence_enabled.setter
    def stream_persistence_enabled(self, value: bool) -> None:
        """
        Set whether stream persistence is enabled.

        Args:
            value: Whether to enable stream persistence
        """
        self._stream_persistence_enabled = value

        # Create persistence directory if enabled
        if value and not os.path.exists(self._stream_persistence_dir):
            try:
                os.makedirs(self._stream_persistence_dir, exist_ok=True)
                self.logger.info(f"Created stream persistence directory: {self._stream_persistence_dir}")
            except Exception as e:
                self.logger.error(f"Failed to create stream persistence directory: {e}")
                self._stream_persistence_enabled = False

    def _initialize_metrics(self) -> None:
        """
        Initialize metrics for all streams.

        This method sets up the initial metrics for each stream and starts
        the metrics collection task if enabled.
        """
        # Initialize metrics for existing subscriptions
        for stream_id in self._subscriptions:
            self._initialize_stream_metrics(stream_id)

        # Start metrics collection task if enabled
        if self._metrics_enabled and (self._metrics_collection_task is None or
                                     self._metrics_collection_task.done()):
            self._metrics_collection_task = asyncio.create_task(self._collect_metrics())
            self.logger.debug(f"Started metrics collection task for actor {self.actor.actor_id}")

    def _initialize_stream_metrics(self, stream_id: str) -> None:
        """
        Initialize metrics for a specific stream.

        Args:
            stream_id: ID of the stream to initialize metrics for
        """
        if stream_id not in self._stream_metrics:
            self._stream_metrics[stream_id] = {
                "messages_received": 0,
                "messages_filtered": 0,
                "messages_transformed": 0,
                "errors": 0,
                "last_message_time": 0.0,
                "avg_processing_time": 0.0,
                "total_processing_time": 0.0,
                "backpressure_events": 0,
                "persistence_writes": 0,
                "persistence_reads": 0
            }

    async def _collect_metrics(self) -> None:
        """
        Collect and report metrics periodically.

        This method runs in a loop, collecting metrics at the specified interval
        and reporting them to the actor's metrics collector if available.
        """
        try:
            while self.actor.is_running and self._metrics_enabled:
                # Wait for the collection interval
                await asyncio.sleep(self._metrics_collection_interval)

                # Report metrics to the actor's metrics collector if available
                try:
                    if hasattr(self.actor, "_metrics_collector"):
                        for stream_id, metrics in self._stream_metrics.items():
                            for metric_name, value in metrics.items():
                                self.actor._metrics_collector.update_metric(
                                    f"stream.{stream_id}.{metric_name}", value
                                )
                        self.logger.debug(f"Reported stream metrics for actor {self.actor.actor_id}")
                except Exception as e:
                    self.logger.error(f"Error reporting stream metrics: {e}")
        except asyncio.CancelledError:
            self.logger.info(f"Metrics collection task cancelled for actor {self.actor.actor_id}")
        except Exception as e:
            self.logger.error(f"Error in metrics collection task: {e}")
            error_details = traceback.format_exc()
            self.logger.error(error_details)

    def add_filter(self, stream_id: str, filter_obj: StreamFilter) -> None:
        """
        Add a filter to a stream.

        Args:
            stream_id: ID of the stream to add the filter to
            filter_obj: Filter to add

        Raises:
            ValueError: If the stream is not subscribed to
        """
        if stream_id not in self._subscriptions:
            raise ValueError(f"Actor {self.actor.actor_id} is not subscribed to stream {stream_id}")

        if stream_id not in self._stream_filters:
            self._stream_filters[stream_id] = []

        self._stream_filters[stream_id].append(filter_obj)
        self.logger.info(f"Added filter {filter_obj.name} to stream {stream_id}")

    def remove_filter(self, stream_id: str, filter_name: str) -> None:
        """
        Remove a filter from a stream.

        Args:
            stream_id: ID of the stream to remove the filter from
            filter_name: Name of the filter to remove

        Raises:
            ValueError: If the stream is not subscribed to or the filter is not found
        """
        if stream_id not in self._subscriptions:
            raise ValueError(f"Actor {self.actor.actor_id} is not subscribed to stream {stream_id}")

        if stream_id not in self._stream_filters:
            raise ValueError(f"Stream {stream_id} has no filters")

        # Find the filter by name
        for i, filter_obj in enumerate(self._stream_filters[stream_id]):
            if filter_obj.name == filter_name:
                self._stream_filters[stream_id].pop(i)
                self.logger.info(f"Removed filter {filter_name} from stream {stream_id}")
                return

        raise ValueError(f"Filter {filter_name} not found for stream {stream_id}")

    def add_transformer(self, stream_id: str, transformer_obj: StreamTransformer) -> None:
        """
        Add a transformer to a stream.

        Args:
            stream_id: ID of the stream to add the transformer to
            transformer_obj: Transformer to add

        Raises:
            ValueError: If the stream is not subscribed to
        """
        if stream_id not in self._subscriptions:
            raise ValueError(f"Actor {self.actor.actor_id} is not subscribed to stream {stream_id}")

        if stream_id not in self._stream_transformers:
            self._stream_transformers[stream_id] = []

        self._stream_transformers[stream_id].append(transformer_obj)
        self.logger.info(f"Added transformer {transformer_obj.name} to stream {stream_id}")

    def remove_transformer(self, stream_id: str, transformer_name: str) -> None:
        """
        Remove a transformer from a stream.

        Args:
            stream_id: ID of the stream to remove the transformer from
            transformer_name: Name of the transformer to remove

        Raises:
            ValueError: If the stream is not subscribed to or the transformer is not found
        """
        if stream_id not in self._subscriptions:
            raise ValueError(f"Actor {self.actor.actor_id} is not subscribed to stream {stream_id}")

        if stream_id not in self._stream_transformers:
            raise ValueError(f"Stream {stream_id} has no transformers")

        # Find the transformer by name
        for i, transformer_obj in enumerate(self._stream_transformers[stream_id]):
            if transformer_obj.name == transformer_name:
                self._stream_transformers[stream_id].pop(i)
                self.logger.info(f"Removed transformer {transformer_name} from stream {stream_id}")
                return

        raise ValueError(f"Transformer {transformer_name} not found for stream {stream_id}")

    def set_stream_priority(self, stream_id: str, priority: StreamPriority) -> None:
        """
        Set the priority for a stream.

        Args:
            stream_id: ID of the stream to set priority for
            priority: Priority to set

        Raises:
            ValueError: If the stream is not subscribed to
        """
        if stream_id not in self._subscriptions:
            raise ValueError(f"Actor {self.actor.actor_id} is not subscribed to stream {stream_id}")

        self._stream_priorities[stream_id] = priority
        self.logger.info(f"Set priority for stream {stream_id} to {priority.name}")

    def get_stream_priority(self, stream_id: str) -> StreamPriority:
        """
        Get the priority for a stream.

        Args:
            stream_id: ID of the stream to get priority for

        Returns:
            The stream's priority

        Raises:
            ValueError: If the stream is not subscribed to
        """
        if stream_id not in self._subscriptions:
            raise ValueError(f"Actor {self.actor.actor_id} is not subscribed to stream {stream_id}")

        return self._stream_priorities.get(stream_id, self._default_priority)

    def set_backpressure_limit(self, stream_id: str, limit: int) -> None:
        """
        Set the backpressure limit for a stream.

        Args:
            stream_id: ID of the stream to set limit for
            limit: Maximum number of pending messages before backpressure is applied

        Raises:
            ValueError: If the stream is not subscribed to or the limit is invalid
        """
        if stream_id not in self._subscriptions:
            raise ValueError(f"Actor {self.actor.actor_id} is not subscribed to stream {stream_id}")

        if limit <= 0:
            raise ValueError(f"Backpressure limit must be positive, got {limit}")

        self._stream_backpressure_limits[stream_id] = limit
        self.logger.info(f"Set backpressure limit for stream {stream_id} to {limit}")

    def get_backpressure_limit(self, stream_id: str) -> int:
        """
        Get the backpressure limit for a stream.

        Args:
            stream_id: ID of the stream to get limit for

        Returns:
            The stream's backpressure limit

        Raises:
            ValueError: If the stream is not subscribed to
        """
        if stream_id not in self._subscriptions:
            raise ValueError(f"Actor {self.actor.actor_id} is not subscribed to stream {stream_id}")

        return self._stream_backpressure_limits.get(stream_id, self._default_backpressure_limit)

    async def subscribe(self, stream_id: str, priority: Optional[StreamPriority] = None,
                      backpressure_limit: Optional[int] = None) -> None:
        """
        Subscribe to a stream.

        Enhanced to support stream prioritization, backpressure handling, and metrics.

        Args:
            stream_id: ID of the stream to subscribe to
            priority: Optional priority for the stream
            backpressure_limit: Optional backpressure limit for the stream

        Raises:
            ValueError: If the actor is already subscribed to the stream
            RuntimeError: If the registry is not available
        """
        if stream_id in self._subscriptions:
            self.logger.warning(f"Actor {self.actor.actor_id} is already subscribed to stream {stream_id}")
            return

        try:
            from ..actor_registry import get_registry
            registry = get_registry()
            if registry is None:
                raise RuntimeError("Registry not available")

            # Subscribe to the stream
            registry.subscribe(stream_id, self.actor.actor_id)
            self._subscriptions.add(stream_id)

            # Initialize stream metrics
            self._initialize_stream_metrics(stream_id)

            # Set stream priority if provided
            if priority is not None:
                self._stream_priorities[stream_id] = priority

            # Set backpressure limit if provided
            if backpressure_limit is not None:
                if backpressure_limit <= 0:
                    raise ValueError(f"Backpressure limit must be positive, got {backpressure_limit}")
                self._stream_backpressure_limits[stream_id] = backpressure_limit

            # Initialize pending messages list for this stream
            if self._stream_persistence_enabled and stream_id not in self._pending_messages:
                self._pending_messages[stream_id] = []

                # Try to load any persisted messages
                await self._load_persisted_messages(stream_id)

            self.logger.info(f"Actor {self.actor.actor_id} subscribed to stream {stream_id}")
        except (ImportError, AttributeError) as e:
            self.logger.error(f"Failed to subscribe to stream {stream_id}: {e}")
            raise RuntimeError(f"Failed to subscribe to stream {stream_id}: {e}") from e

    async def unsubscribe(self, stream_id: str, persist_pending: bool = True) -> None:
        """
        Unsubscribe from a stream.

        Enhanced to support stream persistence and cleanup of stream-related resources.

        Args:
            stream_id: ID of the stream to unsubscribe from
            persist_pending: Whether to persist pending messages before unsubscribing

        Raises:
            ValueError: If the actor is not subscribed to the stream
            RuntimeError: If the registry is not available
        """
        if stream_id not in self._subscriptions:
            self.logger.warning(f"Actor {self.actor.actor_id} is not subscribed to stream {stream_id}")
            return

        try:
            from ..actor_registry import get_registry
            registry = get_registry()
            if registry is None:
                raise RuntimeError("Registry not available")

            # Persist pending messages if enabled and requested
            if self._stream_persistence_enabled and persist_pending and stream_id in self._pending_messages:
                if self._pending_messages[stream_id]:
                    await self._persist_messages(stream_id, self._pending_messages[stream_id])
                    self.logger.info(f"Persisted {len(self._pending_messages[stream_id])} pending messages for stream {stream_id}")

            # Unsubscribe from the stream
            registry.unsubscribe(stream_id, self.actor.actor_id)
            self._subscriptions.remove(stream_id)

            # Clean up stream-related resources
            if stream_id in self._stream_filters:
                del self._stream_filters[stream_id]

            if stream_id in self._stream_transformers:
                del self._stream_transformers[stream_id]

            if stream_id in self._stream_priorities:
                del self._stream_priorities[stream_id]

            if stream_id in self._stream_backpressure_limits:
                del self._stream_backpressure_limits[stream_id]

            if stream_id in self._pending_messages:
                del self._pending_messages[stream_id]

            # Keep metrics for reporting

            self.logger.info(f"Actor {self.actor.actor_id} unsubscribed from stream {stream_id}")
        except (ImportError, AttributeError) as e:
            self.logger.error(f"Failed to unsubscribe from stream {stream_id}: {e}")
            raise RuntimeError(f"Failed to unsubscribe from stream {stream_id}: {e}") from e

    async def publish(self, stream_id: str, message_type: MessageType, payload: Dict[str, Any],
                    priority: Optional[int] = None, ttl: Optional[int] = None,
                    persist_on_failure: bool = True) -> None:
        """
        Publish a message to a stream.

        Enhanced to support message prioritization, TTL, backpressure handling,
        and persistence for reliable delivery.

        Args:
            stream_id: ID of the stream to publish to
            message_type: Type of the message
            payload: Message payload
            priority: Optional priority for the message (0-9, higher is more important)
            ttl: Optional time-to-live in seconds
            persist_on_failure: Whether to persist the message if publishing fails

        Raises:
            RuntimeError: If the registry is not available
        """
        # Start timing for metrics
        start_time = time.time()

        # Create a copy of the payload to avoid modifying the original
        safe_payload = payload.copy() if payload else {}

        # Add metadata to the payload
        safe_payload["_stream_metadata"] = {
            "stream_id": stream_id,
            "publish_time": start_time,
            "publisher_id": self.actor.actor_id,
            "priority": priority,
            "ttl": ttl
        }

        try:
            from ..actor_registry import get_registry
            registry = get_registry()
            if registry is None:
                raise RuntimeError("Registry not available")

            # Determine the effective priority
            effective_priority = priority
            if effective_priority is None and stream_id in self._stream_priorities:
                effective_priority = self._stream_priorities[stream_id].value

            # Publish the message
            await registry.publish(
                stream_id=stream_id,
                message_type=message_type,
                payload=safe_payload,
                sender_id=self.actor.actor_id,
                priority=effective_priority,
                ttl=ttl
            )

            # Update metrics
            if stream_id in self._stream_metrics:
                self._stream_metrics[stream_id]["messages_received"] += 1
                self._stream_metrics[stream_id]["last_message_time"] = time.time()
                processing_time = time.time() - start_time
                self._stream_metrics[stream_id]["total_processing_time"] += processing_time

                # Update average processing time
                msg_count = self._stream_metrics[stream_id]["messages_received"]
                if msg_count > 0:
                    self._stream_metrics[stream_id]["avg_processing_time"] = (
                        self._stream_metrics[stream_id]["total_processing_time"] / msg_count
                    )

            self.logger.info(f"Actor {self.actor.actor_id} published message of type {message_type.name} to stream {stream_id}")
        except (ImportError, AttributeError, RuntimeError) as e:
            self.logger.error(f"Failed to publish to stream {stream_id}: {e}")

            # Update error metrics
            if stream_id in self._stream_metrics:
                self._stream_metrics[stream_id]["errors"] += 1

            # Persist the message if enabled and requested
            if self._stream_persistence_enabled and persist_on_failure:
                try:
                    message_data = {
                        "stream_id": stream_id,
                        "message_type": message_type.name,
                        "payload": safe_payload,
                        "sender_id": self.actor.actor_id,
                        "priority": priority,
                        "ttl": ttl,
                        "timestamp": time.time()
                    }

                    # Add to pending messages
                    if stream_id not in self._pending_messages:
                        self._pending_messages[stream_id] = []

                    self._pending_messages[stream_id].append(message_data)

                    # Enforce backpressure limit
                    limit = self.get_backpressure_limit(stream_id)
                    if len(self._pending_messages[stream_id]) > limit:
                        # Apply backpressure by removing oldest messages
                        excess = len(self._pending_messages[stream_id]) - limit
                        self._pending_messages[stream_id] = self._pending_messages[stream_id][excess:]

                        # Update backpressure metrics
                        if stream_id in self._stream_metrics:
                            self._stream_metrics[stream_id]["backpressure_events"] += 1

                        self.logger.warning(
                            f"Backpressure applied to stream {stream_id}: "
                            f"removed {excess} oldest messages"
                        )

                    # Persist messages
                    await self._persist_messages(stream_id, [message_data])

                    # Update persistence metrics
                    if stream_id in self._stream_metrics:
                        self._stream_metrics[stream_id]["persistence_writes"] += 1

                    self.logger.info(f"Persisted message to stream {stream_id} for later delivery")
                except Exception as persist_error:
                    self.logger.error(f"Failed to persist message to stream {stream_id}: {persist_error}")

            raise RuntimeError(f"Failed to publish to stream {stream_id}: {e}") from e

    async def handle_stream_data(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle data from a stream.

        Enhanced to support stream filtering, transformation, and metrics.

        Args:
            payload: Stream data payload
            context: Message context
        """
        # Start timing for metrics
        start_time = time.time()

        stream_id = context.metadata.get("stream_id")
        if not stream_id:
            self.logger.warning("Received stream data without stream_id")
            return

        # Initialize metrics for this stream if not already done
        if stream_id not in self._stream_metrics:
            self._initialize_stream_metrics(stream_id)

        # Update metrics
        if stream_id in self._stream_metrics:
            self._stream_metrics[stream_id]["messages_received"] += 1
            self._stream_metrics[stream_id]["last_message_time"] = time.time()

        # Apply filters if any
        if stream_id in self._stream_filters and self._stream_filters[stream_id]:
            for filter_obj in self._stream_filters[stream_id]:
                try:
                    if not filter_obj.apply(payload):
                        # Message filtered out
                        if stream_id in self._stream_metrics:
                            self._stream_metrics[stream_id]["messages_filtered"] += 1

                        self.logger.debug(
                            f"Actor {self.actor.actor_id} filtered out message from stream {stream_id} "
                            f"using filter {filter_obj.name}"
                        )
                        return
                except Exception as e:
                    self.logger.error(f"Error applying filter to stream {stream_id}: {e}")
                    # Continue with other filters

        # Apply transformers if any
        transformed_payload = payload
        if stream_id in self._stream_transformers and self._stream_transformers[stream_id]:
            for transformer_obj in self._stream_transformers[stream_id]:
                try:
                    transformed_payload = transformer_obj.apply(transformed_payload)

                    # Update metrics
                    if stream_id in self._stream_metrics:
                        self._stream_metrics[stream_id]["messages_transformed"] += 1
                except Exception as e:
                    self.logger.error(f"Error applying transformer to stream {stream_id}: {e}")
                    # Continue with other transformers

        self.logger.debug(f"Actor {self.actor.actor_id} received data from stream {stream_id}")

        # Update processing time metrics
        if stream_id in self._stream_metrics:
            processing_time = time.time() - start_time
            self._stream_metrics[stream_id]["total_processing_time"] += processing_time

            # Update average processing time
            msg_count = self._stream_metrics[stream_id]["messages_received"]
            if msg_count > 0:
                self._stream_metrics[stream_id]["avg_processing_time"] = (
                    self._stream_metrics[stream_id]["total_processing_time"] / msg_count
                )

        # Process stream data based on stream_id
        # Override this method in subclasses to handle specific streams

    async def handle_subscribe(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a subscription request.

        Enhanced to support stream prioritization and backpressure handling.

        Args:
            payload: Subscription payload
            context: Message context
        """
        stream_id = payload.get("stream_id")
        if not stream_id:
            self.logger.warning("Received subscribe message without stream_id")
            return

        # Extract additional parameters
        priority_value = payload.get("priority")
        priority = None
        if priority_value is not None:
            try:
                priority = StreamPriority(priority_value)
            except (ValueError, TypeError):
                self.logger.warning(f"Invalid priority value: {priority_value}, using default")

        backpressure_limit = payload.get("backpressure_limit")

        # Subscribe to the stream
        await self.subscribe(stream_id, priority, backpressure_limit)

    async def handle_unsubscribe(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle an unsubscription request.

        Enhanced to support stream persistence.

        Args:
            payload: Unsubscription payload
            context: Message context
        """
        stream_id = payload.get("stream_id")
        if not stream_id:
            self.logger.warning("Received unsubscribe message without stream_id")
            return

        # Extract additional parameters
        persist_pending = payload.get("persist_pending", True)

        # Unsubscribe from the stream
        await self.unsubscribe(stream_id, persist_pending)

    async def handle_publish(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a publish request.

        Enhanced to support message prioritization, TTL, and persistence.

        Args:
            payload: Publish payload
            context: Message context
        """
        stream_id = payload.get("stream_id")
        message_type_name = payload.get("message_type")
        stream_payload = payload.get("payload", {})
        priority = payload.get("priority")
        ttl = payload.get("ttl")
        persist_on_failure = payload.get("persist_on_failure", True)

        if not stream_id:
            self.logger.warning("Received publish message without stream_id")
            return

        if not message_type_name:
            self.logger.warning("Received publish message without message_type")
            return

        try:
            message_type = MessageType[message_type_name]
        except KeyError:
            self.logger.warning(f"Unknown message type: {message_type_name}")
            return

        # Publish the message
        await self.publish(
            stream_id=stream_id,
            message_type=message_type,
            payload=stream_payload,
            priority=priority,
            ttl=ttl,
            persist_on_failure=persist_on_failure
        )

    async def _persist_messages(self, stream_id: str, messages: List[Dict[str, Any]]) -> None:
        """
        Persist messages for a stream.

        Args:
            stream_id: ID of the stream to persist messages for
            messages: List of message data to persist

        Raises:
            RuntimeError: If there is an error persisting the messages
        """
        if not self._stream_persistence_enabled:
            return

        if not messages:
            return

        try:
            # Create the stream directory if it doesn't exist
            stream_dir = os.path.join(self._stream_persistence_dir, stream_id)
            os.makedirs(stream_dir, exist_ok=True)

            # Create a filename based on timestamp
            filename = f"{int(time.time())}.json"
            filepath = os.path.join(stream_dir, filename)

            # Write the messages to the file
            with open(filepath, 'w') as f:
                json.dump(messages, f)

            self.logger.debug(f"Persisted {len(messages)} messages for stream {stream_id} to {filepath}")

            # Update metrics
            if stream_id in self._stream_metrics:
                self._stream_metrics[stream_id]["persistence_writes"] += len(messages)
        except Exception as e:
            self.logger.error(f"Error persisting messages for stream {stream_id}: {e}")
            error_details = traceback.format_exc()
            self.logger.error(error_details)
            raise RuntimeError(f"Error persisting messages for stream {stream_id}: {e}") from e

    async def _load_persisted_messages(self, stream_id: str) -> None:
        """
        Load persisted messages for a stream.

        Args:
            stream_id: ID of the stream to load messages for

        Raises:
            RuntimeError: If there is an error loading the messages
        """
        if not self._stream_persistence_enabled:
            return

        try:
            # Check if the stream directory exists
            stream_dir = os.path.join(self._stream_persistence_dir, stream_id)
            if not os.path.exists(stream_dir):
                return

            # Get all JSON files in the directory
            files = [f for f in os.listdir(stream_dir) if f.endswith('.json')]
            if not files:
                return

            # Sort files by timestamp (oldest first)
            files.sort()

            # Load messages from each file
            loaded_messages = []
            for filename in files:
                filepath = os.path.join(stream_dir, filename)
                try:
                    with open(filepath, 'r') as f:
                        messages = json.load(f)
                        loaded_messages.extend(messages)

                    # Delete the file after loading
                    os.remove(filepath)
                except Exception as e:
                    self.logger.error(f"Error loading messages from {filepath}: {e}")
                    # Continue with other files

            # Add loaded messages to pending messages
            if loaded_messages:
                if stream_id not in self._pending_messages:
                    self._pending_messages[stream_id] = []

                self._pending_messages[stream_id].extend(loaded_messages)

                # Update metrics
                if stream_id in self._stream_metrics:
                    self._stream_metrics[stream_id]["persistence_reads"] += len(loaded_messages)

                self.logger.info(f"Loaded {len(loaded_messages)} persisted messages for stream {stream_id}")

                # Process pending messages
                await self._process_pending_messages(stream_id)
        except Exception as e:
            self.logger.error(f"Error loading persisted messages for stream {stream_id}: {e}")
            error_details = traceback.format_exc()
            self.logger.error(error_details)
            raise RuntimeError(f"Error loading persisted messages for stream {stream_id}: {e}") from e

    async def _process_pending_messages(self, stream_id: str) -> None:
        """
        Process pending messages for a stream.

        Args:
            stream_id: ID of the stream to process messages for
        """
        if not self._pending_messages.get(stream_id):
            return

        self.logger.info(f"Processing {len(self._pending_messages[stream_id])} pending messages for stream {stream_id}")

        # Process each message
        successful = []
        failed = []

        for message_data in self._pending_messages[stream_id]:
            try:
                # Extract message data
                message_type_name = message_data.get("message_type")
                payload = message_data.get("payload", {})
                priority = message_data.get("priority")
                ttl = message_data.get("ttl")

                # Check if message has expired
                if ttl is not None:
                    timestamp = message_data.get("timestamp", 0)
                    if time.time() - timestamp > ttl:
                        # Message has expired
                        successful.append(message_data)
                        continue

                # Get message type
                try:
                    message_type = MessageType[message_type_name]
                except (KeyError, TypeError):
                    self.logger.warning(f"Unknown message type: {message_type_name}")
                    failed.append(message_data)
                    continue

                # Publish the message
                await self.publish(
                    stream_id=stream_id,
                    message_type=message_type,
                    payload=payload,
                    priority=priority,
                    ttl=ttl,
                    persist_on_failure=False  # Don't persist again if it fails
                )

                # Mark as successful
                successful.append(message_data)
            except Exception as e:
                self.logger.error(f"Error processing pending message for stream {stream_id}: {e}")
                failed.append(message_data)

        # Update pending messages
        self._pending_messages[stream_id] = failed

        self.logger.info(
            f"Processed {len(successful)} pending messages for stream {stream_id} "
            f"({len(failed)} failed)"
        )
