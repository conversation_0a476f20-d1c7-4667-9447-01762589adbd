"""
Message Router Module
==================

This module defines the MessageRouter class, which is responsible for
routing messages between actors, handling dynamic discovery via the registry,
and managing message delivery.

The MessageRouter implements the MessageRouterProtocol and provides methods
for sending messages to other actors, handling message delivery failures,
and notifying supervisors of errors.

Enhanced with:
- State-based message routing
- Circuit breaker pattern for unavailable actors
- Dead letter queue for undeliverable messages
- Fallback handlers for messages to failed actors
"""

import asyncio
import logging
import time
import traceback
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, TYPE_CHECKING, cast

from ..actor_state import ActorState
from ..context_wave import ContextWave
from ..message import Message, MessagePriority, MessageType
from ..protocols import Message<PERSON><PERSON>er<PERSON><PERSON>oc<PERSON>, DeadLetterQueueProtocol
from .exceptions import RoutingError, CircuitBreakerOpenError

# Avoid circular imports
if TYPE_CHECKING:
    from ..actor import Actor
    from ..actor_registry import ActorRegistry


class CircuitBreaker:
    """
    Circuit breaker for preventing repeated calls to failing actors.

    This class implements the circuit breaker pattern, which prevents repeated
    calls to actors that are failing, allowing them time to recover.

    Attributes:
        actor_id: ID of the actor being protected
        failure_threshold: Number of consecutive failures before opening the circuit
        reset_timeout: Time in seconds before attempting to reset the circuit
        half_open_timeout: Time in seconds to wait in half-open state before fully closing
        logger: Logger for this circuit breaker
    """

    def __init__(self, actor_id: str, failure_threshold: int = 3,
                reset_timeout: float = 60.0, half_open_timeout: float = 5.0):
        """
        Initialize the circuit breaker.

        Args:
            actor_id: ID of the actor being protected
            failure_threshold: Number of consecutive failures before opening the circuit
            reset_timeout: Time in seconds before attempting to reset the circuit
            half_open_timeout: Time in seconds to wait in half-open state before fully closing
        """
        self.actor_id = actor_id
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.half_open_timeout = half_open_timeout
        self.logger = logging.getLogger("vibe_check_actor_system.circuit_breaker")

        # Circuit state
        self._failure_count = 0
        self._last_failure_time = 0.0
        self._state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self._last_state_change_time = time.time()

    def record_success(self) -> None:
        """Record a successful call to the protected actor."""
        if self._state == "HALF_OPEN":
            # If we've been in half-open state for long enough, close the circuit
            if time.time() - self._last_state_change_time >= self.half_open_timeout:
                self._state = "CLOSED"
                self._last_state_change_time = time.time()
                self._failure_count = 0
                self.logger.info(f"Circuit for actor {self.actor_id} closed after successful half-open period")
            else:
                # Otherwise, just decrement the failure count
                self._failure_count = max(0, self._failure_count - 1)
        elif self._state == "CLOSED":
            # Reset failure count on success
            self._failure_count = 0

    def record_failure(self) -> None:
        """Record a failed call to the protected actor."""
        self._failure_count += 1
        self._last_failure_time = time.time()

        # If we've reached the threshold, open the circuit
        if self._state == "CLOSED" and self._failure_count >= self.failure_threshold:
            self._state = "OPEN"
            self._last_state_change_time = time.time()
            self.logger.warning(
                f"Circuit for actor {self.actor_id} opened after {self._failure_count} consecutive failures"
            )

    def allow_request(self) -> bool:
        """
        Check if a request should be allowed through the circuit breaker.

        Returns:
            True if the request should be allowed, False otherwise
        """
        current_time = time.time()

        if self._state == "OPEN":
            # Check if it's time to try resetting
            if current_time - self._last_state_change_time >= self.reset_timeout:
                self._state = "HALF_OPEN"
                self._last_state_change_time = current_time
                self.logger.info(f"Circuit for actor {self.actor_id} half-open after {self.reset_timeout}s timeout")
                return True
            return False

        # Always allow requests in CLOSED or HALF_OPEN state
        return True

    def get_state(self) -> str:
        """
        Get the current state of the circuit breaker.

        Returns:
            Current state: "CLOSED", "OPEN", or "HALF_OPEN"
        """
        return self._state

    def get_failure_count(self) -> int:
        """
        Get the current failure count.

        Returns:
            Number of consecutive failures
        """
        return self._failure_count

    def get_time_to_reset(self) -> float:
        """
        Get the time remaining until the circuit breaker resets.

        Returns:
            Time in seconds until reset, or 0 if the circuit is closed
        """
        if self._state != "OPEN":
            return 0.0

        time_elapsed = time.time() - self._last_state_change_time
        return max(0.0, self.reset_timeout - time_elapsed)


class MessageRouter(MessageRouterProtocol):
    """
    Handles routing of messages between actors.

    This component is responsible for sending messages to other actors,
    handling dynamic discovery via the registry, and managing message
    delivery failures. It implements the MessageRouterProtocol.

    Enhanced with:
    - State-based message routing
    - Circuit breaker pattern for unavailable actors
    - Dead letter queue for undeliverable messages
    - Fallback handlers for messages to failed actors

    Attributes:
        actor: The actor that owns this component
        logger: Logger for this component
        circuit_breakers: Dictionary of circuit breakers for actors
        dead_letter_queue: Queue for messages that could not be delivered
        fallback_handlers: Dictionary of fallback handlers for message types
    """

    def __init__(self, actor: 'Actor') -> None:
        """
        Initialize the message router.

        Args:
            actor: The actor that owns this component
        """
        self.actor = actor
        self.logger = logging.getLogger("vibe_check_actor_system.router")

        # Circuit breakers for actors
        self._circuit_breakers: Dict[str, CircuitBreaker] = {}

        # Dead letter queue (will be set later)
        self._dead_letter_queue: Optional[DeadLetterQueueProtocol] = None

        # Fallback handlers for message types
        self._fallback_handlers: Dict[MessageType, str] = {}

        # Metrics
        self._metrics: Dict[str, Any] = {
            "messages_routed": 0,
            "messages_failed": 0,
            "circuit_breaker_trips": 0,
            "dead_letter_messages": 0,
            "fallback_handler_uses": 0,
            "registry_lookups": 0,
            "state_based_routing_redirects": 0,
        }

    def set_dead_letter_queue(self, dead_letter_queue: DeadLetterQueueProtocol) -> None:
        """
        Set the dead letter queue for undeliverable messages.

        Args:
            dead_letter_queue: The dead letter queue to use
        """
        self._dead_letter_queue = dead_letter_queue
        self.logger.info(f"Set dead letter queue for router of actor {self.actor.actor_id}")

    def add_fallback_handler(self, message_type: MessageType, handler_id: str) -> None:
        """
        Add a fallback handler for a message type.

        Args:
            message_type: The message type to handle
            handler_id: ID of the actor to use as a fallback handler
        """
        self._fallback_handlers[message_type] = handler_id
        self.logger.info(
            f"Added fallback handler {handler_id} for message type {message_type.name} "
            f"in router of actor {self.actor.actor_id}"
        )

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get metrics about the router.

        Returns:
            Dictionary of router metrics
        """
        # Add circuit breaker metrics
        circuit_breaker_metrics = {}
        for actor_id, circuit_breaker in self._circuit_breakers.items():
            circuit_breaker_metrics[actor_id] = {
                "state": circuit_breaker.get_state(),
                "failure_count": circuit_breaker.get_failure_count(),
                "time_to_reset": circuit_breaker.get_time_to_reset()
            }

        metrics = self._metrics.copy()
        metrics["circuit_breakers"] = circuit_breaker_metrics
        return metrics

    async def send(self, recipient_id: str, msg_type: MessageType,
                  payload: Dict[str, Any], context: Optional[ContextWave] = None,
                  priority: int = 0, ttl: Optional[int] = None) -> None:
        """
        Send a message to another actor.

        Enhanced with:
        - State-based message routing
        - Circuit breaker pattern for unavailable actors
        - Dead letter queue for undeliverable messages
        - Fallback handlers for messages to failed actors

        Args:
            recipient_id: ID of the recipient actor
            msg_type: Type of the message
            payload: Message payload
            context: Optional context to propagate
            priority: Optional priority for the message (0-9, higher is more important)
            ttl: Optional time-to-live in seconds

        Raises:
            ValueError: If the recipient is not found
            RuntimeError: If there is an error sending the message
            CircuitBreakerOpenError: If the circuit breaker for the recipient is open
        """
        # Start timing for metrics
        start_time = time.time()
        self._metrics["messages_routed"] += 1

        # Check circuit breaker for recipient
        circuit_breaker = self._circuit_breakers.get(recipient_id)
        if circuit_breaker and not circuit_breaker.allow_request():
            self.logger.warning(
                f"Circuit breaker for actor {recipient_id} is open, "
                f"message of type {msg_type.name} will not be sent"
            )
            self._metrics["circuit_breaker_trips"] += 1

            # Try to use fallback handler if available
            if msg_type in self._fallback_handlers:
                fallback_id = self._fallback_handlers[msg_type]
                self.logger.info(
                    f"Using fallback handler {fallback_id} for message of type {msg_type.name} "
                    f"to actor {recipient_id}"
                )
                self._metrics["fallback_handler_uses"] += 1

                # Modify payload to include original recipient
                payload_copy = payload.copy() if isinstance(payload, dict) else {}
                payload_copy["original_recipient_id"] = recipient_id

                # Send to fallback handler
                try:
                    await self.send(fallback_id, msg_type, payload_copy, context, priority, ttl)
                    return
                except Exception as e:
                    self.logger.error(f"Fallback handler {fallback_id} failed: {e}")

            # Add to dead letter queue if available
            if self._dead_letter_queue:
                message = Message(
                    type=msg_type,
                    payload=payload,
                    context=context,
                    recipient_id=recipient_id,
                    sender_id=self.actor.actor_id,
                    priority=priority,
                    ttl=ttl
                )
                await self._dead_letter_queue.add(
                    message,
                    f"Circuit breaker open for actor {recipient_id}"
                )
                self._metrics["dead_letter_messages"] += 1
                self.logger.info(
                    f"Added message of type {msg_type.name} to dead letter queue "
                    f"due to open circuit breaker for actor {recipient_id}"
                )

            # Raise exception
            raise CircuitBreakerOpenError(
                f"Circuit breaker open for actor {recipient_id}",
                recipient_id,
                circuit_breaker.get_failure_count(),
                circuit_breaker.failure_threshold,
                circuit_breaker.get_time_to_reset()
            )

        # Try to get recipient from known actors
        recipient = self.actor._known_actors.get(recipient_id)

        # If not found, try to get from registry
        if recipient is None:
            try:
                from ..actor_registry import get_registry
                registry = get_registry()
                recipient = registry.get_actor(recipient_id)
                self._metrics["registry_lookups"] += 1

                # If found in registry, add to known actors
                if recipient:
                    self.actor._known_actors[recipient_id] = recipient
                    self.logger.info(f"Actor {self.actor.actor_id} discovered actor {recipient_id} via registry")
            except (ImportError, AttributeError) as e:
                self.logger.warning(f"Could not use registry to find actor {recipient_id}: {e}")

        # If still not found, log error and return
        if recipient is None:
            self.logger.error(f"Actor {self.actor.actor_id} tried to send to unknown actor {recipient_id}")

            # Update metrics
            self.actor._metrics["errors"] += 1
            self._metrics["messages_failed"] += 1

            # Try to use fallback handler if available
            if msg_type in self._fallback_handlers:
                fallback_id = self._fallback_handlers[msg_type]
                self.logger.info(
                    f"Using fallback handler {fallback_id} for message of type {msg_type.name} "
                    f"to unknown actor {recipient_id}"
                )
                self._metrics["fallback_handler_uses"] += 1

                # Modify payload to include original recipient
                payload_copy = payload.copy() if isinstance(payload, dict) else {}
                payload_copy["original_recipient_id"] = recipient_id

                # Send to fallback handler
                try:
                    await self.send(fallback_id, msg_type, payload_copy, context, priority, ttl)
                    return
                except Exception as e:
                    self.logger.error(f"Fallback handler {fallback_id} failed: {e}")

            # Add to dead letter queue if available
            if self._dead_letter_queue:
                message = Message(
                    type=msg_type,
                    payload=payload,
                    context=context,
                    recipient_id=recipient_id,
                    sender_id=self.actor.actor_id,
                    priority=priority,
                    ttl=ttl
                )
                await self._dead_letter_queue.add(
                    message,
                    f"Unknown recipient: {recipient_id}"
                )
                self._metrics["dead_letter_messages"] += 1
                self.logger.info(
                    f"Added message of type {msg_type.name} to dead letter queue "
                    f"due to unknown recipient {recipient_id}"
                )

            # Try to notify supervisor if available
            if self.actor.supervisor_id and self.actor.supervisor_id in self.actor._known_actors:
                error_context = ContextWave()
                error_context.metadata["sender_id"] = self.actor.actor_id
                error_payload = {
                    "error": f"Unknown recipient: {recipient_id}",
                    "sender_id": self.actor.actor_id,
                    "message_type": msg_type.name
                }
                await self.send(self.actor.supervisor_id, MessageType.ERROR, error_payload, error_context)

            raise ValueError(f"Unknown recipient: {recipient_id}")

        # Check recipient state if possible
        try:
            # Try to get actor state
            from ..actor_state_machine import ActorStateMachine
            if hasattr(recipient, "_state_machine") and isinstance(recipient._state_machine, ActorStateMachine):
                recipient_state = recipient._state_machine.state

                # Check if message is valid for recipient state
                if not self._is_valid_for_state(msg_type, recipient_state):
                    self.logger.warning(
                        f"Message of type {msg_type.name} is not valid for actor {recipient_id} "
                        f"in state {recipient_state.value}"
                    )

                    # Try to find a better recipient based on message type and state
                    alternate_recipient = await self._find_alternate_recipient(msg_type, recipient_state)
                    if alternate_recipient:
                        self.logger.info(
                            f"Redirecting message of type {msg_type.name} from {recipient_id} "
                            f"to alternate recipient {alternate_recipient}"
                        )
                        self._metrics["state_based_routing_redirects"] += 1

                        # Modify payload to include original recipient
                        payload_copy = payload.copy() if isinstance(payload, dict) else {}
                        payload_copy["original_recipient_id"] = recipient_id

                        # Send to alternate recipient
                        await self.send(alternate_recipient, msg_type, payload_copy, context, priority, ttl)
                        return
        except (ImportError, AttributeError, Exception) as e:
            # If we can't check state, just continue
            self.logger.debug(f"Could not check state for actor {recipient_id}: {e}")

        # Propagate context
        propagated_context = self.actor.context_wave.propagate(context)

        # Add sender info to context metadata
        propagated_context.metadata["sender_id"] = self.actor.actor_id

        # Ensure sender_id is also in the payload for redundancy
        if isinstance(payload, dict):
            # Create a copy to avoid modifying the original
            payload_copy = payload.copy()
            # Add sender_id to payload if not already present
            if "from" not in payload_copy:
                payload_copy["from"] = self.actor.actor_id
        else:
            # If payload is not a dict, create a new one
            payload_copy = {"from": self.actor.actor_id}
            self.logger.warning(f"Payload was not a dict, created new payload with sender_id: {payload_copy}")

        # Add history entry
        history_entry = {
            "timestamp": asyncio.get_event_loop().time(),
            "sender": self.actor.actor_id,
            "recipient": recipient_id,
            "type": msg_type.name
        }
        propagated_context.history.append(history_entry)

        # Create message with enhanced properties
        message = Message(
            type=msg_type,
            payload=payload_copy,  # Use the copy with sender_id
            context=propagated_context,
            recipient_id=recipient_id,
            sender_id=self.actor.actor_id,
            priority=priority,
            ttl=ttl
        )

        try:
            # Send the message
            await recipient.receive(message)

            # Record success in circuit breaker
            if recipient_id in self._circuit_breakers:
                self._circuit_breakers[recipient_id].record_success()

            # Update metrics
            self.actor._metrics["messages_sent"] += 1
            self.actor._metrics["last_activity"] = time.time()
            send_time = time.time() - start_time
            self.logger.debug(f"{self.actor.__class__.__name__} {self.actor.actor_id} sent {msg_type.name} to {recipient_id} in {send_time:.6f}s")
        except Exception as e:
            # Update error metrics
            self.actor._metrics["errors"] += 1
            self._metrics["messages_failed"] += 1

            # Record failure in circuit breaker
            if recipient_id not in self._circuit_breakers:
                self._circuit_breakers[recipient_id] = CircuitBreaker(recipient_id)
            self._circuit_breakers[recipient_id].record_failure()

            self.logger.error(f"Failed to send message from {self.actor.actor_id} to {recipient_id}: {e}")
            error_details = traceback.format_exc()
            self.logger.error(error_details)

            # Try to use fallback handler if available
            if msg_type in self._fallback_handlers:
                fallback_id = self._fallback_handlers[msg_type]
                self.logger.info(
                    f"Using fallback handler {fallback_id} for failed message of type {msg_type.name} "
                    f"to actor {recipient_id}"
                )
                self._metrics["fallback_handler_uses"] += 1

                # Modify payload to include original recipient and error
                payload_copy = payload.copy() if isinstance(payload, dict) else {}
                payload_copy["original_recipient_id"] = recipient_id
                payload_copy["error"] = str(e)

                # Send to fallback handler
                try:
                    await self.send(fallback_id, msg_type, payload_copy, context, priority, ttl)
                    return
                except Exception as e2:
                    self.logger.error(f"Fallback handler {fallback_id} failed: {e2}")

            # Add to dead letter queue if available
            if self._dead_letter_queue:
                await self._dead_letter_queue.add(
                    message,
                    f"Failed to deliver: {e}"
                )
                self._metrics["dead_letter_messages"] += 1
                self.logger.info(
                    f"Added message of type {msg_type.name} to dead letter queue "
                    f"due to delivery failure to actor {recipient_id}"
                )

            # Try to notify supervisor if available
            if self.actor.supervisor_id and self.actor.supervisor_id in self.actor._known_actors:
                error_context = ContextWave()
                error_context.metadata["sender_id"] = self.actor.actor_id
                error_payload = {
                    "error": str(e),
                    "sender_id": self.actor.actor_id,
                    "recipient_id": recipient_id,
                    "message_type": msg_type.name
                }
                try:
                    await self.send(self.actor.supervisor_id, MessageType.ERROR, error_payload, error_context)
                except Exception as e2:
                    self.logger.error(f"Failed to notify supervisor {self.actor.supervisor_id} of error: {e2}")

            raise RuntimeError(f"Failed to send message to {recipient_id}: {e}") from e

    def _is_valid_for_state(self, msg_type: MessageType, state: ActorState) -> bool:
        """
        Check if a message type is valid for an actor state.

        Args:
            msg_type: The message type to check
            state: The actor state to check against

        Returns:
            True if the message type is valid for the state, False otherwise
        """
        # Define valid message types for each actor state
        valid_message_types: Dict[ActorState, Set[MessageType]] = {
            ActorState.CREATED: {
                MessageType.INITIALIZE,
                MessageType.REGISTER,
                MessageType.DISCOVER,
                MessageType.STOP,
                MessageType.SHUTDOWN,
            },
            ActorState.INITIALIZING: {
                MessageType.INITIALIZE,
                MessageType.REGISTER,
                MessageType.DISCOVER,
                MessageType.STOP,
                MessageType.SHUTDOWN,
            },
            ActorState.INITIALIZED: {
                MessageType.START,
                MessageType.INITIALIZE,
                MessageType.REGISTER,
                MessageType.DISCOVER,
                MessageType.STOP,
                MessageType.SHUTDOWN,
            },
            ActorState.STARTING: {
                MessageType.START,
                MessageType.REGISTER,
                MessageType.DISCOVER,
                MessageType.STOP,
                MessageType.SHUTDOWN,
            },
            ActorState.READY: {
                # All message types are valid when the actor is ready
                # This will be handled specially
            },
            ActorState.STOPPING: {
                MessageType.STOP,
                MessageType.SHUTDOWN,
            },
            ActorState.STOPPED: {
                MessageType.INITIALIZE,
                MessageType.REGISTER,
                MessageType.DISCOVER,
                MessageType.SHUTDOWN,
            },
            ActorState.FAILED: {
                MessageType.ROLLBACK,
                MessageType.STOP,
                MessageType.SHUTDOWN,
            },
            ActorState.ROLLBACK: {
                MessageType.INITIALIZE,
                MessageType.REGISTER,
                MessageType.DISCOVER,
                MessageType.STOP,
                MessageType.SHUTDOWN,
            },
        }

        # If actor is READY, all message types are valid except INITIALIZE
        if state == ActorState.READY:
            return msg_type != MessageType.INITIALIZE

        # For other states, check the valid message types
        valid_types = valid_message_types.get(state, set())
        return msg_type in valid_types

    async def _find_alternate_recipient(self, msg_type: MessageType, original_state: ActorState) -> Optional[str]:
        """
        Find an alternate recipient for a message based on message type and original actor state.

        Args:
            msg_type: The message type to route
            original_state: The state of the original recipient

        Returns:
            ID of an alternate recipient, or None if no suitable recipient is found
        """
        try:
            from ..actor_registry import get_registry
            registry = get_registry()

            # Find actors that can handle this message type
            # For now, just find any actor in the READY state
            actors = registry.find_actors()
            for actor in actors:
                # Skip the original actor
                if actor.actor_id == self.actor.actor_id:
                    continue

                # Check if actor has a state machine
                if hasattr(actor, "_state_machine"):
                    from ..actor_state_machine import ActorStateMachine
                    if isinstance(actor._state_machine, ActorStateMachine):
                        # Check if actor is in READY state
                        if actor._state_machine.state == ActorState.READY:
                            # Check if message type is valid for this actor
                            if self._is_valid_for_state(msg_type, actor._state_machine.state):
                                return actor.actor_id

            # No suitable actor found
            return None
        except (ImportError, AttributeError, Exception) as e:
            self.logger.error(f"Error finding alternate recipient: {e}")
            return None
