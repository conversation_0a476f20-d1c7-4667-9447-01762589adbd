"""
Stream Manager Module
=================

This module defines the StreamManager class, which is responsible for handling
publish-subscribe patterns, allowing actors to subscribe to streams and publish
messages to them.

The StreamManager implements the StreamManagerProtocol and provides methods for
subscribing to streams, unsubscribing from streams, and publishing messages to
streams. It also supports filtering and transformation of messages.
"""

import asyncio
import logging
import time
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union, cast
from dataclasses import dataclass, field

from ..context_wave import ContextWave
from ..message import Message, MessagePriority, MessageType
from ..protocols import StreamManagerProtocol, ActorProtocol


@dataclass
class StreamFilter:
    """
    Filter for stream messages.

    Filters determine which messages are delivered to a subscriber based on
    criteria such as message type, sender, or payload content.

    Attributes:
        name: Name of the filter
        filter_func: Function that determines if a message passes the filter
    """
    name: str
    filter_func: Callable[[Message], bool]


@dataclass
class StreamTransformer:
    """
    Transformer for stream messages.

    Transformers modify messages before they are delivered to a subscriber,
    allowing for preprocessing, enrichment, or format conversion.

    Attributes:
        name: Name of the transformer
        transform_func: Function that transforms a message
    """
    name: str
    transform_func: Callable[[Message], Message]


@dataclass
class StreamSubscription:
    """
    Subscription to a stream.

    Represents a subscriber's subscription to a stream, including filters,
    transformers, and subscription options.

    Attributes:
        subscriber_id: ID of the subscriber
        filters: List of filters to apply to messages
        transformers: List of transformers to apply to messages
        priority: Priority of the subscription (higher gets messages first)
        backpressure_limit: Maximum number of pending messages before backpressure is applied
        active: Whether the subscription is active
        created_at: When the subscription was created
        last_message_at: When the last message was delivered
        message_count: Number of messages delivered
    """
    subscriber_id: str
    filters: List[StreamFilter] = field(default_factory=list)
    transformers: List[StreamTransformer] = field(default_factory=list)
    priority: int = MessagePriority.NORMAL
    backpressure_limit: int = 100
    active: bool = True
    created_at: float = field(default_factory=time.time)
    last_message_at: float = 0.0
    message_count: int = 0


class Stream:
    """
    Stream for publish-subscribe messaging.

    A stream is a channel that publishers can send messages to and subscribers
    can receive messages from. It supports filtering, transformation, and
    prioritization of messages.

    Attributes:
        stream_id: ID of the stream
        subscriptions: List of subscriptions to the stream
        message_count: Number of messages published to the stream
        created_at: When the stream was created
        last_message_at: When the last message was published
    """

    def __init__(self, stream_id: str):
        """
        Initialize the stream.

        Args:
            stream_id: ID of the stream
        """
        self.stream_id = stream_id
        self.subscriptions: List[StreamSubscription] = []
        self.message_count: int = 0
        self.created_at: float = time.time()
        self.last_message_at: float = 0.0
        self._lock = asyncio.Lock()

    async def add_subscription(self, subscription: StreamSubscription) -> None:
        """
        Add a subscription to the stream.

        Args:
            subscription: Subscription to add
        """
        async with self._lock:
            # Check if subscription already exists
            for existing in self.subscriptions:
                if existing.subscriber_id == subscription.subscriber_id:
                    # Update existing subscription
                    existing.filters = subscription.filters
                    existing.transformers = subscription.transformers
                    existing.priority = subscription.priority
                    existing.backpressure_limit = subscription.backpressure_limit
                    existing.active = True
                    return

            # Add new subscription
            self.subscriptions.append(subscription)

            # Sort subscriptions by priority (higher first)
            self.subscriptions.sort(key=lambda s: -s.priority)

    async def remove_subscription(self, subscriber_id: str) -> None:
        """
        Remove a subscription from the stream.

        Args:
            subscriber_id: ID of the subscriber to remove
        """
        async with self._lock:
            # Find and remove the subscription
            self.subscriptions = [
                s for s in self.subscriptions if s.subscriber_id != subscriber_id
            ]

    async def publish(self, message: Message, registry: Any) -> int:
        """
        Publish a message to the stream.

        Args:
            message: Message to publish
            registry: Actor registry for looking up subscribers

        Returns:
            Number of subscribers the message was delivered to
        """
        self.message_count += 1
        self.last_message_at = time.time()

        # Create a copy of subscriptions to avoid lock contention
        async with self._lock:
            subscriptions = self.subscriptions.copy()

        # Track number of successful deliveries
        delivery_count = 0

        # Deliver to each subscriber
        for subscription in subscriptions:
            if not subscription.active:
                continue

            # Apply filters
            if not self._passes_filters(message, subscription.filters):
                continue

            # Apply transformers
            transformed_message = self._apply_transformers(message, subscription.transformers)

            # Get the subscriber
            subscriber = registry.get_actor(subscription.subscriber_id)
            if not subscriber:
                continue

            try:
                # Deliver the message
                await subscriber.receive(transformed_message)

                # Update subscription stats
                subscription.last_message_at = time.time()
                subscription.message_count += 1
                delivery_count += 1
            except Exception as e:
                # Log delivery failure
                logging.error(
                    f"Failed to deliver message to subscriber {subscription.subscriber_id}: {e}"
                )

        return delivery_count

    def _passes_filters(self, message: Message, filters: List[StreamFilter]) -> bool:
        """
        Check if a message passes all filters.

        Args:
            message: Message to check
            filters: Filters to apply

        Returns:
            True if the message passes all filters, False otherwise
        """
        # If no filters, message passes
        if not filters:
            return True

        # Check each filter
        for filter_obj in filters:
            try:
                if not filter_obj.filter_func(message):
                    return False
            except Exception as e:
                # Log filter error
                logging.error(f"Error in filter {filter_obj.name}: {e}")
                # If a filter errors, assume message doesn't pass
                return False

        return True

    def _apply_transformers(self, message: Message, transformers: List[StreamTransformer]) -> Message:
        """
        Apply transformers to a message.

        Args:
            message: Message to transform
            transformers: Transformers to apply

        Returns:
            Transformed message
        """
        # If no transformers, return original message
        if not transformers:
            return message

        # Apply each transformer in sequence
        transformed = message
        for transformer in transformers:
            try:
                transformed = transformer.transform_func(transformed)
            except Exception as e:
                # Log transformer error
                logging.error(f"Error in transformer {transformer.name}: {e}")
                # If a transformer errors, use the message as-is
                continue

        return transformed


class StreamManager(StreamManagerProtocol):
    """
    Manages streams for publish-subscribe messaging.

    This component is responsible for handling publish-subscribe patterns,
    allowing actors to subscribe to streams and publish messages to them.
    It implements the StreamManagerProtocol.

    Attributes:
        actor: The actor that owns this component
        streams: Dictionary of streams by ID
        logger: Logger for this component
    """

    def __init__(self, actor: 'ActorProtocol') -> None:
        """
        Initialize the stream manager.

        Args:
            actor: The actor that owns this component
        """
        self.actor = actor
        self.streams: Dict[str, Stream] = {}
        self.logger = logging.getLogger("vibe_check_actor_system.stream_manager")
        self._filters: Dict[str, Dict[str, StreamFilter]] = {}
        self._transformers: Dict[str, Dict[str, StreamTransformer]] = {}
        self._metrics: Dict[str, Any] = {
            "streams_created": 0,
            "streams_removed": 0,
            "subscriptions_created": 0,
            "subscriptions_removed": 0,
            "messages_published": 0,
            "messages_delivered": 0,
            "filter_errors": 0,
            "transformer_errors": 0,
            "delivery_errors": 0
        }

    async def subscribe(self, stream_id: str, priority: Optional[int] = None,
                       backpressure_limit: Optional[int] = None) -> None:
        """
        Subscribe to a stream.

        Args:
            stream_id: ID of the stream to subscribe to
            priority: Optional priority for the subscription
            backpressure_limit: Optional backpressure limit for the subscription
        """
        # Get or create the stream
        stream = self._get_or_create_stream(stream_id)

        # Create subscription
        subscription = StreamSubscription(
            subscriber_id=self.actor.actor_id,
            priority=priority if priority is not None else MessagePriority.NORMAL,
            backpressure_limit=backpressure_limit if backpressure_limit is not None else 100
        )

        # Add filters if any
        if stream_id in self._filters:
            subscription.filters = list(self._filters[stream_id].values())

        # Add transformers if any
        if stream_id in self._transformers:
            subscription.transformers = list(self._transformers[stream_id].values())

        # Add subscription to stream
        await stream.add_subscription(subscription)

        # Update metrics
        self._metrics["subscriptions_created"] += 1

        self.logger.info(
            f"Actor {self.actor.actor_id} subscribed to stream {stream_id} "
            f"with priority {subscription.priority}"
        )

    async def unsubscribe(self, stream_id: str) -> None:
        """
        Unsubscribe from a stream.

        Args:
            stream_id: ID of the stream to unsubscribe from
        """
        # Check if stream exists
        if stream_id not in self.streams:
            self.logger.warning(
                f"Actor {self.actor.actor_id} tried to unsubscribe from "
                f"non-existent stream {stream_id}"
            )
            return

        # Remove subscription
        await self.streams[stream_id].remove_subscription(self.actor.actor_id)

        # Update metrics
        self._metrics["subscriptions_removed"] += 1

        self.logger.info(
            f"Actor {self.actor.actor_id} unsubscribed from stream {stream_id}"
        )

    async def publish(self, stream_id: str, message_type: MessageType,
                     payload: Dict[str, Any], priority: Optional[int] = None,
                     ttl: Optional[int] = None) -> int:
        """
        Publish a message to a stream.

        Args:
            stream_id: ID of the stream to publish to
            message_type: Type of the message
            payload: Message payload
            priority: Optional priority for the message
            ttl: Optional time-to-live for the message

        Returns:
            Number of subscribers the message was delivered to
        """
        # Get or create the stream
        stream = self._get_or_create_stream(stream_id)

        # Create message
        message = Message(
            type=message_type,
            payload=payload,
            sender_id=self.actor.actor_id,
            priority=priority if priority is not None else MessagePriority.NORMAL,
            ttl=ttl
        )

        # Get registry
        try:
            from ..actor_registry import get_registry
            registry = get_registry()
        except (ImportError, AttributeError) as e:
            self.logger.error(f"Failed to get registry: {e}")
            return 0

        # Publish message
        delivery_count = await stream.publish(message, registry)

        # Update metrics
        self._metrics["messages_published"] += 1
        self._metrics["messages_delivered"] += delivery_count

        self.logger.debug(
            f"Actor {self.actor.actor_id} published message of type {message_type.name} "
            f"to stream {stream_id}, delivered to {delivery_count} subscribers"
        )

        return delivery_count

    def add_filter(self, stream_id: str, filter_name: str,
                  filter_func: Callable[[Message], bool]) -> None:
        """
        Add a filter to a stream subscription.

        Args:
            stream_id: ID of the stream
            filter_name: Name of the filter
            filter_func: Function that determines if a message passes the filter
        """
        # Create filter
        filter_obj = StreamFilter(name=filter_name, filter_func=filter_func)

        # Add to filters
        if stream_id not in self._filters:
            self._filters[stream_id] = {}
        self._filters[stream_id][filter_name] = filter_obj

        # Update subscription if it exists
        if stream_id in self.streams:
            for subscription in self.streams[stream_id].subscriptions:
                if subscription.subscriber_id == self.actor.actor_id:
                    subscription.filters.append(filter_obj)
                    break

        self.logger.debug(
            f"Actor {self.actor.actor_id} added filter {filter_name} to stream {stream_id}"
        )

    def remove_filter(self, stream_id: str, filter_name: str) -> None:
        """
        Remove a filter from a stream subscription.

        Args:
            stream_id: ID of the stream
            filter_name: Name of the filter to remove
        """
        # Check if filter exists
        if stream_id not in self._filters or filter_name not in self._filters[stream_id]:
            self.logger.warning(
                f"Actor {self.actor.actor_id} tried to remove non-existent filter "
                f"{filter_name} from stream {stream_id}"
            )
            return

        # Remove filter
        del self._filters[stream_id][filter_name]

        # Update subscription if it exists
        if stream_id in self.streams:
            for subscription in self.streams[stream_id].subscriptions:
                if subscription.subscriber_id == self.actor.actor_id:
                    subscription.filters = [
                        f for f in subscription.filters if f.name != filter_name
                    ]
                    break

        self.logger.debug(
            f"Actor {self.actor.actor_id} removed filter {filter_name} from stream {stream_id}"
        )

    def add_transformer(self, stream_id: str, transformer_name: str,
                       transform_func: Callable[[Message], Message]) -> None:
        """
        Add a transformer to a stream subscription.

        Args:
            stream_id: ID of the stream
            transformer_name: Name of the transformer
            transform_func: Function that transforms a message
        """
        # Create transformer
        transformer_obj = StreamTransformer(name=transformer_name, transform_func=transform_func)

        # Add to transformers
        if stream_id not in self._transformers:
            self._transformers[stream_id] = {}
        self._transformers[stream_id][transformer_name] = transformer_obj

        # Update subscription if it exists
        if stream_id in self.streams:
            for subscription in self.streams[stream_id].subscriptions:
                if subscription.subscriber_id == self.actor.actor_id:
                    subscription.transformers.append(transformer_obj)
                    break

        self.logger.debug(
            f"Actor {self.actor.actor_id} added transformer {transformer_name} to stream {stream_id}"
        )

    def remove_transformer(self, stream_id: str, transformer_name: str) -> None:
        """
        Remove a transformer from a stream subscription.

        Args:
            stream_id: ID of the stream
            transformer_name: Name of the transformer to remove
        """
        # Check if transformer exists
        if stream_id not in self._transformers or transformer_name not in self._transformers[stream_id]:
            self.logger.warning(
                f"Actor {self.actor.actor_id} tried to remove non-existent transformer "
                f"{transformer_name} from stream {stream_id}"
            )
            return

        # Remove transformer
        del self._transformers[stream_id][transformer_name]

        # Update subscription if it exists
        if stream_id in self.streams:
            for subscription in self.streams[stream_id].subscriptions:
                if subscription.subscriber_id == self.actor.actor_id:
                    subscription.transformers = [
                        t for t in subscription.transformers if t.name != transformer_name
                    ]
                    break

        self.logger.debug(
            f"Actor {self.actor.actor_id} removed transformer {transformer_name} from stream {stream_id}"
        )

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get metrics about the stream manager.

        Returns:
            Dictionary of stream manager metrics
        """
        metrics = self._metrics.copy()
        metrics["stream_count"] = len(self.streams)
        metrics["streams"] = {
            stream_id: stream.get_metrics()
            for stream_id, stream in self.streams.items()
        }
        return metrics

    def _get_or_create_stream(self, stream_id: str) -> Stream:
        """
        Get or create a stream.

        Args:
            stream_id: ID of the stream

        Returns:
            The stream
        """
        if stream_id not in self.streams:
            self.streams[stream_id] = Stream(stream_id)
            self._metrics["streams_created"] += 1
            self.logger.debug(f"Created stream {stream_id}")
        return self.streams[stream_id]

    # End of class
