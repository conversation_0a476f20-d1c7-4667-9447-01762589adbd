"""
Message Processor Module
======================

This module defines the MessageProcessor class, which is responsible for
processing messages from an actor's mailbox.

The MessageProcessor implements the core message processing loop, handling
message retrieval, processing, error handling, and heartbeat sending.
"""

import asyncio
import logging
import time
import traceback
from typing import Any, Dict, Optional, TYPE_CHECKING

from ..context_wave import ContextWave
from ..message import Message, MessageType

# Avoid circular imports
if TYPE_CHECKING:
    from ..actor import Actor


class MessageProcessor:
    """
    Handles the processing of messages from an actor's mailbox.

    This class encapsulates the message processing logic that was previously
    part of the Actor class. It implements the core message processing loop,
    error handling, and heartbeat sending.

    Attributes:
        actor: The actor that owns this processor
        logger: Logger for this processor
    """

    def __init__(self, actor: 'Actor'):
        """
        Initialize the message processor.

        Args:
            actor: The actor that owns this processor
        """
        self.actor = actor
        self.logger = logging.getLogger("vibe_check_actor_system.processor")

    async def process_messages(self) -> None:
        """
        Process messages from the actor's mailbox.

        This is the main message processing loop. It retrieves messages from
        the mailbox, processes them, and handles any errors that occur.

        It also sends heartbeats to the supervisor if needed and processes
        any pending messages that were queued while the actor was not ready.
        """
        try:
            # Process any pending messages if the actor is ready
            if self.actor._ready and self.actor._pending_messages:
                await self._process_pending_messages()

            # Main message processing loop
            while self.actor.is_running:
                try:
                    # Check if we have pending messages to process now that the actor is ready
                    if self.actor._ready and self.actor._pending_messages:
                        await self._process_pending_messages()

                    # Get the next message with a short timeout
                    message = await self._get_next_message()

                    # If we're no longer running, exit the loop
                    if not self.actor.is_running:
                        self.logger.info(f"Actor {self.actor.actor_id} stopped while processing messages")
                        break

                    # Process the message if we got one
                    if message is not None:
                        await self._process_message(message)

                    # Send heartbeat if needed
                    await self._send_heartbeat_if_needed()

                except asyncio.TimeoutError:
                    # No message received, yield control to the event loop
                    await asyncio.sleep(0.001)

                    # Check if we have pending messages to process now
                    if self.actor._ready and self.actor._pending_messages:
                        await self._process_pending_messages()

                    continue
                except asyncio.CancelledError:
                    # Task was cancelled, exit gracefully
                    self.logger.info(f"Actor {self.actor.actor_id} message processing task cancelled")
                    break
                except Exception as e:
                    # Handle unexpected errors in the processing loop
                    await self._handle_processing_error(e)

                    # Brief pause to prevent tight error loops
                    await asyncio.sleep(0.1)

        except asyncio.CancelledError:
            self.logger.info(f"Actor {self.actor.actor_id} message processing task cancelled")
        except Exception as e:
            # This is a fatal error in the message processing loop
            await self._handle_fatal_error(e)
        finally:
            self.logger.info(f"Actor {self.actor.actor_id} message processing loop exited")

    async def _get_next_message(self) -> Optional[Message]:
        """
        Get the next message from the mailbox with a timeout.

        Returns:
            The next message, or None if no message was available
        """
        try:
            # Use a shorter timeout to be more responsive to shutdown requests
            message = await asyncio.wait_for(self.actor.mailbox.get(), timeout=0.05)

            # Check if message has expired (could have been in queue for a while)
            if message.is_expired():
                self.logger.warning(f"Actor {self.actor.actor_id} discarded expired message of type {message.type.name}")
                self.actor.mailbox.task_done()
                return None

            return message
        except asyncio.TimeoutError:
            return None

    async def _process_message(self, message: Message) -> None:
        """
        Process a message.

        Args:
            message: The message to process
        """
        try:
            # Handle the message
            await self.actor._handle_message(message)

            # Mark the message as done
            self.actor.mailbox.task_done()

            # Update last activity time
            self.actor._metrics["last_activity"] = time.time()

        except Exception as e:
            # Update error metrics
            self.actor._metrics["errors"] += 1

            # Log the error with more details
            error_details = traceback.format_exc()
            self.logger.error(f"Error processing message in {self.actor.actor_id}: {e}\n{error_details}")

            # Mark the message as done even if processing failed
            self.actor.mailbox.task_done()

            # Notify supervisor of error if available
            await self._notify_supervisor_of_error(e, error_details, message)

    async def _send_heartbeat_if_needed(self) -> None:
        """
        Send a heartbeat to the supervisor if needed.
        """
        current_time = time.time()
        if (self.actor.supervisor_id and
            current_time - self.actor._last_heartbeat > self.actor._heartbeat_interval):

            # Update metrics before sending heartbeat
            self.actor._metrics["uptime"] = current_time - self.actor._metrics["start_time"]

            # Create heartbeat payload
            self.actor._last_heartbeat = current_time
            heartbeat_payload = {
                "actor_id": self.actor.actor_id,
                "timestamp": current_time,
                "metrics": self.actor._metrics,
                "actor_type": self.actor.actor_type,
                "tags": list(self.actor.tags),
                "capabilities": list(self.actor.capabilities)
            }

            # Send heartbeat
            try:
                await self.actor.send(
                    self.actor.supervisor_id,
                    MessageType.HEARTBEAT,
                    heartbeat_payload
                )
                self.logger.debug(f"Actor {self.actor.actor_id} sent heartbeat to supervisor {self.actor.supervisor_id}")
            except Exception as e:
                self.logger.warning(f"Failed to send heartbeat to supervisor: {e}")

    async def _notify_supervisor_of_error(self, error: Exception, error_details: str,
                                         message: Optional[Message] = None) -> None:
        """
        Notify the supervisor of an error.

        Args:
            error: The error that occurred
            error_details: Detailed error information (traceback)
            message: The message that caused the error (if any)
        """
        if self.actor.supervisor_id:
            try:
                error_context = ContextWave()
                error_context.metadata["sender_id"] = self.actor.actor_id
                error_payload = {
                    "error": str(error),
                    "error_details": error_details,
                    "actor_id": self.actor.actor_id,
                    "message_type": message.type.name if message and hasattr(message, 'type') else "unknown",
                    "timestamp": time.time(),
                    "fatal": False
                }
                await self.actor.send(self.actor.supervisor_id, MessageType.ERROR, error_payload, error_context)
            except Exception as send_error:
                self.logger.error(f"Failed to notify supervisor of error: {send_error}")

    async def _handle_processing_error(self, error: Exception) -> None:
        """
        Handle a non-fatal error in the message processing loop.

        Args:
            error: The error that occurred
        """
        # Update error metrics
        self.actor._metrics["errors"] += 1

        # Log the error with more details
        error_details = traceback.format_exc()
        self.logger.error(f"Error in message processing loop of {self.actor.actor_id}: {error}\n{error_details}")

        # Notify supervisor of error if available
        await self._notify_supervisor_of_error(error, error_details)

    async def _process_pending_messages(self) -> None:
        """
        Process any pending messages that were queued while the actor was not ready.

        This method is called when the actor becomes ready and has pending messages
        to process. It moves the pending messages to the mailbox for processing.
        """
        if not self.actor._pending_messages:
            return

        self.logger.info(f"Actor {self.actor.actor_id} processing {len(self.actor._pending_messages)} pending messages")

        # Process pending messages in order of receipt
        pending_messages = self.actor._pending_messages.copy()
        self.actor._pending_messages.clear()

        for queued_item in pending_messages:
            try:
                # Extract the message and queued time
                # We know queued_item is a dict because that's how we store pending messages in actor.receive()
                if not isinstance(queued_item, dict):
                    self.logger.warning(f"Actor {self.actor.actor_id} found invalid pending message (not a dict): {queued_item}")
                    continue

                message = queued_item.get("message")
                queued_at = queued_item.get("queued_at", 0)

                if not message:
                    self.logger.warning(f"Actor {self.actor.actor_id} found invalid pending message: {queued_item}")
                    continue

                # Check if message has expired
                if hasattr(message, "is_expired") and callable(message.is_expired) and message.is_expired():
                    message_type = message.type.name if hasattr(message, 'type') else 'unknown'
                    self.logger.warning(f"Actor {self.actor.actor_id} discarded expired pending message of type {message_type}")
                    continue

                # Log how long the message was queued
                queue_time = time.time() - queued_at
                message_type = message.type.name if hasattr(message, 'type') else 'unknown'
                self.logger.debug(f"Actor {self.actor.actor_id} processing pending message of type {message_type} (queued for {queue_time:.2f}s)")

                # Add to mailbox for processing
                await self.actor.mailbox.put(message)

            except Exception as e:
                self.logger.error(f"Error processing pending message in actor {self.actor.actor_id}: {e}")
                import traceback
                self.logger.error(traceback.format_exc())

                # Update error metrics
                self.actor._metrics["errors"] += 1

    async def _handle_fatal_error(self, error: Exception) -> None:
        """
        Handle a fatal error in the message processing loop.

        Args:
            error: The error that occurred
        """
        # This is a fatal error in the message processing loop
        self.logger.error(f"Fatal error in actor {self.actor.actor_id} message processing loop: {error}")
        error_details = traceback.format_exc()
        self.logger.error(error_details)

        # Update metrics
        self.actor._metrics["errors"] += 1

        # Notify supervisor of fatal error if available
        if self.actor.supervisor_id:
            try:
                # Use direct send to avoid potential recursion
                error_context = ContextWave()
                error_context.metadata["sender_id"] = self.actor.actor_id
                error_payload = {
                    "error": str(error),
                    "error_details": error_details,
                    "actor_id": self.actor.actor_id,
                    "timestamp": time.time(),
                    "fatal": True
                }

                # Try to get supervisor from registry if not in known_actors
                supervisor = None
                if self.actor.supervisor_id in self.actor._known_actors:
                    supervisor = self.actor._known_actors[self.actor.supervisor_id]
                else:
                    try:
                        from ..actor_registry import get_registry
                        registry = get_registry()
                        supervisor = registry.get_actor(self.actor.supervisor_id)
                    except (ImportError, AttributeError):
                        pass

                if supervisor:
                    message = Message(
                        type=MessageType.ERROR,
                        payload=error_payload,
                        context=error_context,
                        recipient_id=self.actor.supervisor_id,
                        sender_id=self.actor.actor_id
                    )
                    await supervisor.receive(message)
                else:
                    self.logger.error(f"Could not find supervisor {self.actor.supervisor_id} to notify of fatal error")
            except Exception as e2:
                self.logger.error(f"Failed to notify supervisor of fatal error: {e2}")
