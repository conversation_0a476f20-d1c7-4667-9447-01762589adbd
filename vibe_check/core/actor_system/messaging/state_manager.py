"""
State Manager Module
================

This module defines the StateManager class, which is responsible for saving and
loading actor state, enabling persistence across restarts.

The StateManager implements the StateManagerProtocol and provides methods for
saving and loading actor state, as well as getting and setting state.
"""

import asyncio
import json
import logging
import os
import pickle
import time
from enum import Enum
from pathlib import Path
from typing import Any, Dict, Optional, Set, Tuple, Union, cast, Protocol, runtime_checkable

from ..protocols import StateManagerProtocol, ActorProtocol


class StateFormat(Enum):
    """
    Enumeration of state serialization formats.

    These formats determine how actor state is serialized for persistence.
    """
    JSON = "json"
    PICKLE = "pickle"
    CUSTOM = "custom"


@runtime_checkable
class StateSerializer(Protocol):
    """
    Protocol for state serializers.

    State serializers are responsible for converting actor state to and from
    a serialized format for persistence.
    """

    def serialize(self, state: Dict[str, Any]) -> bytes:
        """
        Serialize state to bytes.

        Args:
            state: State to serialize

        Returns:
            Serialized state as bytes
        """
        ...

    def deserialize(self, data: bytes) -> Dict[str, Any]:
        """
        Deserialize state from bytes.

        Args:
            data: Serialized state as bytes

        Returns:
            Deserialized state
        """
        ...


class JsonSerializer:
    """
    JSON serializer for actor state.

    This serializer converts actor state to and from JSON format for persistence.
    """

    def serialize(self, state: Dict[str, Any]) -> bytes:
        """
        Serialize state to JSON bytes.

        Args:
            state: State to serialize

        Returns:
            Serialized state as JSON bytes
        """
        return json.dumps(state, default=self._json_default).encode("utf-8")

    def deserialize(self, data: bytes) -> Dict[str, Any]:
        """
        Deserialize state from JSON bytes.

        Args:
            data: Serialized state as JSON bytes

        Returns:
            Deserialized state
        """
        return json.loads(data.decode("utf-8"))

    def _json_default(self, obj: Any) -> Any:
        """
        Default JSON serialization for non-serializable objects.

        Args:
            obj: Object to serialize

        Returns:
            Serialized object

        Raises:
            TypeError: If the object cannot be serialized
        """
        if isinstance(obj, set):
            return list(obj)
        if isinstance(obj, Enum):
            return obj.value
        if hasattr(obj, "__dict__"):
            return obj.__dict__
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


class PickleSerializer:
    """
    Pickle serializer for actor state.

    This serializer converts actor state to and from pickle format for persistence.
    """

    def serialize(self, state: Dict[str, Any]) -> bytes:
        """
        Serialize state to pickle bytes.

        Args:
            state: State to serialize

        Returns:
            Serialized state as pickle bytes
        """
        return pickle.dumps(state)

    def deserialize(self, data: bytes) -> Dict[str, Any]:
        """
        Deserialize state from pickle bytes.

        Args:
            data: Serialized state as pickle bytes

        Returns:
            Deserialized state
        """
        return pickle.loads(data)


class StateError(Exception):
    """Base class for state-related exceptions."""
    pass


class StateNotFoundError(StateError):
    """Exception raised when state is not found."""
    pass


@runtime_checkable
class StateStorage(Protocol):
    """
    Protocol for state storage.

    State storage is responsible for saving and loading serialized state.
    """

    async def save(self, actor_id: str, data: bytes) -> None:
        """
        Save serialized state.

        Args:
            actor_id: ID of the actor
            data: Serialized state as bytes
        """
        ...

    async def load(self, actor_id: str) -> bytes:
        """
        Load serialized state.

        Args:
            actor_id: ID of the actor

        Returns:
            Serialized state as bytes

        Raises:
            StateNotFoundError: If state is not found
        """
        ...

    async def exists(self, actor_id: str) -> bool:
        """
        Check if state exists.

        Args:
            actor_id: ID of the actor

        Returns:
            True if state exists, False otherwise
        """
        ...

    async def delete(self, actor_id: str) -> None:
        """
        Delete state.

        Args:
            actor_id: ID of the actor
        """
        ...


class FileStorage:
    """
    File-based state storage.

    This storage saves and loads serialized state to and from files.
    """

    def __init__(self, directory: Union[str, Path], extension: str = ".state"):
        """
        Initialize the file storage.

        Args:
            directory: Directory to store state files
            extension: Extension for state files
        """
        self.directory = Path(directory)
        self.extension = extension
        self.directory.mkdir(parents=True, exist_ok=True)

    def _get_path(self, actor_id: str) -> Path:
        """
        Get the path for an actor's state file.

        Args:
            actor_id: ID of the actor

        Returns:
            Path to the actor's state file
        """
        return self.directory / f"{actor_id}{self.extension}"

    async def save(self, actor_id: str, data: bytes) -> None:
        """
        Save serialized state to a file.

        Args:
            actor_id: ID of the actor
            data: Serialized state as bytes
        """
        path = self._get_path(actor_id)
        # Use asyncio.to_thread for non-blocking file I/O
        await asyncio.to_thread(self._write_file, path, data)

    async def load(self, actor_id: str) -> bytes:
        """
        Load serialized state from a file.

        Args:
            actor_id: ID of the actor

        Returns:
            Serialized state as bytes

        Raises:
            StateNotFoundError: If state file is not found
        """
        path = self._get_path(actor_id)
        if not path.exists():
            raise StateNotFoundError(f"State not found for actor {actor_id}")

        # Use asyncio.to_thread for non-blocking file I/O
        return await asyncio.to_thread(self._read_file, path)

    async def exists(self, actor_id: str) -> bool:
        """
        Check if state file exists.

        Args:
            actor_id: ID of the actor

        Returns:
            True if state file exists, False otherwise
        """
        path = self._get_path(actor_id)
        return path.exists()

    async def delete(self, actor_id: str) -> None:
        """
        Delete state file.

        Args:
            actor_id: ID of the actor
        """
        path = self._get_path(actor_id)
        if path.exists():
            # Use asyncio.to_thread for non-blocking file I/O
            await asyncio.to_thread(path.unlink)

    def _write_file(self, path: Path, data: bytes) -> None:
        """
        Write data to a file.

        Args:
            path: Path to the file
            data: Data to write
        """
        # Write to a temporary file first, then rename for atomicity
        temp_path = path.with_suffix(f"{path.suffix}.tmp")
        temp_path.write_bytes(data)
        temp_path.rename(path)

    def _read_file(self, path: Path) -> bytes:
        """
        Read data from a file.

        Args:
            path: Path to the file

        Returns:
            Data read from the file
        """
        return path.read_bytes()


class StateManager(StateManagerProtocol):
    """
    Manages actor state persistence.

    This component is responsible for saving and loading actor state,
    enabling persistence across restarts. It implements the StateManagerProtocol.

    Attributes:
        actor: The actor that owns this component
        state_format: Format for state serialization
        storage: Storage for serialized state
        serializer: Serializer for state
        logger: Logger for this component
    """

    def __init__(self, actor: 'ActorProtocol', state_format: StateFormat = StateFormat.JSON,
                storage_dir: Optional[Union[str, Path]] = None,
                custom_serializer: Optional[StateSerializer] = None,
                custom_storage: Optional[StateStorage] = None):
        """
        Initialize the state manager.

        Args:
            actor: The actor that owns this component
            state_format: Format for state serialization
            storage_dir: Directory for state storage
            custom_serializer: Custom serializer for state
            custom_storage: Custom storage for state
        """
        self.actor = actor
        self.state_format = state_format
        self.logger = logging.getLogger("vibe_check_actor_system.state_manager")

        # Set up serializer
        if custom_serializer:
            self.serializer = custom_serializer
        elif state_format == StateFormat.JSON:
            self.serializer = JsonSerializer()
        elif state_format == StateFormat.PICKLE:
            self.serializer = PickleSerializer()
        else:
            raise ValueError(f"Unsupported state format: {state_format}")

        # Set up storage
        if custom_storage:
            self.storage = custom_storage
        else:
            if not storage_dir:
                # Default to a directory in the project
                storage_dir = os.path.join(
                    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                    "data", "actor_state"
                )
            self.storage = FileStorage(storage_dir)

        # Initialize state
        self._state: Dict[str, Any] = {}
        self._last_save_time: float = 0.0
        self._save_interval: float = 60.0  # seconds
        self._auto_save: bool = True
        self._save_task: Optional[asyncio.Task[None]] = None
        self._save_lock = asyncio.Lock()
        self._metrics: Dict[str, Any] = {
            "saves": 0,
            "loads": 0,
            "save_errors": 0,
            "load_errors": 0,
            "last_save_time": 0.0,
            "last_load_time": 0.0,
            "state_size_bytes": 0,
            "save_duration": 0.0,
            "load_duration": 0.0
        }

    async def save_state(self) -> None:
        """Save actor state to disk."""
        async with self._save_lock:
            start_time = time.time()
            try:
                # Get state
                state = self.get_state()

                # Serialize state
                data = self.serializer.serialize(state)

                # Save state
                await self.storage.save(self.actor.actor_id, data)

                # Update metrics
                self._metrics["saves"] += 1
                self._metrics["last_save_time"] = time.time()
                self._metrics["state_size_bytes"] = len(data)
                self._metrics["save_duration"] = time.time() - start_time
                self._last_save_time = time.time()

                self.logger.info(
                    f"Saved state for actor {self.actor.actor_id} "
                    f"({len(data)} bytes, {self._metrics['save_duration']:.6f}s)"
                )
            except Exception as e:
                self._metrics["save_errors"] += 1
                self.logger.error(f"Failed to save state for actor {self.actor.actor_id}: {e}")
                raise StateError(f"Failed to save state: {e}") from e

    async def load_state(self) -> None:
        """Load actor state from disk."""
        start_time = time.time()
        try:
            # Check if state exists
            if not await self.storage.exists(self.actor.actor_id):
                self.logger.info(f"No state found for actor {self.actor.actor_id}")
                return

            # Load state
            data = await self.storage.load(self.actor.actor_id)

            # Deserialize state
            state = self.serializer.deserialize(data)

            # Set state
            self.set_state(state)

            # Update metrics
            self._metrics["loads"] += 1
            self._metrics["last_load_time"] = time.time()
            self._metrics["state_size_bytes"] = len(data)
            self._metrics["load_duration"] = time.time() - start_time

            self.logger.info(
                f"Loaded state for actor {self.actor.actor_id} "
                f"({len(data)} bytes, {self._metrics['load_duration']:.6f}s)"
            )
        except StateNotFoundError:
            self.logger.info(f"No state found for actor {self.actor.actor_id}")
        except Exception as e:
            self._metrics["load_errors"] += 1
            self.logger.error(f"Failed to load state for actor {self.actor.actor_id}: {e}")
            raise StateError(f"Failed to load state: {e}") from e

    def get_state(self) -> Dict[str, Any]:
        """
        Get actor state for persistence.

        Returns:
            Dictionary of state to save
        """
        # Get state from actor if available
        if hasattr(self.actor, "get_state") and callable(getattr(self.actor, "get_state")):
            return self.actor.get_state()

        # Otherwise, return internal state
        return self._state.copy()

    def set_state(self, state: Dict[str, Any]) -> None:
        """
        Set actor state from persistence.

        Args:
            state: Dictionary of state to restore
        """
        # Set state on actor if available
        if hasattr(self.actor, "set_state") and callable(getattr(self.actor, "set_state")):
            self.actor.set_state(state)
        else:
            # Otherwise, update internal state
            self._state.update(state)

    async def start_auto_save(self, interval: float = 60.0) -> None:
        """
        Start automatic state saving.

        Args:
            interval: Interval in seconds between saves
        """
        self._auto_save = True
        self._save_interval = interval

        # Cancel existing task if any
        if self._save_task and not self._save_task.done():
            self._save_task.cancel()
            try:
                await self._save_task
            except asyncio.CancelledError:
                pass

        # Start new task
        self._save_task = asyncio.create_task(self._auto_save_task())
        self.logger.info(
            f"Started auto-save for actor {self.actor.actor_id} "
            f"with interval {interval}s"
        )

    async def stop_auto_save(self) -> None:
        """Stop automatic state saving."""
        self._auto_save = False

        # Cancel existing task if any
        if self._save_task and not self._save_task.done():
            self._save_task.cancel()
            try:
                await self._save_task
            except asyncio.CancelledError:
                pass

            self._save_task = None

        self.logger.info(f"Stopped auto-save for actor {self.actor.actor_id}")

    async def _auto_save_task(self) -> None:
        """Task for automatic state saving."""
        try:
            while self._auto_save:
                # Wait for the save interval
                await asyncio.sleep(self._save_interval)

                # Save state
                try:
                    await self.save_state()
                except Exception as e:
                    self.logger.error(f"Error in auto-save task: {e}")
        except asyncio.CancelledError:
            # Task was cancelled, exit gracefully
            self.logger.debug(f"Auto-save task cancelled for actor {self.actor.actor_id}")
            raise

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get metrics about the state manager.

        Returns:
            Dictionary of state manager metrics
        """
        metrics = self._metrics.copy()
        metrics["auto_save_enabled"] = self._auto_save
        metrics["save_interval"] = self._save_interval
        metrics["time_since_last_save"] = time.time() - self._last_save_time if self._last_save_time > 0 else 0.0
        return metrics

    async def delete_state(self) -> None:
        """Delete actor state from disk."""
        try:
            await self.storage.delete(self.actor.actor_id)
            self.logger.info(f"Deleted state for actor {self.actor.actor_id}")
        except Exception as e:
            self.logger.error(f"Failed to delete state for actor {self.actor.actor_id}: {e}")
            raise StateError(f"Failed to delete state: {e}") from e
