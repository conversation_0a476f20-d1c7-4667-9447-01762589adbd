"""
Registry Synchronizer Module
=======================

This module provides synchronization between the ActorRegistry and ActorInitializer
to ensure consistency. It implements atomic operations for registry updates,
adds consistency validation, and provides recovery mechanisms for inconsistent states.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Set, Tuple, Any, Callable
import json
from pathlib import Path
import os

from .consolidated_initializer import ActorState, get_initializer
from .exceptions import ActorInitializationError
from .actor_registry import ActorRegistry, get_registry, get_registry_async
from .actor import Actor

logger = logging.getLogger("vibe_check_registry_synchronizer")


class RegistryInconsistencyError(Exception):
    """Exception raised when an inconsistency is detected between registry and initializer."""

    def __init__(self, message: str, details: Dict[str, Any]):
        """
        Initialize the exception.

        Args:
            message: Error message
            details: Details about the inconsistency
        """
        self.details = details
        super().__init__(message)


class RegistrySynchronizer:
    """
    Synchronizes the ActorRegistry and ActorInitializer.

    This class ensures consistency between the registry and initializer,
    implements atomic operations for registry updates, adds consistency
    validation, and provides recovery mechanisms for inconsistent states.
    """

    def __init__(self, output_dir: Optional[str] = None):
        """
        Initialize the synchronizer.

        Args:
            output_dir: Optional directory to save synchronization data
        """
        self._initializer = get_initializer()
        self._registry = get_registry()
        self._output_dir = output_dir
        self._lock = asyncio.Lock()
        self._initialized = False
        self._callbacks_registered = False
        self._inconsistencies: List[Dict[str, Any]] = []
        self._last_check_time = 0.0
        self._check_interval = 10.0  # seconds
        self._check_task: Optional[asyncio.Task] = None

        # Create output directory if specified
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

    async def initialize(self) -> None:
        """
        Initialize the synchronizer.

        This method patches the registry and initializer methods to ensure
        consistency and starts a periodic consistency check.
        """
        async with self._lock:
            if self._initialized:
                logger.warning("RegistrySynchronizer already initialized")
                return

            # Register callbacks with the registry and initializer
            if not self._callbacks_registered:
                await self._register_callbacks()
                self._callbacks_registered = True

            # Start periodic consistency check
            self._check_task = asyncio.create_task(self._periodic_consistency_check())

            self._initialized = True
            logger.info("RegistrySynchronizer initialized")

    async def _register_callbacks(self) -> None:
        """
        Register callbacks with the registry and initializer.

        This method patches the registry and initializer methods to ensure
        consistency between them.
        """
        # Patch registry methods
        original_register_actor = self._registry.register_actor

        def wrapped_register_actor(
            actor_id: str,
            actor: Actor,
            actor_type: Optional[str] = None,
            tags: Optional[Set[str]] = None,
            capabilities: Optional[Set[str]] = None
        ) -> None:
            # Call the original method
            original_register_actor(actor_id, actor, actor_type, tags, capabilities)

            # Ensure actor is registered with initializer
            asyncio.create_task(self._ensure_actor_in_initializer(actor_id, actor))

        # Replace the method
        self._registry.register_actor = wrapped_register_actor

        original_unregister_actor = self._registry.unregister_actor

        def wrapped_unregister_actor(actor_id: str) -> None:
            # Call the original method
            original_unregister_actor(actor_id)

            # Ensure actor is unregistered from initializer
            asyncio.create_task(self._ensure_actor_not_in_initializer(actor_id))

        # Replace the method
        self._registry.unregister_actor = wrapped_unregister_actor

        # Patch initializer methods
        original_register_actor_init = self._initializer.register_actor

        async def wrapped_register_actor_init(actor_id: str) -> None:
            # Call the original method
            await original_register_actor_init(actor_id)

            # Ensure actor is registered with registry
            asyncio.create_task(self._ensure_actor_in_registry(actor_id))

        # Replace the method
        self._initializer.register_actor = wrapped_register_actor_init

        logger.info("Registered callbacks with registry and initializer")

    async def _ensure_actor_in_initializer(self, actor_id: str, actor: Actor) -> None:
        """
        Ensure an actor is registered with the initializer.

        Args:
            actor_id: ID of the actor
            actor: The actor instance
        """
        try:
            # Check if actor is already registered with initializer
            is_registered = await self._initializer.is_actor_registered(actor_id)

            if not is_registered:
                # Register with initializer
                await self._initializer.register_actor(actor_id)
                logger.info(f"Registered actor {actor_id} with initializer (from registry)")
        except Exception as e:
            logger.error(f"Error ensuring actor {actor_id} is in initializer: {e}")
            self._record_inconsistency(
                "registry_to_initializer",
                f"Failed to register actor {actor_id} with initializer: {e}",
                {"actor_id": actor_id, "error": str(e)}
            )

    async def _ensure_actor_not_in_initializer(self, actor_id: str) -> None:
        """
        Ensure an actor is not registered with the initializer.

        Args:
            actor_id: ID of the actor
        """
        try:
            # Check if actor is registered with initializer
            is_registered = await self._initializer.is_actor_registered(actor_id)

            if is_registered:
                # Unregister from initializer
                # Note: The initializer doesn't have an unregister method,
                # so we'll need to implement this functionality
                logger.warning(f"Cannot unregister actor {actor_id} from initializer: not implemented")
                self._record_inconsistency(
                    "registry_to_initializer",
                    f"Cannot unregister actor {actor_id} from initializer: not implemented",
                    {"actor_id": actor_id}
                )
        except Exception as e:
            logger.error(f"Error ensuring actor {actor_id} is not in initializer: {e}")
            self._record_inconsistency(
                "registry_to_initializer",
                f"Failed to unregister actor {actor_id} from initializer: {e}",
                {"actor_id": actor_id, "error": str(e)}
            )

    async def _ensure_actor_in_registry(self, actor_id: str) -> None:
        """
        Ensure an actor is registered with the registry.

        Args:
            actor_id: ID of the actor
        """
        try:
            # Check if actor is already registered with registry
            actor = self._registry.get_actor(actor_id)

            if actor is None:
                # We can't register with registry because we don't have the actor instance
                logger.warning(f"Cannot register actor {actor_id} with registry: no actor instance")
                self._record_inconsistency(
                    "initializer_to_registry",
                    f"Cannot register actor {actor_id} with registry: no actor instance",
                    {"actor_id": actor_id}
                )
        except Exception as e:
            logger.error(f"Error ensuring actor {actor_id} is in registry: {e}")
            self._record_inconsistency(
                "initializer_to_registry",
                f"Failed to register actor {actor_id} with registry: {e}",
                {"actor_id": actor_id, "error": str(e)}
            )

    def _record_inconsistency(self, type_: str, message: str, details: Dict[str, Any]) -> None:
        """
        Record an inconsistency between registry and initializer.

        Args:
            type_: Type of inconsistency
            message: Error message
            details: Details about the inconsistency
        """
        inconsistency = {
            "type": type_,
            "message": message,
            "details": details,
            "timestamp": time.time()
        }
        self._inconsistencies.append(inconsistency)
        logger.error(f"Registry inconsistency: {message}")

    async def _periodic_consistency_check(self) -> None:
        """
        Periodically check for inconsistencies between registry and initializer.
        """
        try:
            while True:
                # Wait for the check interval
                await asyncio.sleep(self._check_interval)

                # Perform consistency check
                await self.check_consistency()

                # Update last check time
                self._last_check_time = time.time()
        except asyncio.CancelledError:
            logger.info("Periodic consistency check cancelled")
        except Exception as e:
            logger.error(f"Error in periodic consistency check: {e}")

    async def check_consistency(self) -> List[Dict[str, Any]]:
        """
        Check for inconsistencies between registry and initializer.

        Returns:
            List of inconsistencies found
        """
        # Clear previous inconsistencies
        self._inconsistencies = []

        try:
            # Get all actors from registry
            registry_actors = set(self._registry._actors.keys())

            # Get all actors from initializer
            initializer_actors = set()
            with self._initializer._state_lock:
                initializer_actors = set(self._initializer._actor_states.keys())

            # Check for actors in registry but not in initializer
            for actor_id in registry_actors - initializer_actors:
                self._record_inconsistency(
                    "registry_only",
                    f"Actor {actor_id} is in registry but not in initializer",
                    {"actor_id": actor_id}
                )

                # Try to fix the inconsistency
                actor = self._registry.get_actor(actor_id)
                if actor:
                    await self._ensure_actor_in_initializer(actor_id, actor)

            # Check for actors in initializer but not in registry
            for actor_id in initializer_actors - registry_actors:
                self._record_inconsistency(
                    "initializer_only",
                    f"Actor {actor_id} is in initializer but not in registry",
                    {"actor_id": actor_id}
                )

                # We can't fix this inconsistency because we don't have the actor instance

            logger.info(f"Consistency check completed: {len(self._inconsistencies)} inconsistencies found")

            return self._inconsistencies
        except Exception as e:
            logger.error(f"Error checking consistency: {e}")
            return []

    async def export_inconsistencies(self, filename: Optional[str] = None) -> Optional[str]:
        """
        Export inconsistencies to a JSON file.

        Args:
            filename: Optional filename to save to

        Returns:
            Path to the saved file, or None if saving failed
        """
        if not self._output_dir:
            logger.warning("Cannot export inconsistencies: no output directory specified")
            return None

        try:
            # Create a default filename if none provided
            if not filename:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"registry_inconsistencies_{timestamp}.json"

            filepath = Path(self._output_dir) / filename

            # Prepare data for serialization
            data = {
                "inconsistencies": self._inconsistencies,
                "timestamp": time.time(),
                "last_check_time": self._last_check_time
            }

            # Save to file
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Exported inconsistencies to {filepath}")
            return str(filepath)

        except Exception as e:
            logger.error(f"Failed to export inconsistencies: {e}")
            return None

    async def stop(self) -> None:
        """Stop the synchronizer."""
        if self._check_task and not self._check_task.done():
            self._check_task.cancel()
            try:
                await self._check_task
            except asyncio.CancelledError:
                pass

        logger.info("RegistrySynchronizer stopped")


# Singleton instance
_synchronizer = None


def get_registry_synchronizer(output_dir: Optional[str] = None) -> RegistrySynchronizer:
    """
    Get the singleton registry synchronizer instance.

    Args:
        output_dir: Optional directory to save synchronization data

    Returns:
        The registry synchronizer instance
    """
    global _synchronizer

    if _synchronizer is None:
        _synchronizer = RegistrySynchronizer(output_dir)
        logger.info("Created new registry synchronizer")

    return _synchronizer


def reset_registry_synchronizer() -> None:
    """Reset the singleton registry synchronizer instance."""
    global _synchronizer

    # Don't try to stop the synchronizer, just reset it
    # This avoids issues with asyncio.create_task outside of an event loop
    _synchronizer = None
    logger.info("Reset registry synchronizer")
