"""
Actor System Diagnostics Module
==========================

This module provides diagnostic tools for the actor system, including
initialization tracking, dependency visualization, and error reporting.
"""

import asyncio
import enum
import json
import logging
import time
from typing import Any, Dict, List, Optional, Set, Tuple, Union

from .actor_state import ActorState

# Configure logging
logger = logging.getLogger("vibe_check_actor_diagnostics")

# Define initialization steps as an enum for type safety
class InitializationStep(enum.Enum):
    """
    Enum representing the steps in the actor initialization process.

    These steps provide more granular tracking than the actor states,
    allowing for better diagnostics during initialization.

    Enhanced with:
    - Additional steps for dependency resolution
    - Cleanup and rollback steps
    - Start completion step
    """
    REGISTRATION = "registration"
    DEPENDENCY_CHECK = "dependency_check"
    DEPENDENCY_RESOLUTION = "dependency_resolution"  # Added for dependency resolution phase
    INITIALIZATION_START = "initialization_start"
    INITIALIZATION_COMPLETE = "initialization_complete"
    START_BEGIN = "start_begin"
    START_COMPLETE = "start_complete"  # Added for start completion
    READY = "ready"
    FAILED = "failed"
    CLEANUP = "cleanup"  # Added for cleanup phase
    ROLLBACK = "rollback"  # Added for rollback phase
    SYNCHRONIZATION = "synchronization"  # For tracking synchronization points


class InitializationTracker:
    """
    Tracks the initialization process for actors in the system.

    This class records events during actor initialization, including state
    transitions, dependencies, and errors. It provides methods for visualizing
    the initialization process and diagnosing issues.
    """

    def __init__(self) -> None:
        """Initialize the tracker."""
        self._events: List[Dict[str, Any]] = []
        self._dependencies: Dict[str, Set[str]] = {}
        self._lock = asyncio.Lock()
        logger.info("Initialization tracker created")

    async def record_event(self, actor_id: str, step: Union[InitializationStep, str],
                          details: Optional[Dict[str, Any]] = None,
                          error: Optional[Exception] = None) -> None:
        """
        Record an initialization event.

        Args:
            actor_id: ID of the actor
            step: Initialization step (either an InitializationStep enum or a string)
            details: Optional details about the event
            error: Optional error that occurred
        """
        async with self._lock:
            # Convert step to string if it's an enum
            step_value = step.value if isinstance(step, InitializationStep) else str(step)

            event = {
                "actor_id": actor_id,
                "step": step_value,
                "timestamp": time.time(),
                "details": details or {}
            }

            if error:
                event["error"] = {
                    "type": type(error).__name__,
                    "message": str(error)
                }

            self._events.append(event)
            logger.debug(f"Recorded initialization event: {event}")

    async def record_dependency(self, actor_id: str, dependency_id: str) -> None:
        """
        Record a dependency between actors.

        Args:
            actor_id: ID of the dependent actor
            dependency_id: ID of the actor that must be ready first
        """
        async with self._lock:
            if actor_id not in self._dependencies:
                self._dependencies[actor_id] = set()

            self._dependencies[actor_id].add(dependency_id)
            logger.debug(f"Recorded dependency: {actor_id} depends on {dependency_id}")

    async def get_initialization_timeline(self) -> List[Dict[str, Any]]:
        """
        Get the initialization timeline.

        Returns:
            List of initialization events in chronological order
        """
        async with self._lock:
            return sorted(self._events, key=lambda e: e["timestamp"])

    async def get_actor_timeline(self, actor_id: str) -> List[Dict[str, Any]]:
        """
        Get the initialization timeline for a specific actor.

        Args:
            actor_id: ID of the actor

        Returns:
            List of initialization events for the actor in chronological order
        """
        async with self._lock:
            actor_events = [e for e in self._events if e["actor_id"] == actor_id]
            return sorted(actor_events, key=lambda e: e["timestamp"])

    async def get_dependency_graph(self) -> Dict[str, List[str]]:
        """
        Get the dependency graph.

        Returns:
            Dictionary mapping actor IDs to lists of dependency IDs
        """
        async with self._lock:
            return {actor_id: list(deps) for actor_id, deps in self._dependencies.items()}

    async def get_initialization_errors(self) -> List[Dict[str, Any]]:
        """
        Get all initialization errors.

        Returns:
            List of initialization events with errors
        """
        async with self._lock:
            return [e for e in self._events if "error" in e]

    async def get_actor_errors(self, actor_id: str) -> List[Dict[str, Any]]:
        """
        Get initialization errors for a specific actor.

        Args:
            actor_id: ID of the actor

        Returns:
            List of initialization events with errors for the actor
        """
        async with self._lock:
            return [e for e in self._events if e["actor_id"] == actor_id and "error" in e]

    async def get_initialization_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the initialization process.

        Returns:
            Dictionary with initialization summary
        """
        async with self._lock:
            # Group events by actor
            actor_events: Dict[str, List[Dict[str, Any]]] = {}
            for event in self._events:
                actor_id = event["actor_id"]
                if actor_id not in actor_events:
                    actor_events[actor_id] = []
                actor_events[actor_id].append(event)

            # Calculate statistics
            total_actors = len(actor_events)
            failed_actors = len([actor_id for actor_id, events in actor_events.items()
                               if any("error" in e for e in events)])
            ready_actors = len([actor_id for actor_id, events in actor_events.items()
                              if any(e["step"] == InitializationStep.READY.value for e in events)])

            return {
                "total_actors": total_actors,
                "failed_actors": failed_actors,
                "ready_actors": ready_actors,
                "success_rate": (ready_actors / total_actors) if total_actors > 0 else 0,
                "timestamp": time.time()
            }


# Singleton instance
_tracker: Optional[InitializationTracker] = None


def get_tracker() -> Optional[InitializationTracker]:
    """
    Get the singleton tracker instance.

    Returns:
        The initialization tracker instance, or None if not initialized
    """
    global _tracker
    return _tracker


def initialize_tracker() -> InitializationTracker:
    """
    Initialize the singleton tracker instance.

    Returns:
        The initialization tracker instance
    """
    global _tracker
    if _tracker is None:
        _tracker = InitializationTracker()
        logger.info("Initialized actor system diagnostics tracker")
    return _tracker
