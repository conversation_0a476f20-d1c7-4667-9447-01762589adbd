"""
Project Actor Module
==================

This module defines the ProjectActor class, which orchestrates the analysis
of an entire project. It is responsible for:

1. Discovering files in the project
2. Creating FileActor instances for each file
3. Selecting appropriate tools for analysis based on file characteristics
4. Coordinating the analysis workflow
5. Aggregating results from multiple files and tools
6. Requesting report and visualization generation

The ProjectActor is the entry point for the analysis process and implements
the CAW principles of choreographed interactions and contextual adaptation.
"""

import asyncio
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

from ...models.project_metrics import ProjectMetrics
from ...models.progress_tracker import ProgressTracker
from ..actor import Actor
from ..actor_pool import ActorPool
from ..actor_registry import get_registry
from ..context_wave import ContextWave
from ..message import MessageType

logger = logging.getLogger("vibe_check_actor_system")


class ProjectActor(Actor):
    """
    Actor representing the project being analyzed.

    This is the main entry point for the analysis process.
    """

    def __init__(self, actor_id: str, project_path: Path,
                 metrics: Optional[ProjectMetrics] = None,
                 progress: Optional[ProgressTracker] = None,
                 config: Optional[Dict[str, Any]] = None,
                 supervisor_id: Optional[str] = None,
                 state_dir: Optional[str] = None):
        """
        Initialize the project actor.

        Args:
            actor_id: Unique ID for this actor
            project_path: Path to the project to analyze
            metrics: Optional ProjectMetrics instance to store results
            progress: Optional ProgressTracker to report progress
            config: Optional configuration dictionary
            supervisor_id: Optional ID of the supervisor actor
            state_dir: Optional directory for state persistence
        """
        super().__init__(
            actor_id=actor_id,
            actor_type="project",
            tags={"project", "coordination"},
            capabilities={"project_analysis", "coordination"},
            supervisor_id=supervisor_id,
            state_dir=state_dir
        )

        self.project_path = project_path
        self.metrics = metrics or ProjectMetrics()
        self.progress = progress
        self.config = config or {}
        self.file_actors = {}  # Mapping from file_id to FileActor
        self.tool_actors = {}  # Mapping from tool_id to ToolActor
        self.report_actor = None
        self.visualization_actor = None
        self.active_files = set()  # Set of files currently being analyzed
        self.completed_files = set()  # Set of files that have completed analysis
        self.file_tool_map = {}  # Mapping from file_id to set of tool_ids

        # Analysis state
        self.analysis_start_time = 0.0
        self.analysis_end_time = 0.0
        self.analysis_in_progress = False

        # File actor pool
        self.file_actor_pool = None

    def _initialize_handlers(self) -> None:
        """Initialize message handlers."""
        super()._initialize_handlers()

        # Analysis messages
        self._message_handlers[MessageType.ANALYZE_PROJECT] = self.handle_analyze_project
        self._message_handlers[MessageType.FILE_METADATA] = self.handle_file_metadata
        self._message_handlers[MessageType.ANALYSIS_RESULT] = self.handle_analysis_result
        self._message_handlers[MessageType.FINAL_REPORT] = self.handle_final_report
        self._message_handlers[MessageType.VISUALIZATION] = self.handle_visualization

        # Status messages
        self._message_handlers[MessageType.ANALYSIS_PROGRESS] = self.handle_analysis_progress
        self._message_handlers[MessageType.ANALYSIS_COMPLETE] = self.handle_analysis_complete

        # Stream messages
        self._message_handlers[MessageType.STREAM_DATA] = self.handle_stream_data

    def set_file_actors(self, file_actor_ids: List[str]) -> None:
        """
        Set the file actors for this project.

        Args:
            file_actor_ids: List of file actor IDs
        """
        # Use the registry to get actors
        registry = get_registry()

        # Get actors from registry
        self.file_actors = {}
        for actor_id in file_actor_ids:
            actor = registry.get_actor(actor_id)
            if actor:
                self.file_actors[actor_id] = actor

        logger.info(f"Set {len(self.file_actors)} file actors for project {self.actor_id}")

    def set_tool_actor(self, tool_actor_id: str) -> None:
        """
        Set the tool actor for this project.

        Args:
            tool_actor_id: Tool actor ID
        """
        # Use the registry to get the actor
        registry = get_registry()
        self.tool_actor = registry.get_actor(tool_actor_id)

        if self.tool_actor:
            # Store in tool_actors dictionary
            self.tool_actors[tool_actor_id] = self.tool_actor
            logger.info(f"Set tool actor {tool_actor_id} for project {self.actor_id}")

            # Subscribe to tool results stream
            asyncio.create_task(self.subscribe("tool_results"))
        else:
            logger.warning(f"Tool actor {tool_actor_id} not found for project {self.actor_id}")

    def set_report_actor(self, report_actor_id: str) -> None:
        """
        Set the report actor for this project.

        Args:
            report_actor_id: Report actor ID
        """
        # Use the registry to get the actor
        registry = get_registry()
        self.report_actor = registry.get_actor(report_actor_id)

        if self.report_actor:
            logger.info(f"Set report actor {report_actor_id} for project {self.actor_id}")

            # Subscribe to report stream
            asyncio.create_task(self.subscribe("report"))
        else:
            logger.warning(f"Report actor {report_actor_id} not found for project {self.actor_id}")

    def set_visualization_actor(self, visualization_actor_id: str) -> None:
        """
        Set the visualization actor for this project.

        Args:
            visualization_actor_id: Visualization actor ID
        """
        # Use the registry to get the actor
        registry = get_registry()
        self.visualization_actor = registry.get_actor(visualization_actor_id)

        if self.visualization_actor:
            logger.info(f"Set visualization actor {visualization_actor_id} for project {self.actor_id}")

            # Subscribe to visualization stream
            asyncio.create_task(self.subscribe("visualization"))
        else:
            logger.warning(f"Visualization actor {visualization_actor_id} not found for project {self.actor_id}")

    async def handle_analyze_project(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle an analyze project request.

        Args:
            payload: Message payload with project information
            context: Message context
        """
        # Check if analysis is already in progress
        if self.analysis_in_progress:
            logger.warning(f"Analysis already in progress for project {self.actor_id}")

            # Notify sender
            sender_id = context.metadata.get("sender_id")
            if sender_id:
                error_context = context.propagate()
                await self.send(
                    sender_id,
                    MessageType.ERROR,
                    {
                        "error": "Analysis already in progress",
                        "project_path": str(self.project_path)
                    },
                    error_context
                )
            return

        # Set analysis state
        self.analysis_in_progress = True
        self.analysis_start_time = time.time()

        # Update metrics
        self._metrics["analysis_started"] = self.analysis_start_time

        # Extract payload
        project_path = payload.get("project_path")
        output_dir = payload.get("output_dir")
        completion_callback = payload.get("completion_callback")

        logger.info(f"Received analyze_project request for {project_path}")
        logger.info(f"Output directory: {output_dir}")

        # Set the project path in the metrics
        self.metrics.project_path = project_path

        # Add output directory to context if provided
        if output_dir:
            context.metadata["output_dir"] = output_dir
            logger.info(f"Added output_dir to context: {output_dir}")

        # Publish analysis start to project_status stream
        await self.publish(
            "project_status",
            MessageType.ANALYSIS_PROGRESS,
            {
                "project_path": str(project_path),
                "status": "started",
                "progress": 0.0,
                "timestamp": self.analysis_start_time
            }
        )

        try:
            # Start the analysis
            await self.start_analysis()

            # Generate reports after analysis is complete
            if self.report_actor:
                logger.info("Sending generate_report request to report actor")
                report_context = context.propagate()

                # Create payload with all necessary information
                report_payload = {
                    "project_path": str(project_path),
                    "metrics": self.metrics,
                    "output_dir": output_dir
                }

                # Send the request to the report actor
                await self.send(self.report_actor.actor_id, MessageType.GENERATE_REPORT, report_payload, report_context)
                logger.info("Generate_report request sent")

            # Call the completion callback if provided
            if completion_callback and callable(completion_callback):
                logger.info("Calling completion callback")
                completion_callback(self.metrics)

            # Return the metrics to the sender
            sender_id = context.metadata.get("sender_id")
            if sender_id:
                logger.info(f"Sending final report to {sender_id}")
                await self.send(sender_id, MessageType.FINAL_REPORT, {
                    "project_path": project_path,
                    "metrics": self.metrics
                }, context)

            # Publish analysis complete to project_status stream
            self.analysis_end_time = time.time()
            self._metrics["analysis_completed"] = self.analysis_end_time
            self._metrics["analysis_duration"] = self.analysis_end_time - self.analysis_start_time

            await self.publish(
                "project_status",
                MessageType.ANALYSIS_COMPLETE,
                {
                    "project_path": str(project_path),
                    "status": "completed",
                    "progress": 1.0,
                    "timestamp": self.analysis_end_time,
                    "duration": self._metrics["analysis_duration"],
                    "metrics": self.metrics
                }
            )

            # Save state
            await self._save_state()

            # Return the metrics directly as well
            logger.info("Returning metrics")
            return self.metrics

        except Exception as e:
            # Log error
            logger.error(f"Error during analysis: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Update metrics
            self._metrics["errors"] += 1
            self._metrics["last_error"] = str(e)

            # Publish error to project_status stream
            await self.publish(
                "project_status",
                MessageType.ERROR,
                {
                    "project_path": str(project_path),
                    "error": str(e),
                    "traceback": traceback.format_exc(),
                    "timestamp": time.time()
                }
            )

            # Notify sender
            sender_id = context.metadata.get("sender_id")
            if sender_id:
                error_context = context.propagate()
                await self.send(
                    sender_id,
                    MessageType.ERROR,
                    {
                        "error": str(e),
                        "project_path": str(project_path),
                        "traceback": traceback.format_exc()
                    },
                    error_context
                )

            # Re-raise the exception
            raise

        finally:
            # Reset analysis state
            self.analysis_in_progress = False

    async def start_analysis(self) -> None:
        """
        Start the analysis process.

        Enhanced to use the actor pool for file analysis.
        """
        # Create the initial context
        ctx = ContextWave(
            metadata={"project_name": self.project_path.name},
            configuration=self._get_initial_configuration(),
            adaptive_params={"complexity_threshold": 10, "max_line_length": 100}
        )

        if self.progress:
            self.progress.start_global("Project Analysis", 4)  # File discovery, analysis, reporting, visualization
            self.progress.start_phase("Discovering files")

        logger.info(f"Starting analysis of {self.project_path}")

        # Discover files to analyze
        files = await self._discover_files()

        if self.progress:
            self.progress.complete_phase("File discovery", f"Found {len(files)} files")
            self.progress.increment_global()
            self.progress.start_phase("Analyzing files", len(files))

        logger.info(f"Found {len(files)} files to analyze")

        # Update metrics
        self._metrics["files_discovered"] = len(files)
        self._metrics["discovery_time"] = time.time() - self.analysis_start_time

        # Publish progress update
        await self.publish(
            "project_status",
            MessageType.ANALYSIS_PROGRESS,
            {
                "project_path": str(self.project_path),
                "status": "discovering",
                "progress": 0.1,
                "files_discovered": len(files),
                "timestamp": time.time()
            }
        )

        # Check if we should use the file actor pool
        use_pool = self.config.get("use_actor_pool", True) and len(files) > 5

        if use_pool and not self.file_actor_pool:
            # Create a file actor pool if we don't have one
            from .file_actor import FileActor

            # Create a factory function for file actors
            def file_actor_factory(actor_id, **kwargs):
                file_path = kwargs.get("file_path")
                return FileActor(
                    actor_id=actor_id,
                    file_path=file_path,
                    project_metrics=self.metrics,
                    supervisor_id=self.supervisor_id
                )

            # Create the file actor pool
            num_file_actors = self.config.get("num_file_actors", os.cpu_count() or 4)
            self.file_actor_pool = ActorPool(
                pool_id="file_actor_pool",
                actor_factory=file_actor_factory,
                min_size=max(1, num_file_actors // 2),
                max_size=num_file_actors * 2
            )

            # Start the pool
            await self.file_actor_pool.start()
            logger.info(f"Created file actor pool with {num_file_actors} actors")

        # Process files
        if use_pool and self.file_actor_pool:
            # Use the actor pool for processing files
            logger.info(f"Using actor pool for processing {len(files)} files")

            # Track active files
            self.active_files = set()

            # Process files in batches to avoid overwhelming the pool
            batch_size = 10
            for i in range(0, len(files), batch_size):
                batch = files[i:i+batch_size]
                batch_tasks = []

                for file_path in batch:
                    # Create a unique ID for this file
                    relative_path = file_path.relative_to(self.project_path)
                    file_id = f"file-{relative_path}"

                    # Avoid processing the same file twice
                    if file_id in self.active_files:
                        logger.warning(f"Duplicate file ID: {file_id}")
                        continue

                    # Track active files
                    self.active_files.add(file_id)

                    # Create file context
                    file_context = ctx.propagate()
                    file_context.metadata["file_path"] = str(file_path)
                    file_context.metadata["relative_path"] = str(relative_path)
                    file_context.metadata["file_id"] = file_id

                    # Submit task to pool
                    task = self.file_actor_pool.submit(
                        message_type=MessageType.INIT_ANALYSIS,
                        payload={
                            "project_path": str(self.project_path),
                            "file_path": str(file_path),
                            "file_id": file_id,
                            "config": self.config
                        },
                        context=file_context
                    )
                    batch_tasks.append(task)

                # Wait for batch to complete
                if batch_tasks:
                    results = await asyncio.gather(*batch_tasks)

                    # Process results
                    for result in results:
                        if isinstance(result, dict) and "file_id" in result:
                            file_id = result["file_id"]
                            self.completed_files.add(file_id)
                            self.active_files.discard(file_id)

                            # Update progress
                            if self.progress:
                                self.progress.increment()

                    # Publish progress update
                    progress = len(self.completed_files) / len(files)
                    await self.publish(
                        "project_status",
                        MessageType.ANALYSIS_PROGRESS,
                        {
                            "project_path": str(self.project_path),
                            "status": "analyzing",
                            "progress": 0.1 + (progress * 0.7),  # Scale to 10-80%
                            "files_completed": len(self.completed_files),
                            "files_total": len(files),
                            "timestamp": time.time()
                        }
                    )
        else:
            # Use individual file actors
            logger.info(f"Using individual file actors for processing {len(files)} files")

            # Create file actors
            for file_path in files:
                # Create a unique ID for this file
                relative_path = file_path.relative_to(self.project_path)
                file_id = f"file-{relative_path}"

                # Avoid registering the same file twice
                if file_id in self.file_actors:
                    logger.warning(f"Duplicate file ID: {file_id}")
                    continue

                # Let file import happen on demand
                from .file_actor import FileActor

                # Create and register the file actor
                file_actor = FileActor(
                    actor_id=file_id,
                    file_path=file_path,
                    project_metrics=self.metrics,
                    supervisor_id=self.supervisor_id
                )
                await file_actor.start()

                # Register with registry
                registry = get_registry()
                registry.register_actor(
                    actor_id=file_id,
                    actor=file_actor,
                    actor_type="file",
                    tags={"file", "analysis"},
                    capabilities={"file_analysis", "code_parsing"}
                )

                self.file_actors[file_id] = file_actor

                # Track active files
                self.active_files.add(file_id)

                # Send the initial analysis request
                file_context = ctx.propagate()
                file_context.metadata["file_path"] = str(file_path)
                file_context.metadata["relative_path"] = str(relative_path)

                await self.send(file_id, MessageType.INIT_ANALYSIS, {
                    "project_path": str(self.project_path),
                    "config": self.config
                }, file_context)

                # Publish progress update periodically
                if len(self.active_files) % 10 == 0:
                    progress = len(self.active_files) / len(files)
                    await self.publish(
                        "project_status",
                        MessageType.ANALYSIS_PROGRESS,
                        {
                            "project_path": str(self.project_path),
                            "status": "analyzing",
                            "progress": 0.1 + (progress * 0.4),  # Scale to 10-50%
                            "files_started": len(self.active_files),
                            "files_total": len(files),
                            "timestamp": time.time()
                        }
                    )

    async def _discover_files(self) -> List[Path]:
        """
        Discover files to analyze in the project.

        Returns:
            List of file paths to analyze
        """
        files = []
        exclude_patterns = self.config.get("exclude_patterns", [])
        file_extensions = self.config.get("file_extensions", [".py"])

        # Ensure file extensions start with a dot
        file_extensions = [ext if ext.startswith('.') else f'.{ext}' for ext in file_extensions]

        # Add .md files if we're analyzing docs
        if self.config.get("analyze_docs", False) and ".md" not in file_extensions:
            file_extensions.append(".md")

        # Walk through the project directory
        for root, dirs, filenames in os.walk(str(self.project_path)):
            # Filter out excluded directories
            dirs[:] = [d for d in dirs if not any(pat in os.path.join(root, d) for pat in exclude_patterns)]

            # Filter files by extension
            for filename in filenames:
                if any(filename.endswith(ext) for ext in file_extensions):
                    filepath = Path(os.path.join(root, filename))
                    # Skip if the file matches an exclude pattern
                    if not any(pat in str(filepath) for pat in exclude_patterns):
                        files.append(filepath)

        return files

    def _get_initial_configuration(self) -> Dict[str, Any]:
        """
        Get the initial configuration for the analysis.

        Returns:
            Dictionary with the initial configuration
        """
        # Start with the provided configuration
        configuration = {
            "file_extensions": self.config.get("file_extensions", [".py"]),
            "exclude_patterns": self.config.get("exclude_patterns", []),
            "analyze_docs": self.config.get("analyze_docs", False),
            "tool_configs": self.config.get("tools", {})
        }

        # Add default values for any missing configuration
        if "max_line_length" not in configuration:
            configuration["max_line_length"] = 100

        return configuration

    async def handle_file_metadata(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle file metadata received from a file actor.

        Args:
            payload: Message payload with file metadata
            context: Message context
        """
        file_id = context.metadata.get("sender_id")
        file_path = payload.get("file_path")
        metadata = payload.get("metadata", {})

        logger.info(f"Received metadata for {file_path}")

        # Adapt the complexity threshold based on the file complexity
        complexity = metadata.get("complexity", 0)
        if complexity > context.adaptive_params.get("complexity_threshold", 10):
            # If file is complex, adjust our threshold upward for future files
            new_threshold = (context.adaptive_params.get("complexity_threshold", 10) + complexity) / 2
            context.adaptive_params["complexity_threshold"] = new_threshold
            logger.debug(f"Adjusted complexity threshold to {new_threshold}")

        # Select appropriate tools based on metadata
        selected_tools = await self._select_tools(metadata, context)

        # Store the selected tools for this file
        self.file_tool_map[file_id] = set(selected_tools.keys())

        # Request analysis from each tool
        for tool_id, tool_config in selected_tools.items():
            if tool_id in self._known_actors:
                # Adapt tool configuration based on file metadata
                adapted_config = self._adapt_tool_config(tool_config, metadata)

                # Create a new context for this tool with the adapted configuration
                tool_context = context.propagate()
                tool_context.configuration.update(adapted_config)

                # Send analysis request to tool
                await self.send(tool_id, MessageType.REQUEST_ANALYSIS, {
                    "file_id": file_id,
                    "file_path": file_path,
                    "metadata": metadata
                }, tool_context)
            else:
                logger.error(f"Tool {tool_id} not registered")

    async def handle_analysis_result(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle analysis result from a tool actor.

        Args:
            payload: Message payload with analysis result
            context: Message context
        """
        tool_id = context.metadata.get("sender_id")
        file_id = payload.get("file_id")
        # We don't use analysis_result directly here
        _ = payload.get("result", {})  # analysis_result

        if not file_id:
            logger.error(f"Received analysis result from {tool_id} without file_id")
            return

        logger.info(f"Received {tool_id} analysis result for {file_id}")

        # Update metrics
        self._metrics["analysis_results"] = self._metrics.get("analysis_results", 0) + 1

        # Publish tool result to tool_results stream
        await self.publish(
            "tool_results",
            MessageType.TOOL_RESULT,
            {
                "tool_id": tool_id,
                "file_id": file_id,
                "result_type": payload.get("result_type", "unknown"),
                "timestamp": time.time()
            }
        )

        # If we don't have a tools set for this file, initialize it
        if file_id not in self.file_tool_map:
            self.file_tool_map[file_id] = set()

        # Mark this tool as completed for this file
        self.file_tool_map[file_id].discard(tool_id)

        # Check if all tools have reported for this file
        if not self.file_tool_map[file_id]:
            logger.info(f"All tools reported for {file_id}, marking file as complete")

            # Update progress
            if self.progress:
                self.progress.increment()

            # Mark file as completed
            self.active_files.discard(file_id)
            self.completed_files.add(file_id)

            # Publish file completion to file_analysis stream
            await self.publish(
                "file_analysis",
                MessageType.FILE_METADATA,
                {
                    "file_id": file_id,
                    "status": "completed",
                    "timestamp": time.time()
                }
            )

            # If all files have been analyzed, generate reports
            if not self.active_files and self.completed_files:
                # Publish analysis progress update
                await self.publish(
                    "project_status",
                    MessageType.ANALYSIS_PROGRESS,
                    {
                        "project_path": str(self.project_path),
                        "status": "analyzed",
                        "progress": 0.8,  # 80% complete
                        "files_completed": len(self.completed_files),
                        "timestamp": time.time()
                    }
                )

                await self._generate_reports(context)

    async def handle_stream_data(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle data from a stream.

        Args:
            payload: Stream data payload
            context: Message context
        """
        stream_id = context.metadata.get("stream_id")
        if not stream_id:
            logger.warning("Received stream data without stream_id")
            return

        # Handle different streams
        if stream_id == "file_analysis":
            # Handle file analysis stream data
            file_id = payload.get("file_id")
            status = payload.get("status")

            if file_id and status:
                logger.debug(f"Received file analysis stream data for {file_id}: {status}")

                # Update metrics
                if status == "completed":
                    self._metrics["files_analyzed"] = self._metrics.get("files_analyzed", 0) + 1

        elif stream_id == "tool_results":
            # Handle tool results stream data
            tool_id = payload.get("tool_id")
            result_type = payload.get("result_type")

            if tool_id and result_type:
                logger.debug(f"Received tool results stream data from {tool_id}: {result_type}")

                # Update metrics
                self._metrics["tool_results"] = self._metrics.get("tool_results", 0) + 1

        elif stream_id == "project_status":
            # Handle project status stream data
            status = payload.get("status")
            progress = payload.get("progress")

            if status:
                logger.debug(f"Received project status stream data: {status} ({progress:.1%})")

    async def handle_analysis_progress(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle an analysis progress update.

        Args:
            payload: Progress payload
            context: Message context
        """
        status = payload.get("status")
        progress = payload.get("progress", 0.0)

        logger.info(f"Analysis progress: {status} ({progress:.1%})")

        # Update progress tracker if available
        if self.progress:
            self.progress.update_progress(progress)

    async def handle_analysis_complete(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle an analysis complete notification.

        Args:
            payload: Completion payload
            context: Message context
        """
        project_path = payload.get("project_path")
        duration = payload.get("duration", 0.0)

        logger.info(f"Analysis complete for {project_path} in {duration:.2f}s")

        # Update metrics
        self._metrics["analysis_duration"] = duration

    async def _generate_reports(self, context: ContextWave) -> None:
        """
        Generate reports after all files have been analyzed.

        Args:
            context: Message context
        """
        if self.progress:
            self.progress.complete_phase("File analysis", f"Analyzed {len(self.completed_files)} files")
            self.progress.increment_global()
            self.progress.start_phase("Analyzing directories")

        logger.info("All files analyzed, analyzing directories")

        # Analyze directories before generating reports
        await self._analyze_directories()

        if self.progress:
            self.progress.complete_phase("Directory analysis", f"Analyzed {len(self.metrics.directories)} directories")
            self.progress.start_phase("Generating reports")

        logger.info("Directory analysis complete, generating reports")

        # If we have a report actor, send the results
        if self.report_actor and self.report_actor.actor_id in self._known_actors:
            report_context = context.propagate()

            # Get output directory from context or config
            output_dir = context.metadata.get("output_dir") or self.config.get("output_dir")

            # Create payload with all necessary information
            payload = {
                "project_path": str(self.project_path),
                "metrics": self.metrics
            }

            # Add output directory if available
            if output_dir:
                payload["output_dir"] = str(output_dir)
                logger.info(f"Including output_dir in report request: {output_dir}")

            await self.send(self.report_actor.actor_id, MessageType.GENERATE_REPORT, payload, report_context)

    async def handle_final_report(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle final report from the report actor.

        Args:
            payload: Message payload with the report
            context: Message context
        """
        report = payload.get("report", {})

        if self.progress:
            self.progress.complete_phase("Report generation", "Reports generated")
            self.progress.increment_global()

        logger.info("Received final report")

        # If we have a visualization actor, request visualizations
        if self.visualization_actor and self.visualization_actor.actor_id in self._known_actors:
            if self.progress:
                self.progress.start_phase("Generating visualizations")

            visualization_context = context.propagate()
            await self.send(self.visualization_actor.actor_id, MessageType.REQUEST_VISUALIZATION, {
                "project_path": str(self.project_path),
                "metrics": self.metrics,
                "report": report
            }, visualization_context)
        else:
            # No visualization actor, analysis is complete
            if self.progress:
                self.progress.complete_global("Analysis complete!")

    async def handle_visualization(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle visualizations from the visualization actor.

        Args:
            payload: Message payload with the visualizations
            context: Message context
        """
        if self.progress:
            self.progress.complete_phase("Visualization generation", "Visualizations generated")
            self.progress.complete_global("Analysis complete!")

        logger.info("Analysis complete with visualizations")

        # In a real implementation, you would do something with the visualizations

    async def _select_tools(self, metadata: Dict[str, Any], context: ContextWave) -> Dict[str, Dict[str, Any]]:
        """
        Select appropriate tools based on file metadata.

        Args:
            metadata: File metadata
            context: Message context with configuration

        Returns:
            Dictionary mapping tool IDs to their configurations
        """
        # Get tool configurations from context
        tool_configs = context.configuration.get("tool_configs", {})

        # Start with a base set of tools that are always included
        selected_tools = {}

        # Add tools based on file characteristics
        file_path = metadata.get("file_path", "")
        extension = Path(file_path).suffix.lower()

        # Python file analysis tools
        if extension == '.py':
            # Always include ruff/flake8 for linting
            if "ruff" in tool_configs and tool_configs["ruff"].get("enabled", False):
                selected_tools["tool-ruff"] = {"tool": "ruff", **tool_configs["ruff"]}
            elif "flake8" in tool_configs and tool_configs["flake8"].get("enabled", False):
                selected_tools["tool-flake8"] = {"tool": "flake8", **tool_configs["flake8"]}

            # Include mypy for type checking if file has type annotations
            if "mypy" in tool_configs and tool_configs["mypy"].get("enabled", False):
                # Check if file has type annotations or if we should check all files
                check_all = tool_configs["mypy"].get("check_all_files", False)
                if check_all or metadata.get("has_type_annotations", False):
                    selected_tools["tool-mypy"] = {"tool": "mypy", **tool_configs["mypy"]}

            # Include bandit for security analysis if enabled
            if "bandit" in tool_configs and tool_configs["bandit"].get("enabled", False):
                selected_tools["tool-bandit"] = {"tool": "bandit", **tool_configs["bandit"]}

            # Include complexity analysis if enabled
            if "complexity" in tool_configs and tool_configs["complexity"].get("enabled", False):
                selected_tools["tool-complexity"] = {"tool": "complexity", **tool_configs["complexity"]}

        # Markdown file analysis tools
        elif extension == '.md' and context.configuration.get("analyze_docs", False):
            if "markdown" in tool_configs and tool_configs["markdown"].get("enabled", False):
                selected_tools["tool-markdown"] = {"tool": "markdown", **tool_configs["markdown"]}

        return selected_tools

    async def _analyze_directories(self) -> None:
        """
        Analyze directories in the project.

        This method builds a directory structure from the analyzed files and
        calculates metrics for each directory.
        """
        from ...models.directory_metrics import DirectoryMetrics

        logger.info("Analyzing directories")

        # Get all file paths from the metrics
        file_paths = list(self.metrics.files.keys())

        # Build a set of all directories
        directories = set()
        for file_path in file_paths:
            # Get all parent directories
            path = Path(file_path)
            current = path.parent
            while str(current) != '.':
                directories.add(str(current))
                current = current.parent

        # Create directory metrics for each directory
        for directory in directories:
            # Skip if directory metrics already exist
            if directory in self.metrics.directories:
                continue

            # Create directory metrics
            dir_metrics = DirectoryMetrics(path=directory)

            # Find all files in this directory
            dir_files = [fp for fp in file_paths if Path(fp).parent.as_posix() == directory]

            # Update directory metrics with file metrics
            dir_metrics.update_with_file_metrics(dir_files, self.metrics.files)

            # Add directory metrics to project metrics
            self.metrics.directories[directory] = dir_metrics

        # Calculate directory hierarchy relationships
        self._calculate_directory_relationships()

        logger.info(f"Analyzed {len(self.metrics.directories)} directories")

    def _calculate_directory_relationships(self) -> None:
        """
        Calculate relationships between directories.

        This method identifies parent-child relationships between directories
        and updates the directory metrics accordingly.
        """
        # Get all directory paths
        directory_paths = list(self.metrics.directories.keys())

        # For each directory, find its parent and children
        for dir_path in directory_paths:
            dir_metrics = self.metrics.directories[dir_path]

            # Find parent directory
            parent_path = str(Path(dir_path).parent)
            if parent_path in self.metrics.directories and parent_path != dir_path:
                dir_metrics.parent_dir = parent_path

            # Find child directories
            dir_metrics.child_directories = [
                d for d in directory_paths
                if str(Path(d).parent) == dir_path and d != dir_path
            ]

    def _adapt_tool_config(self, tool_config: Dict[str, Any], metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Adapt tool configuration based on file metadata.

        Args:
            tool_config: Tool configuration
            metadata: File metadata

        Returns:
            Adapted tool configuration
        """
        # Clone the configuration so we don't modify the original
        config = tool_config.copy()

        # Adapt configuration based on metadata
        if "complexity" in metadata:
            complexity = metadata["complexity"]

            # For complex files, use more strict linting
            if complexity > self.context_wave.adaptive_params.get("complexity_threshold", 10):
                if config.get("tool") == "ruff":
                    if "args" not in config:
                        config["args"] = []
                    config["args"].append("--select=E,F,W,C90")

        # For large files, allocate more resources
        if metadata.get("lines", 0) > 500:
            config["timeout"] = max(config.get("timeout", 30), 60)  # Increase timeout for large files

        return config

    def _get_state(self) -> Dict[str, Any]:
        """
        Get actor state for persistence.

        Returns:
            Dictionary of state to save
        """
        state = super()._get_state()

        # Add project-specific state
        state.update({
            "project_path": str(self.project_path),
            "active_files": list(self.active_files),
            "completed_files": list(self.completed_files),
            "analysis_in_progress": self.analysis_in_progress,
            "analysis_start_time": self.analysis_start_time,
            "analysis_end_time": self.analysis_end_time,
            "file_tool_map": {k: list(v) for k, v in self.file_tool_map.items()},
            "metrics": self.metrics.to_dict() if hasattr(self.metrics, "to_dict") else {}
        })

        return state

    def _set_state(self, state: Dict[str, Any]) -> None:
        """
        Set actor state from persistence.

        Args:
            state: Dictionary of state to restore
        """
        super()._set_state(state)

        # Restore project-specific state
        if "project_path" in state:
            self.project_path = Path(state["project_path"])

        if "active_files" in state:
            self.active_files = set(state["active_files"])

        if "completed_files" in state:
            self.completed_files = set(state["completed_files"])

        if "analysis_in_progress" in state:
            self.analysis_in_progress = state["analysis_in_progress"]

        if "analysis_start_time" in state:
            self.analysis_start_time = state["analysis_start_time"]

        if "analysis_end_time" in state:
            self.analysis_end_time = state["analysis_end_time"]

        if "file_tool_map" in state:
            self.file_tool_map = {k: set(v) for k, v in state["file_tool_map"].items()}

        if "metrics" in state and hasattr(self.metrics, "from_dict"):
            self.metrics.from_dict(state["metrics"])
