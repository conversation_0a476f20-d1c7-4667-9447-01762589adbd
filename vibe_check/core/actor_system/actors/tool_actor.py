"""
Tool Actor Module
==============

This module defines the ToolActor class, which is responsible for running analysis tools
on files. It integrates with the various analysis tools through the ToolBridge, which
implements the CAW principle of contextual adaptation.

The ToolActor works together with the FileActor, ProjectActor, and ReportActor to
implement the CAW principles of choreographed interactions and contextual adaptation.

This module also provides the ToolActorFactory class, which creates and registers
tool actors for different analysis tools.
"""

import asyncio
import json
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

from ...bridge.tool_bridge import ToolBridge
from ...models.file_metrics import FileMetrics
from ..actor import Actor
from ..context_wave import ContextWave
from ..message import Message, MessageType
from ..actor_registry import get_registry

logger = logging.getLogger("vibe_check_actor_system")


class ToolActor(Actor):
    """
    Actor responsible for running analysis tools on files.

    This actor implements the CAW principle of contextual adaptation by selecting and
    configuring tools based on file characteristics and context metadata.
    """

    def __init__(self, actor_id: str, tool_config: Optional[Dict[str, Any]] = None,
                 supervisor_id: Optional[str] = None,
                 state_dir: Optional[str] = None):
        """
        Initialize the ToolActor.

        Args:
            actor_id: Unique ID for this actor
            tool_config: Optional default configuration for all tools
            supervisor_id: Optional ID of the supervisor actor
            state_dir: Optional directory for state persistence
        """
        super().__init__(
            actor_id=actor_id,
            actor_type="tool",
            tags={"tool", "analysis"},
            capabilities={"code_analysis", "security_analysis", "quality_analysis"},
            supervisor_id=supervisor_id,
            state_dir=state_dir
        )

        self.tool_bridge: ToolBridge = ToolBridge()
        self.tool_config: Dict[str, Any] = tool_config or {}
        self.file_contents: Dict[str, str] = {}  # Cache for file contents
        self.report_actor_id: Optional[str] = None
        self.visualization_actor_id: Optional[str] = None

        # Analysis state
        self.analysis_start_time: float = 0.0
        self.analysis_end_time: float = 0.0
        self.analysis_in_progress: bool = False
        self.files_analyzed: Set[str] = set()
        self.files_in_progress: Set[str] = set()

    def _initialize_handlers(self) -> None:
        """Initialize message handlers."""
        super()._initialize_handlers()

        # Analysis messages
        self._message_handlers[MessageType.ANALYZE_FILE] = self.handle_analyze_file
        self._message_handlers[MessageType.FILE_CONTENT] = self.handle_file_content
        self._message_handlers[MessageType.TOOL_CONFIG] = self.handle_tool_config
        self._message_handlers[MessageType.LIST_TOOLS] = self.handle_list_tools

        # Stream messages
        self._message_handlers[MessageType.STREAM_DATA] = self.handle_stream_data

    def set_report_actor(self, report_actor_id: str) -> None:
        """
        Set the report actor for this tool.

        Args:
            report_actor_id: Report actor ID
        """
        # Use the registry to get the actor
        registry = get_registry()
        report_actor = registry.get_actor(report_actor_id)

        if report_actor:
            self.report_actor_id = report_actor_id
            logger.info(f"Set report actor {report_actor_id} for tool {self.actor_id}")

            # Subscribe to report stream
            asyncio.create_task(self.subscribe("report"))
        else:
            logger.warning(f"Report actor {report_actor_id} not found for tool {self.actor_id}")

    def set_visualization_actor(self, visualization_actor_id: str) -> None:
        """
        Set the visualization actor for this tool.

        Args:
            visualization_actor_id: Visualization actor ID
        """
        # Use the registry to get the actor
        registry = get_registry()
        visualization_actor = registry.get_actor(visualization_actor_id)

        if visualization_actor:
            self.visualization_actor_id = visualization_actor_id
            logger.info(f"Set visualization actor {visualization_actor_id} for tool {self.actor_id}")

            # Subscribe to visualization stream
            asyncio.create_task(self.subscribe("visualization"))
        else:
            logger.warning(f"Visualization actor {visualization_actor_id} not found for tool {self.actor_id}")

    async def _read_file_content(self, file_path: str, file_id: Optional[str], context: ContextWave) -> Optional[str]:
        """
        Read file content from disk.

        This is a helper method for the handle_analyze_file method to make it less complex.

        Args:
            file_path: Path to the file
            file_id: ID of the file
            context: Message context

        Returns:
            File content or None if the file couldn't be read
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return f.read()
        except UnicodeDecodeError:
            # Try with a different encoding
            try:
                with open(file_path, "r", encoding="latin-1") as f:
                    logger.warning(f"File {file_path} opened with latin-1 encoding")
                    return f.read()
            except Exception as e:
                logger.error(f"Error reading file {file_path} with latin-1 encoding: {e}")
                await self._send_file_error(file_path, file_id, f"Error reading file with latin-1 encoding: {e}", context)
                return None
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            await self._send_file_error(file_path, file_id, f"Error reading file: {e}", context)
            return None

    async def _send_file_error(self, file_path: str, file_id: Optional[str], error_message: str, context: ContextWave) -> None:
        """
        Send an error message about a file.

        This is a helper method for the handle_analyze_file method to make it less complex.

        Args:
            file_path: Path to the file
            file_id: ID of the file
            error_message: Error message
            context: Message context
        """
        # Send error message back to sender
        sender_id = context.metadata.get("sender_id")
        if sender_id:
            error_payload = {
                "file_path": file_path,
                "file_id": file_id,
                "error": error_message,
                "timestamp": time.time()
            }
            await self.send(sender_id, MessageType.TOOL_ERROR, error_payload, context)

    async def _run_requested_tools(self, file_path: str, file_id: Optional[str], file_content: str,
                                  requested_tools: List[str], payload: Dict[str, Any],
                                  context: ContextWave) -> Dict[str, Any]:
        """
        Run the requested tools on a file.

        This is a helper method for the handle_analyze_file method to make it less complex.

        Args:
            file_path: Path to the file
            file_id: ID of the file
            file_content: Content of the file
            requested_tools: List of tools to run
            payload: Original message payload
            context: Message context

        Returns:
            Analysis results
        """
        results = {}
        for tool_name in requested_tools:
            try:
                # Get tool-specific config if provided
                tool_config = payload.get("tool_configs", {}).get(tool_name, self.tool_config.get(tool_name, {}))

                # Run the tool through the bridge
                tool_result = await self.tool_bridge.run_tool(
                    tool_name, file_path, file_content, context, tool_config
                )
                results[tool_name] = tool_result

                # Publish tool result to tool_results stream
                try:
                    await self.publish(
                        "tool_results",
                        MessageType.TOOL_RESULT,
                        {
                            "file_id": file_id,
                            "file_path": file_path,
                            "tool_id": self.actor_id,
                            "tool": tool_name,
                            "result_type": "analysis",
                            "timestamp": time.time()
                        }
                    )
                except Exception as publish_error:
                    logger.warning(f"Error publishing tool result for {tool_name}: {publish_error}")
            except Exception as e:
                # Log error but continue with other tools
                import traceback
                error_details = traceback.format_exc()
                logger.error(f"Error running tool {tool_name} on {file_path}: {e}\n{error_details}")
                results[tool_name] = {"error": str(e), "error_details": error_details}

        return {
            "file_path": file_path,
            "tools": requested_tools,
            "results": results
        }

    async def _send_analysis_results(self, file_path: str, file_id: Optional[str], file_content: str,
                                    analysis_results: Dict[str, Any], payload: Dict[str, Any],
                                    context: ContextWave) -> None:
        """
        Send analysis results to the report actor and the sender.

        This is a helper method for the handle_analyze_file method to make it less complex.

        Args:
            file_path: Path to the file
            file_id: ID of the file
            file_content: Content of the file
            analysis_results: Analysis results
            payload: Original message payload
            context: Message context
        """
        # Extract and enrich the file metrics
        file_metrics = self._extract_file_metrics(file_path, file_content, analysis_results)

        # Send the file metrics to the report actor
        report_actor_id = payload.get("report_actor_id") or self.report_actor_id
        if report_actor_id:
            try:
                metrics_payload = {
                    "file_path": file_path,
                    "file_id": file_id,
                    "file_metrics": file_metrics.to_dict() if file_metrics else None,
                    "analysis_results": analysis_results,
                    "timestamp": time.time()
                }
                await self.send(report_actor_id, MessageType.FILE_METRICS, metrics_payload, context)
            except Exception as e:
                import traceback
                logger.error(f"Error sending metrics to report actor: {e}\n{traceback.format_exc()}")

        # Send the analysis results back to the sender
        sender_id = context.metadata.get("sender_id")
        if sender_id:
            try:
                response_payload = {
                    "file_path": file_path,
                    "file_id": file_id,
                    "metrics": file_metrics.to_dict() if file_metrics else None,
                    "analysis_results": analysis_results,
                    "timestamp": time.time()
                }
                await self.send(sender_id, MessageType.TOOL_RESULTS, response_payload, context)
            except Exception as e:
                import traceback
                logger.error(f"Error sending results to sender: {e}\n{traceback.format_exc()}")

    async def _publish_analysis_status(self, file_id: Optional[str], file_path: str, status: str,
                                      error: Optional[str] = None,
                                      error_details: Optional[str] = None,
                                      duration: Optional[float] = None) -> None:
        """
        Publish analysis status to the tool_results stream.

        This is a helper method for the handle_analyze_file method to make it less complex.

        Args:
            file_id: ID of the file
            file_path: Path to the file
            status: Status of the analysis (started, completed, error)
            error: Optional error message
            error_details: Optional error details
            duration: Optional duration of the analysis
        """
        try:
            payload = {
                "file_id": file_id,
                "file_path": file_path,
                "tool_id": self.actor_id,
                "status": status,
                "timestamp": time.time()
            }

            # Add optional fields
            if error:
                payload["error"] = error
            if error_details:
                payload["error_details"] = error_details
            if duration:
                payload["duration"] = duration

            # Determine message type
            message_type = MessageType.ERROR if status == "error" else MessageType.TOOL_RESULT

            # Publish to stream
            await self.publish("tool_results", message_type, payload)
        except Exception as e:
            logger.warning(f"Error publishing analysis status: {e}")

    async def handle_analyze_file(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle request to analyze a file.

        This method implements the CAW principle of contextual adaptation by selecting
        and configuring tools based on file characteristics and context metadata.

        Args:
            payload: Analysis request parameters
            context: Message context
        """
        # Extract file information
        file_path = payload.get("file_path")
        file_id = payload.get("file_id")

        if not file_path:
            logger.error("Missing file_path in analyze_file message")
            return

        # Check if this file is already being analyzed
        if file_path in self.files_in_progress:
            logger.warning(f"File {file_path} is already being analyzed")

            # Notify sender
            sender_id = context.metadata.get("sender_id")
            if sender_id:
                try:
                    error_context = context.propagate()
                    await self.send(
                        sender_id,
                        MessageType.ERROR,
                        {
                            "error": "File is already being analyzed",
                            "file_path": file_path,
                            "file_id": file_id,
                            "timestamp": time.time()
                        },
                        error_context
                    )
                except Exception as e:
                    logger.error(f"Error sending 'already analyzing' message: {e}")
            return

        # Mark file as in progress
        self.files_in_progress.add(file_path)

        # Set analysis state
        if not self.analysis_in_progress:
            self.analysis_in_progress = True
            self.analysis_start_time = time.time()

        # Update metrics
        self._metrics["files_analyzed"] = self._metrics.get("files_analyzed", 0) + 1
        self._metrics["last_file"] = file_path

        # Track analysis start time for performance monitoring
        analysis_start_time = time.time()

        logger.info(f"Analyzing file {file_path}")

        try:
            # Extract file content from payload or read from disk
            file_content = payload.get("content")
            if file_content is None:
                file_content = await self._read_file_content(file_path, file_id, context)
                if file_content is None:
                    # Error reading file, already logged and error sent
                    return

            # Cache the file content for potential reuse
            self.file_contents[file_path] = file_content

            # Check if specific tools were requested
            requested_tools = payload.get("tools", [])

            # Enrich context with basic file information
            # This contextual adaptation is a key aspect of CAW
            self._enrich_context_with_file_info(context, file_path, file_content)

            # Publish analysis start to tool_results stream
            await self._publish_analysis_status(file_id, file_path, "started")

            # Run specified tools or auto-select based on context
            if requested_tools:
                # Run only the requested tools
                analysis_results = await self._run_requested_tools(
                    file_path, file_id, file_content, requested_tools, payload, context
                )
            else:
                # Auto-select and run appropriate tools based on context
                try:
                    analysis_results = await self.tool_bridge.run_selected_tools(
                        file_path, file_content, context, self.tool_config
                    )

                    # Publish tool results to tool_results stream
                    for tool_name in analysis_results.get("results", {}).keys():
                        try:
                            await self.publish(
                                "tool_results",
                                MessageType.TOOL_RESULT,
                                {
                                    "file_id": file_id,
                                    "file_path": file_path,
                                    "tool_id": self.actor_id,
                                    "tool": tool_name,
                                    "result_type": "analysis",
                                    "timestamp": time.time()
                                }
                            )
                        except Exception as e:
                            # Log but continue
                            logger.warning(f"Error publishing tool result for {tool_name}: {e}")
                except Exception as e:
                    import traceback
                    error_details = traceback.format_exc()
                    logger.error(f"Error running selected tools: {e}\n{error_details}")
                    analysis_results = {
                        "file_path": file_path,
                        "error": str(e),
                        "error_details": error_details
                    }

            # Send analysis results
            await self._send_analysis_results(
                file_path, file_id, file_content, analysis_results, payload, context
            )

            # Mark file as analyzed
            self.files_analyzed.add(file_path)

            # Update metrics
            self._metrics["files_completed"] = self._metrics.get("files_completed", 0) + 1

            # Calculate and log performance metrics
            analysis_end_time = time.time()
            analysis_duration = analysis_end_time - analysis_start_time
            self._metrics["last_analysis_duration"] = analysis_duration
            self._metrics["total_analysis_time"] = self._metrics.get("total_analysis_time", 0) + analysis_duration
            self._metrics["avg_analysis_time"] = (
                self._metrics["total_analysis_time"] / self._metrics["files_completed"]
                if self._metrics["files_completed"] > 0 else 0
            )

            logger.info(f"Completed analysis of {file_path} in {analysis_duration:.2f}s")

            # Publish analysis complete to tool_results stream
            await self._publish_analysis_status(file_id, file_path, "completed", duration=analysis_duration)

            # Save state
            try:
                await self._save_state()
            except Exception as e:
                logger.warning(f"Error saving state: {e}")

        except Exception as e:
            # Log error
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Error analyzing file {file_path}: {e}\n{error_details}")

            # Update metrics
            self._metrics["errors"] = self._metrics.get("errors", 0) + 1
            self._metrics["last_error"] = str(e)  # Store as string

            # Calculate and log performance metrics even for failed analysis
            analysis_end_time = time.time()
            analysis_duration = analysis_end_time - analysis_start_time
            self._metrics["last_analysis_duration"] = analysis_duration

            # Publish error to tool_results stream
            await self._publish_analysis_status(
                file_id, file_path, "error",
                error=str(e), error_details=error_details,
                duration=analysis_duration
            )

            # Notify sender
            sender_id = context.metadata.get("sender_id")
            if sender_id:
                try:
                    error_context = context.propagate()
                    await self.send(
                        sender_id,
                        MessageType.ERROR,
                        {
                            "file_id": file_id,
                            "file_path": file_path,
                            "error": str(e),
                            "error_details": error_details,
                            "timestamp": time.time()
                        },
                        error_context
                    )
                except Exception as send_error:
                    logger.error(f"Error sending error to sender: {send_error}")

            # Re-raise the exception
            raise

        finally:
            # Mark file as no longer in progress
            self.files_in_progress.discard(file_path)

            # Check if all files are done
            if not self.files_in_progress and self.analysis_in_progress:
                self.analysis_in_progress = False
                self.analysis_end_time = time.time()
                self._metrics["analysis_duration"] = self.analysis_end_time - self.analysis_start_time

    async def handle_stream_data(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle data from a stream.

        Args:
            payload: Stream data payload
            context: Message context
        """
        stream_id = context.metadata.get("stream_id")
        if not stream_id:
            logger.warning("Received stream data without stream_id")
            return

        # Handle different streams
        if stream_id == "tool_results":
            # Handle tool results stream data
            tool_id = payload.get("tool_id")
            file_id = payload.get("file_id")

            # Only process if it's from another tool
            if tool_id != self.actor_id:
                logger.debug(f"Received tool results stream data from {tool_id}")

                # Update metrics
                self._metrics["tool_results"] = self._metrics.get("tool_results", 0) + 1

        elif stream_id == "file_analysis":
            # Handle file analysis stream data
            file_id = payload.get("file_id")
            status = payload.get("status")

            if file_id and status:
                logger.debug(f"Received file analysis stream data for {file_id}: {status}")

                # Update metrics
                self._metrics["file_analysis"] = self._metrics.get("file_analysis", 0) + 1

    def _enrich_context_with_file_info(self, context: ContextWave,
                                      file_path: str, content: str) -> None:
        """
        Enrich the context wave with file-specific information.

        This implements the CAW principle of contextual adaptation.

        Args:
            context: Context wave to enrich
            file_path: Path to the file
            content: File content
        """
        path = Path(file_path)

        # Basic file information
        context.metadata["file_name"] = path.name
        context.metadata["file_extension"] = path.suffix
        context.metadata["file_size"] = len(content)
        context.metadata["line_count"] = content.count('\n') + 1

        # Detect file type
        if path.name.startswith("test_") or "_test" in path.name:
            context.metadata["file_type"] = "test"
        elif path.name == "__init__.py":
            context.metadata["file_type"] = "init"
        elif "interface" in path.name.lower() or "protocol" in path.name.lower():
            context.metadata["file_type"] = "interface"

        # Calculate simple complexity metric
        complexity = 0.0
        if content:
            # Basic complexity calculation based on structural elements
            complexity += content.count("if ") * 0.1
            complexity += content.count("for ") * 0.1
            complexity += content.count("while ") * 0.1
            complexity += content.count("try:") * 0.1
            complexity += content.count("except ") * 0.1
            complexity += content.count("class ") * 0.2
            complexity += content.count("def ") * 0.05
            complexity += content.count("async ") * 0.05
            complexity += content.count("with ") * 0.05
            complexity = min(1.0, complexity)  # Normalize to 0.0-1.0 range

        context.metadata["file_complexity"] = complexity

        # Detect type annotations
        if "typing" in content or ": " in content or " -> " in content:
            context.metadata["has_type_annotations"] = True

        # Detect doc comments
        if '"""' in content or "'''" in content:
            context.metadata["has_docstrings"] = True

        # Detect security-sensitive code patterns
        security_patterns = [
            "subprocess", "os.system", "eval", "exec", "pickle.loads",
            "open(", "execute(", "sql", "password", "token", "api_key"
        ]
        if any(pattern in content.lower() for pattern in security_patterns):
            context.metadata["security_sensitive"] = True

    def _extract_file_metrics(self, file_path: str, content: str,
                            analysis_results: Dict[str, Any]) -> Optional[FileMetrics]:
        """
        Extract and calculate file metrics from analysis results.

        Args:
            file_path: Path to the file
            content: File content
            analysis_results: Results from analysis tools

        Returns:
            File metrics object or None if metrics couldn't be calculated
        """
        try:
            path = Path(file_path)
            line_count = content.count('\n') + 1
            is_empty = len(content.strip()) == 0

            # Create basic metrics
            metrics = FileMetrics(
                path=str(path),
                name=path.name,
                lines=line_count,
                size=len(content)
            )

            # Set is_empty property if available
            if hasattr(metrics, 'is_empty'):
                metrics.is_empty = is_empty

            # Get results from various tools
            results = analysis_results.get("results", {})

            # Extract complexity metrics if available
            if "complexity" in results:
                complexity_result = results["complexity"]
                if "complexity_score" in complexity_result:
                    metrics.complexity = complexity_result["complexity_score"]
                if "maintainability_score" in complexity_result:
                    if hasattr(metrics, 'maintainability_index'):
                        metrics.maintainability_index = complexity_result["maintainability_score"]

            # Extract documentation metrics if available
            if "doc_analyzer" in results:
                doc_result = results["doc_analyzer"]
                if "doc_coverage" in doc_result:
                    if hasattr(metrics, 'docstring_coverage'):
                        metrics.docstring_coverage = doc_result["doc_coverage"]
                if "doc_quality" in doc_result and hasattr(metrics, 'documentation_quality'):
                    metrics.documentation_quality = doc_result["doc_quality"]

                        # Extract security metrics if available
            if "bandit" in results:
                bandit_result = results["bandit"]
                security_score = bandit_result.get("security_score", 100)
                metrics.security_score = security_score

                # Add issue counts
                issues = bandit_result.get("issues", [])
                metrics.security_issues = len(issues)
                metrics.high_severity_issues = sum(1 for i in issues if i.get("severity") == "HIGH")

                # Add issues to file metrics
                for issue in issues:
                    metrics.issues.append({
                        "code": issue.get("code", ""),
                        "message": issue.get("message", ""),
                        "line": issue.get("line", 0),
                        "severity": issue.get("severity", "MEDIUM"),
                        "tool": "bandit",
                        "type": "security"
                    })

                # Add issue counts
                issues = bandit_result.get("issues", [])
                metrics.security_issues = len(issues)
                metrics.high_severity_issues = sum(1 for i in issues if i.get("severity") == "HIGH")

                logger.info(f"Found {len(issues)} security issues ({metrics.high_severity_issues} high severity) in {file_path}")

                # Add issues to file metrics
                for issue in issues:
                    issue_data = {
                        "code": issue.get("code", ""),
                        "message": issue.get("message", ""),
                        "line": issue.get("line", 0),
                        "severity": issue.get("severity", "MEDIUM"),
                        "tool": "bandit",
                        "type": "security"
                    }
                    metrics.issues.append(issue_data)
                    logger.debug(f"Added security issue to file metrics: {issue_data['code']} - {issue_data['message']}")

            # Extract type checking metrics if available
            if "mypy" in results:
                mypy_result = results["mypy"]
                # Store type issues count in tool_results if not directly supported
                if not hasattr(metrics, 'type_issues'):
                    metrics.tool_results["mypy"] = {
                        "type_issues": len(mypy_result.get("issues", []))
                    }
                metrics.type_coverage = mypy_result.get("type_coverage", 0)

            # Extract code quality metrics if available
            if "ruff" in results:
                ruff_result = results["ruff"]
                lint_issues_count = len(ruff_result.get("issues", []))

                # Store lint issues count in tool_results if not directly supported
                if not hasattr(metrics, 'lint_issues'):
                    metrics.tool_results["ruff"] = {
                        "lint_issues": lint_issues_count
                    }

                # Calculate code quality score based on issue density
                if metrics.lines > 0:
                    issue_density = lint_issues_count / metrics.lines
                    # Convert to a 0-100 score (higher is better)
                    quality_score = max(0, min(100, 100 - (issue_density * 1000)))
                    # Store in tool_results if not directly supported
                    metrics.tool_results["quality_score"] = quality_score
                else:
                    metrics.tool_results["quality_score"] = 100  # Empty files get perfect score

            # Calculate overall health score
            metrics.calculate_health_score()

            return metrics

        except Exception as e:
            import traceback
            logger.error(f"Error extracting file metrics: {e}\n{traceback.format_exc()}")
            return None

    async def handle_tool_config(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle tool configuration update.

        Args:
            payload: Tool configuration parameters
            context: Message context
        """
        # Update the tool configuration
        if "config" in payload:
            self.tool_config.update(payload["config"])

        # Send confirmation
        sender_id = context.metadata.get("sender_id")
        if sender_id:
            response_payload = {
                "status": "configured",
                "config": self.tool_config
            }
            await self.send(sender_id, MessageType.TOOL_CONFIGURED, response_payload, context)

    async def handle_list_tools(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle request to list available tools.

        Args:
            payload: Request parameters
            context: Message context
        """
        # Refresh tool availability
        self.tool_bridge._refresh_tool_availability()

        # Get the list of available tools
        available_tools = list(self.tool_bridge.tool_availability_cache)

        # Send the list back to the requester
        sender_id = context.metadata.get("sender_id")
        if sender_id:
            response_payload = {
                "available_tools": available_tools
            }
            await self.send(sender_id, MessageType.TOOL_LIST, response_payload, context)

    async def handle_file_content(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle file content received from a file actor.

        Args:
            payload: Message payload with file content
            context: Message context
        """
        file_path = payload.get("file_path")
        content = payload.get("content")
        file_id = payload.get("file_id")

        if not file_path or content is None:
            logger.error("Missing file_path or content in file_content message")
            return

        logger.info(f"Received content for {file_path}")

        # Cache the file content
        self.file_contents[file_path] = content

        # Get the tool name from the context
        tool_name = context.metadata.get("tool_name", "unknown")

        # Create a new payload for analysis
        analysis_payload = {
            "file_path": file_path,
            "content": content,
            "file_id": file_id,
            "tools": [tool_name],
            "report_actor_id": getattr(self, "report_actor_id", None)
        }

        # Create a new context for the analysis
        analysis_context = context.propagate()
        analysis_context.metadata["operation"] = "analyze_file"

        # Analyze the file
        await self.handle_analyze_file(analysis_payload, analysis_context)


    def _get_state(self) -> Dict[str, Any]:
        """
        Get actor state for persistence.

        Returns:
            Dictionary of state to save
        """
        state = super()._get_state()

        # Add tool-specific state
        state.update({
            "tool_config": self.tool_config,
            "analysis_in_progress": self.analysis_in_progress,
            "analysis_start_time": self.analysis_start_time,
            "analysis_end_time": self.analysis_end_time,
            "files_analyzed": list(self.files_analyzed),
            "files_in_progress": list(self.files_in_progress)
        })

        return state

    def _set_state(self, state: Dict[str, Any]) -> None:
        """
        Set actor state from persistence.

        Args:
            state: Dictionary of state to restore
        """
        super()._set_state(state)

        # Restore tool-specific state
        if "tool_config" in state:
            self.tool_config = state["tool_config"]

        if "analysis_in_progress" in state:
            self.analysis_in_progress = state["analysis_in_progress"]

        if "analysis_start_time" in state:
            self.analysis_start_time = state["analysis_start_time"]

        if "analysis_end_time" in state:
            self.analysis_end_time = state["analysis_end_time"]

        if "files_analyzed" in state:
            self.files_analyzed = set(state["files_analyzed"])

        if "files_in_progress" in state:
            self.files_in_progress = set(state["files_in_progress"])

    async def run_tool(self, file_path: str) -> Dict[str, Any]:
        """
        Run analysis tools on a file and return the results.

        This method is a simplified interface for testing purposes.
        It runs the default tools on the file and returns the results.

        Args:
            file_path: Path to the file to analyze

        Returns:
            Dictionary containing analysis results with issues
        """
        # Create a context for the analysis
        context = ContextWave()
        context.metadata["operation"] = "run_tool"

        # Read the file content
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                file_content = f.read()
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            return {"error": str(e), "issues": []}

        # Enrich context with file information
        self._enrich_context_with_file_info(context, file_path, file_content)

        # Run default tools or ruff if no tools are configured
        tools_to_run = list(self.tool_config.keys()) if self.tool_config else ["ruff"]

        # Run the tools
        results = {}
        issues = []

        for tool_name in tools_to_run:
            try:
                # Get tool config
                tool_config = self.tool_config.get(tool_name, {})

                # Run the tool
                tool_result = await self.tool_bridge.run_tool(
                    tool_name, file_path, file_content, context, tool_config
                )

                # Store the result
                results[tool_name] = tool_result

                # Extract issues
                if isinstance(tool_result, dict) and "issues" in tool_result:
                    issues.extend(tool_result["issues"])
            except Exception as e:
                logger.error(f"Error running tool {tool_name} on {file_path}: {e}")
                results[tool_name] = {"error": str(e)}

        # Return the combined results
        return {
            "file_path": file_path,
            "tools": tools_to_run,
            "results": results,
            "issues": issues
        }


class ToolActorFactory:
    """
    Factory for creating and registering tool actors.

    This class implements the factory pattern for creating tool actors
    for different analysis tools.
    """

    @staticmethod
    async def create_and_register_tools(
        project_actor: 'Actor',
        tool_configs: Dict[str, Dict[str, Any]],
        report_actor_id: Optional[str] = None,
        visualization_actor_id: Optional[str] = None,
        supervisor_id: Optional[str] = None,
        state_dir: Optional[str] = None
    ) -> Dict[str, ToolActor]:
        """
        Create and register tool actors for the specified tools.

        Args:
            project_actor: Project actor to register with
            tool_configs: Configuration for each tool
            report_actor_id: Optional report actor ID
            visualization_actor_id: Optional visualization actor ID
            supervisor_id: Optional supervisor actor ID
            state_dir: Optional directory for state persistence

        Returns:
            Dictionary mapping tool names to tool actors
        """
        tool_actors = {}
        registry = get_registry()

        # Create a tool actor for each enabled tool
        for tool_name, tool_config in tool_configs.items():
            # Skip disabled tools
            if not tool_config.get("enabled", True):
                continue

            # Create the tool actor with a unique ID
            actor_id = f"tool_actor_{tool_name}_{id(tool_config)}"
            tool_actor = ToolActor(
                actor_id=actor_id,
                tool_config=tool_config,
                supervisor_id=supervisor_id,
                state_dir=state_dir
            )

            # Get the actor ID from the tool actor
            actor_id = tool_actor.actor_id

            # Set report and visualization actors if provided
            if report_actor_id:
                tool_actor.set_report_actor(report_actor_id)

            if visualization_actor_id:
                tool_actor.set_visualization_actor(visualization_actor_id)

            # Start the actor
            await tool_actor.start()

            # Register with registry
            registry.register_actor(
                actor_id=actor_id,
                actor=tool_actor,
                actor_type="tool",
                tags={"tool", "analysis", tool_name},
                capabilities={"code_analysis", "security_analysis", "quality_analysis"}
            )

            # Store in the dictionary
            tool_actors[tool_name] = tool_actor

            logger.info(f"Created and registered tool actor {actor_id}")

        return tool_actors
