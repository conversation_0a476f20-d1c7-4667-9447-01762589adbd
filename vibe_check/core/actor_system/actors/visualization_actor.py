"""
Visualization Actor Module
========================

This module defines the VisualizationActor class, which is responsible for generating
visualizations from analysis results. It creates visual representations of the project
structure, dependencies, complexity, and other metrics.

The VisualizationActor works together with the ProjectActor and ReportActor to
implement the CAW principles of choreographed interactions and contextual adaptation.
"""

import asyncio
import logging
import os
import json
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

import matplotlib.pyplot as plt
import networkx as nx
import numpy as np
from matplotlib.colors import LinearSegmentedColormap

from ...models.project_metrics import ProjectMetrics
from ...models.file_metrics import FileMetrics
from ...utils.file_utils import ensure_dir
from ..actor import Actor
from ..context_wave import ContextWave
from ..message import Message, MessageType

logger = logging.getLogger("vibe_check_actor_system")


class VisualizationActor(Actor):
    """
    Actor responsible for generating visualizations from analysis results.

    This actor receives aggregated analysis results and generates visualizations
    like dependency graphs, complexity heatmaps, and metric charts.
    """

    def __init__(self, actor_id: str, output_dir: Optional[Union[str, Path]] = None,
                 supervisor_id: Optional[str] = None, state_dir: Optional[str] = None):
        """
        Initialize the VisualizationActor.

        Args:
            actor_id: Unique ID for this actor
            output_dir: Optional directory to save visualization outputs (can be set later)
            supervisor_id: Optional ID of the supervisor actor
            state_dir: Optional directory for state persistence
        """
        super().__init__(
            actor_id=actor_id,
            actor_type="visualization",
            tags={"visualization", "output"},
            capabilities={"visualization", "metrics_visualization"},
            supervisor_id=supervisor_id,
            state_dir=state_dir
        )

        # Set output directory (default to current directory if not provided)
        self.output_dir: Path = Path(output_dir) if output_dir else Path.cwd() / "visualizations"
        ensure_dir(self.output_dir)

        self.project_metrics: Optional[ProjectMetrics] = None
        self.file_metrics: Dict[str, FileMetrics] = {}
        self.visualization_config: Dict[str, Any] = {
            "colormap": "viridis",
            "dpi": 150,
            "figsize": (10, 8),
            "node_size": 1500,
            "font_size": 8,
            "edge_width": 1.0,
            "use_edge_weights": True,
            "interactive": True
        }

    async def handle_configure_visualization(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle configuration for visualizations.

        Args:
            payload: Visualization configuration parameters
            context: Message context
        """
        # Update configuration with provided parameters
        if "config" in payload:
            self.visualization_config.update(payload["config"])

        # Adapt configuration based on context
        if "complexity" in context.metadata:
            complexity = context.metadata["complexity"]
            if complexity > 0.7:  # High complexity projects
                self.visualization_config["node_size"] = 1200
                self.visualization_config["edge_width"] = 0.8
                self.visualization_config["font_size"] = 6
            elif complexity < 0.3:  # Low complexity projects
                self.visualization_config["node_size"] = 1800
                self.visualization_config["edge_width"] = 1.5
                self.visualization_config["font_size"] = 10

        # Send confirmation
        response_payload = {
            "status": "configured",
            "config": self.visualization_config
        }
        sender = context.metadata.get("sender_id")
        if sender:
            await self.send(sender, MessageType.VISUALIZATION_CONFIGURED, response_payload, context)

    async def handle_visualize_project(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle request to visualize project metrics.

        Args:
            payload: Project metrics to visualize
            context: Message context
        """
        # Extract project metrics
        if "project_metrics" in payload:
            self.project_metrics = ProjectMetrics.from_dict(payload["project_metrics"])

        # Extract file metrics if available
        if "file_metrics" in payload:
            for file_path, metrics_dict in payload["file_metrics"].items():
                self.file_metrics[file_path] = FileMetrics.from_dict(metrics_dict)

        # Generate visualizations based on available metrics
        visualization_paths = {}

        # Check and generate dependency graph if dependencies are available
        if self.project_metrics and hasattr(self.project_metrics, 'dependencies'):
            dependency_graph_path = await self._generate_dependency_graph()
            if dependency_graph_path:
                visualization_paths["dependency_graph"] = str(dependency_graph_path)

        # Generate complexity heatmap if complexity metrics are available
        if self.file_metrics:
            complexity_heatmap_path = await self._generate_complexity_heatmap()
            if complexity_heatmap_path:
                visualization_paths["complexity_heatmap"] = str(complexity_heatmap_path)

        # Generate code metrics chart
        metrics_chart_path = await self._generate_metrics_chart()
        if metrics_chart_path:
            visualization_paths["metrics_chart"] = str(metrics_chart_path)

        # Send the visualization paths back
        response_payload = {
            "status": "completed",
            "visualization_paths": visualization_paths
        }
        sender = context.metadata.get("sender_id")
        if sender:
            await self.send(sender, MessageType.VISUALIZATION_COMPLETED, response_payload, context)

    async def _generate_dependency_graph(self) -> Optional[Path]:
        """
        Generate a dependency graph visualization.

        Returns:
            Path to the generated visualization file or None if generation failed
        """
        try:
            if not self.project_metrics:
                logger.error("Cannot generate dependency graph: project_metrics is None")
                return None

            # Create the output path
            project_name = getattr(self.project_metrics, 'project_name',
                                  Path(self.project_metrics.project_path).name if self.project_metrics.project_path else "project")
            output_path = self.output_dir / f"{project_name}_dependency_graph.png"

            # Create a directed graph
            G: nx.DiGraph = nx.DiGraph()

            # Add nodes and edges from dependencies
            dependencies = {}
            if hasattr(self.project_metrics, 'dependencies'):
                dependencies = self.project_metrics.dependencies
            elif hasattr(self.project_metrics, 'files'):
                # Build dependencies from file metrics
                for file_path, file_metric in self.project_metrics.files.items():
                    if hasattr(file_metric, 'internal_deps') and file_metric.internal_deps:
                        dependencies[file_path] = file_metric.internal_deps

            for source, targets in dependencies.items():
                if source not in G:
                    G.add_node(source)
                for target in targets:
                    if target not in G:
                        G.add_node(target)
                    G.add_edge(source, target)

            # If no dependencies found, return None
            if not G.nodes():
                logger.warning("No dependencies found for visualization")
                return None

            # Calculate node sizes based on in-degree (number of incoming edges)
            in_degree = {}
            for node in G.nodes():
                in_degree[node] = G.in_degree(node)
            node_sizes = [1000 + 500 * (in_degree.get(node, 0) if isinstance(in_degree.get(node, 0), int) else 0) for node in G.nodes()]

            # Calculate edge weights if enabled
            edge_weights = None
            if self.visualization_config["use_edge_weights"] and hasattr(self.project_metrics, 'edge_weights'):
                edge_weights = [self.project_metrics.edge_weights.get((u, v), 1.0)
                               for u, v in G.edges()]

            # Set up the figure
            plt.figure(figsize=self.visualization_config["figsize"],
                      dpi=self.visualization_config["dpi"])

            # Use a layout algorithm based on graph size
            nodes_list = list(G.nodes())
            if len(nodes_list) > 30:
                pos = nx.spring_layout(G, seed=42)
            else:
                pos = nx.kamada_kawai_layout(G)

            # Draw the graph
            nx.draw_networkx(
                G,
                pos=pos,
                with_labels=True,
                node_size=node_sizes,
                node_color=list(range(len(nodes_list))),
                cmap=plt.get_cmap(self.visualization_config["colormap"]),
                font_size=self.visualization_config["font_size"],
                width=edge_weights if edge_weights else self.visualization_config["edge_width"],
                edge_color='gray',
                arrows=True,
                connectionstyle='arc3,rad=0.1',
                arrowsize=10
            )

            # Save the figure
            plt.tight_layout()
            plt.savefig(output_path, bbox_inches='tight')
            plt.close()

            return output_path
        except Exception as e:
            logger.error(f"Error generating dependency graph: {e}")
            return None

    async def _generate_complexity_heatmap(self) -> Optional[Path]:
        """
        Generate a complexity heatmap visualization.

        Returns:
            Path to the generated visualization file or None if generation failed
        """
        try:
            if not self.project_metrics:
                logger.error("Cannot generate complexity heatmap: project_metrics is None")
                return None

            # Create the output path
            project_name = getattr(self.project_metrics, 'project_name',
                                  Path(self.project_metrics.project_path).name if self.project_metrics.project_path else "project")
            output_path = self.output_dir / f"{project_name}_complexity_heatmap.png"

            # Extract complexity metrics for each file
            file_paths = []
            complexity_values = []
            for file_path, metrics in self.file_metrics.items():
                if hasattr(metrics, 'complexity'):
                    file_paths.append(Path(file_path).name)
                    complexity_values.append(metrics.complexity)

            # Sort by complexity (descending)
            sorted_indices = np.argsort(complexity_values)[::-1]
            file_paths = [file_paths[i] for i in sorted_indices]
            complexity_values = [complexity_values[i] for i in sorted_indices]

            # Limit to the top N most complex files for readability
            N = min(20, len(file_paths))
            file_paths = file_paths[:N]
            complexity_values = complexity_values[:N]

            # Create a custom colormap (green to yellow to red)
            colors = [(0, 0.7, 0), (1, 1, 0), (1, 0, 0)]
            cmap = LinearSegmentedColormap.from_list('complexity_cmap', colors, N=256)

            # Set up the figure
            plt.figure(figsize=(12, 8), dpi=self.visualization_config["dpi"])

            # Create the heatmap
            plt.barh(range(len(file_paths)), complexity_values, color=[cmap(x/max(complexity_values)) for x in complexity_values])
            plt.yticks(range(len(file_paths)), file_paths)
            plt.xlabel('Complexity Score')
            plt.title('File Complexity Heatmap')
            plt.tight_layout()

            # Save the figure
            plt.savefig(output_path, bbox_inches='tight')
            plt.close()

            return output_path
        except Exception as e:
            logger.error(f"Error generating complexity heatmap: {e}")
            return None

    async def _generate_metrics_chart(self) -> Optional[Path]:
        """
        Generate a chart of various code metrics.

        Returns:
            Path to the generated visualization file or None if generation failed
        """
        try:
            if not self.project_metrics:
                logger.error("Cannot generate metrics chart: project_metrics is None")
                return None

            # Create the output path
            project_name = getattr(self.project_metrics, 'project_name',
                                  Path(self.project_metrics.project_path).name if self.project_metrics.project_path else "project")
            output_path = self.output_dir / f"{project_name}_metrics_chart.png"

            # Extract metric categories
            metrics = {}

            if self.project_metrics:
                # Extract general metrics
                metrics["Files"] = getattr(self.project_metrics, 'total_file_count', len(self.project_metrics.files))
                metrics["Lines"] = getattr(self.project_metrics, 'total_line_count',
                                         sum(getattr(fm, 'lines', 0) for fm in self.project_metrics.files.values()))

                # Quality metrics if available
                if hasattr(self.project_metrics, 'quality_score'):
                    metrics["Quality"] = self.project_metrics.quality_score * 100

                # Security score if available
                if hasattr(self.project_metrics, 'security_score'):
                    metrics["Security"] = self.project_metrics.security_score

                # Documentation score if available
                if hasattr(self.project_metrics, 'doc_coverage'):
                    metrics["Documentation"] = self.project_metrics.doc_coverage

            # Set up the figure
            plt.figure(figsize=(10, 8), dpi=self.visualization_config["dpi"])

            # Create bar chart
            categories = list(metrics.keys())
            values = list(metrics.values())

            # Use different colors for different metric types
            colors = ['#3498db', '#2ecc71', '#f39c12', '#e74c3c', '#9b59b6']

            plt.bar(categories, values, color=colors[:len(categories)])
            plt.ylabel('Value')
            plt.title('Project Metrics Overview')

            # Add value labels on top of bars
            for i, v in enumerate(values):
                plt.text(i, v + 0.5, str(round(v, 1)), ha='center')

            plt.tight_layout()

            # Save the figure
            plt.savefig(output_path, bbox_inches='tight')
            plt.close()

            return output_path
        except Exception as e:
            logger.error(f"Error generating metrics chart: {e}")
            return None

    async def handle_visualization_request(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle a general visualization request.

        Args:
            payload: Request parameters
            context: Message context
        """
        # Extract the visualization type
        visualization_type = payload.get("type")
        if not visualization_type:
            logger.error("Visualization request missing 'type' parameter")
            return

        # Delegate to specific visualization methods based on type
        if visualization_type == "dependency_graph":
            graph_path = await self._generate_dependency_graph()
            response_payload = {
                "type": visualization_type,
                "path": str(graph_path) if graph_path else None
            }
        elif visualization_type == "complexity_heatmap":
            heatmap_path = await self._generate_complexity_heatmap()
            response_payload = {
                "type": visualization_type,
                "path": str(heatmap_path) if heatmap_path else None
            }
        elif visualization_type == "metrics_chart":
            chart_path = await self._generate_metrics_chart()
            response_payload = {
                "type": visualization_type,
                "path": str(chart_path) if chart_path else None
            }
        else:
            logger.warning(f"Unknown visualization type: {visualization_type}")
            response_payload = {
                "type": visualization_type,
                "error": "Unsupported visualization type"
            }

        # Send the response back to the requester
        sender = context.metadata.get("sender_id")
        if sender:
            await self.send(sender, MessageType.VISUALIZATION_RESULT, response_payload, context)
