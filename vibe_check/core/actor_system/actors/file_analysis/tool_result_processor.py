"""
Tool Result Processor Module
=========================

This module defines the ToolResultProcessor class, which is responsible for
processing tool results for files.
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Protocol, TypedDict

# Configure logging
logger = logging.getLogger("vibe_check_actor_system")


class QualityMetrics(TypedDict):
    """Type definition for quality metrics dictionary."""
    linting_issues: int


class FileMetricsProtocol(Protocol):
    """Protocol defining the interface required for file metrics."""
    path: str
    lines: int
    type_coverage: float
    security_issues: int
    tool_results: Dict[str, Any]
    quality_metrics: Dict[str, Any]
    security_details: List[Dict[str, Any]]


class FileActorProtocol(Protocol):
    """Protocol defining the interface required from FileActor by ToolResultProcessor."""
    file_path: Path
    file_metrics: Optional[FileMetricsProtocol]

    async def _read_content(self) -> Optional[str]:
        """Read the content of the file."""
        ...

    async def _calculate_type_coverage(self, content: str) -> float:
        """Calculate type coverage for the file content."""
        ...


class ToolResultProcessor:
    """
    Processes tool results for files.

    This class encapsulates the tool result processing logic that was previously
    in the FileActor._update_metrics_from_tool_result method. It is responsible for:
    - Processing results from different tools (ruff, flake8, mypy, bandit, etc.)
    - Updating file metrics based on tool results

    Implementation:
        This class breaks down the complex tool result processing logic into smaller,
        more focused methods. It provides a clean interface for processing results
        from different tools.
    """

    def __init__(self, file_actor: FileActorProtocol) -> None:
        """
        Initialize the tool result processor.

        Args:
            file_actor: The file actor that owns this processor
        """
        self.file_actor = file_actor

    async def process_tool_result(self, tool_name: str, result: Dict[str, Any]) -> None:
        """
        Process a tool result and update file metrics.

        Args:
            tool_name: Name of the tool
            result: Tool result dictionary
        """
        logger.debug(f"Processing {tool_name} result for {self.file_actor.file_path}")

        # Delegate to the appropriate tool-specific processor
        if tool_name in ("ruff", "flake8"):
            await self._process_linting_result(result)
        elif tool_name == "mypy":
            await self._process_mypy_result(result)
        elif tool_name == "bandit":
            await self._process_bandit_result(result)
        else:
            # For other tools, just store the result
            self._store_tool_result(tool_name.lower(), result)

    def _store_tool_result(self, tool_name: str, result: Dict[str, Any]) -> None:
        """
        Store a tool result in the file metrics.

        Args:
            tool_name: Name of the tool
            result: Tool result dictionary
        """
        if self.file_actor.file_metrics:
            self.file_actor.file_metrics.tool_results[tool_name] = result

    async def _process_linting_result(self, result: Dict[str, Any]) -> None:
        """
        Process a linting result (ruff or flake8).

        Args:
            result: Linting result dictionary
        """
        if not self.file_actor.file_metrics:
            return

        # Extract info from linting results
        issues = result.get("issues", [])
        issue_count = len(issues)

        # Create quality metrics dictionary
        quality_metrics: QualityMetrics = {
            "linting_issues": issue_count
        }

        # Update file metrics - cast to Dict[str, Any] to match expected type
        self.file_actor.file_metrics.quality_metrics = dict(quality_metrics)

        logger.debug(f"Processed linting result: {issue_count} issues found")

    async def _process_mypy_result(self, result: Dict[str, Any]) -> None:
        """
        Process a mypy result.

        Args:
            result: Mypy result dictionary
        """
        if not self.file_actor.file_metrics:
            return

        # First try to get type coverage from the mypy result directly
        type_coverage = result.get("type_coverage", 0.0)

        if type_coverage > 0:
            # Use the value provided by mypy
            self.file_actor.file_metrics.type_coverage = type_coverage
            logger.debug(f"Using mypy-provided type coverage: {type_coverage:.1f}%")
        else:
            # Calculate type coverage based on the content
            await self._calculate_type_coverage(result)

    async def _calculate_type_coverage(self, result: Dict[str, Any]) -> None:
        """
        Calculate type coverage when not provided directly by mypy.

        Args:
            result: Mypy result dictionary
        """
        if not self.file_actor.file_metrics:
            return

        content = await self.file_actor._read_content()
        if content:
            # Use the type analyzer to calculate coverage
            coverage = await self.file_actor._calculate_type_coverage(content)
            self.file_actor.file_metrics.type_coverage = coverage
            logger.debug(f"Calculated type coverage: {coverage:.1f}%")
        else:
            # Fallback to a simple heuristic based on issues
            self._estimate_type_coverage_from_issues(result)

    def _estimate_type_coverage_from_issues(self, result: Dict[str, Any]) -> None:
        """
        Estimate type coverage based on type-related issues.

        Args:
            result: Mypy result dictionary
        """
        if not self.file_actor.file_metrics or self.file_actor.file_metrics.lines <= 0:
            return

        issues = result.get("issues", [])
        type_issues = sum(1 for i in issues if "type" in i.get("message", "").lower())

        # Estimate coverage as inverse of issue density
        coverage = max(0.0, 100.0 - (type_issues * 100.0 / self.file_actor.file_metrics.lines))
        self.file_actor.file_metrics.type_coverage = coverage

        logger.debug(f"Estimated type coverage from issues: {coverage:.1f}%")

    async def _process_bandit_result(self, result: Dict[str, Any]) -> None:
        """
        Process a bandit result.

        Args:
            result: Bandit result dictionary
        """
        if not self.file_actor.file_metrics:
            return

        # Extract security issues from bandit results
        security_issues = result.get("issues", [])
        issue_count = len(security_issues)

        # Store security issues in file metrics
        self.file_actor.file_metrics.security_issues = issue_count

        # Store detailed security issues
        self.file_actor.file_metrics.security_details = security_issues

        logger.debug(f"Processed bandit result: {issue_count} security issues found")
