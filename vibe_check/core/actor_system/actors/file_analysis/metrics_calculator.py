"""
Metrics Calculator Module
======================

This module defines the MetricsCalculator class, which is responsible for
calculating various metrics for files in the actor system.
"""

import logging
from typing import Any, Dict, Optional

# Configure logging
logger = logging.getLogger("vibe_check_actor_system")


class MetricsCalculator:
    """
    Calculates metrics for files.
    
    This class encapsulates the metrics calculation logic that was previously
    in the FileActor class. It is responsible for:
    - Calculating docstring coverage
    - Calculating type coverage
    - Processing tool results to update metrics
    
    Implementation:
        This class uses composition to break down the complex metrics calculation
        logic into smaller, more focused components. It delegates to specialized
        analyzers for different types of metrics.
    """
    
    def __init__(self, file_actor: Any):
        """
        Initialize the metrics calculator.
        
        Args:
            file_actor: The file actor that owns this calculator
        """
        self.file_actor = file_actor
        
        # Import specialized analyzers
        from .docstring_analyzer import DocstringAnalyzer
        from .type_analyzer import TypeAnalyzer
        from .tool_result_processor import ToolResultProcessor
        
        # Create specialized analyzers
        self.docstring_analyzer = DocstringAnalyzer(file_actor)
        self.type_analyzer = TypeAnalyzer(file_actor)
        self.tool_result_processor = ToolResultProcessor(file_actor)
        
    async def update_metrics_from_tool_result(self, tool_name: str, result: Dict[str, Any]) -> None:
        """
        Update file metrics based on tool results.
        
        This method delegates to the appropriate specialized analyzer based on the tool name.
        
        Args:
            tool_name: Name of the tool
            result: Tool result dictionary
        """
        if not self.file_actor.file_metrics:
            return
            
        # Delegate to the tool result processor
        await self.tool_result_processor.process_tool_result(tool_name, result)
        
    async def calculate_docstring_coverage(self, content: str) -> float:
        """
        Calculate docstring coverage for a file.
        
        Args:
            content: File content
            
        Returns:
            Docstring coverage percentage (0-100)
        """
        return await self.docstring_analyzer.calculate_coverage(content)
        
    async def calculate_type_coverage(self, content: str) -> float:
        """
        Calculate type coverage for a file.
        
        Args:
            content: File content
            
        Returns:
            Type coverage percentage (0-100)
        """
        return await self.type_analyzer.calculate_coverage(content)
