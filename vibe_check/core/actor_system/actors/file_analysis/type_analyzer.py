"""
Type Analyzer Module
=================

This module defines the TypeAnalyzer class, which is responsible for
analyzing type coverage in files.
"""

import ast
import logging
from pathlib import Path
from typing import Dict, List, Optional, Protocol, Tuple, Union

# Configure logging
logger = logging.getLogger("vibe_check_actor_system")


class FileActorProtocol(Protocol):
    """Protocol defining the interface required from FileActor by TypeAnalyzer."""
    file_path: Path


class TypeAnalyzer:
    """
    Analyzes type coverage in files.

    This class encapsulates the type analysis logic that was previously
    in the FileActor._calculate_type_coverage method. It is responsible for:
    - Parsing Python code to extract functions and methods
    - Checking for type annotations in these elements
    - Calculating overall type coverage

    Implementation:
        This class breaks down the complex type analysis logic into smaller,
        more focused methods. It provides a clean interface for calculating
        type coverage.
    """

    def __init__(self, file_actor: FileActorProtocol) -> None:
        """
        Initialize the type analyzer.

        Args:
            file_actor: The file actor that owns this analyzer
        """
        self.file_actor = file_actor

    async def calculate_coverage(self, content: str) -> float:
        """
        Calculate type coverage for a file.

        Args:
            content: File content

        Returns:
            Type coverage percentage (0-100)
        """
        # Skip empty files
        if not content or not content.strip():
            logger.debug(f"Skipping empty file for type coverage: {self.file_actor.file_path}")
            return 0.0

        # Skip non-Python files
        if not self.file_actor.file_path.suffix == '.py':
            logger.debug(f"Skipping non-Python file for type coverage: {self.file_actor.file_path}")
            return 0.0

        try:
            # Parse the AST
            tree = ast.parse(content)

            # Extract type annotations
            functions, methods, parameters, return_types = self._extract_type_annotations(tree)

            # Calculate coverage
            coverage = self._calculate_coverage_percentage(functions, methods, parameters, return_types)

            logger.debug(
                f"Type coverage for {self.file_actor.file_path}: {coverage:.1f}% "
                f"(functions: {functions}, methods: {methods}, "
                f"parameters: {parameters}, return_types: {return_types})"
            )

            return coverage

        except SyntaxError:
            # If we can't parse the file, assume 0% coverage
            logger.warning(f"Syntax error parsing {self.file_actor.file_path} for type coverage")
            return 0.0
        except Exception as e:
            # Log other errors
            logger.error(f"Error calculating type coverage for {self.file_actor.file_path}: {e}")
            return 0.0

    def _extract_type_annotations(self, tree: ast.AST) -> Tuple[int, int, int, int]:
        """
        Extract type annotations from an AST.

        Args:
            tree: The AST to extract type annotations from

        Returns:
            Tuple containing:
            - Number of functions
            - Number of methods
            - Number of parameters with type annotations
            - Number of return types
        """
        # Counters
        functions = 0
        methods = 0
        parameters = 0
        return_types = 0

        # Create a mapping of nodes to their parents
        parent_map = self._build_parent_map(tree)

        # Process all nodes
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                # Get the parent node
                parent_node = parent_map.get(node)

                # Count function/method
                if parent_node is not None and isinstance(parent_node, ast.ClassDef):
                    methods += 1
                else:
                    functions += 1

                # Count return type
                if node.returns:
                    return_types += 1

                # Count parameter types
                for arg in node.args.args:
                    if arg.annotation:
                        parameters += 1

        return functions, methods, parameters, return_types

    def _build_parent_map(self, tree: ast.AST) -> Dict[ast.AST, ast.AST]:
        """
        Build a mapping of AST nodes to their parent nodes.

        Args:
            tree: The AST to build the parent map for

        Returns:
            Dictionary mapping AST nodes to their parent nodes
        """
        parent_map: Dict[ast.AST, ast.AST] = {}

        for node in ast.walk(tree):
            for child in ast.iter_child_nodes(node):
                parent_map[child] = node

        return parent_map

    def _calculate_coverage_percentage(
        self,
        functions: int,
        methods: int,
        parameters: int,
        return_types: int
    ) -> float:
        """
        Calculate type coverage percentage.

        Args:
            functions: Number of functions
            methods: Number of methods
            parameters: Number of parameters with type annotations
            return_types: Number of return types

        Returns:
            Type coverage percentage (0-100)
        """
        # Calculate total possible annotations
        total_functions_methods = functions + methods
        total_possible_annotations = total_functions_methods * 2  # Return type + at least one parameter

        # Calculate actual annotations
        actual_annotations = parameters + return_types

        # Calculate percentage
        if total_possible_annotations > 0:
            return min(100.0, (actual_annotations / total_possible_annotations) * 100.0)
        else:
            return 0.0
