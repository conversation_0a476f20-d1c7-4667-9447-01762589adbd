"""
Docstring Analyzer Module
======================

This module defines the DocstringAnalyzer class, which is responsible for
analyzing docstring coverage in files.
"""

import ast
import logging
from pathlib import Path
from typing import Dict, List, Optional, Protocol, Tuple

# Configure logging
logger = logging.getLogger("vibe_check_actor_system")


class FileActorProtocol(Protocol):
    """Protocol defining the interface required from FileActor by DocstringAnalyzer."""
    file_path: Path


class DocstringAnalyzer:
    """
    Analyzes docstring coverage in files.

    This class encapsulates the docstring analysis logic that was previously
    in the FileActor._calculate_docstring_coverage method. It is responsible for:
    - Parsing Python code to extract classes, functions, and methods
    - Checking for docstrings in these elements
    - Calculating overall docstring coverage

    Implementation:
        This class breaks down the complex docstring analysis logic into smaller,
        more focused methods. It provides a clean interface for calculating
        docstring coverage.
    """

    def __init__(self, file_actor: FileActorProtocol) -> None:
        """
        Initialize the docstring analyzer.

        Args:
            file_actor: The file actor that owns this analyzer
        """
        self.file_actor = file_actor

    async def calculate_coverage(self, content: str) -> float:
        """
        Calculate docstring coverage for a file.

        Args:
            content: File content

        Returns:
            Docstring coverage percentage (0-100)
        """
        # Skip empty files
        if not content or not content.strip():
            logger.debug(f"Skipping empty file for docstring coverage: {self.file_actor.file_path}")
            return 0.0

        # Skip non-Python files
        if not self.file_actor.file_path.suffix == '.py':
            logger.debug(f"Skipping non-Python file for docstring coverage: {self.file_actor.file_path}")
            return 0.0

        try:
            # Parse the AST
            tree = ast.parse(content)

            # Extract docstrings
            module_docstring, classes, functions, methods = self._extract_docstrings(tree)

            # Calculate coverage
            coverage = self._calculate_coverage_percentage(module_docstring, classes, functions, methods)

            logger.debug(
                f"Docstring coverage for {self.file_actor.file_path}: {coverage:.1f}% "
                f"(module: {module_docstring}, classes: {len(classes)}, "
                f"functions: {len(functions)}, methods: {len(methods)})"
            )

            return coverage

        except SyntaxError:
            # If we can't parse the file, assume 0% coverage
            logger.warning(f"Syntax error parsing {self.file_actor.file_path} for docstring coverage")
            return 0.0
        except Exception as e:
            # Log other errors
            logger.error(f"Error calculating docstring coverage for {self.file_actor.file_path}: {e}")
            return 0.0

    def _extract_docstrings(self, tree: ast.Module) -> Tuple[bool, List[Tuple[str, bool]], List[Tuple[str, bool]], List[Tuple[str, bool]]]:
        """
        Extract docstrings from an AST.

        Args:
            tree: The AST to extract docstrings from

        Returns:
            Tuple containing:
            - Whether the module has a docstring
            - List of (class_name, has_docstring) tuples
            - List of (function_name, has_docstring) tuples
            - List of (method_name, has_docstring) tuples
        """
        # Check for module docstring
        module_docstring = ast.get_docstring(tree) is not None

        # Lists to store results
        classes: List[Tuple[str, bool]] = []
        functions: List[Tuple[str, bool]] = []
        methods: List[Tuple[str, bool]] = []

        # Create a mapping of nodes to their parents
        parent_map = self._build_parent_map(tree)

        # First pass: collect all class nodes
        class_nodes: Dict[int, str] = {}
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                class_nodes[id(node)] = node.name
                has_docstring = ast.get_docstring(node) is not None
                classes.append((node.name, has_docstring))

        # Second pass: process functions and methods
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                has_docstring = ast.get_docstring(node) is not None
                parent_node = parent_map.get(node)

                # Check if this function is inside a class (method)
                if parent_node is not None and isinstance(parent_node, ast.ClassDef):
                    methods.append((f"{parent_node.name}.{node.name}", has_docstring))
                else:
                    # If not a method, it's a top-level function
                    functions.append((node.name, has_docstring))

        return module_docstring, classes, functions, methods

    def _build_parent_map(self, tree: ast.AST) -> Dict[ast.AST, ast.AST]:
        """
        Build a mapping of AST nodes to their parent nodes.

        Args:
            tree: The AST to build the parent map for

        Returns:
            Dictionary mapping AST nodes to their parent nodes
        """
        parent_map: Dict[ast.AST, ast.AST] = {}

        for node in ast.walk(tree):
            for child in ast.iter_child_nodes(node):
                parent_map[child] = node

        return parent_map

    def _calculate_coverage_percentage(
        self,
        module_docstring: bool,
        classes: List[Tuple[str, bool]],
        functions: List[Tuple[str, bool]],
        methods: List[Tuple[str, bool]]
    ) -> float:
        """
        Calculate docstring coverage percentage.

        Args:
            module_docstring: Whether the module has a docstring
            classes: List of (class_name, has_docstring) tuples
            functions: List of (function_name, has_docstring) tuples
            methods: List of (method_name, has_docstring) tuples

        Returns:
            Docstring coverage percentage (0-100)
        """
        # Count total elements and elements with docstrings
        total_elements = 1 + len(classes) + len(functions) + len(methods)  # +1 for module
        elements_with_docstrings = int(module_docstring)

        # Count elements with docstrings
        elements_with_docstrings += sum(1 for _, has_docstring in classes if has_docstring)
        elements_with_docstrings += sum(1 for _, has_docstring in functions if has_docstring)
        elements_with_docstrings += sum(1 for _, has_docstring in methods if has_docstring)

        # Calculate percentage
        if total_elements > 0:
            return (elements_with_docstrings / total_elements) * 100.0
        else:
            return 0.0
