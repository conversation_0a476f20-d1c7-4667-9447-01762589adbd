"""
File Analysis Package
===================

This package contains modules for analyzing files in the actor system.
It implements the CAW principle of adaptive file analysis based on context.

Modules:
- metrics_calculator.py: Calculates metrics for files
- docstring_analyzer.py: Analyzes docstring coverage in files
- type_analyzer.py: Analyzes type coverage in files
- tool_result_processor.py: Processes tool results for files
"""

from .metrics_calculator import MetricsCalculator
from .docstring_analyzer import DocstringAnalyzer
from .type_analyzer import TypeAnalyzer
from .tool_result_processor import ToolResultProcessor

__all__ = [
    'MetricsCalculator',
    'DocstringAnalyzer',
    'TypeAnalyzer',
    'ToolResultProcessor',
]
