"""
System Monitor Module
================

This module provides comprehensive monitoring of the actor system,
including actor health checks, message queue monitoring, resource usage
tracking, performance metrics collection, and alerting for system issues.
"""

import asyncio
import logging
import time
import os
import json
import psutil
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any, Callable, Union
from enum import Enum
import threading

from ..consolidated_initializer import ActorState, get_initializer
from ..actor_registry import ActorRegistry, get_registry
from ..actor import Actor
from ..message import Message, MessageType

logger = logging.getLogger("vibe_check_system_monitor")


class HealthStatus(Enum):
    """Enum representing the possible health statuses of an actor."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


class ResourceMetrics:
    """Class for tracking resource usage metrics."""

    def __init__(self):
        """Initialize the resource metrics."""
        self.cpu_percent = 0.0
        self.memory_percent = 0.0
        self.memory_usage = 0
        self.thread_count = 0
        self.open_files = 0
        self.timestamp = time.time()

    def to_dict(self) -> Dict[str, Any]:
        """Convert the metrics to a dictionary for serialization."""
        return {
            "cpu_percent": self.cpu_percent,
            "memory_percent": self.memory_percent,
            "memory_usage": self.memory_usage,
            "thread_count": self.thread_count,
            "open_files": self.open_files,
            "timestamp": self.timestamp
        }


class ActorMetrics:
    """Class for tracking actor-specific metrics."""

    def __init__(self, actor_id: str):
        """
        Initialize the actor metrics.

        Args:
            actor_id: ID of the actor
        """
        self.actor_id = actor_id
        self.message_count = 0
        self.error_count = 0
        self.processing_time = 0.0
        self.queue_size = 0
        self.last_activity = 0.0
        self.health_status = HealthStatus.UNKNOWN
        self.state = None
        self.timestamp = time.time()

    def to_dict(self) -> Dict[str, Any]:
        """Convert the metrics to a dictionary for serialization."""
        return {
            "actor_id": self.actor_id,
            "message_count": self.message_count,
            "error_count": self.error_count,
            "processing_time": self.processing_time,
            "queue_size": self.queue_size,
            "last_activity": self.last_activity,
            "health_status": self.health_status.value if self.health_status else None,
            "state": self.state.value if self.state else None,
            "timestamp": self.timestamp
        }


class SystemMonitor:
    """
    Monitors the actor system and collects metrics.

    This class provides comprehensive monitoring of the actor system,
    including actor health checks, message queue monitoring, resource usage
    tracking, performance metrics collection, and alerting for system issues.
    """

    def __init__(self, output_dir: Optional[str] = None, check_interval: float = 10.0):
        """
        Initialize the system monitor.

        Args:
            output_dir: Optional directory to save monitoring data
            check_interval: Interval in seconds between health checks
        """
        self._initializer = get_initializer()
        self._registry = get_registry()
        self._output_dir = output_dir
        self._check_interval = check_interval
        self._lock = asyncio.Lock()
        self._initialized = False
        self._check_task: Optional[asyncio.Task] = None
        self._resource_metrics: List[ResourceMetrics] = []
        self._actor_metrics: Dict[str, List[ActorMetrics]] = {}
        self._health_callbacks: List[Callable[[Dict[str, Any]], None]] = []
        self._alert_threshold = 0.8  # 80% resource usage triggers an alert

        # Create output directory if specified
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

    async def initialize(self) -> None:
        """
        Initialize the system monitor.

        This method starts the periodic health check task.
        """
        async with self._lock:
            if self._initialized:
                logger.warning("SystemMonitor already initialized")
                return

            # Start periodic health check
            self._check_task = asyncio.create_task(self._periodic_health_check())

            self._initialized = True
            logger.info("SystemMonitor initialized")

    async def _periodic_health_check(self) -> None:
        """
        Periodically check the health of the actor system.
        """
        try:
            while True:
                # Wait for the check interval
                await asyncio.sleep(self._check_interval)

                # Perform health check
                health_data = await self.check_health()

                # Notify callbacks
                for callback in self._health_callbacks:
                    try:
                        callback(health_data)
                    except Exception as e:
                        logger.error(f"Error in health check callback: {e}")

                # Check for alerts
                await self._check_alerts(health_data)
        except asyncio.CancelledError:
            logger.info("Periodic health check cancelled")
        except Exception as e:
            logger.error(f"Error in periodic health check: {e}")

    async def check_health(self) -> Dict[str, Any]:
        """
        Check the health of the actor system.

        Returns:
            Dictionary with health data
        """
        # Collect resource metrics
        resource_metrics = await self._collect_resource_metrics()
        self._resource_metrics.append(resource_metrics)

        # Trim resource metrics history (keep last 100)
        if len(self._resource_metrics) > 100:
            self._resource_metrics = self._resource_metrics[-100:]

        # Collect actor metrics
        actor_metrics = await self._collect_actor_metrics()

        # Store actor metrics
        for actor_id, metrics in actor_metrics.items():
            if actor_id not in self._actor_metrics:
                self._actor_metrics[actor_id] = []

            self._actor_metrics[actor_id].append(metrics)

            # Trim actor metrics history (keep last 100)
            if len(self._actor_metrics[actor_id]) > 100:
                self._actor_metrics[actor_id] = self._actor_metrics[actor_id][-100:]

        # Prepare health data
        health_data = {
            "timestamp": time.time(),
            "resource_metrics": resource_metrics.to_dict(),
            "actor_metrics": {
                actor_id: metrics.to_dict()
                for actor_id, metrics in actor_metrics.items()
            },
            "system_status": self._calculate_system_status(resource_metrics, actor_metrics)
        }

        return health_data

    async def _collect_resource_metrics(self) -> ResourceMetrics:
        """
        Collect resource usage metrics.

        Returns:
            ResourceMetrics object with collected metrics
        """
        metrics = ResourceMetrics()

        # Get process info
        process = psutil.Process()

        # Collect metrics
        metrics.cpu_percent = process.cpu_percent(interval=0.1)
        metrics.memory_percent = process.memory_percent()
        metrics.memory_usage = process.memory_info().rss
        metrics.thread_count = process.num_threads()
        metrics.open_files = len(process.open_files())
        metrics.timestamp = time.time()

        return metrics

    async def _collect_actor_metrics(self) -> Dict[str, ActorMetrics]:
        """
        Collect metrics for all actors.

        Returns:
            Dictionary mapping actor IDs to ActorMetrics objects
        """
        metrics = {}

        # Get all actors from registry
        actors = self._registry._actors

        for actor_id, actor in actors.items():
            # Create metrics object
            actor_metrics = ActorMetrics(actor_id)

            # Get actor state
            try:
                # Access the actor state directly from the initializer's state dictionary
                with self._initializer._state_lock:
                    actor_metrics.state = self._initializer._actor_states.get(actor_id)
            except Exception:
                # Actor not registered with initializer
                pass

            # Get queue size if available
            if hasattr(actor, "_message_queue") and hasattr(actor._message_queue, "qsize"):
                try:
                    actor_metrics.queue_size = actor._message_queue.qsize()
                except Exception:
                    # Queue size not available
                    pass

            # Get message count if available
            if hasattr(actor, "_message_count"):
                actor_metrics.message_count = actor._message_count

            # Get error count if available
            if hasattr(actor, "_error_count"):
                actor_metrics.error_count = actor._error_count

            # Get processing time if available
            if hasattr(actor, "_processing_time"):
                actor_metrics.processing_time = actor._processing_time

            # Get last activity if available
            if hasattr(actor, "_last_activity"):
                actor_metrics.last_activity = actor._last_activity

            # Calculate health status
            actor_metrics.health_status = await self._calculate_actor_health(actor_id, actor, actor_metrics)

            # Store metrics
            metrics[actor_id] = actor_metrics

        return metrics

    async def _calculate_actor_health(
        self,
        actor_id: str,
        actor: Actor,
        metrics: ActorMetrics
    ) -> HealthStatus:
        """
        Calculate the health status of an actor.

        Args:
            actor_id: ID of the actor
            actor: The actor instance
            metrics: Metrics for the actor

        Returns:
            Health status of the actor
        """
        # Check if actor is in FAILED state
        if metrics.state == ActorState.FAILED:
            return HealthStatus.UNHEALTHY

        # Check if actor is in READY state
        if metrics.state != ActorState.READY:
            return HealthStatus.DEGRADED

        # Check if actor has a high error rate
        if metrics.message_count > 0 and metrics.error_count / metrics.message_count > 0.1:
            return HealthStatus.DEGRADED

        # Check if actor has a large queue
        if metrics.queue_size > 100:
            return HealthStatus.DEGRADED

        # Check if actor has been inactive for too long
        if metrics.last_activity > 0 and time.time() - metrics.last_activity > 300:  # 5 minutes
            return HealthStatus.DEGRADED

        # Actor is healthy
        return HealthStatus.HEALTHY

    def _calculate_system_status(
        self,
        resource_metrics: ResourceMetrics,
        actor_metrics: Dict[str, ActorMetrics]
    ) -> Dict[str, Any]:
        """
        Calculate the overall status of the actor system.

        Args:
            resource_metrics: Resource usage metrics
            actor_metrics: Metrics for all actors

        Returns:
            Dictionary with system status information
        """
        # Count actors by health status
        health_counts = {status: 0 for status in HealthStatus}
        for metrics in actor_metrics.values():
            health_counts[metrics.health_status] += 1

        # Calculate overall health
        total_actors = len(actor_metrics)
        if total_actors == 0:
            overall_health = HealthStatus.UNKNOWN
        elif health_counts[HealthStatus.UNHEALTHY] > 0:
            overall_health = HealthStatus.DEGRADED
        elif health_counts[HealthStatus.DEGRADED] > total_actors * 0.2:  # More than 20% degraded
            overall_health = HealthStatus.DEGRADED
        else:
            overall_health = HealthStatus.HEALTHY

        # Check resource usage
        if resource_metrics.cpu_percent > 90 or resource_metrics.memory_percent > 90:
            overall_health = HealthStatus.DEGRADED

        return {
            "overall_health": overall_health.value,
            "actor_health": {
                status.value: count for status, count in health_counts.items()
            },
            "total_actors": total_actors,
            "resource_usage": {
                "cpu_percent": resource_metrics.cpu_percent,
                "memory_percent": resource_metrics.memory_percent
            }
        }

    async def _check_alerts(self, health_data: Dict[str, Any]) -> None:
        """
        Check for conditions that should trigger alerts.

        Args:
            health_data: Health data from check_health
        """
        # Check resource usage
        resource_metrics = health_data["resource_metrics"]
        if resource_metrics["cpu_percent"] > self._alert_threshold * 100:
            logger.warning(f"High CPU usage: {resource_metrics['cpu_percent']}%")

        if resource_metrics["memory_percent"] > self._alert_threshold * 100:
            logger.warning(f"High memory usage: {resource_metrics['memory_percent']}%")

        # Check actor health
        for actor_id, metrics in health_data["actor_metrics"].items():
            if metrics["health_status"] == HealthStatus.UNHEALTHY.value:
                logger.warning(f"Actor {actor_id} is unhealthy")
            elif metrics["health_status"] == HealthStatus.DEGRADED.value:
                logger.info(f"Actor {actor_id} is degraded")

        # Check system health
        system_status = health_data["system_status"]
        if system_status["overall_health"] == HealthStatus.UNHEALTHY.value:
            logger.error("Actor system is unhealthy")
        elif system_status["overall_health"] == HealthStatus.DEGRADED.value:
            logger.warning("Actor system is degraded")

    def add_health_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        Add a callback to be called when health check is performed.

        Args:
            callback: Function to call with health data
        """
        self._health_callbacks.append(callback)

    def remove_health_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        Remove a health check callback.

        Args:
            callback: Callback to remove
        """
        if callback in self._health_callbacks:
            self._health_callbacks.remove(callback)

    async def export_metrics(self, filename: Optional[str] = None) -> Optional[str]:
        """
        Export metrics to a JSON file.

        Args:
            filename: Optional filename to save to

        Returns:
            Path to the saved file, or None if saving failed
        """
        if not self._output_dir:
            logger.warning("Cannot export metrics: no output directory specified")
            return None

        try:
            # Create a default filename if none provided
            if not filename:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"system_metrics_{timestamp}.json"

            filepath = Path(self._output_dir) / filename

            # Prepare data for serialization
            data = {
                "resource_metrics": [
                    metrics.to_dict() for metrics in self._resource_metrics
                ],
                "actor_metrics": {
                    actor_id: [metrics.to_dict() for metrics in metrics_list]
                    for actor_id, metrics_list in self._actor_metrics.items()
                },
                "timestamp": time.time()
            }

            # Save to file
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Exported metrics to {filepath}")
            return str(filepath)

        except Exception as e:
            logger.error(f"Failed to export metrics: {e}")
            return None

    async def stop(self) -> None:
        """Stop the system monitor."""
        if self._check_task and not self._check_task.done():
            self._check_task.cancel()
            try:
                await self._check_task
            except asyncio.CancelledError:
                pass

        logger.info("SystemMonitor stopped")


# Singleton instance
_monitor = None


def get_system_monitor(output_dir: Optional[str] = None, check_interval: float = 10.0) -> SystemMonitor:
    """
    Get the singleton system monitor instance.

    Args:
        output_dir: Optional directory to save monitoring data
        check_interval: Interval in seconds between health checks

    Returns:
        The system monitor instance
    """
    global _monitor

    if _monitor is None:
        _monitor = SystemMonitor(output_dir, check_interval)
        logger.info("Created new system monitor")

    return _monitor


def reset_system_monitor() -> None:
    """Reset the singleton system monitor instance."""
    global _monitor

    # Don't try to stop the monitor, just reset it
    # This avoids issues with asyncio.create_task outside of an event loop
    _monitor = None
    logger.info("Reset system monitor")
