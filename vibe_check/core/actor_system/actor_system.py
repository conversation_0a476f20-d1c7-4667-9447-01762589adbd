"""
Actor System
=========

This module provides the ActorSystem class for managing actors and message passing.
It implements the CAW principle of choreographed interactions by facilitating
message delivery between actors without centrally controlling their behavior.
"""

import asyncio
import logging
from typing import Dict, Optional, Any, List, Tuple, Union

from .actor import Actor
from .message import Message
from .components import (
    SupervisorComponent, StreamManager, StateManager, MetricsCollector
)

logger = logging.getLogger("vibe_check_actor_system")


class ActorSystem:
    """
    Manages actors and message passing in the actor system.

    The ActorSystem is responsible for:
    1. Registering actors
    2. Delivering messages between actors
    3. Managing actor lifecycle
    """

    def __init__(self) -> None:
        """Initialize the actor system."""
        self.actors: Dict[str, Actor] = {}
        self.running = False
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self._task: Optional[asyncio.Task] = None

        # Initialize components
        self._supervisor: Optional[SupervisorComponent] = None
        self._stream_manager: Optional[StreamManager] = None
        self._state_manager: Optional[StateManager] = None
        self._metrics_collector: Optional[MetricsCollector] = None

    def register_actor(self, actor: Actor) -> None:
        """
        Register an actor with the system.

        Args:
            actor: Actor to register
        """
        # Get the actor ID (handle both actor.id and actor.actor_id)
        actor_id = getattr(actor, 'actor_id', getattr(actor, 'id', None))
        if actor_id is None:
            raise ValueError("Actor must have an actor_id or id attribute")

        if actor_id in self.actors:
            raise ValueError(f"Actor with ID {actor_id} already registered")

        self.actors[actor_id] = actor

        # Set the actor's system reference if it has a system attribute
        if hasattr(actor, 'system'):
            actor.system = self

        # Add the actor to the supervisor's monitoring list if the supervisor is running
        if self._supervisor and self.running:
            asyncio.create_task(self._supervisor.monitor_actor(actor_id))

    def unregister_actor(self, actor_id: str) -> None:
        """
        Unregister an actor from the system.

        Args:
            actor_id: ID of the actor to unregister
        """
        if actor_id in self.actors:
            actor = self.actors[actor_id]

            # Clear the actor's system reference if it has a system attribute
            if hasattr(actor, 'system'):
                actor.system = None

            del self.actors[actor_id]

    def get_actor(self, actor_id: str) -> Optional[Actor]:
        """
        Get an actor by ID.

        Args:
            actor_id: ID of the actor to get

        Returns:
            Actor with the specified ID, or None if not found
        """
        return self.actors.get(actor_id)

    async def send_message(self, message: Message) -> None:
        """
        Send a message to an actor.

        This method validates the message, ensures it has a valid recipient,
        and adds it to the message queue for delivery.

        Args:
            message: Message to send

        Raises:
            ValueError: If the message doesn't have a valid recipient_id
        """
        # Validate message has a recipient_id
        if not hasattr(message, 'recipient_id') or not message.recipient_id:
            # Try to extract recipient from context if available
            if message.context and 'recipient_id' in message.context.metadata:
                message.recipient_id = message.context.metadata['recipient_id']
            else:
                error_msg = f"Cannot send message of type {message.type.name if hasattr(message, 'type') else 'unknown'} without recipient_id"
                logger.error(error_msg)

                # If the message has a sender, send an error message back
                if hasattr(message, 'sender_id') and message.sender_id:
                    try:
                        from .message import MessageType
                        error_message = Message(
                            type=MessageType.ERROR,
                            payload={
                                "error": error_msg,
                                "original_message_type": message.type.name if hasattr(message, 'type') else "unknown"
                            },
                            recipient_id=message.sender_id,
                            sender_id="actor_system"
                        )
                        await self.message_queue.put(error_message)
                    except Exception as e:
                        logger.error(f"Failed to send error message: {e}")

                raise ValueError(error_msg)

        # Check if recipient exists
        if message.recipient_id not in self.actors:
            error_msg = f"Recipient actor {message.recipient_id} not found for message of type {message.type.name if hasattr(message, 'type') else 'unknown'}"
            logger.warning(error_msg)

            # If the message has a sender, send an error message back
            if hasattr(message, 'sender_id') and message.sender_id:
                try:
                    from .message import MessageType
                    error_message = Message(
                        type=MessageType.ERROR,
                        payload={
                            "error": error_msg,
                            "original_message_type": message.type.name if hasattr(message, 'type') else "unknown"
                        },
                        recipient_id=message.sender_id,
                        sender_id="actor_system"
                    )
                    await self.message_queue.put(error_message)
                except Exception as e:
                    logger.error(f"Failed to send error message: {e}")

            # Still queue the message in case the actor is registered later
            # This allows for more resilient message delivery

        # Add the message to the queue
        await self.message_queue.put(message)

        # Log message queued
        logger.debug(
            f"Queued message of type {message.type.name if hasattr(message, 'type') else 'unknown'} "
            f"from {message.sender_id if hasattr(message, 'sender_id') else 'unknown'} "
            f"to {message.recipient_id}"
        )

    async def start(self, wait_for_ready: bool = True, timeout: float = 30.0) -> bool:
        """
        Start the actor system.

        Args:
            wait_for_ready: If True, wait for all actors to be ready before returning
            timeout: Maximum time to wait for actors to be ready (in seconds)

        Returns:
            True if the system started successfully, False otherwise
        """
        if self.running:
            return True

        self.running = True
        self._task = asyncio.create_task(self._process_messages())

        # Initialize and start all components
        try:
            # Initialize and start the supervisor component
            if not self._supervisor:
                self._supervisor = SupervisorComponent()
                await self._supervisor.start()
                logger.info("Supervisor component started")

                # Monitor all registered actors
                for actor_id in self.actors:
                    await self._supervisor.monitor_actor(actor_id)
                    logger.debug(f"Actor {actor_id} is now being monitored")

            # Initialize and start the stream manager
            if not self._stream_manager:
                self._stream_manager = StreamManager()
                await self._stream_manager.start()
                logger.info("Stream manager started")

            # Initialize and start the state manager
            if not self._state_manager:
                self._state_manager = StateManager()
                await self._state_manager.start()
                logger.info("State manager started")

            # Initialize and start the metrics collector
            if not self._metrics_collector:
                self._metrics_collector = MetricsCollector()
                await self._metrics_collector.start()
                logger.info("Metrics collector started")
        except Exception as e:
            logger.error(f"Error starting components: {e}")
            # Continue even if components fail to start

        # Wait for all actors to be ready if requested
        if wait_for_ready and self.actors:
            logger.info(f"Waiting for {len(self.actors)} actors to be ready")

            # Get the initializer
            try:
                from .actor_initializer import get_initializer
                initializer = get_initializer()

                if initializer:
                    # Wait for all actors to be ready
                    try:
                        # Try to use the enhanced method with verify_readiness parameter
                        ready = await initializer.wait_for_all_ready(timeout=timeout, verify_readiness=True)
                    except TypeError:
                        # Fall back to the original method if the parameter is not supported
                        logger.info("Using legacy wait_for_all_ready method without readiness verification")
                        ready = await initializer.wait_for_all_ready(timeout=timeout)

                    if not ready:
                        logger.warning("Not all actors are ready after timeout")
                        return False

                    logger.info("All actors are ready")
                else:
                    logger.warning("No initializer available, cannot wait for actors to be ready")
            except Exception as e:
                logger.error(f"Error waiting for actors to be ready: {e}")
                return False

        return True

    async def _stop_actors(self) -> None:
        """
        Stop all actors in the system.

        This is a helper method for the stop method to make it less complex.
        """
        if not self.actors:
            return

        logger.info(f"Stopping {len(self.actors)} actors")
        stop_tasks = []

        # Create stop tasks for all actors
        for actor_id, actor in self.actors.items():
            try:
                stop_tasks.append(actor.stop())
            except Exception as e:
                logger.error(f"Error creating stop task for actor {actor_id}: {e}")

        if not stop_tasks:
            return

        # Wait for all actors to stop with a timeout
        try:
            await asyncio.wait_for(
                asyncio.gather(*stop_tasks, return_exceptions=True),
                timeout=2.0
            )
            logger.info("All actors stopped")
        except asyncio.TimeoutError:
            logger.warning("Timeout waiting for actors to stop")
        except Exception as e:
            import traceback
            logger.error(f"Error stopping actors: {e}\n{traceback.format_exc()}")

    async def _drain_message_queue(self) -> None:
        """
        Drain the message queue.

        This is a helper method for the stop method to make it less complex.
        """
        if self.message_queue.empty():
            return

        queue_size = self.message_queue.qsize()
        logger.info(f"Draining {queue_size} messages from queue")

        try:
            # Process remaining messages with a timeout
            drained_count = 0
            while not self.message_queue.empty():
                try:
                    self.message_queue.get_nowait()
                    self.message_queue.task_done()
                    drained_count += 1
                except asyncio.QueueEmpty:
                    break

            logger.info(f"Drained {drained_count} messages from queue")
        except Exception as e:
            import traceback
            logger.error(f"Error draining message queue: {e}\n{traceback.format_exc()}")

    async def stop(self) -> None:
        """
        Stop the actor system.

        This method gracefully shuts down the actor system by:
        1. Setting the running flag to false to prevent new messages
        2. Stopping all actors
        3. Cancelling the message processing task
        4. Draining the message queue
        """
        if not self.running:
            logger.info("Actor system is already stopped")
            return

        logger.info("Stopping actor system")

        # First, stop accepting new messages
        self.running = False

        # Stop all actors first to prevent new messages from being sent
        await self._stop_actors()

        # Cancel the message processing task
        if self._task and not self._task.done():
            logger.info("Cancelling message processing task")
            self._task.cancel()
            try:
                await asyncio.wait_for(asyncio.shield(self._task), timeout=1.0)
            except (asyncio.TimeoutError, asyncio.CancelledError):
                # This is expected
                pass
            except Exception as e:
                import traceback
                logger.error(f"Error cancelling message processing task: {e}\n{traceback.format_exc()}")
            finally:
                self._task = None

        # Drain the message queue
        await self._drain_message_queue()

        # Stop all components
        try:
            # Stop the metrics collector
            if self._metrics_collector:
                try:
                    await self._metrics_collector.stop()
                    logger.info("Metrics collector stopped")
                except Exception as e:
                    logger.error(f"Error stopping metrics collector: {e}")
                finally:
                    self._metrics_collector = None

            # Stop the state manager
            if self._state_manager:
                try:
                    await self._state_manager.stop()
                    logger.info("State manager stopped")
                except Exception as e:
                    logger.error(f"Error stopping state manager: {e}")
                finally:
                    self._state_manager = None

            # Stop the stream manager
            if self._stream_manager:
                try:
                    await self._stream_manager.stop()
                    logger.info("Stream manager stopped")
                except Exception as e:
                    logger.error(f"Error stopping stream manager: {e}")
                finally:
                    self._stream_manager = None

            # Stop the supervisor component last
            if self._supervisor:
                try:
                    await self._supervisor.stop()
                    logger.info("Supervisor component stopped")
                except Exception as e:
                    logger.error(f"Error stopping supervisor component: {e}")
                finally:
                    self._supervisor = None
        except Exception as e:
            logger.error(f"Error stopping components: {e}")

        logger.info("Actor system stopped")

    async def shutdown(self) -> None:
        """
        Shutdown the actor system.

        This is an alias for the stop method to maintain compatibility with
        code that expects a shutdown method.
        """
        logger.info("Shutdown method called (alias for stop)")
        await self.stop()

    def get_supervisor(self) -> Optional[SupervisorComponent]:
        """
        Get the supervisor component.

        Returns:
            The supervisor component, or None if not initialized
        """
        return self._supervisor

    def get_stream_manager(self) -> Optional[StreamManager]:
        """
        Get the stream manager component.

        Returns:
            The stream manager component, or None if not initialized
        """
        return self._stream_manager

    def get_state_manager(self) -> Optional[StateManager]:
        """
        Get the state manager component.

        Returns:
            The state manager component, or None if not initialized
        """
        return self._state_manager

    def get_metrics_collector(self) -> Optional[MetricsCollector]:
        """
        Get the metrics collector component.

        Returns:
            The metrics collector component, or None if not initialized
        """
        return self._metrics_collector

    async def _send_error_message(self, recipient_id: str, error_msg: str,
                            original_message_type: str = "unknown",
                            error_details: Optional[str] = None) -> None:
        """
        Send an error message to an actor.

        This is a helper method for the _process_messages method to make it less complex.

        Args:
            recipient_id: ID of the actor to send the error message to
            error_msg: Error message text
            original_message_type: Type of the original message that caused the error
            error_details: Optional detailed error information (e.g., traceback)
        """
        if not recipient_id:
            return

        try:
            from .message import MessageType

            # Create payload
            payload = {
                "error": error_msg,
                "original_message_type": original_message_type
            }

            # Add error details if provided
            if error_details:
                payload["error_details"] = error_details

            # Create and send error message
            error_message = Message(
                type=MessageType.ERROR,
                payload=payload,
                recipient_id=recipient_id,
                sender_id="actor_system"
            )
            await self.message_queue.put(error_message)
        except Exception as e:
            logger.error(f"Failed to send error message to {recipient_id}: {e}")

    async def _deliver_message(self, message: Message, recipient: Actor) -> None:
        """
        Deliver a message to an actor.

        This is a helper method for the _process_messages method to make it less complex.

        Args:
            message: Message to deliver
            recipient: Actor to deliver the message to
        """
        recipient_id = message.recipient_id

        try:
            # Deliver the message
            await recipient.receive(message)
            logger.debug(
                f"Delivered message of type {message.type.name if hasattr(message, 'type') else 'unknown'} "
                f"from {message.sender_id if hasattr(message, 'sender_id') else 'unknown'} "
                f"to {recipient_id}"
            )
        except Exception as e:
            # Log the error
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Error delivering message to actor {recipient_id}: {e}\n{error_details}")

            # If the message has a sender, send an error message back
            if hasattr(message, 'sender_id') and message.sender_id:
                await self._send_error_message(
                    recipient_id=message.sender_id,
                    error_msg=str(e),
                    original_message_type=message.type.name if hasattr(message, 'type') else "unknown",
                    error_details=error_details
                )

    async def _process_message(self, message: Message) -> None:
        """
        Process a single message.

        This is a helper method for the _process_messages method to make it less complex.

        Args:
            message: Message to process
        """
        # Get the recipient actor
        recipient_id = message.recipient_id
        # Ensure recipient_id is a string
        if recipient_id is None:
            logger.warning("Message has no recipient_id")
            return

        recipient = self.actors.get(recipient_id)

        if recipient:
            # Deliver the message to the recipient
            await self._deliver_message(message, recipient)
        else:
            # Actor not found
            error_msg = f"Actor {recipient_id} not found for message delivery"
            logger.warning(error_msg)

            # If the message has a sender, send an error message back
            if hasattr(message, 'sender_id') and message.sender_id:
                await self._send_error_message(
                    recipient_id=message.sender_id,
                    error_msg=error_msg,
                    original_message_type=message.type.name if hasattr(message, 'type') else "unknown"
                )

    async def _process_messages(self) -> None:
        """
        Process messages in the queue.

        This method runs as a background task and processes messages from the queue,
        delivering them to the appropriate actors. It handles errors gracefully and
        ensures that the message queue remains in a consistent state.
        """
        try:
            while True:
                # Check if we should stop processing
                if not self.running:
                    logger.info("Actor system stopped while processing messages")
                    break

                try:
                    # Use a timeout to be more responsive to shutdown requests
                    message = await asyncio.wait_for(self.message_queue.get(), timeout=0.1)

                    # Process the message
                    await self._process_message(message)

                    # Mark the message as done
                    self.message_queue.task_done()

                except asyncio.TimeoutError:
                    # No message received, continue loop
                    await asyncio.sleep(0.001)  # Yield control to the event loop with minimal delay
                    continue
                except asyncio.CancelledError:
                    # Task was cancelled, exit gracefully
                    logger.info("Actor system message processing task cancelled")
                    break
                except Exception as e:
                    # Log the error
                    import traceback
                    error_details = traceback.format_exc()
                    logger.error(f"Error processing message: {e}\n{error_details}")

                    # Mark the message as done even if processing failed
                    try:
                        self.message_queue.task_done()
                    except Exception as task_done_error:
                        # Queue might be in an inconsistent state
                        logger.error(f"Error marking message as done: {task_done_error}")

                    # Brief pause to prevent tight error loops
                    await asyncio.sleep(0.1)

        except asyncio.CancelledError:
            logger.info("Actor system message processing task cancelled")
        except Exception as e:
            # This is a fatal error in the message processing loop
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Fatal error in actor system message processing loop: {e}\n{error_details}")
        finally:
            logger.info("Actor system message processing loop exited")

    async def __aenter__(self) -> 'ActorSystem':
        """
        Enter the context manager asynchronously.

        This method is used when the actor system is used as an async context manager.
        It starts the actor system, waits for all actors to be ready, and returns it.

        Returns:
            The actor system instance

        Raises:
            RuntimeError: If the actor system fails to start
        """
        success = await self.start(wait_for_ready=True, timeout=30.0)
        if not success:
            raise RuntimeError("Actor system failed to start: not all actors are ready")
        return self

    async def __aexit__(self, exc_type: Optional[type], exc_val: Optional[Exception], exc_tb: Optional[object]) -> None:
        """
        Exit the context manager asynchronously.

        This method is used when the actor system is used as an async context manager.
        It stops the actor system.

        Args:
            exc_type: Exception type if an exception was raised in the context
            exc_val: Exception value if an exception was raised in the context
            exc_tb: Exception traceback if an exception was raised in the context
        """
        await self.stop()

    def __enter__(self) -> 'ActorSystem':
        """
        Enter the context manager.

        This method is used when the actor system is used as a context manager.
        It creates a task to start the actor system and returns it.

        Note: This is not ideal as it doesn't wait for the system to start.
        Use the async context manager (__aenter__) instead for better control.

        Returns:
            The actor system instance

        Warns:
            UserWarning: If the actor system is used as a synchronous context manager
        """
        import warnings
        warnings.warn(
            "Using ActorSystem as a synchronous context manager is not recommended. "
            "Use 'async with ActorSystem() as system:' for proper initialization and readiness checks.",
            UserWarning
        )

        # Create a task to start the actor system
        # Note: This is not ideal as it doesn't wait for the system to start
        asyncio.create_task(self.start(wait_for_ready=False))
        return self

    def __exit__(self, exc_type: Optional[type], exc_val: Optional[Exception], exc_tb: Optional[object]) -> None:
        """
        Exit the context manager.

        This method is used when the actor system is used as a context manager.
        It creates a task to stop the actor system.

        Args:
            exc_type: Exception type if an exception was raised in the context
            exc_val: Exception value if an exception was raised in the context
            exc_tb: Exception traceback if an exception was raised in the context
        """
        # Create a task to stop the actor system
        # Note: This is not ideal as it doesn't wait for the system to stop
        asyncio.create_task(self.stop())
