# Visualization Components

This directory contains the visualization components for the actor system. These components are responsible for generating visualizations, updating them in real-time, adding interactive features, managing templates, and integrating them into a comprehensive dashboard.

## Overview

The visualization system provides a comprehensive framework for creating and managing visualizations for the actor system. It supports different visualization types, real-time updates, interactive features, template management, and dashboard integration.

The visualization system consists of the following components:

- **VisualizationGenerator**: Generates various types of visualizations
- **RealTimeVisualizer**: Updates visualizations in real-time
- **InteractiveVisualizer**: Adds interactive features to visualizations
- **TemplateManager**: Manages visualization templates
- **Dashboard**: Integrates visualizations into a comprehensive dashboard

## Components

### VisualizationGenerator

The `VisualizationGenerator` class is responsible for generating various types of visualizations for the actor system. It supports different visualization types, including line charts, bar charts, heatmaps, and network graphs, and provides templates for common monitoring scenarios.

```python
# Example usage
generator = VisualizationGenerator(
    output_dir="visualizations",
    format=VisualizationFormat.PNG,
    dpi=150,
    figsize=(10, 8),
    colormap="viridis",
    interactive=True,
    registry=metrics_registry
)

# Generate a line chart
line_chart_path = generator.generate_visualization(
    visualization_type=VisualizationType.LINE_CHART,
    data={
        "labels": ["Jan", "Feb", "Mar", "Apr", "May"],
        "datasets": [
            {
                "label": "Dataset 1",
                "data": [10, 20, 30, 40, 50]
            }
        ]
    },
    options={
        "title": {
            "text": "Line Chart Example"
        }
    },
    filename="line_chart_example"
)

# Generate visualizations from a template
visualization_paths = generator.generate_from_template(
    template=VisualizationTemplate.ACTOR_HEALTH,
    data=actor_health_data,
    options={
        "title": {
            "text": "Actor Health"
        }
    },
    filename="actor_health"
)
```

### RealTimeVisualizer

The `RealTimeVisualizer` class is responsible for updating visualizations in real-time as new data becomes available. It supports different update intervals and efficient data streaming to avoid performance bottlenecks.

```python
# Example usage
real_time_visualizer = RealTimeVisualizer(
    generator=visualization_generator,
    registry=metrics_registry,
    update_interval=UpdateInterval.SECONDS_5,
    streaming_strategy=DataStreamingStrategy.INCREMENTAL,
    window_size=100
)

# Add a real-time visualization
real_time_visualizer.add_visualization(
    viz_id="actor_message_rate",
    viz_type=VisualizationType.LINE_CHART,
    data_source="metrics",
    config={
        "metrics_config": {
            "metric_name": "messages_processed_rate"
        },
        "options": {
            "title": {
                "text": "Actor Message Processing Rate"
            }
        }
    },
    on_update=lambda viz_id, data: print(f"Updated visualization {viz_id}")
)

# Start the real-time visualizer
await real_time_visualizer.start()

# Get a real-time visualization
visualization = real_time_visualizer.get_visualization("actor_message_rate")

# Stop the real-time visualizer
await real_time_visualizer.stop()
```

### InteractiveVisualizer

The `InteractiveVisualizer` class is responsible for adding interactive features to visualizations, such as zoom, pan, filtering, sorting, and drill-down functionality. It supports tooltips, hover information, and other interactive features to enhance the user experience.

```python
# Example usage
interactive_visualizer = InteractiveVisualizer(
    generator=visualization_generator,
    output_dir="interactive_visualizations",
    format=InteractiveVisualizationFormat.HTML,
    interactions=[InteractionType.ZOOM, InteractionType.PAN, InteractionType.TOOLTIP]
)

# Add a custom interaction handler
interactive_visualizer.add_custom_interaction(
    name="custom_interaction",
    handler=lambda data: print(f"Custom interaction: {data}")
)

# Generate an interactive line chart
interactive_line_chart_path = interactive_visualizer.generate_interactive_visualization(
    visualization_type=VisualizationType.LINE_CHART,
    data={
        "labels": ["Jan", "Feb", "Mar", "Apr", "May"],
        "datasets": [
            {
                "label": "Dataset 1",
                "data": [10, 20, 30, 40, 50]
            }
        ]
    },
    options={
        "title": {
            "text": "Interactive Line Chart Example"
        }
    },
    filename="interactive_line_chart_example"
)
```

### TemplateManager

The `TemplateManager` class is responsible for managing visualization templates, enabling the creation, customization, and management of visualization templates. It supports configuration-based customization, export capabilities, and embedding visualizations in external dashboards or reports.

```python
# Example usage
template_manager = TemplateManager(
    generator=visualization_generator,
    output_dir="templates"
)

# Register a custom template
template_manager.register_custom_template(
    template_id="custom_template",
    template={
        "name": "Custom Template",
        "description": "A custom visualization template",
        "visualizations": [
            {
                "type": VisualizationType.LINE_CHART.value,
                "name": "Line Chart",
                "data_source": "metrics",
                "metrics_config": {
                    "metric_name": "messages_processed_rate"
                },
                "options": {
                    "title": {
                        "text": "Line Chart Example"
                    }
                }
            }
        ]
    }
)

# Generate visualizations from a template
visualization_paths = template_manager.generate_from_template(
    template_id="custom_template",
    data=custom_data,
    options={
        "title": {
            "text": "Custom Template"
        }
    },
    filename="custom_template"
)

# Export a template
template_path = template_manager.export_template(
    template_id="custom_template",
    format=TemplateExportFormat.JSON,
    output_path="custom_template.json"
)
```

### Dashboard

The `Dashboard` class is responsible for integrating visualizations into a comprehensive dashboard, enabling the creation of a monitoring dashboard with multiple visualization panels. It supports multiple visualization panels, layout customization, and saving and loading dashboard configurations.

```python
# Example usage
dashboard = Dashboard(
    generator=visualization_generator,
    real_time_visualizer=real_time_visualizer,
    interactive_visualizer=interactive_visualizer,
    template_manager=template_manager,
    output_dir="dashboard",
    layout=DashboardLayout.GRID,
    theme=DashboardTheme.LIGHT,
    title="Actor System Dashboard",
    description="Comprehensive monitoring dashboard for the actor system",
    refresh_interval=5
)

# Add a panel to the dashboard
dashboard.add_panel(
    panel_id="actor_health_panel",
    panel_type="template",
    panel_config={
        "template_id": "actor_health",
        "title": "Actor Health",
        "description": "Visualizations for monitoring actor health",
        "position": {
            "row": 0,
            "col": 0,
            "width": 6,
            "height": 4
        }
    }
)

# Start the dashboard
await dashboard.start()

# Save the dashboard configuration
config_path = dashboard.save_configuration("dashboard_config")

# Generate the dashboard HTML
dashboard_path = dashboard.generate_dashboard("actor_system_dashboard")

# Stop the dashboard
await dashboard.stop()
```

## Integration with Actor System

The visualization system is integrated with the actor system through the following mechanisms:

1. The `VisualizationGenerator` uses the metrics registry to access actor metrics
2. The `RealTimeVisualizer` updates visualizations based on changes in actor metrics
3. The `Dashboard` integrates visualizations into a comprehensive monitoring dashboard

## Usage Examples

### Basic Visualization

```python
# Create a visualization generator
generator = VisualizationGenerator(
    output_dir="visualizations",
    format=VisualizationFormat.PNG,
    registry=metrics_registry
)

# Generate a line chart
line_chart_path = generator.generate_visualization(
    visualization_type=VisualizationType.LINE_CHART,
    data={
        "labels": ["Jan", "Feb", "Mar", "Apr", "May"],
        "datasets": [
            {
                "label": "Dataset 1",
                "data": [10, 20, 30, 40, 50]
            }
        ]
    },
    options={
        "title": {
            "text": "Line Chart Example"
        }
    },
    filename="line_chart_example"
)
```

### Real-Time Visualization

```python
# Create a real-time visualizer
real_time_visualizer = RealTimeVisualizer(
    generator=visualization_generator,
    registry=metrics_registry,
    update_interval=UpdateInterval.SECONDS_5
)

# Add a real-time visualization
real_time_visualizer.add_visualization(
    viz_id="actor_message_rate",
    viz_type=VisualizationType.LINE_CHART,
    data_source="metrics",
    config={
        "metrics_config": {
            "metric_name": "messages_processed_rate"
        }
    }
)

# Start the real-time visualizer
await real_time_visualizer.start()
```

### Interactive Visualization

```python
# Create an interactive visualizer
interactive_visualizer = InteractiveVisualizer(
    generator=visualization_generator,
    format=InteractiveVisualizationFormat.HTML
)

# Generate an interactive line chart
interactive_line_chart_path = interactive_visualizer.generate_interactive_visualization(
    visualization_type=VisualizationType.LINE_CHART,
    data={
        "labels": ["Jan", "Feb", "Mar", "Apr", "May"],
        "datasets": [
            {
                "label": "Dataset 1",
                "data": [10, 20, 30, 40, 50]
            }
        ]
    }
)
```

### Template-Based Visualization

```python
# Create a template manager
template_manager = TemplateManager(
    generator=visualization_generator
)

# Generate visualizations from a template
visualization_paths = template_manager.generate_from_template(
    template_id="actor_health",
    data=actor_health_data
)
```

### Dashboard Creation

```python
# Create a dashboard
dashboard = Dashboard(
    generator=visualization_generator,
    real_time_visualizer=real_time_visualizer,
    interactive_visualizer=interactive_visualizer,
    template_manager=template_manager,
    title="Actor System Dashboard"
)

# Add panels to the dashboard
dashboard.add_panel(
    panel_id="actor_health_panel",
    panel_type="template",
    panel_config={
        "template_id": "actor_health"
    }
)

dashboard.add_panel(
    panel_id="message_flow_panel",
    panel_type="template",
    panel_config={
        "template_id": "message_flow"
    }
)

# Generate the dashboard HTML
dashboard_path = dashboard.generate_dashboard("actor_system_dashboard")
```
