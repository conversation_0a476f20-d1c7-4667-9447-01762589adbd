"""
Dashboard Module
============

This module provides a dashboard for the actor system, enabling the integration
of multiple visualizations into a comprehensive monitoring dashboard.

The dashboard supports multiple visualization panels, layout customization,
and saving and loading dashboard configurations.
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union, cast, Callable

from .visualization_generator import (
    VisualizationGenerator, VisualizationType, VisualizationFormat, VisualizationTemplate
)
from .real_time_visualizer import RealTimeVisualizer
from .interactive_visualizer import InteractiveVisualizer
from .template_manager import TemplateManager


class DashboardLayout(Enum):
    """Layout options for the dashboard."""
    GRID = "grid"
    ROWS = "rows"
    COLUMNS = "columns"
    TABS = "tabs"
    CUSTOM = "custom"


class DashboardTheme(Enum):
    """Theme options for the dashboard."""
    LIGHT = "light"
    DARK = "dark"
    BLUE = "blue"
    GREEN = "green"
    RED = "red"
    CUSTOM = "custom"


class Dashboard:
    """
    Dashboard for the actor system.

    This class provides methods for creating a comprehensive monitoring dashboard
    with multiple visualization panels.

    Attributes:
        generator: Visualization generator for creating visualizations
        real_time_visualizer: Real-time visualizer for updating visualizations
        interactive_visualizer: Interactive visualizer for interactive features
        template_manager: Template manager for visualization templates
        output_dir: Directory to save dashboard outputs
        panels: Dictionary of dashboard panels
        layout: Dashboard layout
        theme: Dashboard theme
        title: Dashboard title
        description: Dashboard description
        refresh_interval: Dashboard refresh interval in seconds
    """

    def __init__(self, generator: VisualizationGenerator,
                real_time_visualizer: RealTimeVisualizer,
                interactive_visualizer: InteractiveVisualizer,
                template_manager: TemplateManager,
                output_dir: Optional[Union[str, Path]] = None,
                layout: Union[str, DashboardLayout] = DashboardLayout.GRID,
                theme: Union[str, DashboardTheme] = DashboardTheme.LIGHT,
                title: str = "Actor System Dashboard",
                description: str = "Comprehensive monitoring dashboard for the actor system",
                refresh_interval: int = 5):
        """
        Initialize the dashboard.

        Args:
            generator: Visualization generator for creating visualizations
            real_time_visualizer: Real-time visualizer for updating visualizations
            interactive_visualizer: Interactive visualizer for interactive features
            template_manager: Template manager for visualization templates
            output_dir: Optional directory to save dashboard outputs (defaults to generator's output_dir)
            layout: Dashboard layout
            theme: Dashboard theme
            title: Dashboard title
            description: Dashboard description
            refresh_interval: Dashboard refresh interval in seconds
        """
        self.generator = generator
        self.real_time_visualizer = real_time_visualizer
        self.interactive_visualizer = interactive_visualizer
        self.template_manager = template_manager
        self.output_dir = Path(output_dir) if output_dir else generator.output_dir
        
        # Set layout
        if isinstance(layout, DashboardLayout):
            self.layout = layout
        else:
            try:
                self.layout = DashboardLayout(layout)
            except ValueError:
                self.logger.warning(f"Unknown layout: {layout}")
                self.layout = DashboardLayout.GRID
        
        # Set theme
        if isinstance(theme, DashboardTheme):
            self.theme = theme
        else:
            try:
                self.theme = DashboardTheme(theme)
            except ValueError:
                self.logger.warning(f"Unknown theme: {theme}")
                self.theme = DashboardTheme.LIGHT
        
        self.title = title
        self.description = description
        self.refresh_interval = refresh_interval
        self.panels: Dict[str, Dict[str, Any]] = {}
        self.update_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Create output directory if it doesn't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up logging
        self.logger = logging.getLogger("vibe_check_actor_system.visualization.dashboard")

    async def start(self) -> None:
        """Start the dashboard."""
        if self.running:
            self.logger.warning("Dashboard is already running")
            return
        
        self.running = True
        self.update_task = asyncio.create_task(self._update_loop())
        self.logger.info(f"Started dashboard with refresh interval {self.refresh_interval}s")

    async def stop(self) -> None:
        """Stop the dashboard."""
        if not self.running:
            self.logger.warning("Dashboard is not running")
            return
        
        self.running = False
        if self.update_task:
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass
            self.update_task = None
        
        self.logger.info("Stopped dashboard")

    async def _update_loop(self) -> None:
        """Update loop for the dashboard."""
        while self.running:
            try:
                # Update the dashboard
                await self.update()
                
                # Wait for the next update
                await asyncio.sleep(self.refresh_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in update loop: {e}")
                await asyncio.sleep(self.refresh_interval)

    async def update(self) -> None:
        """Update the dashboard."""
        # Update all panels
        for panel_id, panel in list(self.panels.items()):
            try:
                # Update the panel
                await self._update_panel(panel_id, panel)
            except Exception as e:
                self.logger.error(f"Error updating panel {panel_id}: {e}")

    async def _update_panel(self, panel_id: str, panel: Dict[str, Any]) -> None:
        """
        Update a dashboard panel.

        Args:
            panel_id: Panel ID
            panel: Panel configuration
        """
        # Get the panel type
        panel_type = panel.get("type")
        
        # Update the panel based on its type
        if panel_type == "visualization":
            # Update a visualization panel
            viz_id = panel.get("visualization_id")
            if viz_id:
                # Update the visualization
                self.real_time_visualizer.get_visualization(viz_id)
        elif panel_type == "template":
            # Update a template panel
            template_id = panel.get("template_id")
            if template_id:
                # Update the template
                template = self.template_manager.get_template(template_id)
                if template:
                    # Update all visualizations in the template
                    for viz_config in template.get("visualizations", []):
                        viz_id = viz_config.get("id")
                        if viz_id:
                            # Update the visualization
                            self.real_time_visualizer.get_visualization(viz_id)

    def add_panel(self, panel_id: str, panel_type: str, panel_config: Dict[str, Any]) -> None:
        """
        Add a panel to the dashboard.

        Args:
            panel_id: Unique ID for the panel
            panel_type: Type of panel (visualization, template)
            panel_config: Panel configuration
        """
        if panel_id in self.panels:
            self.logger.warning(f"Panel {panel_id} already exists, overwriting")
        
        # Create the panel configuration
        panel = {
            "id": panel_id,
            "type": panel_type,
            "config": panel_config,
            "created_at": time.time()
        }
        
        # Add the panel to the dashboard
        self.panels[panel_id] = panel
        self.logger.info(f"Added panel {panel_id}")

    def remove_panel(self, panel_id: str) -> None:
        """
        Remove a panel from the dashboard.

        Args:
            panel_id: ID of the panel to remove
        """
        if panel_id not in self.panels:
            self.logger.warning(f"Panel {panel_id} not found")
            return
        
        # Remove the panel
        del self.panels[panel_id]
        self.logger.info(f"Removed panel {panel_id}")

    def get_panel(self, panel_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a dashboard panel.

        Args:
            panel_id: ID of the panel to get

        Returns:
            Panel configuration, or None if not found
        """
        return self.panels.get(panel_id)

    def get_all_panels(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all dashboard panels.

        Returns:
            Dictionary of panel configurations
        """
        return self.panels.copy()

    def set_layout(self, layout: Union[str, DashboardLayout]) -> None:
        """
        Set the dashboard layout.

        Args:
            layout: Dashboard layout
        """
        if isinstance(layout, DashboardLayout):
            self.layout = layout
        else:
            try:
                self.layout = DashboardLayout(layout)
            except ValueError:
                self.logger.warning(f"Unknown layout: {layout}")
                self.layout = DashboardLayout.GRID
        
        self.logger.info(f"Set dashboard layout to {self.layout.value}")

    def set_theme(self, theme: Union[str, DashboardTheme]) -> None:
        """
        Set the dashboard theme.

        Args:
            theme: Dashboard theme
        """
        if isinstance(theme, DashboardTheme):
            self.theme = theme
        else:
            try:
                self.theme = DashboardTheme(theme)
            except ValueError:
                self.logger.warning(f"Unknown theme: {theme}")
                self.theme = DashboardTheme.LIGHT
        
        self.logger.info(f"Set dashboard theme to {self.theme.value}")

    def set_refresh_interval(self, interval: int) -> None:
        """
        Set the dashboard refresh interval.

        Args:
            interval: Refresh interval in seconds
        """
        self.refresh_interval = interval
        self.logger.info(f"Set dashboard refresh interval to {self.refresh_interval}s")

    def save_configuration(self, filename: Optional[str] = None) -> str:
        """
        Save the dashboard configuration.

        Args:
            filename: Optional filename for the configuration (without extension)

        Returns:
            Path to the saved configuration
        """
        # Generate filename if not provided
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"dashboard_config_{timestamp}"
        
        # Create the configuration
        config = {
            "title": self.title,
            "description": self.description,
            "layout": self.layout.value,
            "theme": self.theme.value,
            "refresh_interval": self.refresh_interval,
            "panels": self.panels
        }
        
        # Save the configuration
        output_path = self.output_dir / f"{filename}.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2)
        
        self.logger.info(f"Saved dashboard configuration to {output_path}")
        return str(output_path)

    def load_configuration(self, config_path: Union[str, Path]) -> None:
        """
        Load a dashboard configuration.

        Args:
            config_path: Path to the configuration file
        """
        config_path = Path(config_path) if isinstance(config_path, str) else config_path
        
        try:
            # Load the configuration
            with open(config_path, "r", encoding="utf-8") as f:
                config = json.load(f)
            
            # Set the dashboard properties
            self.title = config.get("title", self.title)
            self.description = config.get("description", self.description)
            self.set_layout(config.get("layout", self.layout.value))
            self.set_theme(config.get("theme", self.theme.value))
            self.set_refresh_interval(config.get("refresh_interval", self.refresh_interval))
            
            # Set the panels
            self.panels = config.get("panels", {})
            
            self.logger.info(f"Loaded dashboard configuration from {config_path}")
        except Exception as e:
            self.logger.error(f"Error loading dashboard configuration: {e}")

    def generate_dashboard(self, filename: Optional[str] = None) -> str:
        """
        Generate the dashboard HTML.

        Args:
            filename: Optional filename for the dashboard (without extension)

        Returns:
            Path to the generated dashboard
        """
        # Generate filename if not provided
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"dashboard_{timestamp}"
        
        # Generate the dashboard HTML
        output_path = self.output_dir / f"{filename}.html"
        self._generate_dashboard_html(output_path)
        
        self.logger.info(f"Generated dashboard HTML: {output_path}")
        return str(output_path)

    def _generate_dashboard_html(self, output_path: Path) -> None:
        """
        Generate the dashboard HTML.

        Args:
            output_path: Output path for the dashboard HTML
        """
        # Implementation will be added in the next update
        pass
