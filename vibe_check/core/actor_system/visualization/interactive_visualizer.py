"""
Interactive Visualizer Module
=========================

This module provides interactive visualization capabilities for the actor system,
enabling the creation of visualizations with zoom, pan, filtering, sorting, and
drill-down functionality.

The interactive visualizer supports tooltips, hover information, and other
interactive features to enhance the user experience.
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union, cast, Callable

from .visualization_generator import (
    VisualizationGenerator, VisualizationType, VisualizationFormat, VisualizationTemplate
)


class InteractionType(Enum):
    """Types of interactions supported by the interactive visualizer."""
    ZOOM = "zoom"
    PAN = "pan"
    FILTER = "filter"
    SORT = "sort"
    DRILL_DOWN = "drill_down"
    TOOLTIP = "tooltip"
    HOVER = "hover"
    CLICK = "click"
    SELECTION = "selection"
    BRUSH = "brush"
    CUSTOM = "custom"


class InteractiveVisualizationFormat(Enum):
    """Output formats for interactive visualizations."""
    HTML = "html"
    JSON = "json"
    IFRAME = "iframe"
    EMBED = "embed"


class InteractiveVisualizer:
    """
    Interactive visualizer for the actor system.

    This class provides methods for creating interactive visualizations with
    zoom, pan, filtering, sorting, and drill-down functionality.

    Attributes:
        generator: Visualization generator for creating visualizations
        output_dir: Directory to save visualization outputs
        format: Output format for visualizations
        interactions: List of supported interactions
        custom_interactions: Dictionary of custom interaction handlers
    """

    def __init__(self, generator: VisualizationGenerator,
                output_dir: Optional[Union[str, Path]] = None,
                format: Union[str, InteractiveVisualizationFormat] = InteractiveVisualizationFormat.HTML,
                interactions: Optional[List[Union[str, InteractionType]]] = None):
        """
        Initialize the interactive visualizer.

        Args:
            generator: Visualization generator for creating visualizations
            output_dir: Optional directory to save visualization outputs (defaults to generator's output_dir)
            format: Output format for visualizations
            interactions: Optional list of supported interactions (defaults to all)
        """
        self.generator = generator
        self.output_dir = Path(output_dir) if output_dir else generator.output_dir
        
        # Set format
        if isinstance(format, InteractiveVisualizationFormat):
            self.format = format.value
        else:
            self.format = format
        
        # Set supported interactions
        if interactions:
            self.interactions = []
            for interaction in interactions:
                if isinstance(interaction, InteractionType):
                    self.interactions.append(interaction)
                else:
                    try:
                        self.interactions.append(InteractionType(interaction))
                    except ValueError:
                        pass
        else:
            self.interactions = list(InteractionType)
        
        self.custom_interactions: Dict[str, Callable] = {}
        
        # Create output directory if it doesn't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up logging
        self.logger = logging.getLogger("vibe_check_actor_system.visualization.interactive")

    def add_custom_interaction(self, name: str, handler: Callable) -> None:
        """
        Add a custom interaction handler.

        Args:
            name: Name of the custom interaction
            handler: Handler function for the interaction
        """
        self.custom_interactions[name] = handler
        self.logger.info(f"Added custom interaction handler: {name}")

    def remove_custom_interaction(self, name: str) -> None:
        """
        Remove a custom interaction handler.

        Args:
            name: Name of the custom interaction to remove
        """
        if name in self.custom_interactions:
            del self.custom_interactions[name]
            self.logger.info(f"Removed custom interaction handler: {name}")
        else:
            self.logger.warning(f"Custom interaction handler not found: {name}")

    def generate_interactive_visualization(self, 
                                         visualization_type: Union[str, VisualizationType],
                                         data: Dict[str, Any],
                                         options: Optional[Dict[str, Any]] = None,
                                         filename: Optional[str] = None) -> str:
        """
        Generate an interactive visualization.

        Args:
            visualization_type: Type of visualization to generate
            data: Data for the visualization
            options: Optional visualization options
            filename: Optional filename for the visualization (without extension)

        Returns:
            Path to the generated visualization
        """
        # Convert visualization type to enum if it's a string
        if isinstance(visualization_type, str):
            try:
                visualization_type = VisualizationType(visualization_type)
            except ValueError:
                self.logger.warning(f"Unknown visualization type: {visualization_type}")
                visualization_type = VisualizationType.LINE_CHART
        
        # Generate filename if not provided
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"interactive_{visualization_type.value}_{timestamp}"
        
        # Generate visualization based on type
        if visualization_type == VisualizationType.LINE_CHART:
            return self.generate_interactive_line_chart(data, options, filename)
        elif visualization_type == VisualizationType.BAR_CHART:
            return self.generate_interactive_bar_chart(data, options, filename)
        elif visualization_type == VisualizationType.HEATMAP:
            return self.generate_interactive_heatmap(data, options, filename)
        elif visualization_type == VisualizationType.NETWORK_GRAPH:
            return self.generate_interactive_network_graph(data, options, filename)
        elif visualization_type == VisualizationType.PIE_CHART:
            return self.generate_interactive_pie_chart(data, options, filename)
        elif visualization_type == VisualizationType.SCATTER_PLOT:
            return self.generate_interactive_scatter_plot(data, options, filename)
        elif visualization_type == VisualizationType.AREA_CHART:
            return self.generate_interactive_area_chart(data, options, filename)
        elif visualization_type == VisualizationType.BUBBLE_CHART:
            return self.generate_interactive_bubble_chart(data, options, filename)
        elif visualization_type == VisualizationType.RADAR_CHART:
            return self.generate_interactive_radar_chart(data, options, filename)
        elif visualization_type == VisualizationType.TIMELINE:
            return self.generate_interactive_timeline(data, options, filename)
        else:
            self.logger.warning(f"Unsupported visualization type: {visualization_type}")
            return self.generate_interactive_line_chart(data, options, filename)

    def generate_interactive_line_chart(self, data: Dict[str, Any], 
                                      options: Optional[Dict[str, Any]] = None,
                                      filename: Optional[str] = None) -> str:
        """
        Generate an interactive line chart.

        Args:
            data: Data for the visualization
            options: Optional visualization options
            filename: Optional filename for the visualization (without extension)

        Returns:
            Path to the generated visualization
        """
        # Ensure the output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate filename if not provided
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"interactive_line_chart_{timestamp}"
        
        # Set default options
        default_options = {
            "title": "Interactive Line Chart",
            "xAxis": {
                "title": "X Axis"
            },
            "yAxis": {
                "title": "Y Axis"
            },
            "tooltip": {
                "enabled": True
            },
            "zoom": {
                "enabled": InteractionType.ZOOM in self.interactions
            },
            "pan": {
                "enabled": InteractionType.PAN in self.interactions
            },
            "legend": {
                "enabled": True
            }
        }
        
        # Merge options
        chart_options = default_options.copy()
        if options:
            self._deep_update(chart_options, options)
        
        # Create the chart specification
        chart_spec = {
            "type": "line",
            "data": data,
            "options": chart_options
        }
        
        # Generate the HTML file
        output_path = self.output_dir / f"{filename}.{self.format}"
        self._generate_html_file(chart_spec, output_path)
        
        return str(output_path)

    def generate_interactive_bar_chart(self, data: Dict[str, Any], 
                                     options: Optional[Dict[str, Any]] = None,
                                     filename: Optional[str] = None) -> str:
        """
        Generate an interactive bar chart.

        Args:
            data: Data for the visualization
            options: Optional visualization options
            filename: Optional filename for the visualization (without extension)

        Returns:
            Path to the generated visualization
        """
        # Implementation will be added in the next update
        raise NotImplementedError("Interactive bar chart generation not yet implemented")

    def generate_interactive_heatmap(self, data: Dict[str, Any], 
                                   options: Optional[Dict[str, Any]] = None,
                                   filename: Optional[str] = None) -> str:
        """
        Generate an interactive heatmap.

        Args:
            data: Data for the visualization
            options: Optional visualization options
            filename: Optional filename for the visualization (without extension)

        Returns:
            Path to the generated visualization
        """
        # Implementation will be added in the next update
        raise NotImplementedError("Interactive heatmap generation not yet implemented")

    def generate_interactive_network_graph(self, data: Dict[str, Any], 
                                        options: Optional[Dict[str, Any]] = None,
                                        filename: Optional[str] = None) -> str:
        """
        Generate an interactive network graph.

        Args:
            data: Data for the visualization
            options: Optional visualization options
            filename: Optional filename for the visualization (without extension)

        Returns:
            Path to the generated visualization
        """
        # Implementation will be added in the next update
        raise NotImplementedError("Interactive network graph generation not yet implemented")

    def _generate_html_file(self, chart_spec: Dict[str, Any], output_path: Path) -> None:
        """
        Generate an HTML file for an interactive visualization.

        Args:
            chart_spec: Chart specification
            output_path: Output file path
        """
        # Convert the chart specification to JSON
        chart_json = json.dumps(chart_spec)
        
        # Generate the HTML content
        html_content = self._generate_html_content(chart_json)
        
        # Write the HTML content to the output file
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(html_content)
        
        self.logger.info(f"Generated interactive visualization: {output_path}")

    def _generate_html_content(self, chart_json: str) -> str:
        """
        Generate HTML content for an interactive visualization.

        Args:
            chart_json: Chart specification as JSON

        Returns:
            HTML content
        """
        # Generate the HTML content
        html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Vibe Check Interactive Visualization</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/hammerjs@2.0.8"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@1.2.1"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .chart-container {{ width: 800px; height: 500px; margin: 0 auto; }}
        .controls {{ margin-top: 20px; text-align: center; }}
        button {{ margin: 0 5px; padding: 5px 10px; }}
    </style>
</head>
<body>
    <div class="chart-container">
        <canvas id="chart"></canvas>
    </div>
    <div class="controls">
        <button id="reset-zoom">Reset Zoom</button>
        <button id="toggle-tooltip">Toggle Tooltip</button>
        <button id="toggle-legend">Toggle Legend</button>
    </div>
    <script>
        const ctx = document.getElementById('chart').getContext('2d');
        const chartSpec = {chart_json};
        const chart = new Chart(ctx, chartSpec);
        
        // Add event listeners for controls
        document.getElementById('reset-zoom').addEventListener('click', () => {{
            chart.resetZoom();
        }});
        
        document.getElementById('toggle-tooltip').addEventListener('click', () => {{
            chartSpec.options.tooltip.enabled = !chartSpec.options.tooltip.enabled;
            chart.update();
        }});
        
        document.getElementById('toggle-legend').addEventListener('click', () => {{
            chartSpec.options.legend.display = !chartSpec.options.legend.display;
            chart.update();
        }});
    </script>
</body>
</html>
"""
        
        return html

    def _deep_update(self, d: Dict[str, Any], u: Dict[str, Any]) -> Dict[str, Any]:
        """
        Deep update a dictionary.

        Args:
            d: Dictionary to update
            u: Dictionary with updates

        Returns:
            Updated dictionary
        """
        for k, v in u.items():
            if isinstance(v, dict) and k in d and isinstance(d[k], dict):
                self._deep_update(d[k], v)
            else:
                d[k] = v
        return d
