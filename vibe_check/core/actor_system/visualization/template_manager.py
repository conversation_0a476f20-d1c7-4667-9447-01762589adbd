"""
Template Manager Module
===================

This module provides a template manager for visualization templates, enabling
the creation, customization, and management of visualization templates.

The template manager supports configuration-based customization, export capabilities,
and embedding visualizations in external dashboards or reports.
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union, cast, Callable

from .visualization_generator import (
    VisualizationGenerator, VisualizationType, VisualizationFormat, VisualizationTemplate
)


class TemplateExportFormat(Enum):
    """Export formats for visualization templates."""
    PNG = "png"
    SVG = "svg"
    PDF = "pdf"
    HTML = "html"
    JSON = "json"
    IFRAME = "iframe"
    EMBED = "embed"


class TemplateManager:
    """
    Manager for visualization templates.

    This class provides methods for creating, customizing, and managing
    visualization templates.

    Attributes:
        generator: Visualization generator for creating visualizations
        output_dir: Directory to save template outputs
        templates: Dictionary of registered templates
        custom_templates: Dictionary of custom templates
    """

    def __init__(self, generator: VisualizationGenerator,
                output_dir: Optional[Union[str, Path]] = None):
        """
        Initialize the template manager.

        Args:
            generator: Visualization generator for creating visualizations
            output_dir: Optional directory to save template outputs (defaults to generator's output_dir)
        """
        self.generator = generator
        self.output_dir = Path(output_dir) if output_dir else generator.output_dir
        self.templates: Dict[str, Dict[str, Any]] = {}
        self.custom_templates: Dict[str, Dict[str, Any]] = {}
        
        # Create output directory if it doesn't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up logging
        self.logger = logging.getLogger("vibe_check_actor_system.visualization.template")
        
        # Register built-in templates
        self._register_built_in_templates()

    def _register_built_in_templates(self) -> None:
        """Register built-in visualization templates."""
        # Actor health template
        self.templates["actor_health"] = {
            "name": "Actor Health",
            "description": "Visualizations for monitoring actor health",
            "visualizations": [
                {
                    "type": VisualizationType.LINE_CHART.value,
                    "name": "Actor Message Processing Rate",
                    "data_source": "metrics",
                    "metrics_config": {
                        "metric_name": "messages_processed_rate"
                    },
                    "options": {
                        "title": {
                            "text": "Actor Message Processing Rate"
                        },
                        "xAxis": {
                            "title": {
                                "text": "Time"
                            }
                        },
                        "yAxis": {
                            "title": {
                                "text": "Messages/sec"
                            }
                        }
                    }
                },
                {
                    "type": VisualizationType.LINE_CHART.value,
                    "name": "Actor Error Rate",
                    "data_source": "metrics",
                    "metrics_config": {
                        "metric_name": "error_rate"
                    },
                    "options": {
                        "title": {
                            "text": "Actor Error Rate"
                        },
                        "xAxis": {
                            "title": {
                                "text": "Time"
                            }
                        },
                        "yAxis": {
                            "title": {
                                "text": "Errors/sec"
                            }
                        }
                    }
                },
                {
                    "type": VisualizationType.GAUGE.value,
                    "name": "Actor Health Score",
                    "data_source": "metrics",
                    "metrics_config": {
                        "metric_name": "health_score"
                    },
                    "options": {
                        "title": {
                            "text": "Actor Health Score"
                        },
                        "min": 0,
                        "max": 100,
                        "thresholds": [
                            {
                                "value": 30,
                                "color": "red"
                            },
                            {
                                "value": 70,
                                "color": "yellow"
                            },
                            {
                                "value": 100,
                                "color": "green"
                            }
                        ]
                    }
                }
            ]
        }
        
        # Message flow template
        self.templates["message_flow"] = {
            "name": "Message Flow",
            "description": "Visualizations for monitoring message flow between actors",
            "visualizations": [
                {
                    "type": VisualizationType.NETWORK_GRAPH.value,
                    "name": "Actor Message Flow",
                    "data_source": "metrics",
                    "metrics_config": {
                        "metric_name": "message_flow"
                    },
                    "options": {
                        "title": {
                            "text": "Actor Message Flow"
                        },
                        "node": {
                            "color": "blue"
                        },
                        "edge": {
                            "color": "gray",
                            "width": 2
                        }
                    }
                },
                {
                    "type": VisualizationType.BAR_CHART.value,
                    "name": "Message Types",
                    "data_source": "metrics",
                    "metrics_config": {
                        "metric_name": "message_types"
                    },
                    "options": {
                        "title": {
                            "text": "Message Types"
                        },
                        "xAxis": {
                            "title": {
                                "text": "Message Type"
                            }
                        },
                        "yAxis": {
                            "title": {
                                "text": "Count"
                            }
                        }
                    }
                }
            ]
        }
        
        # System overview template
        self.templates["system_overview"] = {
            "name": "System Overview",
            "description": "Visualizations for monitoring the overall system",
            "visualizations": [
                {
                    "type": VisualizationType.LINE_CHART.value,
                    "name": "System Message Rate",
                    "data_source": "metrics",
                    "metrics_config": {
                        "metric_name": "system_message_rate"
                    },
                    "options": {
                        "title": {
                            "text": "System Message Rate"
                        },
                        "xAxis": {
                            "title": {
                                "text": "Time"
                            }
                        },
                        "yAxis": {
                            "title": {
                                "text": "Messages/sec"
                            }
                        }
                    }
                },
                {
                    "type": VisualizationType.PIE_CHART.value,
                    "name": "Actor Distribution",
                    "data_source": "metrics",
                    "metrics_config": {
                        "metric_name": "actor_distribution"
                    },
                    "options": {
                        "title": {
                            "text": "Actor Distribution"
                        }
                    }
                },
                {
                    "type": VisualizationType.LINE_CHART.value,
                    "name": "System Error Rate",
                    "data_source": "metrics",
                    "metrics_config": {
                        "metric_name": "system_error_rate"
                    },
                    "options": {
                        "title": {
                            "text": "System Error Rate"
                        },
                        "xAxis": {
                            "title": {
                                "text": "Time"
                            }
                        },
                        "yAxis": {
                            "title": {
                                "text": "Errors/sec"
                            }
                        }
                    }
                }
            ]
        }
        
        self.logger.info(f"Registered {len(self.templates)} built-in templates")

    def register_template(self, template_id: str, template: Dict[str, Any]) -> None:
        """
        Register a visualization template.

        Args:
            template_id: Unique ID for the template
            template: Template configuration
        """
        if template_id in self.templates:
            self.logger.warning(f"Template {template_id} already exists, overwriting")
        
        self.templates[template_id] = template
        self.logger.info(f"Registered template: {template_id}")

    def register_custom_template(self, template_id: str, template: Dict[str, Any]) -> None:
        """
        Register a custom visualization template.

        Args:
            template_id: Unique ID for the template
            template: Template configuration
        """
        if template_id in self.custom_templates:
            self.logger.warning(f"Custom template {template_id} already exists, overwriting")
        
        self.custom_templates[template_id] = template
        self.logger.info(f"Registered custom template: {template_id}")

    def get_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a visualization template.

        Args:
            template_id: ID of the template to get

        Returns:
            Template configuration, or None if not found
        """
        if template_id in self.templates:
            return self.templates[template_id]
        elif template_id in self.custom_templates:
            return self.custom_templates[template_id]
        else:
            self.logger.warning(f"Template not found: {template_id}")
            return None

    def get_all_templates(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all visualization templates.

        Returns:
            Dictionary of template configurations
        """
        # Combine built-in and custom templates
        all_templates = self.templates.copy()
        all_templates.update(self.custom_templates)
        return all_templates

    def generate_from_template(self, template_id: str, data: Optional[Dict[str, Any]] = None,
                             options: Optional[Dict[str, Any]] = None,
                             filename: Optional[str] = None) -> Dict[str, str]:
        """
        Generate visualizations from a template.

        Args:
            template_id: ID of the template to use
            data: Optional data for the visualizations
            options: Optional visualization options
            filename: Optional base filename for the visualizations (without extension)

        Returns:
            Dictionary mapping visualization names to file paths
        """
        # Get the template
        template = self.get_template(template_id)
        if not template:
            self.logger.error(f"Template not found: {template_id}")
            return {}
        
        # Generate filename if not provided
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{template_id}_{timestamp}"
        
        # Generate visualizations
        visualization_paths = {}
        for i, viz_config in enumerate(template.get("visualizations", [])):
            try:
                # Get visualization type
                viz_type = viz_config.get("type", VisualizationType.LINE_CHART.value)
                
                # Get visualization name
                viz_name = viz_config.get("name", f"Visualization {i+1}")
                
                # Get visualization data
                viz_data = self._get_visualization_data(viz_config, data)
                
                # Get visualization options
                viz_options = viz_config.get("options", {})
                if options:
                    # Merge with provided options
                    viz_options = self._deep_update(viz_options.copy(), options)
                
                # Generate the visualization
                viz_filename = f"{filename}_{i+1}_{viz_name.lower().replace(' ', '_')}"
                viz_path = self.generator.generate_visualization(
                    visualization_type=viz_type,
                    data=viz_data,
                    options=viz_options,
                    filename=viz_filename
                )
                
                # Add to paths
                visualization_paths[viz_name] = viz_path
            except Exception as e:
                self.logger.error(f"Error generating visualization {i+1} from template {template_id}: {e}")
        
        return visualization_paths

    def _get_visualization_data(self, viz_config: Dict[str, Any], 
                              provided_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Get data for a visualization.

        Args:
            viz_config: Visualization configuration
            provided_data: Optional provided data

        Returns:
            Data for the visualization
        """
        # If data is provided, use it
        if provided_data and viz_config.get("name") in provided_data:
            return provided_data[viz_config["name"]]
        
        # Otherwise, use the data source from the configuration
        data_source = viz_config.get("data_source")
        if data_source == "metrics":
            # Get metrics data
            metrics_config = viz_config.get("metrics_config", {})
            if "metric_name" in metrics_config and self.generator.registry:
                # Get a specific metric
                metric_name = metrics_config["metric_name"]
                actor_id = metrics_config.get("actor_id")
                
                if actor_id:
                    # Get the metric for a specific actor
                    return self.generator.registry.get_actor_metrics(actor_id).get(metric_name, {})
                else:
                    # Get the metric for all actors
                    return self.generator.registry.get_metric(metric_name)
        
        # If no data is available, return an empty dict
        return {}

    def export_template(self, template_id: str, 
                      format: Union[str, TemplateExportFormat] = TemplateExportFormat.JSON,
                      output_path: Optional[Union[str, Path]] = None) -> str:
        """
        Export a visualization template.

        Args:
            template_id: ID of the template to export
            format: Export format
            output_path: Optional output path (defaults to output_dir/template_id.format)

        Returns:
            Path to the exported template
        """
        # Get the template
        template = self.get_template(template_id)
        if not template:
            self.logger.error(f"Template not found: {template_id}")
            return ""
        
        # Convert format to enum if it's a string
        if isinstance(format, str):
            try:
                format = TemplateExportFormat(format)
            except ValueError:
                self.logger.warning(f"Unknown export format: {format}")
                format = TemplateExportFormat.JSON
        
        # Generate output path if not provided
        if output_path is None:
            output_path = self.output_dir / f"{template_id}.{format.value}"
        else:
            output_path = Path(output_path) if isinstance(output_path, str) else output_path
        
        # Export the template based on format
        if format == TemplateExportFormat.JSON:
            # Export as JSON
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(template, f, indent=2)
        elif format == TemplateExportFormat.HTML:
            # Export as HTML
            self._export_template_as_html(template, output_path)
        elif format == TemplateExportFormat.IFRAME:
            # Export as iframe HTML
            self._export_template_as_iframe(template, output_path)
        elif format == TemplateExportFormat.EMBED:
            # Export as embed HTML
            self._export_template_as_embed(template, output_path)
        else:
            self.logger.warning(f"Unsupported export format: {format}")
            return ""
        
        self.logger.info(f"Exported template {template_id} to {output_path}")
        return str(output_path)

    def _export_template_as_html(self, template: Dict[str, Any], output_path: Path) -> None:
        """
        Export a template as HTML.

        Args:
            template: Template configuration
            output_path: Output path
        """
        # Implementation will be added in the next update
        pass

    def _export_template_as_iframe(self, template: Dict[str, Any], output_path: Path) -> None:
        """
        Export a template as iframe HTML.

        Args:
            template: Template configuration
            output_path: Output path
        """
        # Implementation will be added in the next update
        pass

    def _export_template_as_embed(self, template: Dict[str, Any], output_path: Path) -> None:
        """
        Export a template as embed HTML.

        Args:
            template: Template configuration
            output_path: Output path
        """
        # Implementation will be added in the next update
        pass

    def _deep_update(self, d: Dict[str, Any], u: Dict[str, Any]) -> Dict[str, Any]:
        """
        Deep update a dictionary.

        Args:
            d: Dictionary to update
            u: Dictionary with updates

        Returns:
            Updated dictionary
        """
        for k, v in u.items():
            if isinstance(v, dict) and k in d and isinstance(d[k], dict):
                self._deep_update(d[k], v)
            else:
                d[k] = v
        return d
