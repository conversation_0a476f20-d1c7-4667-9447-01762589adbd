"""
Real-Time Visualizer Module
========================

This module provides real-time visualization capabilities for the actor system,
enabling the creation of visualizations that update automatically as new data
becomes available.

The real-time visualizer supports different update intervals and efficient data
streaming to avoid performance bottlenecks.
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union, cast, Callable

from .visualization_generator import (
    VisualizationGenerator, VisualizationType, VisualizationFormat, VisualizationTemplate
)
from ..metrics.registry import MetricsRegistry


class UpdateInterval(Enum):
    """Update intervals for real-time visualizations."""
    SECOND_1 = 1
    SECONDS_5 = 5
    SECONDS_10 = 10
    SECONDS_30 = 30
    MINUTE_1 = 60
    MINUTES_5 = 300
    MINUTES_10 = 600
    MINUTES_30 = 1800
    HOUR_1 = 3600


class DataStreamingStrategy(Enum):
    """Data streaming strategies for real-time visualizations."""
    FULL_REFRESH = "full_refresh"  # Replace all data on each update
    INCREMENTAL = "incremental"    # Only add new data points
    WINDOWED = "windowed"          # Keep a sliding window of data points
    AGGREGATED = "aggregated"      # Aggregate data points over time


class RealTimeVisualizer:
    """
    Real-time visualizer for the actor system.

    This class provides methods for creating visualizations that update
    automatically as new data becomes available.

    Attributes:
        generator: Visualization generator for creating visualizations
        registry: Metrics registry for accessing actor metrics
        update_interval: Interval for updating visualizations
        streaming_strategy: Strategy for streaming data to visualizations
        window_size: Size of the data window for windowed streaming
        active_visualizations: Dictionary of active visualizations
        update_task: Task for updating visualizations
    """

    def __init__(self, generator: VisualizationGenerator, registry: MetricsRegistry,
                update_interval: Union[int, UpdateInterval] = UpdateInterval.SECONDS_5,
                streaming_strategy: Union[str, DataStreamingStrategy] = DataStreamingStrategy.INCREMENTAL,
                window_size: int = 100):
        """
        Initialize the real-time visualizer.

        Args:
            generator: Visualization generator for creating visualizations
            registry: Metrics registry for accessing actor metrics
            update_interval: Interval for updating visualizations (in seconds)
            streaming_strategy: Strategy for streaming data to visualizations
            window_size: Size of the data window for windowed streaming
        """
        self.generator = generator
        self.registry = registry
        
        # Set update interval
        if isinstance(update_interval, UpdateInterval):
            self.update_interval = update_interval.value
        else:
            self.update_interval = update_interval
        
        # Set streaming strategy
        if isinstance(streaming_strategy, str):
            try:
                self.streaming_strategy = DataStreamingStrategy(streaming_strategy)
            except ValueError:
                self.streaming_strategy = DataStreamingStrategy.INCREMENTAL
        else:
            self.streaming_strategy = streaming_strategy
        
        self.window_size = window_size
        self.active_visualizations: Dict[str, Dict[str, Any]] = {}
        self.update_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Set up logging
        self.logger = logging.getLogger("vibe_check_actor_system.visualization.real_time")

    async def start(self) -> None:
        """Start the real-time visualizer."""
        if self.running:
            self.logger.warning("Real-time visualizer is already running")
            return
        
        self.running = True
        self.update_task = asyncio.create_task(self._update_loop())
        self.logger.info(f"Started real-time visualizer with update interval {self.update_interval}s")

    async def stop(self) -> None:
        """Stop the real-time visualizer."""
        if not self.running:
            self.logger.warning("Real-time visualizer is not running")
            return
        
        self.running = False
        if self.update_task:
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass
            self.update_task = None
        
        self.logger.info("Stopped real-time visualizer")

    async def _update_loop(self) -> None:
        """Update loop for real-time visualizations."""
        while self.running:
            try:
                # Update all active visualizations
                await self._update_visualizations()
                
                # Wait for the next update
                await asyncio.sleep(self.update_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in update loop: {e}")
                await asyncio.sleep(self.update_interval)

    async def _update_visualizations(self) -> None:
        """Update all active visualizations."""
        for viz_id, viz_config in list(self.active_visualizations.items()):
            try:
                # Get the data for this visualization
                data = await self._get_visualization_data(viz_config)
                
                # Update the visualization data
                self._update_visualization_data(viz_id, data)
                
                # Generate the updated visualization
                if "on_update" in viz_config and callable(viz_config["on_update"]):
                    viz_config["on_update"](viz_id, data)
            except Exception as e:
                self.logger.error(f"Error updating visualization {viz_id}: {e}")

    async def _get_visualization_data(self, viz_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get data for a visualization.

        Args:
            viz_config: Visualization configuration

        Returns:
            Data for the visualization
        """
        # Get the data source
        data_source = viz_config.get("data_source")
        if data_source is None:
            return {}
        
        # Get the data based on the source
        if data_source == "metrics":
            # Get metrics from the registry
            return await self._get_metrics_data(viz_config)
        elif data_source == "custom":
            # Get custom data from the data provider
            data_provider = viz_config.get("data_provider")
            if data_provider and callable(data_provider):
                return await data_provider()
            return {}
        else:
            self.logger.warning(f"Unknown data source: {data_source}")
            return {}

    async def _get_metrics_data(self, viz_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get metrics data for a visualization.

        Args:
            viz_config: Visualization configuration

        Returns:
            Metrics data for the visualization
        """
        # Get the metrics configuration
        metrics_config = viz_config.get("metrics_config", {})
        
        # Get the metrics
        if "metric_name" in metrics_config:
            # Get a specific metric
            metric_name = metrics_config["metric_name"]
            actor_id = metrics_config.get("actor_id")
            
            if actor_id:
                # Get the metric for a specific actor
                metric = self.registry.get_metric(metric_name, actor_id)
            else:
                # Get the metric for all actors
                metric = self.registry.get_metric(metric_name)
            
            return {"metric_name": metric_name, "metric": metric}
        elif "actor_id" in metrics_config:
            # Get all metrics for a specific actor
            actor_id = metrics_config["actor_id"]
            metrics = self.registry.get_actor_metrics(actor_id)
            
            return {"actor_id": actor_id, "metrics": metrics}
        else:
            # Get all metrics
            metrics = self.registry.get_all_metrics()
            
            return {"metrics": metrics}

    def _update_visualization_data(self, viz_id: str, new_data: Dict[str, Any]) -> None:
        """
        Update the data for a visualization.

        Args:
            viz_id: Visualization ID
            new_data: New data for the visualization
        """
        if viz_id not in self.active_visualizations:
            self.logger.warning(f"Visualization {viz_id} not found")
            return
        
        viz_config = self.active_visualizations[viz_id]
        
        # Update the data based on the streaming strategy
        if self.streaming_strategy == DataStreamingStrategy.FULL_REFRESH:
            # Replace all data
            viz_config["data"] = new_data
        elif self.streaming_strategy == DataStreamingStrategy.INCREMENTAL:
            # Add new data points
            if "data" not in viz_config:
                viz_config["data"] = {}
            
            # Merge the new data with the existing data
            self._merge_data(viz_config["data"], new_data)
        elif self.streaming_strategy == DataStreamingStrategy.WINDOWED:
            # Keep a sliding window of data points
            if "data" not in viz_config:
                viz_config["data"] = {}
            
            # Add the new data points
            self._merge_data(viz_config["data"], new_data)
            
            # Trim the data to the window size
            self._trim_data(viz_config["data"], self.window_size)
        elif self.streaming_strategy == DataStreamingStrategy.AGGREGATED:
            # Aggregate data points over time
            if "data" not in viz_config:
                viz_config["data"] = {}
            
            # Aggregate the new data with the existing data
            self._aggregate_data(viz_config["data"], new_data)

    def _merge_data(self, existing_data: Dict[str, Any], new_data: Dict[str, Any]) -> None:
        """
        Merge new data with existing data.

        Args:
            existing_data: Existing data
            new_data: New data to merge
        """
        # Implementation will be added in the next update
        pass

    def _trim_data(self, data: Dict[str, Any], window_size: int) -> None:
        """
        Trim data to a specific window size.

        Args:
            data: Data to trim
            window_size: Window size
        """
        # Implementation will be added in the next update
        pass

    def _aggregate_data(self, existing_data: Dict[str, Any], new_data: Dict[str, Any]) -> None:
        """
        Aggregate new data with existing data.

        Args:
            existing_data: Existing data
            new_data: New data to aggregate
        """
        # Implementation will be added in the next update
        pass

    def add_visualization(self, viz_id: str, viz_type: Union[str, VisualizationType],
                         data_source: str, config: Dict[str, Any],
                         on_update: Optional[Callable[[str, Dict[str, Any]], None]] = None) -> None:
        """
        Add a real-time visualization.

        Args:
            viz_id: Unique ID for the visualization
            viz_type: Type of visualization
            data_source: Data source for the visualization (metrics, custom)
            config: Visualization configuration
            on_update: Optional callback function to call when the visualization is updated
        """
        if viz_id in self.active_visualizations:
            self.logger.warning(f"Visualization {viz_id} already exists")
            return
        
        # Create the visualization configuration
        viz_config = {
            "id": viz_id,
            "type": viz_type,
            "data_source": data_source,
            "config": config,
            "data": {},
            "created_at": time.time()
        }
        
        # Add the update callback if provided
        if on_update:
            viz_config["on_update"] = on_update
        
        # Add the visualization to the active visualizations
        self.active_visualizations[viz_id] = viz_config
        self.logger.info(f"Added visualization {viz_id}")

    def remove_visualization(self, viz_id: str) -> None:
        """
        Remove a real-time visualization.

        Args:
            viz_id: ID of the visualization to remove
        """
        if viz_id not in self.active_visualizations:
            self.logger.warning(f"Visualization {viz_id} not found")
            return
        
        # Remove the visualization
        del self.active_visualizations[viz_id]
        self.logger.info(f"Removed visualization {viz_id}")

    def get_visualization(self, viz_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a real-time visualization.

        Args:
            viz_id: ID of the visualization to get

        Returns:
            Visualization configuration, or None if not found
        """
        return self.active_visualizations.get(viz_id)

    def get_all_visualizations(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all real-time visualizations.

        Returns:
            Dictionary of visualization configurations
        """
        return self.active_visualizations.copy()

    def set_update_interval(self, interval: Union[int, UpdateInterval]) -> None:
        """
        Set the update interval for real-time visualizations.

        Args:
            interval: Update interval in seconds
        """
        if isinstance(interval, UpdateInterval):
            self.update_interval = interval.value
        else:
            self.update_interval = interval
        
        self.logger.info(f"Set update interval to {self.update_interval}s")

    def set_streaming_strategy(self, strategy: Union[str, DataStreamingStrategy]) -> None:
        """
        Set the data streaming strategy for real-time visualizations.

        Args:
            strategy: Data streaming strategy
        """
        if isinstance(strategy, str):
            try:
                self.streaming_strategy = DataStreamingStrategy(strategy)
            except ValueError:
                self.logger.warning(f"Unknown streaming strategy: {strategy}")
                self.streaming_strategy = DataStreamingStrategy.INCREMENTAL
        else:
            self.streaming_strategy = strategy
        
        self.logger.info(f"Set streaming strategy to {self.streaming_strategy.value}")

    def set_window_size(self, size: int) -> None:
        """
        Set the window size for windowed data streaming.

        Args:
            size: Window size
        """
        self.window_size = size
        self.logger.info(f"Set window size to {self.window_size}")
