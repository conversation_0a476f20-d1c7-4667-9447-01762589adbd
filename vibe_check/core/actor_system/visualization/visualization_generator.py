"""
Visualization Generator Module
==========================

This module provides a comprehensive visualization generator for the actor system,
enabling the creation of various types of visualizations for monitoring and analysis.

The visualization generator supports different visualization types, including line charts,
bar charts, heatmaps, and network graphs, and provides templates for common monitoring
scenarios.
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union, cast

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import networkx as nx
import numpy as np
from matplotlib.colors import LinearSegmentedColormap

from ..metrics.types import MetricType, Metric
from ..metrics.collector import MetricsCollector
from ..metrics.registry import MetricsRegistry


class VisualizationType(Enum):
    """Types of visualizations supported by the generator."""
    LINE_CHART = "line_chart"
    BAR_CHART = "bar_chart"
    HEATMAP = "heatmap"
    NETWORK_GRAPH = "network_graph"
    PIE_CHART = "pie_chart"
    SCATTER_PLOT = "scatter_plot"
    AREA_CHART = "area_chart"
    BUBBLE_CHART = "bubble_chart"
    RADAR_CHART = "radar_chart"
    TIMELINE = "timeline"


class VisualizationFormat(Enum):
    """Output formats for visualizations."""
    PNG = "png"
    SVG = "svg"
    PDF = "pdf"
    HTML = "html"
    JSON = "json"


class VisualizationTemplate(Enum):
    """Templates for common monitoring scenarios."""
    ACTOR_HEALTH = "actor_health"
    MESSAGE_FLOW = "message_flow"
    RESOURCE_USAGE = "resource_usage"
    SYSTEM_OVERVIEW = "system_overview"
    ACTOR_DEPENDENCIES = "actor_dependencies"
    ACTOR_METRICS = "actor_metrics"
    ACTOR_STATES = "actor_states"
    ACTOR_ERRORS = "actor_errors"
    ACTOR_PERFORMANCE = "actor_performance"
    CUSTOM = "custom"


class VisualizationGenerator:
    """
    Generator for actor system visualizations.

    This class provides methods for generating various types of visualizations
    for monitoring and analyzing the actor system.

    Attributes:
        output_dir: Directory to save visualization outputs
        format: Output format for visualizations
        dpi: DPI for raster outputs
        figsize: Figure size for visualizations
        colormap: Colormap for visualizations
        interactive: Whether to generate interactive visualizations
        registry: Metrics registry for accessing actor metrics
    """

    def __init__(self, output_dir: Union[str, Path], 
                format: Union[str, VisualizationFormat] = VisualizationFormat.PNG,
                dpi: int = 150, figsize: Tuple[int, int] = (10, 8),
                colormap: str = "viridis", interactive: bool = True,
                registry: Optional[MetricsRegistry] = None):
        """
        Initialize the visualization generator.

        Args:
            output_dir: Directory to save visualization outputs
            format: Output format for visualizations
            dpi: DPI for raster outputs
            figsize: Figure size for visualizations
            colormap: Colormap for visualizations
            interactive: Whether to generate interactive visualizations
            registry: Optional metrics registry for accessing actor metrics
        """
        self.output_dir = Path(output_dir) if isinstance(output_dir, str) else output_dir
        self.format = format.value if isinstance(format, VisualizationFormat) else format
        self.dpi = dpi
        self.figsize = figsize
        self.colormap = colormap
        self.interactive = interactive
        self.registry = registry
        
        # Create output directory if it doesn't exist
        self.ensure_output_dir()
        
        # Set up logging
        self.logger = logging.getLogger("vibe_check_actor_system.visualization")

    def ensure_output_dir(self) -> None:
        """Ensure the output directory exists."""
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def generate_visualization(self, 
                              visualization_type: Union[str, VisualizationType],
                              data: Dict[str, Any],
                              options: Optional[Dict[str, Any]] = None,
                              filename: Optional[str] = None) -> str:
        """
        Generate a visualization.

        Args:
            visualization_type: Type of visualization to generate
            data: Data for the visualization
            options: Optional visualization options
            filename: Optional filename for the visualization (without extension)

        Returns:
            Path to the generated visualization
        """
        # Convert visualization type to enum if it's a string
        if isinstance(visualization_type, str):
            try:
                visualization_type = VisualizationType(visualization_type)
            except ValueError:
                self.logger.warning(f"Unknown visualization type: {visualization_type}")
                visualization_type = VisualizationType.LINE_CHART
        
        # Generate filename if not provided
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{visualization_type.value}_{timestamp}"
        
        # Generate visualization based on type
        if visualization_type == VisualizationType.LINE_CHART:
            return self.generate_line_chart(data, options, filename)
        elif visualization_type == VisualizationType.BAR_CHART:
            return self.generate_bar_chart(data, options, filename)
        elif visualization_type == VisualizationType.HEATMAP:
            return self.generate_heatmap(data, options, filename)
        elif visualization_type == VisualizationType.NETWORK_GRAPH:
            return self.generate_network_graph(data, options, filename)
        elif visualization_type == VisualizationType.PIE_CHART:
            return self.generate_pie_chart(data, options, filename)
        elif visualization_type == VisualizationType.SCATTER_PLOT:
            return self.generate_scatter_plot(data, options, filename)
        elif visualization_type == VisualizationType.AREA_CHART:
            return self.generate_area_chart(data, options, filename)
        elif visualization_type == VisualizationType.BUBBLE_CHART:
            return self.generate_bubble_chart(data, options, filename)
        elif visualization_type == VisualizationType.RADAR_CHART:
            return self.generate_radar_chart(data, options, filename)
        elif visualization_type == VisualizationType.TIMELINE:
            return self.generate_timeline(data, options, filename)
        else:
            self.logger.warning(f"Unsupported visualization type: {visualization_type}")
            return self.generate_line_chart(data, options, filename)

    def generate_from_template(self, 
                              template: Union[str, VisualizationTemplate],
                              data: Optional[Dict[str, Any]] = None,
                              options: Optional[Dict[str, Any]] = None,
                              filename: Optional[str] = None) -> Dict[str, str]:
        """
        Generate visualizations from a template.

        Args:
            template: Template to use
            data: Optional data for the visualizations (if not provided, will be fetched from registry)
            options: Optional visualization options
            filename: Optional base filename for the visualizations (without extension)

        Returns:
            Dictionary mapping visualization names to file paths
        """
        # Convert template to enum if it's a string
        if isinstance(template, str):
            try:
                template = VisualizationTemplate(template)
            except ValueError:
                self.logger.warning(f"Unknown template: {template}")
                template = VisualizationTemplate.SYSTEM_OVERVIEW
        
        # Generate filename if not provided
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{template.value}_{timestamp}"
        
        # Generate visualizations based on template
        if template == VisualizationTemplate.ACTOR_HEALTH:
            return self.generate_actor_health_template(data, options, filename)
        elif template == VisualizationTemplate.MESSAGE_FLOW:
            return self.generate_message_flow_template(data, options, filename)
        elif template == VisualizationTemplate.RESOURCE_USAGE:
            return self.generate_resource_usage_template(data, options, filename)
        elif template == VisualizationTemplate.SYSTEM_OVERVIEW:
            return self.generate_system_overview_template(data, options, filename)
        elif template == VisualizationTemplate.ACTOR_DEPENDENCIES:
            return self.generate_actor_dependencies_template(data, options, filename)
        elif template == VisualizationTemplate.ACTOR_METRICS:
            return self.generate_actor_metrics_template(data, options, filename)
        elif template == VisualizationTemplate.ACTOR_STATES:
            return self.generate_actor_states_template(data, options, filename)
        elif template == VisualizationTemplate.ACTOR_ERRORS:
            return self.generate_actor_errors_template(data, options, filename)
        elif template == VisualizationTemplate.ACTOR_PERFORMANCE:
            return self.generate_actor_performance_template(data, options, filename)
        elif template == VisualizationTemplate.CUSTOM:
            if data is None or "visualizations" not in data:
                self.logger.warning("Custom template requires 'visualizations' in data")
                return {}
            return self.generate_custom_template(data["visualizations"], options, filename)
        else:
            self.logger.warning(f"Unsupported template: {template}")
            return self.generate_system_overview_template(data, options, filename)

    # Individual visualization generators
    def generate_line_chart(self, data: Dict[str, Any], options: Optional[Dict[str, Any]] = None,
                           filename: Optional[str] = None) -> str:
        """Generate a line chart visualization."""
        # Implementation will be added in the next update
        raise NotImplementedError("Line chart generation not yet implemented")

    def generate_bar_chart(self, data: Dict[str, Any], options: Optional[Dict[str, Any]] = None,
                          filename: Optional[str] = None) -> str:
        """Generate a bar chart visualization."""
        # Implementation will be added in the next update
        raise NotImplementedError("Bar chart generation not yet implemented")

    def generate_heatmap(self, data: Dict[str, Any], options: Optional[Dict[str, Any]] = None,
                        filename: Optional[str] = None) -> str:
        """Generate a heatmap visualization."""
        # Implementation will be added in the next update
        raise NotImplementedError("Heatmap generation not yet implemented")

    def generate_network_graph(self, data: Dict[str, Any], options: Optional[Dict[str, Any]] = None,
                             filename: Optional[str] = None) -> str:
        """Generate a network graph visualization."""
        # Implementation will be added in the next update
        raise NotImplementedError("Network graph generation not yet implemented")

    # Template generators
    def generate_actor_health_template(self, data: Optional[Dict[str, Any]] = None,
                                     options: Optional[Dict[str, Any]] = None,
                                     filename: Optional[str] = None) -> Dict[str, str]:
        """Generate visualizations for the actor health template."""
        # Implementation will be added in the next update
        raise NotImplementedError("Actor health template not yet implemented")

    def generate_message_flow_template(self, data: Optional[Dict[str, Any]] = None,
                                     options: Optional[Dict[str, Any]] = None,
                                     filename: Optional[str] = None) -> Dict[str, str]:
        """Generate visualizations for the message flow template."""
        # Implementation will be added in the next update
        raise NotImplementedError("Message flow template not yet implemented")

    def generate_system_overview_template(self, data: Optional[Dict[str, Any]] = None,
                                        options: Optional[Dict[str, Any]] = None,
                                        filename: Optional[str] = None) -> Dict[str, str]:
        """Generate visualizations for the system overview template."""
        # Implementation will be added in the next update
        raise NotImplementedError("System overview template not yet implemented")

    def generate_custom_template(self, visualizations: List[Dict[str, Any]],
                               options: Optional[Dict[str, Any]] = None,
                               filename: Optional[str] = None) -> Dict[str, str]:
        """Generate visualizations for a custom template."""
        # Implementation will be added in the next update
        raise NotImplementedError("Custom template not yet implemented")
