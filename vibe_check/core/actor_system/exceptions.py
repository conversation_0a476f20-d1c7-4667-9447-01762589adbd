"""
Actor System Exceptions Module
==========================

This module defines the exception hierarchy for the actor system.
These exceptions are used to provide detailed error information
and enable proper error handling and recovery.

The exception hierarchy follows the CAW principle of contextual
adaptation by providing rich context information with each exception.
"""

from enum import Enum
from typing import Any, Dict, Optional, Type

from .consolidated_initializer import ActorState


class ActorSystemError(Exception):
    """Base exception class for all actor system errors."""

    def __init__(self, message: str, actor_id: Optional[str] = None,
                 details: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the exception.

        Args:
            message: Error message
            actor_id: Optional ID of the actor that raised the exception
            details: Optional dictionary with additional error details
        """
        self.actor_id = actor_id
        self.details = details or {}

        # Create a detailed error message
        error_msg = message
        if actor_id:
            error_msg = f"Actor {actor_id}: {error_msg}"

        super().__init__(error_msg)


class ActorInitializationError(ActorSystemError):
    """Exception raised when an actor fails to initialize."""

    def __init__(self, message: str, actor_id: str, phase: str,
                 state: ActorState, original_error: Optional[Exception] = None) -> None:
        """
        Initialize the exception.

        Args:
            message: Error message
            actor_id: ID of the actor that failed to initialize
            phase: Initialization phase that failed (e.g., "initialize", "start")
            state: State of the actor when the error occurred
            original_error: Original exception that caused the failure
        """
        details = {
            "phase": phase,
            "state": state.value,
            "original_error": str(original_error) if original_error else None,
            "timestamp": None  # Will be set by the initializer
        }

        super().__init__(
            f"Failed during {phase} phase (state: {state.value}): {message}",
            actor_id=actor_id,
            details=details
        )

        self.phase = phase
        self.state = state
        self.original_error = original_error


class DependencyErrorType(Enum):
    """Enum representing the types of dependency errors."""
    MISSING = "missing"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CYCLE = "cycle"
    INVALID = "invalid"
    UNKNOWN = "unknown"


class ActorDependencyError(ActorSystemError):
    """Exception raised when an actor's dependencies cannot be satisfied."""

    def __init__(self, message: str, actor_id: str,
                 dependencies: Dict[str, str],
                 error_type: DependencyErrorType = DependencyErrorType.UNKNOWN,
                 cycle_path: Optional[list] = None) -> None:
        """
        Initialize the exception.

        Args:
            message: Error message
            actor_id: ID of the actor with dependency issues
            dependencies: Dictionary mapping dependency IDs to their error states
            error_type: Type of dependency error
            cycle_path: Optional list of actor IDs forming a dependency cycle
        """
        details = {
            "dependencies": dependencies,
            "error_type": error_type.value,
            "cycle_path": cycle_path
        }

        super().__init__(
            message,
            actor_id=actor_id,
            details=details
        )

        self.dependencies = dependencies
        self.error_type = error_type
        self.cycle_path = cycle_path


class ActorMessageError(ActorSystemError):
    """Exception raised when there's an error processing a message."""

    def __init__(self, message: str, actor_id: str,
                 message_type: str, sender_id: Optional[str] = None,
                 original_error: Optional[Exception] = None) -> None:
        """
        Initialize the exception.

        Args:
            message: Error message
            actor_id: ID of the actor that failed to process the message
            message_type: Type of the message that caused the error
            sender_id: Optional ID of the sender of the message
            original_error: Original exception that caused the failure
        """
        details = {
            "message_type": message_type,
            "sender_id": sender_id,
            "original_error": str(original_error) if original_error else None
        }

        super().__init__(
            f"Error processing message of type {message_type}: {message}",
            actor_id=actor_id,
            details=details
        )

        self.message_type = message_type
        self.sender_id = sender_id
        self.original_error = original_error


class ActorTimeoutError(ActorSystemError):
    """Exception raised when an actor operation times out."""

    def __init__(self, message: str, actor_id: str,
                 operation: str, timeout: float) -> None:
        """
        Initialize the exception.

        Args:
            message: Error message
            actor_id: ID of the actor that timed out
            operation: Operation that timed out
            timeout: Timeout value in seconds
        """
        details = {
            "operation": operation,
            "timeout": timeout
        }

        super().__init__(
            f"Timeout ({timeout}s) during {operation}: {message}",
            actor_id=actor_id,
            details=details
        )

        self.operation = operation
        self.timeout = timeout


class ActorNotFoundError(ActorSystemError):
    """Exception raised when an actor cannot be found."""

    def __init__(self, message: str, actor_id: str) -> None:
        """
        Initialize the exception.

        Args:
            message: Error message
            actor_id: ID of the actor that could not be found
        """
        super().__init__(
            message,
            actor_id=actor_id
        )
