"""
Dependency Injection Framework
==========================

This module provides a dependency injection framework for the actor system,
enabling loose coupling between components, easier testing, and more flexible
configuration.

The framework supports:
- Component registration and resolution
- Singleton and transient lifetimes
- Factory functions for complex component creation
- Lazy initialization of components
- Scoped dependencies for request/context-specific instances
"""

from .container import DependencyContainer, get_container, reset_container
from .provider import Provider, SingletonProvider, TransientProvider, FactoryProvider
from .interfaces import Injectable, Inject, Component, ComponentScope

__all__ = [
    'DependencyContainer',
    'get_container',
    'reset_container',
    'Provider',
    'SingletonProvider',
    'TransientProvider',
    'FactoryProvider',
    'Injectable',
    'Inject',
    'Component',
    'ComponentScope'
]
