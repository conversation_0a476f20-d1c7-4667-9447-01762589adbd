"""
Dependency Container
===============

This module defines the dependency container for the dependency injection framework.
The container is responsible for registering and resolving dependencies.
"""

import asyncio
import inspect
import logging
from typing import Any, Callable, Dict, List, Optional, Set, Type, TypeVar, Union, get_type_hints, Tuple

from .interfaces import ComponentScope
from .provider import Provider, SingletonProvider, TransientProvider, FactoryProvider

T = TypeVar('T')

# Configure logging
logger = logging.getLogger("vibe_check_dependency_injection")


class CircularDependencyError(Exception):
    """
    Exception raised when a circular dependency is detected.

    This exception includes detailed information about the dependency cycle,
    making it easier to diagnose and fix circular dependency issues.
    """

    def __init__(self, dependency_chain: List[str]):
        """
        Initialize the exception.

        Args:
            dependency_chain: List of component names in the dependency cycle
        """
        self.dependency_chain = dependency_chain

        # Create a detailed error message showing the cycle
        cycle_str = " -> ".join(dependency_chain)
        error_msg = f"Circular dependency detected: {cycle_str}"

        super().__init__(error_msg)


class DependencyContainer:
    """
    Container for managing dependencies.

    The container is responsible for registering and resolving dependencies.
    It supports singleton and transient lifetimes, as well as factory functions
    for complex component creation.

    Enhanced with:
    - Cycle detection to prevent circular dependencies
    - Detailed error reporting for dependency resolution issues
    - Support for optional dependencies
    - Integration with the actor system
    """

    def __init__(self) -> None:
        """Initialize the container."""
        self._providers: Dict[str, Provider] = {}
        self._type_mappings: Dict[Type, str] = {}
        self._lock = asyncio.Lock()
        self._dependency_graph: Dict[str, Set[str]] = {}  # For cycle detection
        self._currently_resolving: Set[str] = set()  # Track components being resolved

    def register(
        self,
        component_type: Type[T],
        provider: Optional[Provider] = None,
        scope: ComponentScope = ComponentScope.SINGLETON,
        name: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        Register a component with the container.

        Args:
            component_type: The type of component to register
            provider: Optional provider for the component
            scope: The scope of the component (singleton or transient)
            name: Optional name for the component (defaults to class name)
            **kwargs: Additional arguments to pass to the constructor
        """
        # Generate a name for the component if not provided
        component_name = name or component_type.__name__

        # Create a provider if not provided
        if provider is None:
            if scope == ComponentScope.SINGLETON:
                provider = SingletonProvider(component_type, **kwargs)
            elif scope == ComponentScope.TRANSIENT:
                provider = TransientProvider(component_type, **kwargs)
            else:
                raise ValueError(f"Unsupported scope: {scope}")

        # Register the provider
        self._providers[component_name] = provider

        # Register the type mapping
        self._type_mappings[component_type] = component_name

        logger.debug(f"Registered component {component_name} with scope {scope.value}")

    def register_instance(self, instance: T, name: Optional[str] = None) -> None:
        """
        Register an existing instance with the container.

        Args:
            instance: The instance to register
            name: Optional name for the instance (defaults to class name)
        """
        # Generate a name for the instance if not provided
        instance_name = name or instance.__class__.__name__

        # Create a provider that returns the instance
        provider = FactoryProvider(lambda _: instance)

        # Register the provider
        self._providers[instance_name] = provider

        # Register the type mapping
        self._type_mappings[instance.__class__] = instance_name

        logger.debug(f"Registered instance {instance_name}")

    def register_factory(
        self,
        factory: Callable[['DependencyContainer'], T],
        component_type: Optional[Type[T]] = None,
        name: Optional[str] = None
    ) -> None:
        """
        Register a factory function with the container.

        Args:
            factory: Factory function that creates component instances
            component_type: Optional type of the component (for type mappings)
            name: Optional name for the component (defaults to function name)
        """
        # Generate a name for the factory if not provided
        factory_name = name or factory.__name__

        # Create a provider that uses the factory
        provider = FactoryProvider(factory)

        # Register the provider
        self._providers[factory_name] = provider

        # Register the type mapping if component type is provided
        if component_type is not None:
            self._type_mappings[component_type] = factory_name

        logger.debug(f"Registered factory {factory_name}")

    def resolve(self, component_type: Type[T], name: Optional[str] = None,
                optional: bool = False, resolution_path: Optional[List[str]] = None) -> Optional[T]:
        """
        Resolve a component from the container with cycle detection.

        Enhanced with:
        - Cycle detection to prevent circular dependencies
        - Support for optional dependencies
        - Detailed error reporting with dependency chain

        Args:
            component_type: The type of component to resolve
            name: Optional name of the component to resolve
            optional: If True, return None instead of raising an error when the component is not found
            resolution_path: Internal parameter for tracking the dependency chain during resolution

        Returns:
            An instance of the component, or None if optional=True and the component is not found

        Raises:
            ValueError: If the component is not registered and optional=False
            CircularDependencyError: If a circular dependency is detected
        """
        # Initialize resolution path if not provided
        if resolution_path is None:
            resolution_path = []

        # Use the provided name or look up the name from the type mappings
        component_name = name
        if component_name is None:
            component_name = self._type_mappings.get(component_type)
            if component_name is None:
                if optional:
                    logger.debug(f"Optional component type {component_type.__name__} not registered, returning None")
                    return None
                raise ValueError(f"Component type {component_type.__name__} not registered")

        # Get the provider
        provider = self._providers.get(component_name)
        if provider is None:
            if optional:
                logger.debug(f"Optional component {component_name} not registered, returning None")
                return None
            raise ValueError(f"Component {component_name} not registered")

        # Check for circular dependencies
        if component_name in self._currently_resolving:
            # We've detected a cycle
            cycle_path = resolution_path + [component_name]
            # Find the start of the cycle
            cycle_start = cycle_path.index(component_name)
            # Extract the cycle
            cycle = cycle_path[cycle_start:]
            raise CircularDependencyError(cycle)

        # Add to currently resolving set
        self._currently_resolving.add(component_name)

        # Update resolution path for error reporting
        current_path = resolution_path + [component_name]

        try:
            # Update dependency graph for future analysis
            for parent in resolution_path:
                if parent not in self._dependency_graph:
                    self._dependency_graph[parent] = set()
                self._dependency_graph[parent].add(component_name)

            # Get the instance from the provider, passing the current resolution path
            instance = provider.get(self, current_path)
            return instance  # type: ignore
        except Exception as e:
            # Add context to the error
            if not isinstance(e, CircularDependencyError):
                logger.error(f"Error resolving component {component_name}: {e}")
                if resolution_path:
                    logger.error(f"Dependency chain: {' -> '.join(resolution_path)} -> {component_name}")
            raise
        finally:
            # Remove from currently resolving set
            self._currently_resolving.remove(component_name)

    def resolve_all(self, component_type: Type[T]) -> List[T]:
        """
        Resolve all components of a given type.

        Args:
            component_type: The type of components to resolve

        Returns:
            A list of component instances
        """
        instances = []

        # Find all providers for the given type
        for name, provider in self._providers.items():
            try:
                instance = provider.get(self)
                if isinstance(instance, component_type):
                    instances.append(instance)
            except Exception as e:
                logger.warning(f"Error resolving component {name}: {e}")

        return instances

    def analyze_dependencies(self) -> Dict[str, Any]:
        """
        Analyze the dependency graph to detect cycles and provide insights.

        This method analyzes the dependency graph built during component resolution
        to detect cycles and provide insights about the dependency structure.

        Returns:
            A dictionary containing analysis results, including:
            - cycles: List of detected dependency cycles
            - leaf_components: Components with no dependencies
            - root_components: Components that no other component depends on
            - dependency_counts: Number of dependencies for each component
            - dependent_counts: Number of components that depend on each component
        """
        # Initialize result with proper type annotations
        cycles: List[List[str]] = []
        leaf_components: List[str] = []
        root_components: List[str] = []
        dependency_counts: Dict[str, int] = {}
        dependent_counts: Dict[str, int] = {}

        result: Dict[str, Any] = {
            "cycles": cycles,
            "leaf_components": leaf_components,
            "root_components": root_components,
            "dependency_counts": dependency_counts,
            "dependent_counts": dependent_counts
        }

        # Build the reverse dependency graph
        reverse_graph: Dict[str, Set[str]] = {}
        for component, dependencies in self._dependency_graph.items():
            # Count dependencies
            result["dependency_counts"][component] = len(dependencies)

            # Update reverse graph
            for dependency in dependencies:
                if dependency not in reverse_graph:
                    reverse_graph[dependency] = set()
                reverse_graph[dependency].add(component)

        # Count dependents
        for component, dependents in reverse_graph.items():
            result["dependent_counts"][component] = len(dependents)

        # Find leaf components (no dependencies)
        for component in self._providers.keys():
            if component not in self._dependency_graph or not self._dependency_graph[component]:
                result["leaf_components"].append(component)

        # Find root components (no dependents)
        for component in self._providers.keys():
            if component not in reverse_graph or not reverse_graph[component]:
                result["root_components"].append(component)

        # Detect cycles using depth-first search
        visited: Dict[str, bool] = {component: False for component in self._providers.keys()}
        rec_stack: Dict[str, bool] = {component: False for component in self._providers.keys()}

        def detect_cycle(component: str, path: List[str]) -> None:
            """
            Detect cycles in the dependency graph using depth-first search.

            Args:
                component: Current component being visited
                path: Current path in the DFS traversal
            """
            # Mark the current component as visited and add to recursion stack
            visited[component] = True
            rec_stack[component] = True
            path.append(component)

            # Visit all dependencies
            if component in self._dependency_graph:
                for dependency in self._dependency_graph[component]:
                    # If the dependency is not visited, recursively visit it
                    if not visited.get(dependency, False):
                        detect_cycle(dependency, path.copy())
                    # If the dependency is in the recursion stack, we found a cycle
                    elif rec_stack.get(dependency, False):
                        # Find the start of the cycle
                        cycle_start = path.index(dependency)
                        # Extract the cycle
                        cycle = path[cycle_start:] + [dependency]
                        # Add to result if not already present
                        if cycle not in result["cycles"]:
                            result["cycles"].append(cycle)

            # Remove the component from the recursion stack
            rec_stack[component] = False

        # Run cycle detection for each component
        for component in self._providers.keys():
            if not visited[component]:
                detect_cycle(component, [])

        return result

    def detect_cycles(self) -> List[List[str]]:
        """
        Detect cycles in the dependency graph.

        This is a convenience method that calls analyze_dependencies() and
        returns just the cycles.

        Returns:
            A list of detected dependency cycles, where each cycle is a list of
            component names forming the cycle.
        """
        result = self.analyze_dependencies()
        cycles: List[List[str]] = result["cycles"]
        return cycles

    async def clear(self) -> None:
        """Clear all registered components."""
        async with self._lock:
            self._providers.clear()
            self._type_mappings.clear()
            self._dependency_graph.clear()
            self._currently_resolving.clear()
            logger.info("Cleared dependency container")


# Singleton instance
_container: Optional[DependencyContainer] = None


def get_container() -> DependencyContainer:
    """
    Get the singleton container instance.

    Returns:
        The dependency container instance
    """
    global _container

    if _container is None:
        _container = DependencyContainer()

    return _container


def reset_container() -> None:
    """Reset the singleton container instance."""
    global _container

    _container = None
    logger.info("Reset dependency container")
