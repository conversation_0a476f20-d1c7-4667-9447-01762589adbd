"""
Dependency Providers
===============

This module defines the providers for the dependency injection framework.
Providers are responsible for creating and managing component instances.
"""

import inspect
import logging
from abc import ABC, abstractmethod
from typing import Any, Callable, Dict, List, Optional, Set, Type, TypeVar, Union, get_type_hints, TYPE_CHECKING, Generic

if TYPE_CHECKING:
    from .container import DependencyContainer

T = TypeVar('T')

# Configure logging
logger = logging.getLogger("vibe_check_dependency_provider")


class Provider(ABC):
    """
    Base class for dependency providers.

    Providers are responsible for creating and managing component instances.

    Enhanced with:
    - Support for resolution path tracking for cycle detection
    - Better error handling and reporting
    """

    @abstractmethod
    def get(self, container: 'DependencyContainer',
            resolution_path: Optional[List[str]] = None) -> Any:
        """
        Get an instance of the component.

        Args:
            container: The dependency container
            resolution_path: Optional list of component names in the current resolution path
                            for cycle detection and error reporting

        Returns:
            An instance of the component
        """
        pass


class SingletonProvider(Provider, Generic[T]):
    """
    Provider that creates a single instance of a component.

    This provider creates a component instance once and returns the same
    instance for all subsequent requests.

    Enhanced with:
    - Support for resolution path tracking for cycle detection
    - Better error handling and reporting
    """

    def __init__(self, component_type: Type[T], **kwargs: Any) -> None:
        """
        Initialize the provider.

        Args:
            component_type: The type of component to provide
            **kwargs: Additional arguments to pass to the constructor
        """
        self.component_type = component_type
        self.kwargs = kwargs
        self.instance: Optional[T] = None

    def get(self, container: 'DependencyContainer',
            resolution_path: Optional[List[str]] = None) -> T:
        """
        Get an instance of the component.

        Args:
            container: The dependency container
            resolution_path: Optional list of component names in the current resolution path
                            for cycle detection and error reporting

        Returns:
            An instance of the component
        """
        if self.instance is None:
            # Create the instance
            self.instance = self._create_instance(container, resolution_path)

            # Log instance creation
            component_name = self.component_type.__name__
            logger.debug(f"Created singleton instance of {component_name}")

        return self.instance

    def _create_instance(self, container: 'DependencyContainer',
                        resolution_path: Optional[List[str]] = None) -> T:
        """
        Create an instance of the component.

        Args:
            container: The dependency container
            resolution_path: Optional list of component names in the current resolution path
                            for cycle detection and error reporting

        Returns:
            An instance of the component
        """
        # Check if the class is injectable
        if hasattr(self.component_type, '__injectable__') and getattr(self.component_type, '__injectable__'):
            # Get dependencies
            dependencies = getattr(self.component_type, '__dependencies__', {})

            # Resolve dependencies
            kwargs = self.kwargs.copy()
            for param_name, dependency_info in dependencies.items():
                if param_name not in kwargs:
                    # Only resolve required dependencies or those with no default
                    if dependency_info['required']:
                        # Pass the current resolution path for cycle detection
                        kwargs[param_name] = container.resolve(
                            dependency_info['type'],
                            resolution_path=resolution_path
                        )
                    else:
                        # For optional dependencies, use resolve with optional=True
                        kwargs[param_name] = container.resolve(
                            dependency_info['type'],
                            optional=True,
                            resolution_path=resolution_path
                        ) or dependency_info['default']

            # Create instance with resolved dependencies
            try:
                return self.component_type(**kwargs)
            except Exception as e:
                component_name = self.component_type.__name__
                logger.error(f"Error creating instance of {component_name}: {e}")
                if resolution_path:
                    logger.error(f"Dependency chain: {' -> '.join(resolution_path)} -> {component_name}")
                raise
        else:
            # Create instance with provided kwargs
            try:
                return self.component_type(**self.kwargs)
            except Exception as e:
                component_name = self.component_type.__name__
                logger.error(f"Error creating instance of {component_name}: {e}")
                raise


class TransientProvider(Provider, Generic[T]):
    """
    Provider that creates a new instance of a component for each request.

    This provider creates a new component instance for each request.

    Enhanced with:
    - Support for resolution path tracking for cycle detection
    - Better error handling and reporting
    """

    def __init__(self, component_type: Type[T], **kwargs: Any) -> None:
        """
        Initialize the provider.

        Args:
            component_type: The type of component to provide
            **kwargs: Additional arguments to pass to the constructor
        """
        self.component_type = component_type
        self.kwargs = kwargs

    def get(self, container: 'DependencyContainer',
            resolution_path: Optional[List[str]] = None) -> T:
        """
        Get an instance of the component.

        Args:
            container: The dependency container
            resolution_path: Optional list of component names in the current resolution path
                            for cycle detection and error reporting

        Returns:
            An instance of the component
        """
        # Create a new instance
        return self._create_instance(container, resolution_path)

    def _create_instance(self, container: 'DependencyContainer',
                        resolution_path: Optional[List[str]] = None) -> T:
        """
        Create an instance of the component.

        Args:
            container: The dependency container
            resolution_path: Optional list of component names in the current resolution path
                            for cycle detection and error reporting

        Returns:
            An instance of the component
        """
        # Check if the class is injectable
        if hasattr(self.component_type, '__injectable__') and getattr(self.component_type, '__injectable__'):
            # Get dependencies
            dependencies = getattr(self.component_type, '__dependencies__', {})

            # Resolve dependencies
            kwargs = self.kwargs.copy()
            for param_name, dependency_info in dependencies.items():
                if param_name not in kwargs:
                    # Only resolve required dependencies or those with no default
                    if dependency_info['required']:
                        # Pass the current resolution path for cycle detection
                        kwargs[param_name] = container.resolve(
                            dependency_info['type'],
                            resolution_path=resolution_path
                        )
                    else:
                        # For optional dependencies, use resolve with optional=True
                        kwargs[param_name] = container.resolve(
                            dependency_info['type'],
                            optional=True,
                            resolution_path=resolution_path
                        ) or dependency_info['default']

            # Create instance with resolved dependencies
            try:
                return self.component_type(**kwargs)
            except Exception as e:
                component_name = self.component_type.__name__
                logger.error(f"Error creating instance of {component_name}: {e}")
                if resolution_path:
                    logger.error(f"Dependency chain: {' -> '.join(resolution_path)} -> {component_name}")
                raise
        else:
            # Create instance with provided kwargs
            try:
                return self.component_type(**self.kwargs)
            except Exception as e:
                component_name = self.component_type.__name__
                logger.error(f"Error creating instance of {component_name}: {e}")
                raise


class FactoryProvider(Provider, Generic[T]):
    """
    Provider that uses a factory function to create component instances.

    This provider uses a factory function to create component instances.

    Enhanced with:
    - Support for resolution path tracking for cycle detection
    - Better error handling and reporting
    """

    def __init__(self, factory: Callable[['DependencyContainer'], T]) -> None:
        """
        Initialize the provider.

        Args:
            factory: Factory function that creates component instances
        """
        self.factory = factory

    def get(self, container: 'DependencyContainer',
            resolution_path: Optional[List[str]] = None) -> T:
        """
        Get an instance of the component.

        Args:
            container: The dependency container
            resolution_path: Optional list of component names in the current resolution path
                            for cycle detection and error reporting

        Returns:
            An instance of the component
        """
        # Call the factory function
        try:
            return self.factory(container)
        except Exception as e:
            # Log the error with the resolution path for better debugging
            if resolution_path:
                logger.error(f"Error in factory function: {e}")
                logger.error(f"Dependency chain: {' -> '.join(resolution_path)}")
            raise
