"""
Dependency Injection Interfaces
==========================

This module defines the interfaces and decorators for the dependency injection framework.
"""

import inspect
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Type, TypeVar, Union, get_type_hints

T = TypeVar('T')


class ComponentScope(Enum):
    """Enum representing the scope of a component."""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"


class Component:
    """
    Decorator for registering a class as a component in the dependency container.
    
    Usage:
        @Component(scope=ComponentScope.SINGLETON)
        class MyService:
            pass
    """
    
    def __init__(self, scope: ComponentScope = ComponentScope.SINGLETON, name: Optional[str] = None):
        """
        Initialize the decorator.
        
        Args:
            scope: The scope of the component (singleton, transient, or scoped)
            name: Optional name for the component (defaults to class name)
        """
        self.scope = scope
        self.name = name
    
    def __call__(self, cls: Type[T]) -> Type[T]:
        """
        Register the class as a component.
        
        Args:
            cls: The class to register
            
        Returns:
            The original class
        """
        # Store component metadata on the class
        setattr(cls, '__component__', True)
        setattr(cls, '__component_scope__', self.scope)
        setattr(cls, '__component_name__', self.name or cls.__name__)
        
        # Register the component with the container
        from .container import get_container
        container = get_container()
        container.register(cls, scope=self.scope, name=self.name)
        
        return cls


class Injectable:
    """
    Decorator for marking a class as injectable.
    
    This decorator analyzes the class's constructor and marks the dependencies
    for injection.
    
    Usage:
        @Injectable
        class MyService:
            def __init__(self, dependency: Dependency):
                self.dependency = dependency
    """
    
    def __call__(self, cls: Type[T]) -> Type[T]:
        """
        Mark the class as injectable.
        
        Args:
            cls: The class to mark
            
        Returns:
            The original class
        """
        # Store injectable metadata on the class
        setattr(cls, '__injectable__', True)
        
        # Analyze constructor parameters
        init_signature = inspect.signature(cls.__init__)
        type_hints = get_type_hints(cls.__init__)
        
        dependencies = {}
        for param_name, param in init_signature.parameters.items():
            # Skip self parameter
            if param_name == 'self':
                continue
            
            # Get parameter type
            param_type = type_hints.get(param_name, Any)
            
            # Store dependency information
            dependencies[param_name] = {
                'type': param_type,
                'required': param.default == inspect.Parameter.empty,
                'default': None if param.default == inspect.Parameter.empty else param.default
            }
        
        # Store dependencies on the class
        setattr(cls, '__dependencies__', dependencies)
        
        return cls


class Inject:
    """
    Decorator for injecting dependencies into a method or function.
    
    Usage:
        @Inject(dependency=Dependency)
        def my_function(dependency):
            pass
    """
    
    def __init__(self, **dependencies):
        """
        Initialize the decorator.
        
        Args:
            **dependencies: Mapping of parameter names to dependency types
        """
        self.dependencies = dependencies
    
    def __call__(self, func: Callable) -> Callable:
        """
        Inject dependencies into the function.
        
        Args:
            func: The function to inject dependencies into
            
        Returns:
            Wrapped function with dependencies injected
        """
        # Store inject metadata on the function
        setattr(func, '__inject__', True)
        setattr(func, '__dependencies__', self.dependencies)
        
        # Create a wrapper function that injects dependencies
        def wrapper(*args, **kwargs):
            # Get the container
            from .container import get_container
            container = get_container()
            
            # Inject dependencies that aren't already provided
            for param_name, dependency_type in self.dependencies.items():
                if param_name not in kwargs:
                    kwargs[param_name] = container.resolve(dependency_type)
            
            # Call the original function
            return func(*args, **kwargs)
        
        # Copy metadata
        wrapper.__name__ = func.__name__
        wrapper.__doc__ = func.__doc__
        wrapper.__module__ = func.__module__
        wrapper.__annotations__ = func.__annotations__
        wrapper.__qualname__ = func.__qualname__
        
        return wrapper
