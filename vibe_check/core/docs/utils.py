"""
Documentation Utilities
==================

This module provides utility functions for generating standardized documentation
for the Vibe Check codebase. These utilities help ensure consistent documentation
across the project.
"""

import inspect
from typing import Any, Dict, List, Optional, Type, Union

from .templates import (
    MODULE_TEMPLATE,
    CLASS_TEMPLATE,
    FUNCTION_TEMPLATE,
    ASYNC_FUNCTION_TEMPLATE,
    PROPERTY_TEMPLATE,
    EXCEPTION_TEMPLATE,
    FILE_HEADER_TEMPLATE,
)


def generate_module_docstring(
    module_name: str,
    module_description: str
) -> str:
    """
    Generate a standardized module docstring.
    
    Args:
        module_name: Name of the module
        module_description: Description of the module
        
    Returns:
        Formatted module docstring
    """
    module_underline = '=' * len(module_name)
    
    return MODULE_TEMPLATE.format(
        module_name=module_name,
        module_underline=module_underline,
        module_description=module_description
    )


def generate_class_docstring(
    class_description: str,
    attributes: Optional[Dict[str, str]] = None,
    methods: Optional[Dict[str, str]] = None,
    examples: Optional[List[str]] = None,
    notes: Optional[List[str]] = None
) -> str:
    """
    Generate a standardized class docstring.
    
    Args:
        class_description: Description of the class
        attributes: Dictionary mapping attribute names to descriptions
        methods: Dictionary mapping method names to descriptions
        examples: List of example code snippets
        notes: List of notes about the class
        
    Returns:
        Formatted class docstring
    """
    # Format attributes section
    attributes_section = ""
    if attributes:
        attributes_section = "Attributes:\n"
        for name, desc in attributes.items():
            attributes_section += f"    {name}: {desc}\n"
    
    # Format methods section
    methods_section = ""
    if methods:
        methods_section = "Methods:\n"
        for name, desc in methods.items():
            methods_section += f"    {name}: {desc}\n"
    
    # Format examples section
    examples_section = ""
    if examples:
        examples_section = "Examples:\n"
        for example in examples:
            examples_section += f"    {example}\n"
    
    # Format notes section
    notes_section = ""
    if notes:
        notes_section = "Notes:\n"
        for note in notes:
            notes_section += f"    {note}\n"
    
    return CLASS_TEMPLATE.format(
        class_description=class_description,
        attributes_section=attributes_section,
        methods_section=methods_section,
        examples_section=examples_section,
        notes_section=notes_section
    )


def generate_function_docstring(
    function_description: str,
    args: Optional[Dict[str, str]] = None,
    returns: Optional[str] = None,
    raises: Optional[Dict[str, str]] = None,
    performance: Optional[str] = None,
    examples: Optional[List[str]] = None,
    notes: Optional[List[str]] = None
) -> str:
    """
    Generate a standardized function docstring.
    
    Args:
        function_description: Description of the function
        args: Dictionary mapping argument names to descriptions
        returns: Description of the return value
        raises: Dictionary mapping exception types to descriptions
        performance: Description of performance characteristics
        examples: List of example code snippets
        notes: List of notes about the function
        
    Returns:
        Formatted function docstring
    """
    # Format args section
    args_section = ""
    if args:
        for name, desc in args.items():
            args_section += f"    {name}: {desc}\n"
    
    # Format returns section
    returns_section = returns or "None"
    
    # Format raises section
    raises_section = ""
    if raises:
        for exc_type, desc in raises.items():
            raises_section += f"    {exc_type}: {desc}\n"
    else:
        raises_section = "    None"
    
    # Format performance section
    performance_section = ""
    if performance:
        performance_section = f"Performance:\n    {performance}\n"
    
    # Format examples section
    examples_section = ""
    if examples:
        examples_section = "Examples:\n"
        for example in examples:
            examples_section += f"    {example}\n"
    
    # Format notes section
    notes_section = ""
    if notes:
        notes_section = "Notes:\n"
        for note in notes:
            notes_section += f"    {note}\n"
    
    return FUNCTION_TEMPLATE.format(
        function_description=function_description,
        args_section=args_section,
        returns_section=returns_section,
        raises_section=raises_section,
        performance_section=performance_section,
        examples_section=examples_section,
        notes_section=notes_section
    )


def generate_async_function_docstring(
    function_description: str,
    args: Optional[Dict[str, str]] = None,
    returns: Optional[str] = None,
    raises: Optional[Dict[str, str]] = None,
    performance: Optional[str] = None,
    examples: Optional[List[str]] = None,
    notes: Optional[List[str]] = None
) -> str:
    """
    Generate a standardized async function docstring.
    
    Args:
        function_description: Description of the function
        args: Dictionary mapping argument names to descriptions
        returns: Description of the return value
        raises: Dictionary mapping exception types to descriptions
        performance: Description of performance characteristics
        examples: List of example code snippets
        notes: List of notes about the function
        
    Returns:
        Formatted async function docstring
    """
    # Format args section
    args_section = ""
    if args:
        for name, desc in args.items():
            args_section += f"    {name}: {desc}\n"
    
    # Format returns section
    returns_section = returns or "None"
    
    # Format raises section
    raises_section = ""
    if raises:
        for exc_type, desc in raises.items():
            raises_section += f"    {exc_type}: {desc}\n"
    else:
        raises_section = "    None"
    
    # Format performance section
    performance_section = ""
    if performance:
        performance_section = f"Performance:\n    {performance}\n"
    
    # Format examples section
    examples_section = ""
    if examples:
        examples_section = "Examples:\n"
        for example in examples:
            examples_section += f"    {example}\n"
    
    # Format notes section
    notes_section = ""
    if notes:
        notes_section = "Notes:\n"
        for note in notes:
            notes_section += f"    {note}\n"
    
    return ASYNC_FUNCTION_TEMPLATE.format(
        function_description=function_description,
        args_section=args_section,
        returns_section=returns_section,
        raises_section=raises_section,
        performance_section=performance_section,
        examples_section=examples_section,
        notes_section=notes_section
    )


def generate_property_docstring(
    property_description: str,
    returns: str,
    notes: Optional[List[str]] = None
) -> str:
    """
    Generate a standardized property docstring.
    
    Args:
        property_description: Description of the property
        returns: Description of the property value
        notes: List of notes about the property
        
    Returns:
        Formatted property docstring
    """
    # Format notes section
    notes_section = ""
    if notes:
        notes_section = "Notes:\n"
        for note in notes:
            notes_section += f"    {note}\n"
    
    return PROPERTY_TEMPLATE.format(
        property_description=property_description,
        returns_section=returns,
        notes_section=notes_section
    )


def generate_exception_docstring(
    exception_description: str,
    args: Optional[Dict[str, str]] = None,
    attributes: Optional[Dict[str, str]] = None
) -> str:
    """
    Generate a standardized exception docstring.
    
    Args:
        exception_description: Description of the exception
        args: Dictionary mapping constructor argument names to descriptions
        attributes: Dictionary mapping attribute names to descriptions
        
    Returns:
        Formatted exception docstring
    """
    # Format args section
    args_section = ""
    if args:
        for name, desc in args.items():
            args_section += f"    {name}: {desc}\n"
    
    # Format attributes section
    attributes_section = ""
    if attributes:
        for name, desc in attributes.items():
            attributes_section += f"    {name}: {desc}\n"
    
    return EXCEPTION_TEMPLATE.format(
        exception_description=exception_description,
        args_section=args_section,
        attributes_section=attributes_section
    )


def generate_file_header(
    file_name: str,
    file_description: str,
    file_path: str,
    purpose: str,
    related_files: Optional[List[str]] = None,
    dependencies: Optional[List[str]] = None
) -> str:
    """
    Generate a standardized file header.
    
    Args:
        file_name: Name of the file
        file_description: Description of the file
        file_path: Path to the file
        purpose: Purpose of the file
        related_files: List of related files
        dependencies: List of dependencies
        
    Returns:
        Formatted file header
    """
    file_underline = '=' * len(file_name)
    
    # Format related files
    related_files_str = ", ".join(related_files) if related_files else "None"
    
    # Format dependencies
    dependencies_str = ", ".join(dependencies) if dependencies else "None"
    
    return FILE_HEADER_TEMPLATE.format(
        file_name=file_name,
        file_underline=file_underline,
        file_description=file_description,
        file_path=file_path,
        purpose=purpose,
        related_files=related_files_str,
        dependencies=dependencies_str
    )
