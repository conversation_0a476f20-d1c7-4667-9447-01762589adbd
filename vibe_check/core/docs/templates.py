"""
Documentation Templates
==================

This module provides standardized documentation templates for the Vibe Check codebase.
These templates follow the Google style guide and include all required sections.
"""

# Module docstring template
MODULE_TEMPLATE = '''"""
{module_name}
{module_underline}

{module_description}
"""'''

# Class docstring template
CLASS_TEMPLATE = '''"""
{class_description}

{attributes_section}
{methods_section}
{examples_section}
{notes_section}
"""'''

# Function docstring template
FUNCTION_TEMPLATE = '''"""
{function_description}

Args:
{args_section}

Returns:
{returns_section}

Raises:
{raises_section}

{performance_section}
{examples_section}
{notes_section}
"""'''

# Async function docstring template
ASYNC_FUNCTION_TEMPLATE = '''"""
{function_description}

Args:
{args_section}

Returns:
{returns_section}

Raises:
{raises_section}

{performance_section}
{examples_section}
{notes_section}

This is an asynchronous function that should be awaited.
"""'''

# Property docstring template
PROPERTY_TEMPLATE = '''"""
{property_description}

Returns:
{returns_section}

{notes_section}
"""'''

# Exception docstring template
EXCEPTION_TEMPLATE = '''"""
{exception_description}

Args:
{args_section}

Attributes:
{attributes_section}
"""'''

# File header template
FILE_HEADER_TEMPLATE = '''"""
{file_name}
{file_underline}

{file_description}

File: {file_path}
Purpose: {purpose}
Related Files: {related_files}
Dependencies: {dependencies}
"""'''
