"""
Message Bridge Module
==================

This module defines the MessageBridge class, which translates between pipeline
data flow and actor messages. It enables communication between the pipeline-based
components and the actor-based components during the transition period.
"""

from typing import Any, Dict, Optional, Union

from ..actor_system.context_wave import ContextWave
from ..actor_system.message import Message, MessageType


class MessageBridge:
    """
    Bridge for translating between pipeline data flow and actor messages.
    
    This enables communication between pipeline-based components and
    actor-based components during the transition period.
    """
    
    @staticmethod
    def pipeline_data_to_message(data: Dict[str, Any], message_type: MessageType,
                               context: Optional[ContextWave] = None) -> Message:
        """
        Convert pipeline data to an actor message.
        
        Args:
            data: Pipeline data dictionary
            message_type: Type of message to create
            context: Optional context to include in the message
            
        Returns:
            Actor message
        """
        # Create a new context if none provided
        if context is None:
            context = ContextWave()
        
        # Extract metadata from pipeline data if available
        if "metadata" in data:
            for key, value in data["metadata"].items():
                context.metadata[key] = value
            
            # Remove metadata from payload to avoid duplication
            payload = {k: v for k, v in data.items() if k != "metadata"}
        else:
            payload = data
        
        # Create and return the message
        return Message(message_type, payload, context)
    
    @staticmethod
    def message_to_pipeline_data(message: Message) -> Dict[str, Any]:
        """
        Convert an actor message to pipeline data.
        
        Args:
            message: Actor message
            
        Returns:
            Pipeline data dictionary
        """
        # Start with the message payload
        data = message.payload.copy()
        
        # Add metadata from context
        if message.context is not None:
            data["metadata"] = message.context.metadata.copy()
        else:
            data["metadata"] = {}
        
        # Add message type for reference
        data["message_type"] = message.type.name
        
        return data
    
    @staticmethod
    def adapt_pipeline_result(result: Any, message_type: MessageType,
                           context: Optional[ContextWave] = None) -> Message:
        """
        Adapt a pipeline result to an actor message.
        
        Args:
            result: Result from pipeline stage
            message_type: Type of message to create
            context: Optional context to include in the message
            
        Returns:
            Actor message
        """
        # Create a new context if none provided
        if context is None:
            context = ContextWave()
        
        # Convert result to payload based on type
        if isinstance(result, dict):
            payload = result
        else:
            payload = {"result": result}
        
        # Create and return the message
        return Message(message_type, payload, context)
    
    @staticmethod
    def adapt_message_to_pipeline_input(message: Message) -> Dict[str, Any]:
        """
        Adapt an actor message to pipeline input.
        
        Args:
            message: Actor message
            
        Returns:
            Pipeline input dictionary
        """
        # Start with the message payload
        pipeline_input = message.payload.copy()
        
        # Add context as configuration
        if message.context is not None:
            pipeline_input["config"] = {
                "metadata": message.context.metadata,
                "configuration": message.context.configuration,
                "adaptive_params": message.context.adaptive_params
            }
        else:
            pipeline_input["config"] = {
                "metadata": {},
                "configuration": {},
                "adaptive_params": {}
            }
        
        return pipeline_input
