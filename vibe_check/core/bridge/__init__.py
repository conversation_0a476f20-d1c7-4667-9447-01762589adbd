"""
Vibe Check Bridge Module
===============

This module provides bridge components for integrating the CAW actor system
with the existing pipeline architecture. It enables a gradual transition from
the pipeline-based approach to the actor-based approach while maintaining
functionality throughout the process.

The bridge components include:
1. ActorBridge - Wraps existing pipeline stages as actors
2. MessageBridge - Translates between pipeline data flow and actor messages
3. ContextBridge - Converts shared state to propagating context
4. ToolBridge - Integrates analysis tools with the actor system
5. ToolSelector - Selects appropriate tools for a file
6. ToolConfigAdapter - Adapts tool configuration based on context
7. ToolExecutor - Executes tools on files

These components implement the strategy for a gradual, incremental transition
to the CAW paradigm.
"""

from .actor_bridge import ActorBridge
from .message_bridge import Message<PERSON>ridge
from .context_bridge import Context<PERSON><PERSON>
from .feature_toggle import FeatureToggle
from .tool_bridge import ToolBridge
from .tool_selector import ToolSelector
from .tool_config_adapter import ToolConfigAdapter
from .tool_executor import ToolExecutor

__all__ = [
    'ActorBridge',
    'MessageBridge',
    'ContextBridge',
    'FeatureToggle',
    'Tool<PERSON><PERSON>',
    'ToolSelector',
    'ToolConfigAdapter',
    'ToolExecutor',
]
