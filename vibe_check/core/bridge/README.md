# Vibe Check Bridge Module

This module provides bridge components for integrating the CAW actor system with the existing pipeline architecture. It enables a gradual transition from the pipeline-based approach to the actor-based approach while maintaining functionality throughout the process.

## Overview

The bridge module implements the strategy for a gradual, incremental transition to the CAW paradigm. It provides the following components:

1. **ActorBridge** - Wraps existing pipeline stages as actors
2. **MessageBridge** - Translates between pipeline data flow and actor messages
3. **ContextBridge** - Converts shared state to propagating context
4. **FeatureToggle** - Enables/disables CAW features for gradual adoption

## Components

### ActorBridge

The `ActorBridge` class wraps existing pipeline stages as actors, allowing them to participate in the actor system without requiring a complete rewrite. It implements the adapter pattern to convert pipeline stages to actors that can participate in the CAW choreography system.

Example usage:

```python
# Create a bridge actor for a pipeline stage
structure_bridge = await ActorBridge.create_from_pipeline_stage(
    "structure", structure_analyzer,
    MessageType.INIT_ANALYSIS, MessageType.FILE_METADATA
)

# Register the bridge actor with the project actor
project_actor.register_actor(structure_bridge.actor_id, structure_bridge)
structure_bridge.register_actor(project_actor.actor_id, project_actor)
```

### MessageBridge

The `MessageBridge` class translates between pipeline data flow and actor messages, enabling communication between pipeline-based components and actor-based components during the transition period.

Example usage:

```python
# Convert pipeline data to an actor message
message = MessageBridge.pipeline_data_to_message(
    pipeline_data, MessageType.FILE_METADATA
)

# Convert an actor message to pipeline data
pipeline_data = MessageBridge.message_to_pipeline_data(message)
```

### ContextBridge

The `ContextBridge` class converts between shared state in the pipeline architecture and propagating context in the actor architecture, enabling contextual information to flow between the two architectural styles during the transition period.

Example usage:

```python
# Convert actor context to pipeline configuration
config = context_bridge.context_to_config(context)

# Convert pipeline configuration to actor context
context = context_bridge.config_to_context(config)
```

### FeatureToggle

The `FeatureToggle` class and related functions enable or disable the actor-based architecture, allowing for a gradual transition from the pipeline-based architecture to the actor-based architecture.

Example usage:

```python
# Enable the actor system
set_feature_toggle(FeatureToggle.ACTOR_SYSTEM, True)

# Check if the actor system is enabled
if is_actor_mode_enabled():
    # Use actor system
else:
    # Use pipeline system
```

## Integration Strategy

The bridge module enables a phased integration strategy:

1. **Phase 1: Bridge Infrastructure** - Set up bridge components and feature toggles
2. **Phase 2: Selective Component Replacement** - Replace individual pipeline components with actors
3. **Phase 3: Enhanced Context Adaptation** - Implement full context propagation
4. **Phase 4: Complete Transition** - Remove bridge components and use pure actor system

This approach allows for a gradual transition while maintaining functionality throughout the process.

## Configuration

The bridge module can be configured through the following mechanisms:

1. **Command-line arguments** - Use the `--use-actor-system`, `--use-hybrid-mode`, etc. options
2. **Configuration file** - Set the `use_actor_system`, `use_hybrid_mode`, etc. options in the configuration file
3. **Environment variables** - Set the `VIBE_CHECK_ENABLE_ACTOR_SYSTEM`, `VIBE_CHECK_ENABLE_CONTEXT_PROPAGATION`, etc. environment variables

These configuration options allow for fine-grained control over the transition process.
