"""
Context Bridge Module
==================

This module defines the ContextBridge class, which converts between shared state
in the pipeline architecture and propagating context in the actor architecture.
It enables contextual information to flow between the two architectural styles
during the transition period.
"""

from typing import Any, Dict, Optional

from ..actor_system.context_wave import ContextWave


class ContextBridge:
    """
    Bridge for converting between pipeline shared state and actor context.
    
    This enables contextual information to flow between pipeline-based components
    and actor-based components during the transition period.
    """
    
    def context_to_config(self, context: ContextWave) -> Dict[str, Any]:
        """
        Convert actor context to pipeline configuration.
        
        Args:
            context: Actor context wave
            
        Returns:
            Pipeline configuration dictionary
        """
        # Create a configuration dictionary from context
        config = {
            # Include metadata as top-level keys for backward compatibility
            **context.metadata,
            
            # Include configuration as a nested dictionary
            "config": context.configuration,
            
            # Include adaptive parameters
            "adaptive_params": context.adaptive_params,
            
            # Include history for debugging
            "history": context.history
        }
        
        return config
    
    def config_to_context(self, config: Dict[str, Any]) -> ContextWave:
        """
        Convert pipeline configuration to actor context.
        
        Args:
            config: Pipeline configuration dictionary
            
        Returns:
            Actor context wave
        """
        # Create a new context
        context = ContextWave()
        
        # Extract nested configuration if present
        nested_config = config.get("config", {})
        if isinstance(nested_config, dict):
            context.configuration = nested_config
        
        # Extract adaptive parameters if present
        adaptive_params = config.get("adaptive_params", {})
        if isinstance(adaptive_params, dict):
            context.adaptive_params = adaptive_params
        
        # Extract history if present
        history = config.get("history", [])
        if isinstance(history, list):
            context.history = history
        
        # Add remaining top-level keys as metadata
        # (excluding keys we've already processed)
        excluded_keys = {"config", "adaptive_params", "history"}
        for key, value in config.items():
            if key not in excluded_keys:
                context.metadata[key] = value
        
        return context
    
    def merge_pipeline_state(self, context: ContextWave, state: Dict[str, Any]) -> ContextWave:
        """
        Merge pipeline shared state into actor context.
        
        Args:
            context: Actor context wave
            state: Pipeline shared state
            
        Returns:
            Updated actor context wave
        """
        # Create a new context based on the original
        new_context = context.propagate()
        
        # Extract and merge metadata
        if "metadata" in state:
            for key, value in state["metadata"].items():
                new_context.metadata[key] = value
        
        # Extract and merge configuration
        if "config" in state:
            new_context.configuration.update(state["config"])
        
        # Extract and merge adaptive parameters
        if "adaptive_params" in state:
            for key, value in state["adaptive_params"].items():
                new_context._adapt_parameters({key: value})
        
        # Add remaining top-level keys as metadata
        # (excluding keys we've already processed)
        excluded_keys = {"metadata", "config", "adaptive_params"}
        for key, value in state.items():
            if key not in excluded_keys:
                new_context.metadata[key] = value
        
        return new_context
    
    def extract_pipeline_state(self, context: ContextWave) -> Dict[str, Any]:
        """
        Extract pipeline shared state from actor context.
        
        Args:
            context: Actor context wave
            
        Returns:
            Pipeline shared state dictionary
        """
        # Create a state dictionary from context
        state = {
            "metadata": context.metadata.copy(),
            "config": context.configuration.copy(),
            "adaptive_params": context.adaptive_params.copy()
        }
        
        return state
