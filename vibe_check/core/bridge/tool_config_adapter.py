"""
Tool Config Adapter Module
=====================

This module provides the ToolConfigAdapter class, which is responsible for
adapting tool configuration based on context. It implements the CAW principle
of contextual adaptation.
"""

import logging
from typing import Any, Dict, Optional

from ..actor_system.context_wave import ContextWave

logger = logging.getLogger("vibe_check_tool_config_adapter")


class ToolConfigAdapter:
    """
    Adapts tool configuration based on context.
    
    This class implements the CAW principle of contextual adaptation.
    """
    
    def adapt_tool_config(self, 
                         tool_name: str, 
                         base_config: Dict[str, Any],
                         context: ContextWave) -> Dict[str, Any]:
        """
        Adapt tool configuration based on context.
        
        This implements the CAW principle of contextual adaptation.
        
        Args:
            tool_name: Name of the tool
            base_config: Base configuration
            context: Context wave with metadata
            
        Returns:
            Adapted configuration
        """
        # Start with a copy of the base configuration
        config = base_config.copy() if base_config else {}
        
        # Adapt based on file complexity if available
        if "file_complexity" in context.metadata:
            complexity = context.metadata["file_complexity"]
            
            if tool_name == "ruff":
                # For high complexity files, use stricter settings
                if complexity > 0.7:
                    config["select"] = config.get("select", []) + ["E", "F", "C", "D"]
                    config["ignore"] = [i for i in config.get("ignore", []) if i not in ["E501"]]
                # For low complexity files, use more relaxed settings
                elif complexity < 0.3:
                    config["select"] = config.get("select", []) + ["E", "F"]
                    config["ignore"] = config.get("ignore", []) + ["E501", "D"]
                    
            elif tool_name == "mypy":
                # For high complexity files, use stricter type checking
                if complexity > 0.7:
                    config["disallow_untyped_defs"] = True
                    config["disallow_incomplete_defs"] = True
                # For low complexity files, use more relaxed type checking
                elif complexity < 0.3:
                    config["disallow_untyped_defs"] = False
                    config["disallow_incomplete_defs"] = False
                    
            elif tool_name == "complexity":
                # For high complexity files, lower the threshold for warnings
                if complexity > 0.7:
                    config["threshold"] = config.get("threshold", 10) - 2
                # For low complexity files, raise the threshold
                elif complexity < 0.3:
                    config["threshold"] = config.get("threshold", 10) + 2
                    
            elif tool_name == "bandit":
                # For high complexity files, use more security checks
                if complexity > 0.7:
                    config["confidence"] = "low"  # Detect more potential issues
                # For low complexity files, focus only on high confidence issues
                elif complexity < 0.3:
                    config["confidence"] = "high"
                    
        # Adapt based on file type if available
        if "file_type" in context.metadata:
            file_type = context.metadata["file_type"]
            
            if file_type.lower() == "test" and tool_name == "ruff":
                # For test files, ignore some rules
                config["ignore"] = config.get("ignore", []) + ["D100", "D101", "D102", "D103"]
                
            if file_type.lower() == "init" and tool_name == "mypy":
                # For __init__.py files, allow untyped imports
                config["follow_imports"] = "skip"
                
        # Adapt based on project priorities if available
        if "project_priorities" in context.metadata:
            priorities = context.metadata["project_priorities"]
            
            if "security" in priorities and priorities["security"] > 0.7:
                # For security-focused projects, prioritize security tools
                if tool_name == "bandit":
                    config["severity"] = "low"  # Detect more potential issues
                    
            if "quality" in priorities and priorities["quality"] > 0.7:
                # For quality-focused projects, be stricter with quality tools
                if tool_name == "ruff":
                    config["select"] = config.get("select", []) + ["D", "N", "U"]
                    
        return config
