"""
Actor Bridge Module
================

This module defines the ActorBridge class, which wraps existing pipeline stages
as actors. This enables a gradual transition from the pipeline-based architecture
to the actor-based architecture while maintaining functionality.

The ActorBridge implements the adapter pattern to convert pipeline stages to
actors that can participate in the CAW choreography system.
"""

import asyncio
import logging
import functools
from typing import Any, Callable, Dict, Optional, Type, TypeVar, Coroutine

from ..actor_system.actor import Actor
from ..actor_system.context_wave import ContextWave
from ..actor_system.message import Message, MessageType
from .context_bridge import ContextBridge

# Define a TypeVar for the result of a function
T = TypeVar('T')

# Create a custom to_thread function for Python versions that don't have asyncio.to_thread
async def custom_to_thread(func: Callable[..., T], *args: Any, **kwargs: Any) -> T:
    """
    Run a function in a separate thread and return the result.
    This is a replacement for asyncio.to_thread for Python versions that don't support it.
    """
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(
        None, functools.partial(func, *args, **kwargs)
    )

logger = logging.getLogger("pat_bridge")


class Actor<PERSON>ridge(Actor):
    """
    Bridge adapter that wraps a pipeline stage as an actor.
    
    This allows existing pipeline stages to participate in the actor system
    without requiring a complete rewrite.
    """
    
    def __init__(self, actor_id: str, pipeline_stage: Any, 
                 input_message_type: MessageType, output_message_type: MessageType,
                 result_handler: Optional[Callable[[Any, ContextWave], Dict[str, Any]]] = None):
        """
        Initialize the actor bridge.
        
        Args:
            actor_id: Unique ID for this actor
            pipeline_stage: The pipeline stage to wrap
            input_message_type: The message type this actor responds to
            output_message_type: The message type for results from this actor
            result_handler: Optional function to convert pipeline results to actor messages
        """
        super().__init__(actor_id)
        self.pipeline_stage = pipeline_stage
        self.input_message_type = input_message_type
        self.output_message_type = output_message_type
        self.result_handler = result_handler or self._default_result_handler
        self.context_bridge = ContextBridge()
        
        # Register the handler for the input message type
        self._register_handler(input_message_type)
    
    def _register_handler(self, message_type: MessageType) -> None:
        """
        Register a handler for the specified message type.
        
        Args:
            message_type: The message type to handle
        """
        handler_name = f"handle_{message_type.name.lower()}"
        if not hasattr(self, handler_name):
            setattr(self, handler_name, self._handle_pipeline_stage)
    
    async def _handle_pipeline_stage(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle messages by executing the pipeline stage.
        
        Args:
            payload: Message payload
            context: Message context
        """
        try:
            # Convert context to pipeline configuration
            pipeline_config = self.context_bridge.context_to_config(context)
            
            # Execute the pipeline stage
            if hasattr(self.pipeline_stage, 'process_async') and callable(self.pipeline_stage.process_async):
                # Use async version if available
                result = await self.pipeline_stage.process_async(payload, pipeline_config)
            elif hasattr(self.pipeline_stage, 'process') and callable(self.pipeline_stage.process):
                # Use sync version and run in executor
                result = await custom_to_thread(self.pipeline_stage.process, payload, pipeline_config)
            else:
                # Assume the pipeline stage is a callable
                result = await custom_to_thread(self.pipeline_stage, payload, pipeline_config)
            
            # Convert the result to a message payload
            result_payload = self.result_handler(result, context)
            
            # Send the result to the next actor
            sender_id = context.metadata.get("sender_id")
            if sender_id and sender_id in self._known_actors:
                await self.send(sender_id, self.output_message_type, result_payload, context)
        except Exception as e:
            logger.error(f"Error in pipeline stage {self.actor_id}: {e}")
            # Send an error message back to the sender
            sender_id = context.metadata.get("sender_id")
            if sender_id and sender_id in self._known_actors:
                error_payload = {
                    "error": str(e),
                    "stage": self.actor_id,
                    "input": payload
                }
                await self.send(sender_id, MessageType.ERROR, error_payload, context)
    
    def _default_result_handler(self, result: Any, context: ContextWave) -> Dict[str, Any]:
        """
        Default handler for converting pipeline results to actor message payloads.
        
        Args:
            result: The result from the pipeline stage
            context: The message context
            
        Returns:
            Dictionary payload for the output message
        """
        if isinstance(result, dict):
            return result
        else:
            return {"result": result}
    
    @classmethod
    async def create_from_pipeline_stage(cls, stage_name: str, pipeline_stage: Any,
                                      input_type: MessageType, output_type: MessageType,
                                      result_handler: Optional[Callable] = None) -> 'ActorBridge':
        """
        Create and start an ActorBridge from a pipeline stage.
        
        Args:
            stage_name: Name of the pipeline stage
            pipeline_stage: The pipeline stage to wrap
            input_type: Input message type
            output_type: Output message type
            result_handler: Optional function to convert pipeline results to actor messages
            
        Returns:
            Started ActorBridge instance
        """
        actor_id = f"pipeline-{stage_name}"
        bridge = cls(actor_id, pipeline_stage, input_type, output_type, result_handler)
        await bridge.start()
        return bridge
