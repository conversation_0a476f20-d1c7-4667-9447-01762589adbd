"""
Feature Toggle Module
==================

This module defines the FeatureToggle class and related functions for enabling
or disabling the actor-based architecture. This allows for a gradual transition
from the pipeline-based architecture to the actor-based architecture.
"""

import os
from enum import Enum, auto
from typing import Dict, Optional, Union


class FeatureToggle(Enum):
    """Feature toggles for controlling architectural transitions."""

    # Toggle for context propagation
    CONTEXT_PROPAGATION = auto()

    # Toggle for parallel processing
    PARALLEL_PROCESSING = auto()

    # Toggle for adaptive configuration
    ADAPTIVE_CONFIGURATION = auto()

    # Toggle for visualization enhancements
    VISUALIZATION_ENHANCEMENTS = auto()


# Global feature toggle state
_feature_toggles: Dict[FeatureToggle, bool] = {
    FeatureToggle.CONTEXT_PROPAGATION: False,
    FeatureToggle.PARALLEL_PROCESSING: False,
    FeatureToggle.ADAPTIVE_CONFIGURATION: False,
    FeatureToggle.VISUALIZATION_ENHANCEMENTS: False,
}


def set_feature_toggle(feature: FeatureToggle, enabled: bool) -> None:
    """
    Set the state of a feature toggle.

    Args:
        feature: The feature toggle to set
        enabled: Whether the feature should be enabled
    """
    _feature_toggles[feature] = enabled


def is_feature_enabled(feature: FeatureToggle) -> bool:
    """
    Check if a feature is enabled.

    Args:
        feature: The feature toggle to check

    Returns:
        True if the feature is enabled, False otherwise
    """
    # Check environment variable override first
    env_var = f"VIBE_CHECK_ENABLE_{feature.name}"
    env_value = os.environ.get(env_var)

    if env_value is not None:
        return env_value.lower() in ("1", "true", "yes", "on")

    # Fall back to the toggle state
    return _feature_toggles.get(feature, False)


def configure_from_dict(config: Dict[str, Union[bool, str]]) -> None:
    """
    Configure feature toggles from a dictionary.

    Args:
        config: Dictionary with feature toggle configuration
    """
    # Map config keys to feature toggles
    key_to_toggle = {
        "use_context_propagation": FeatureToggle.CONTEXT_PROPAGATION,
        "use_parallel_processing": FeatureToggle.PARALLEL_PROCESSING,
        "use_adaptive_configuration": FeatureToggle.ADAPTIVE_CONFIGURATION,
        "use_visualization_enhancements": FeatureToggle.VISUALIZATION_ENHANCEMENTS,
    }

    # Set toggles based on config
    for key, toggle in key_to_toggle.items():
        if key in config:
            value = config[key]
            if isinstance(value, bool):
                set_feature_toggle(toggle, value)
            elif isinstance(value, str):
                set_feature_toggle(toggle, value.lower() in ("1", "true", "yes", "on"))


def enable_all_features() -> None:
    """Enable all feature toggles."""
    for feature in FeatureToggle:
        set_feature_toggle(feature, True)


def disable_all_features() -> None:
    """Disable all feature toggles."""
    for feature in FeatureToggle:
        set_feature_toggle(feature, False)


def is_actor_mode_enabled() -> bool:
    """
    Check if the actor-based architecture is enabled.

    This is a convenience function that checks if the CONTEXT_PROPAGATION
    feature is enabled, which is a prerequisite for the actor-based architecture.

    Returns:
        True if the actor-based architecture is enabled, False otherwise
    """
    return is_feature_enabled(FeatureToggle.CONTEXT_PROPAGATION)
