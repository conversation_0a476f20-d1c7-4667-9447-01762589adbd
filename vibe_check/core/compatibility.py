"""
Vibe Check Compatibility Module
=====================

This module provides compatibility functions for running Vibe Check in either pipeline
or actor mode. It implements the strategy for a gradual, incremental transition to the CAW paradigm.
"""

import asyncio
import logging
from pathlib import Path
from typing import Any, Dict, Optional

from .actor_system.actors import (
    ProjectActor,
    ToolActorFactory,
    ReportActor,
    VisualizationActor
)
from .models import ProjectMetrics
from .models.progress_tracker import ProgressTracker

# Import pipeline components for compatibility
# Legacy pipeline components are no longer supported
PIPELINE_AVAILABLE = False

logger = logging.getLogger("vibe_check_compatibility")


async def run_analysis_actor(
    project_path: Path,
    output_dir: Path,
    config: Dict[str, Any],
    progress: Optional[ProgressTracker] = None
) -> ProjectMetrics:
    """
    Run the project analysis using the actor architecture.

    Args:
        project_path: Path to the project to analyze
        output_dir: Directory to store output files
        config: Configuration dictionary
        progress: Optional progress tracker

    Returns:
        ProjectMetrics object with analysis results
    """
    # Initialize the metrics
    metrics = ProjectMetrics()

    # Initialize the project actor
    project_actor = ProjectActor("project", project_path, metrics, progress, config)
    await project_actor.start()

    # Create and register report actor
    report_actor = ReportActor("report", output_dir)
    await report_actor.start()
    project_actor.register_actor("report", report_actor) # Makes project_actor aware of report_actor
    report_actor.register_actor("project", project_actor) # Makes report_actor aware of project_actor
    project_actor.set_report_actor(report_actor.actor_id) # Use setter method

    # Create and register visualization actor
    viz_actor = VisualizationActor("visualization", output_dir)
    await viz_actor.start()
    report_actor.register_actor("visualization", viz_actor)
    viz_actor.register_actor("report", report_actor)
    report_actor.set_visualization_actor(viz_actor.actor_id)

    # Create and register tool actors
    tool_configs = config.get("tools", {})
    tool_actors = await ToolActorFactory.create_and_register_tools(project_actor, tool_configs)

    # Start the analysis
    await project_actor.start_analysis()

    # Wait for all active tasks
    # In a real implementation, we would wait for a done signal
    # For now, we'll just wait a bit to let things finish
    remaining_time = 300  # 5 minutes max
    check_interval = 1  # Check every second

    while remaining_time > 0:
        # Check if the analysis is complete
        if not project_actor.active_files:
            logger.info("Analysis complete")
            break

        # Wait a bit
        await asyncio.sleep(check_interval)
        remaining_time -= check_interval

    # Stop all actors
    await project_actor.stop()
    await report_actor.stop()
    await viz_actor.stop()

    for tool_actor in tool_actors.values():
        await tool_actor.stop()

    return metrics


async def run_analysis(
    project_path: Path,
    output_dir: Path,
    config: Dict[str, Any],
    progress: Optional[ProgressTracker] = None
) -> ProjectMetrics:
    """
    Run the project analysis using the appropriate architecture based on configuration.

    Args:
        project_path: Path to the project to analyze
        output_dir: Directory to store output files
        config: Configuration dictionary
        progress: Optional progress tracker

    Returns:
        ProjectMetrics object with analysis results
    """
    # All modes now fall back to actor mode.
    logger.info("Running analysis in actor mode")
    return await run_analysis_actor(project_path, output_dir, config, progress)
