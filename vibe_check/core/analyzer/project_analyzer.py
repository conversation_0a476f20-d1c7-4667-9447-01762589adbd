"""
Project Analyzer Module
====================

This module defines the ProjectAnalyzer class, which coordinates the analysis
of a project using the actor system. It provides a high-level interface for
starting and managing the analysis process.
"""

import asyncio
import logging
import os
from pathlib import Path
from typing import Any, Dict, Optional, Union

from ..actor_system.actor_system import ActorSystem
from ..actor_system.context_wave import ContextWave
from ..actor_system.message import MessageType
from ..models.project_metrics import ProjectMetrics
from ..models.progress_tracker import ProgressTracker
from ..orchestrator import get_orchestrator

logger = logging.getLogger("vibe_check_analyzer")


class ProjectAnalyzer:
    """
    Analyzer for Python projects using the actor system.

    This class provides a high-level interface for analyzing Python projects
    using the actor system. It coordinates the analysis process by:
    1. Setting up the actor system
    2. Configuring the analysis
    3. Starting the analysis
    4. Collecting and returning the results
    """

    def __init__(
        self,
        project_path: Union[str, Path],
        config: Optional[Dict[str, Any]] = None,
        actor_system: Optional[ActorSystem] = None,
        output_dir: Optional[Union[str, Path]] = None,
        progress_tracker: Optional[ProgressTracker] = None
    ):
        """
        Initialize the project analyzer.

        Args:
            project_path: Path to the project to analyze
            config: Optional configuration dictionary
            actor_system: Optional actor system to use
            output_dir: Optional directory to write output files to
            progress_tracker: Optional progress tracker
        """
        self.project_path = Path(project_path)
        self.config = config or {}
        self.actor_system = actor_system or ActorSystem()
        self.output_dir = Path(output_dir) if output_dir else Path("vibe_check_output")
        self.progress_tracker = progress_tracker
        self.orchestrator = get_orchestrator()
        self.analysis_complete = asyncio.Future()
        self.analysis_result = None

        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)

        # Configure the orchestrator
        self.orchestrator.actor_system = self.actor_system
        self.orchestrator.output_dir = self.output_dir
        self.orchestrator.config = self.config

    async def analyze(self) -> ProjectMetrics:
        """
        Analyze the project using the actor system.

        This method:
        1. Starts the actor system if it's not already running
        2. Creates a context wave with the configuration
        3. Sends an analysis request to the orchestrator
        4. Waits for the analysis to complete
        5. Returns the analysis results

        Returns:
            ProjectMetrics object with analysis results
        """
        logger.info(f"Starting analysis of project: {self.project_path}")

        try:
            # Create a context wave with the configuration
            context = ContextWave(
                data_or_metadata={
                    "project_path": str(self.project_path),
                    "output_dir": str(self.output_dir)
                },
                configuration=self.config,
                adaptive_params={
                    "complexity_threshold": self.config.get("complexity_threshold", 10),
                    "max_line_length": self.config.get("max_line_length", 100)
                }
            )

            # Start the analysis
            result = await self.orchestrator.analyze_project(
                project_path=self.project_path,
                context=context.to_dict()
            )

            # Check if the analysis was successful
            if isinstance(result, dict) and "error" in result:
                logger.error(f"Analysis failed: {result['error']}")
                raise RuntimeError(f"Analysis failed: {result['error']}")

            # Get the metrics from the result
            if isinstance(result, dict) and "metrics" in result:
                metrics = result["metrics"]
            else:
                metrics = result

            logger.info("Analysis completed successfully")
            return metrics

        except Exception as e:
            logger.error(f"Error analyzing project: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise

    def _analysis_completed(self, result: Any) -> None:
        """
        Callback for when the analysis is completed.

        Args:
            result: Analysis result
        """
        if not self.analysis_complete.done():
            self.analysis_result = result
            self.analysis_complete.set_result(result)
