"""
Project Analyzer Module
==================

This module provides the ProjectAnalyzer class, which is responsible for
analyzing an entire project. It abstracts the details of project analysis
to be used by both the simple analyzer and the actor-based system.
"""

import logging
import os
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union

from ..models import ProjectMetrics, FileMetrics, DirectoryMetrics
from ..utils.file_utils import find_python_files
from .file_analyzer import FileAnalyzer
from .metrics_aggregator import MetricsAggregator

logger = logging.getLogger("vibe_check_analysis")


class ProjectAnalyzer:
    """
    Analyzes an entire project.
    
    This class abstracts the details of project analysis to be used by
    both the simple analyzer and the actor-based system.
    """
    
    def __init__(self, 
                 project_path: Union[str, Path],
                 config: Optional[Dict[str, Any]] = None,
                 output_dir: Optional[Union[str, Path]] = None):
        """
        Initialize the project analyzer.
        
        Args:
            project_path: Path to the project to analyze
            config: Optional configuration dictionary
            output_dir: Optional directory to write output files to
        """
        self.project_path = Path(project_path).absolute()
        self.config = config or {}
        self.output_dir = Path(output_dir) if output_dir else None
        
        # Create output directory if specified
        if self.output_dir:
            os.makedirs(self.output_dir, exist_ok=True)
            
        # Initialize file analyzer
        self.file_analyzer = FileAnalyzer(
            project_path=self.project_path,
            tools_config=self.config
        )
        
        # Initialize metrics aggregator
        self.metrics_aggregator = MetricsAggregator()
        
    async def analyze_project(self) -> ProjectMetrics:
        """
        Analyze the project.
        
        Returns:
            ProjectMetrics object with analysis results
        """
        logger.info(f"Starting analysis of project: {self.project_path}")
        start_time = time.time()
        
        # Initialize project metrics
        metrics = ProjectMetrics(
            project_path=str(self.project_path),
            files={},
            directories={}
        )
        
        # Find all Python files in the project
        python_files = find_python_files(self.project_path)
        logger.info(f"Found {len(python_files)} Python files")
        
        # Process each file
        for file_path in python_files:
            logger.info(f"Analyzing file: {file_path.relative_to(self.project_path)}")
            
            # Analyze the file
            file_metrics = await self.file_analyzer.analyze_file(file_path)
            
            # Add file metrics to project metrics
            metrics.files[str(file_path.relative_to(self.project_path))] = file_metrics
        
        # Aggregate metrics
        self.metrics_aggregator.aggregate_metrics(metrics)
        
        # Log completion
        end_time = time.time()
        logger.info(f"Analysis completed in {end_time - start_time:.2f} seconds")
        logger.info(f"Total files: {metrics.total_file_count}")
        logger.info(f"Total lines: {metrics.total_line_count}")
        logger.info(f"Average complexity: {metrics.avg_complexity:.2f}")
        logger.info(f"Total issues: {metrics.issue_count}")
        
        return metrics
