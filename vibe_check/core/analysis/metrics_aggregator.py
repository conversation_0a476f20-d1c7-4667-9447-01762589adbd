"""
Metrics Aggregator Module
=====================

This module provides the MetricsAggregator class, which is responsible for
aggregating metrics from individual files into project-level metrics.
It abstracts the details of metrics aggregation to be used by both
the simple analyzer and the actor-based system.
"""

import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union

from ..models import ProjectMetrics, FileMetrics, DirectoryMetrics

logger = logging.getLogger("vibe_check_analysis")


class MetricsAggregator:
    """
    Aggregates metrics from individual files into project-level metrics.

    This class abstracts the details of metrics aggregation to be used by
    both the simple analyzer and the actor-based system.
    """

    def aggregate_metrics(self, metrics: ProjectMetrics) -> None:
        """
        Aggregate metrics from individual files into project-level metrics.

        Args:
            metrics: ProjectMetrics object to update
        """
        # Calculate directory metrics
        self._calculate_directory_metrics(metrics)

        # Calculate project-level metrics
        self._calculate_project_metrics(metrics)

    def _calculate_directory_metrics(self, metrics: ProjectMetrics) -> None:
        """
        Calculate directory metrics from file metrics.

        Args:
            metrics: ProjectMetrics object to update
        """
        # Group files by directory
        directories: Dict[str, Dict[str, Any]] = {}

        for file_path, file_metrics in metrics.files.items():
            # Get directory path
            dir_path = os.path.dirname(file_path)

            # Create directory entry if it doesn't exist
            if dir_path not in directories:
                directories[dir_path] = {
                    'files': [],
                    'total_lines': 0,
                    'max_file_lines': 0,
                    'max_file': '',
                    'total_complexity': 0,
                    'max_complexity': 0,
                    'issue_count': 0
                }

            # Add file to directory
            files_list = directories[dir_path]['files']
            if isinstance(files_list, list):
                files_list.append(file_path)

            # Update total lines
            directories[dir_path]['total_lines'] = int(directories[dir_path]['total_lines']) + file_metrics.lines

            # Update max file lines
            if file_metrics.lines > int(directories[dir_path]['max_file_lines']):
                directories[dir_path]['max_file_lines'] = file_metrics.lines
                directories[dir_path]['max_file'] = file_path

            # Update complexity metrics
            directories[dir_path]['total_complexity'] = int(directories[dir_path]['total_complexity']) + file_metrics.complexity
            if file_metrics.complexity > int(directories[dir_path]['max_complexity']):
                directories[dir_path]['max_complexity'] = file_metrics.complexity

            # Update issue count
            directories[dir_path]['issue_count'] = int(directories[dir_path]['issue_count']) + len(file_metrics.issues)

        # Create DirectoryMetrics objects
        for dir_path, dir_data in directories.items():
            # Get the files list
            files_list = dir_data['files']
            if not isinstance(files_list, list):
                files_list = []

            # Calculate average lines
            total_lines = int(dir_data['total_lines'])
            avg_lines = total_lines / len(files_list) if files_list else 0.0

            # Calculate average complexity
            total_complexity = int(dir_data['total_complexity'])
            avg_complexity = total_complexity / len(files_list) if files_list else 0.0

            # Create DirectoryMetrics object
            dir_metrics = DirectoryMetrics(
                path=dir_path,
                files=files_list,
                total_lines=total_lines,
                avg_lines=avg_lines,
                max_file_lines=int(dir_data['max_file_lines']),
                max_file=str(dir_data['max_file']),
                _avg_complexity=avg_complexity,
                _max_complexity=int(dir_data['max_complexity']),
                _issue_count=int(dir_data['issue_count'])
            )

            # Add to project metrics
            metrics.directories[dir_path] = dir_metrics

    def _calculate_project_metrics(self, metrics: ProjectMetrics) -> None:
        """
        Calculate project-level metrics from file metrics.

        Args:
            metrics: ProjectMetrics object to update
        """
        # Get total file count (don't try to set it, it's a read-only property)
        total_file_count = len(metrics.files)

        # Calculate total line count (using the property)
        # The total_line_count property is already calculated from the files

        # Calculate average complexity
        total_complexity = sum(file_metrics.complexity for file_metrics in metrics.files.values())
        # Store in the complexity_scores dictionary
        for file_path, file_metrics in metrics.files.items():
            metrics.complexity_scores[file_path] = file_metrics.complexity

        # Calculate issues by severity
        severity_counts: Dict[str, int] = {}
        for file_metrics in metrics.files.values():
            for issue in file_metrics.issues:
                severity = issue.get('severity', 'medium')
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
        metrics._issues_by_severity = severity_counts

        # Calculate issues by tool
        tool_counts: Dict[str, int] = {}
        for file_metrics in metrics.files.values():
            for issue in file_metrics.issues:
                tool = issue.get('source', 'unknown')
                tool_counts[tool] = tool_counts.get(tool, 0) + 1
        metrics._issues_by_tool = tool_counts

        # Calculate type coverage
        for file_path, file_metrics in metrics.files.items():
            metrics.type_coverage[file_path] = file_metrics.type_coverage

        # Calculate docstring coverage
        for file_path, file_metrics in metrics.files.items():
            metrics.doc_coverage[file_path] = file_metrics.docstring_coverage
