"""
Analysis Core Package
=================

This package provides the core analysis functionality for Vibe Check,
used by both the simple analyzer and the actor-based system.

It implements a shared analysis core that abstracts the details of
file analysis, tool execution, and result processing.
"""

from .file_analyzer import FileAnalyzer
from .project_analyzer import ProjectAnalyzer
from .tool_executor import ToolExecutor
from .result_processor import ResultProcessor
from .metrics_aggregator import MetricsAggregator

__all__ = [
    'FileAnalyzer',
    'ProjectAnalyzer',
    'ToolExecutor',
    'ResultProcessor',
    'MetricsAggregator',
]
