"""
Actor Connection Module
=======================

This module provides functionality for connecting actors in the Vibe Check system.
It handles the setup of communication paths between different types of actors.
"""

import asyncio
import logging
from typing import Any, Dict, Optional

from ..actor_system import Actor, get_registry
from ..actor_system.actors.project_actor import ProjectActor
from ..actor_system.actors.report_actor import ReportActor
from ..actor_system.actors.visualization_actor import VisualizationActor

logger = logging.getLogger("vibe_check_actor_connection")


class ActorConnectionManager:
    """
    Manages connections between actors in the system.
    
    This class implements the choreography pattern of the CAW architecture,
    where each actor knows who to send messages to but the overall
    flow emerges from their interactions.
    """

    def __init__(self, actors: Dict[str, Actor]):
        """
        Initialize the actor connection manager.

        Args:
            actors: Dictionary of actor IDs to actor instances
        """
        self.actors = actors
        self.project_actor: Optional[ProjectActor] = None
        self.report_actor: Optional[ReportActor] = None
        self.visualization_actor: Optional[VisualizationActor] = None

        # Get references to important actors
        for actor_id, actor in actors.items():
            if isinstance(actor, ProjectActor):
                self.project_actor = actor
            elif isinstance(actor, ReportActor):
                self.report_actor = actor
            elif isinstance(actor, VisualizationActor):
                self.visualization_actor = actor

    def connect_project_with_file_actors(self) -> None:
        """
        Connect the project actor with file actors.
        """
        try:
            # Get the registry
            registry = get_registry()

            # Set up project actor with file actor IDs
            file_actors = registry.get_actors_by_tag("file")
            file_actor_ids = [actor.actor_id for actor in file_actors]

            if self.project_actor is not None and hasattr(self.project_actor, "set_file_actors"):
                self.project_actor.set_file_actors(file_actor_ids)
                logger.info(f"Set {len(file_actor_ids)} file actors for project actor")
            else:
                logger.warning("Project actor does not have set_file_actors method or is None")
        except Exception as e:
            import traceback
            logger.error(f"Error connecting project with file actors: {e}\n{traceback.format_exc()}")

    def connect_file_with_tool_actors(self) -> None:
        """
        Connect file actors with tool actors.
        """
        try:
            # Get the registry
            registry = get_registry()

            # Get tool actors
            tool_actors = registry.get_actors_by_tag("tool")
            tool_actor_ids = [actor.actor_id for actor in tool_actors]

            # Configure file actors with tool actor IDs
            file_actors = registry.get_actors_by_tag("file")
            for file_actor in file_actors:
                # Set tool actors for file actors
                if hasattr(file_actor, "set_tool_actor"):
                    for tool_id in tool_actor_ids:
                        file_actor.set_tool_actor(tool_id)
                        logger.debug(f"Set tool actor {tool_id} for file actor {file_actor.actor_id}")
                else:
                    logger.warning(f"File actor {file_actor.actor_id} does not have set_tool_actor method")

                # Set report actor ID for file actors
                if hasattr(file_actor, "set_report_actor"):
                    file_actor.set_report_actor("report_actor")
                    logger.debug(f"Set report actor for file actor {file_actor.actor_id}")
                else:
                    logger.warning(f"File actor {file_actor.actor_id} does not have set_report_actor method")
        except Exception as e:
            import traceback
            logger.error(f"Error connecting file with tool actors: {e}\n{traceback.format_exc()}")

    def connect_output_actors(self) -> None:
        """
        Connect all actors with report and visualization actors.
        """
        try:
            # Set report and visualization actors for all actors
            for actor_id, actor in self.actors.items():
                # Set report actor ID for all actors that need it
                if hasattr(actor, "set_report_actor"):
                    actor.set_report_actor("report_actor")
                    logger.debug(f"Set report actor for {actor_id}")

                # Set visualization actor ID for actors that need it
                if hasattr(actor, "set_visualization_actor"):
                    actor.set_visualization_actor("visualization_actor")
                    logger.debug(f"Set visualization actor for {actor_id}")
        except Exception as e:
            import traceback
            logger.error(f"Error connecting output actors: {e}\n{traceback.format_exc()}")

    def setup_reactive_streams(self) -> None:
        """
        Set up reactive streams for all actors.
        """
        try:
            # Get the registry
            registry = get_registry()

            # Create tasks for stream subscriptions
            tasks = []

            # Project status stream
            for actor in self.actors.values():
                tasks.append(asyncio.create_task(actor.subscribe("project_status")))

            # Metrics stream
            for actor in self.actors.values():
                tasks.append(asyncio.create_task(actor.subscribe("metrics")))

            # File analysis stream
            for actor in registry.get_actors_by_tag("file"):
                tasks.append(asyncio.create_task(actor.subscribe("file_analysis")))

            # Tool results stream
            for actor in registry.get_actors_by_tag("tool"):
                tasks.append(asyncio.create_task(actor.subscribe("tool_results")))

            # Report stream
            if self.report_actor:
                tasks.append(asyncio.create_task(self.report_actor.subscribe("report")))

            # Visualization stream
            if self.visualization_actor:
                tasks.append(asyncio.create_task(self.visualization_actor.subscribe("visualization")))

            logger.info(f"Created {len(tasks)} stream subscription tasks")
        except Exception as e:
            import traceback
            logger.error(f"Error setting up reactive streams: {e}\n{traceback.format_exc()}")

    def connect_all_actors(self) -> None:
        """
        Connect all actors to establish communication paths.

        Enhanced to use the actor registry for dynamic discovery and communication.
        This sets up the choreography pattern of the CAW architecture,
        where each actor knows who to send messages to but the overall
        flow emerges from their interactions.
        """
        try:
            # Connect project actor with file actors
            self.connect_project_with_file_actors()

            # Connect file actors with tool actors
            self.connect_file_with_tool_actors()

            # Connect all actors with report and visualization actors
            self.connect_output_actors()

            # Set up reactive streams
            self.setup_reactive_streams()

            logger.info("All actors connected successfully")
        except Exception as e:
            import traceback
            logger.error(f"Error connecting actors: {e}\n{traceback.format_exc()}")
            raise
