"""
Recovery Manager Module
==================

This module is responsible for managing recovery from failures in the actor system.
It implements the CAW principle of adaptive execution by providing strategies for
recovering from different types of failures.

This module was extracted from the Orchestrator class to reduce complexity
and improve maintainability.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Set, Tuple, Union

from ..error_handling import E<PERSON><PERSON><PERSON>ana<PERSON>, get_error_manager

logger = logging.getLogger("vibe_check_recovery_manager")


class RecoveryStrategy:
    """
    Base class for recovery strategies.
    
    This class defines the interface for recovery strategies. Subclasses
    should implement the can_handle and recover methods.
    """
    
    def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Check if this strategy can handle the given error.
        
        Args:
            error: The error to check
            context: Context information about the error
            
        Returns:
            True if this strategy can handle the error, False otherwise
        """
        return False
    
    async def recover(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recover from the given error.
        
        Args:
            error: The error to recover from
            context: Context information about the error
            
        Returns:
            Recovery result
        """
        return {"recovered": False, "error": str(error)}


class ActorFailureRecoveryStrategy(RecoveryStrategy):
    """
    Recovery strategy for actor failures.
    
    This strategy attempts to recover from actor failures by restarting
    the failed actor and its dependencies.
    """
    
    def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Check if this strategy can handle the given error.
        
        Args:
            error: The error to check
            context: Context information about the error
            
        Returns:
            True if this strategy can handle the error, False otherwise
        """
        # Check if this is an actor error
        return (
            "actor" in str(error).lower() or
            "actor_id" in context or
            "actor_system" in context
        )
    
    async def recover(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recover from the given error.
        
        Args:
            error: The error to recover from
            context: Context information about the error
            
        Returns:
            Recovery result
        """
        logger.info(f"Attempting to recover from actor failure: {error}")
        
        # Get the actor ID
        actor_id = context.get("actor_id")
        if not actor_id:
            return {"recovered": False, "error": "No actor ID in context"}
        
        # Get the actor system
        actor_system = context.get("actor_system")
        if not actor_system:
            return {"recovered": False, "error": "No actor system in context"}
        
        # Try to restart the actor
        try:
            # Check if the actor exists
            if actor_id not in actor_system.actors:
                return {"recovered": False, "error": f"Actor {actor_id} not found"}
            
            # Get the actor
            actor = actor_system.actors[actor_id]
            
            # Stop the actor
            await actor.stop()
            
            # Restart the actor
            await actor.start()
            
            logger.info(f"Successfully restarted actor {actor_id}")
            
            return {
                "recovered": True,
                "actor_id": actor_id,
                "actions_taken": ["restart_actor"]
            }
        except Exception as e:
            logger.error(f"Failed to restart actor {actor_id}: {e}")
            return {"recovered": False, "error": f"Failed to restart actor: {e}"}


class MessageTimeoutRecoveryStrategy(RecoveryStrategy):
    """
    Recovery strategy for message timeouts.
    
    This strategy attempts to recover from message timeouts by resending
    the message with a longer timeout.
    """
    
    def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Check if this strategy can handle the given error.
        
        Args:
            error: The error to check
            context: Context information about the error
            
        Returns:
            True if this strategy can handle the error, False otherwise
        """
        # Check if this is a timeout error
        return (
            "timeout" in str(error).lower() or
            "message_timeout" in context or
            "timeout" in context
        )
    
    async def recover(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recover from the given error.
        
        Args:
            error: The error to recover from
            context: Context information about the error
            
        Returns:
            Recovery result
        """
        logger.info(f"Attempting to recover from message timeout: {error}")
        
        # Get the message
        message = context.get("message")
        if not message:
            return {"recovered": False, "error": "No message in context"}
        
        # Get the actor system
        actor_system = context.get("actor_system")
        if not actor_system:
            return {"recovered": False, "error": "No actor system in context"}
        
        # Try to resend the message with a longer timeout
        try:
            # Get the original timeout
            original_timeout = context.get("timeout", 30.0)
            
            # Calculate a new timeout (double the original)
            new_timeout = original_timeout * 2.0
            
            # Resend the message with the new timeout
            result = await actor_system.send_message_with_timeout(
                message,
                timeout=new_timeout
            )
            
            logger.info(f"Successfully resent message with longer timeout ({new_timeout}s)")
            
            return {
                "recovered": True,
                "result": result,
                "actions_taken": ["resend_message_with_longer_timeout"]
            }
        except Exception as e:
            logger.error(f"Failed to resend message: {e}")
            return {"recovered": False, "error": f"Failed to resend message: {e}"}


class SystemOverloadRecoveryStrategy(RecoveryStrategy):
    """
    Recovery strategy for system overload.
    
    This strategy attempts to recover from system overload by reducing
    the concurrency level and switching to sequential execution mode.
    """
    
    def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Check if this strategy can handle the given error.
        
        Args:
            error: The error to check
            context: Context information about the error
            
        Returns:
            True if this strategy can handle the error, False otherwise
        """
        # Check if this is an overload error
        return (
            "overload" in str(error).lower() or
            "resource" in str(error).lower() or
            "memory" in str(error).lower() or
            "cpu" in str(error).lower() or
            "system_load" in context
        )
    
    async def recover(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recover from the given error.
        
        Args:
            error: The error to recover from
            context: Context information about the error
            
        Returns:
            Recovery result
        """
        logger.info(f"Attempting to recover from system overload: {error}")
        
        # Get the execution mode manager
        execution_mode_manager = context.get("execution_mode_manager")
        if not execution_mode_manager:
            return {"recovered": False, "error": "No execution mode manager in context"}
        
        # Try to switch to sequential mode
        try:
            # Switch to sequential mode
            execution_mode_manager.set_execution_mode("sequential")
            
            logger.info("Successfully switched to sequential execution mode")
            
            return {
                "recovered": True,
                "actions_taken": ["switch_to_sequential_mode"]
            }
        except Exception as e:
            logger.error(f"Failed to switch execution mode: {e}")
            return {"recovered": False, "error": f"Failed to switch execution mode: {e}"}


class RecoveryManager:
    """
    Manager for recovery strategies.
    
    This class is responsible for managing recovery strategies and
    coordinating recovery from failures in the actor system.
    
    Implementation:
        This class uses a strategy pattern to select and apply the appropriate
        recovery strategy for a given error. It also provides a fallback
        strategy for errors that cannot be handled by any specific strategy.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the recovery manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        
        # Initialize strategies
        self.strategies: List[RecoveryStrategy] = [
            ActorFailureRecoveryStrategy(),
            MessageTimeoutRecoveryStrategy(),
            SystemOverloadRecoveryStrategy()
        ]
        
        # Initialize error manager
        try:
            self.error_manager = get_error_manager()
        except ImportError:
            self.error_manager = None
        
        logger.info("Initialized recovery manager")
    
    async def recover(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recover from an error.
        
        Args:
            error: The error to recover from
            context: Context information about the error
            
        Returns:
            Recovery result
        """
        logger.info(f"Attempting to recover from error: {error}")
        
        # Try to use the error manager if available
        if self.error_manager:
            try:
                result = self.error_manager.handle_error(error, context)
                if result and result.get("recovered", False):
                    return result
            except Exception as e:
                logger.error(f"Error manager failed to handle error: {e}")
        
        # Try each strategy in order
        for strategy in self.strategies:
            if strategy.can_handle(error, context):
                try:
                    result = await strategy.recover(error, context)
                    if result and result.get("recovered", False):
                        return result
                except Exception as e:
                    logger.error(f"Strategy {strategy.__class__.__name__} failed: {e}")
        
        # If no strategy could handle the error, return a default result
        return {
            "recovered": False,
            "error": str(error),
            "actions_taken": ["none"]
        }
