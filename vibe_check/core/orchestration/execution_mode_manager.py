"""
Execution Mode Manager Module
=========================

This module is responsible for managing the execution mode of the actor system,
including switching between parallel and sequential execution modes based on
system stability metrics. It implements the CAW principle of adaptive execution
by dynamically adjusting the execution mode based on runtime conditions.

This module was extracted from the Orchestrator class to reduce complexity
and improve maintainability.
"""

import logging
import time
from typing import Any, Dict, Optional, Set, Tuple

logger = logging.getLogger("vibe_check_execution_mode_manager")


class ExecutionMode:
    """Enum-like class for execution modes."""
    PARALLEL = "parallel"
    SEQUENTIAL = "sequential"


class ExecutionModeManager:
    """
    Manager for the execution mode of the Vibe Check actor system.

    This class is responsible for managing the execution mode of the actor system,
    including switching between parallel and sequential execution modes based on
    system stability metrics. It implements the CAW principle of adaptive execution
    by dynamically adjusting the execution mode based on runtime conditions.
    
    Implementation:
        This class uses a stability monitoring approach to detect when the system
        is becoming unstable and automatically switches to a more conservative
        execution mode. It also provides methods for manually setting the execution
        mode and retrieving the current mode.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the execution mode manager.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        
        # Set initial execution mode
        self.execution_mode = self.config.get("execution_mode", ExecutionMode.PARALLEL)
        
        # Initialize stability metrics
        self.stability_metrics = {
            "actor_failures": 0,
            "message_timeouts": 0,
            "recovery_attempts": 0,
            "last_mode_switch_time": time.time(),
            "consecutive_failures": 0,
            "error_categories": {}
        }
        
        # Set thresholds
        self.instability_threshold = self.config.get("instability_threshold", 10)
        self.stability_threshold = self.config.get("stability_threshold", 3)
        
        # Set cooldown period
        self.mode_switch_cooldown = self.config.get("mode_switch_cooldown", 60.0)  # seconds
        
        logger.info(f"Initialized execution mode manager with mode: {self.execution_mode}")

    def get_execution_mode(self) -> str:
        """
        Get the current execution mode.

        Returns:
            Current execution mode (parallel or sequential)
        """
        return self.execution_mode  # type: ignore[no-any-return]

    def set_execution_mode(self, mode: str) -> None:
        """
        Set the execution mode manually.

        Args:
            mode: Execution mode to set (parallel or sequential)
        """
        if mode not in (ExecutionMode.PARALLEL, ExecutionMode.SEQUENTIAL):
            logger.warning(f"Invalid execution mode: {mode}, using parallel")
            mode = ExecutionMode.PARALLEL
            
        # Only log if the mode is changing
        if mode != self.execution_mode:
            logger.info(f"Manually changing execution mode from {self.execution_mode} to {mode}")
            self.execution_mode = mode
            self.stability_metrics["last_mode_switch_time"] = time.time()

    def record_failure(self, category: str) -> None:
        """
        Record a failure in the stability metrics.

        Args:
            category: Category of the failure (e.g., actor_failure, message_timeout)
        """
        # Update general failure metrics
        self.stability_metrics["actor_failures"] += 1  # type: ignore[operator]
        self.stability_metrics["consecutive_failures"] += 1  # type: ignore[operator]
        
        # Update category-specific metrics
        if category not in self.stability_metrics["error_categories"]:  # type: ignore[operator]
            self.stability_metrics["error_categories"][category] = 0  # type: ignore[index]
        self.stability_metrics["error_categories"][category] += 1  # type: ignore[index]
        
        # Check if we need to switch to sequential mode
        self._check_for_mode_switch()

    def record_success(self) -> None:
        """
        Record a success in the stability metrics.
        """
        # Reset consecutive failures
        self.stability_metrics["consecutive_failures"] = 0
        
        # Check if we can switch back to parallel mode
        self._check_for_mode_switch()

    def record_recovery(self) -> None:
        """
        Record a recovery attempt in the stability metrics.
        """
        self.stability_metrics["recovery_attempts"] += 1  # type: ignore[operator]

    def _check_for_mode_switch(self) -> None:
        """
        Check if we need to switch execution modes based on stability metrics.
        """
        # Check if we're in cooldown period
        current_time = time.time()
        if current_time - self.stability_metrics["last_mode_switch_time"] < self.mode_switch_cooldown:  # type: ignore[operator]
            return
            
        # Check if we need to switch to sequential mode
        if self.execution_mode == ExecutionMode.PARALLEL:
            if self.stability_metrics["consecutive_failures"] >= self.instability_threshold:
                logger.warning(f"System becoming unstable: {self.stability_metrics['consecutive_failures']} consecutive failures. Switching to sequential mode.")
                self.execution_mode = ExecutionMode.SEQUENTIAL
                self.stability_metrics["last_mode_switch_time"] = current_time
                self.stability_metrics["consecutive_failures"] = 0
                
        # Check if we can switch back to parallel mode
        elif self.execution_mode == ExecutionMode.SEQUENTIAL:
            if self.stability_metrics["consecutive_failures"] == 0:
                # We've had some period of stability, consider switching back
                logger.info(f"System stabilizing. Switching back to parallel mode.")
                self.execution_mode = ExecutionMode.PARALLEL
                self.stability_metrics["last_mode_switch_time"] = current_time

    def get_stability_metrics(self) -> Dict[str, Any]:
        """
        Get the current stability metrics.

        Returns:
            Dictionary of stability metrics
        """
        return {
            "execution_mode": self.execution_mode,
            "actor_failures": self.stability_metrics["actor_failures"],
            "message_timeouts": self.stability_metrics["message_timeouts"],
            "recovery_attempts": self.stability_metrics["recovery_attempts"],
            "consecutive_failures": self.stability_metrics["consecutive_failures"],
            "error_categories": self.stability_metrics["error_categories"],
            "last_mode_switch": self.stability_metrics["last_mode_switch_time"],
            "time_since_last_switch": time.time() - self.stability_metrics["last_mode_switch_time"]  # type: ignore[operator]
        }
