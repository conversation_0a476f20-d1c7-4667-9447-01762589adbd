"""
Actor Lifecycle Manager Module
==========================

This module is responsible for managing the lifecycle of actors in the actor system,
including starting, stopping, and monitoring actors. It implements the CAW principle
of choreographed interactions by ensuring proper initialization and termination
of actors.

This module was extracted from the Orchestrator class to reduce complexity
and improve maintainability.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Set, Tuple

from ..actor_system import Actor

logger = logging.getLogger("vibe_check_actor_lifecycle_manager")


class ActorLifecycleManager:
    """
    Lifecycle manager for the Vibe Check actor system.

    This class is responsible for managing the lifecycle of actors in the actor system,
    including starting, stopping, and monitoring actors. It implements the CAW principle
    of choreographed interactions by ensuring proper initialization and termination
    of actors.

    Implementation:
        This class uses a two-phase initialization process with explicit
        synchronization points to ensure all actors are properly initialized
        before they start communicating. It also handles cleanup on failure
        and provides a configurable fail-fast option.
    """

    def __init__(self, actors: Dict[str, Actor], config: Dict[str, Any]):
        """
        Initialize the actor lifecycle manager.

        Args:
            actors: Dictionary of actor IDs to actor instances
            config: Configuration dictionary
        """
        self.actors = actors
        self.config = config

    async def start_actors(self) -> None:
        """
        Start all actors in the actor system.

        Enhanced to implement a two-phase initialization process with explicit
        synchronization points to ensure all actors are properly initialized
        before they start communicating.

        Enhanced with robust error handling, cleanup on failure, and configurable
        fail-fast option.
        """
        # Track initialized and started actors for cleanup in case of failure
        initialized_actors = set()
        started_actors = set()

        try:
            # Try to use the actor initializer
            try:
                # Import and setup the initializer
                initializer, fail_fast = await self._setup_actor_initializer()

                # Phase 1: Initialize all actors
                initialized_actors = await self._initialize_all_actors(initializer, fail_fast)

                # Register dependencies between actors
                await self._register_actor_dependencies(initializer, initialized_actors)

                # Phase 2: Start all actors
                started_actors = await self._start_all_actors(initializer, initialized_actors, fail_fast)

                # Wait for all actors to be ready
                await self._wait_for_actors_ready(initializer)

            except ImportError:
                # Fall back to the old initialization process if the initializer is not available
                logger.warning("Actor initializer not available, falling back to simple initialization")
                started_actors = await self._fallback_initialization()

        except Exception as e:
            logger.error(f"Error during actor system initialization: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Clean up any started actors
            await self._cleanup_actors(started_actors, initialized_actors)

            # Re-raise the exception
            raise

    async def _setup_actor_initializer(self) -> Tuple[Any, bool]:
        """
        Set up the actor initializer.

        Returns:
            Tuple containing:
            - The initializer instance
            - The fail-fast flag
        """
        from ..actor_system.actor_initializer import (
            get_initializer,
            reset_initializer
        )

        # Reset the initializer to ensure a clean state
        reset_initializer()

        # Create initializer with fail-fast option from config
        fail_fast = self.config.get("actor_system", {}).get("fail_fast", False)
        initializer = get_initializer()
        initializer.set_fail_fast(fail_fast)

        logger.info(f"Reset actor initializer (fail_fast={fail_fast})")

        return initializer, fail_fast

    async def _initialize_all_actors(self, initializer: Any, fail_fast: bool) -> Set[str]:
        """
        Initialize all actors in the system.

        Args:
            initializer: The actor initializer instance
            fail_fast: Whether to fail fast on errors

        Returns:
            Set of initialized actor IDs

        Raises:
            RuntimeError: If no actors were successfully initialized
        """
        initialized_actors: Set[str] = set()
        logger.info("Phase 1: Initializing all actors")

        # Initialize the supervisor actor first
        initialized_actors = await self._initialize_supervisor_actor(initializer, initialized_actors, fail_fast)

        # Initialize all other actors
        # Group actors by type for better logging and debugging
        actor_types: Dict[str, List[str]] = {}
        for actor_id, actor in self.actors.items():
            if actor_id != "supervisor_actor" and actor_id not in initialized_actors:
                actor_type = getattr(actor, "actor_type", type(actor).__name__)
                if actor_type not in actor_types:
                    actor_types[actor_type] = []
                actor_types[actor_type].append(actor_id)

        # Log actor types for debugging
        logger.info(f"Found {len(self.actors)} actors of {len(actor_types)} types: {list(actor_types.keys())}")

        # Initialize actors by type
        for actor_type, actor_ids in actor_types.items():
            logger.info(f"Initializing {len(actor_ids)} actors of type {actor_type}")

            # Initialize actors of this type
            for actor_id in actor_ids:
                try:
                    # Initialize the actor with retry logic
                    max_retries = 3
                    retry_count = 0
                    success = False

                    while retry_count < max_retries and not success:
                        try:
                            # Initialize the actor
                            logger.info(f"Initializing actor {actor_id} (type: {actor_type})")
                            await initializer.initialize_actor(actor_id)
                            initialized_actors.add(actor_id)
                            logger.info(f"Successfully initialized actor {actor_id}")
                            success = True
                        except Exception as retry_error:
                            retry_count += 1
                            if retry_count < max_retries:
                                logger.warning(f"Failed to initialize actor {actor_id} (attempt {retry_count}/{max_retries}): {retry_error}")
                                await asyncio.sleep(0.1 * retry_count)  # Exponential backoff
                            else:
                                # Last attempt failed, re-raise
                                raise
                except Exception as e:
                    logger.error(f"Failed to initialize actor {actor_id} after {max_retries} attempts: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

                    if fail_fast:
                        raise

            logger.info(f"Completed initialization of {len(actor_ids)} actors of type {actor_type}")

        # Check if any actors were initialized
        if not initialized_actors:
            error_msg = "No actors were successfully initialized"
            logger.error(error_msg)

            # Log detailed information about all actors
            for actor_id, actor in self.actors.items():
                actor_type = getattr(actor, "actor_type", type(actor).__name__)
                logger.error(f"Actor {actor_id} (type: {actor_type}) failed to initialize")

                # Try to get the error from the initializer
                try:
                    error = initializer.get_initialization_error(actor_id)
                    if error:
                        logger.error(f"Error for actor {actor_id}: {error}")
                except Exception as e:
                    logger.error(f"Could not get error for actor {actor_id}: {e}")

            raise RuntimeError(error_msg)

        logger.info(f"Successfully initialized {len(initialized_actors)} actors out of {len(self.actors)}")
        return initialized_actors

    async def _initialize_supervisor_actor(self, initializer: Any, initialized_actors: Set[str], fail_fast: bool) -> Set[str]:
        """
        Initialize the supervisor actor.

        Args:
            initializer: The actor initializer instance
            initialized_actors: Set of already initialized actor IDs
            fail_fast: Whether to fail fast on errors

        Returns:
            Updated set of initialized actor IDs
        """
        try:
            # Initialize the supervisor actor first
            supervisor_id = "supervisor_actor"
            if supervisor_id in self.actors:
                await initializer.initialize_actor(supervisor_id)
                initialized_actors.add(supervisor_id)
                logger.info(f"Initialized supervisor actor {supervisor_id}")
        except Exception as e:
            logger.error(f"Failed to initialize supervisor actor: {e}")
            if fail_fast:
                raise

        return initialized_actors

    async def _register_actor_dependencies(self, initializer: Any, initialized_actors: Set[str]) -> None:
        """
        Register dependencies between actors.

        Args:
            initializer: The actor initializer instance
            initialized_actors: Set of initialized actor IDs
        """
        logger.info("Registering actor dependencies")

        # Register dependencies for each actor
        for actor_id in initialized_actors:
            actor = self.actors.get(actor_id)
            if not actor:
                continue

            # Register supervisor dependency
            if hasattr(actor, "supervisor_id") and actor.supervisor_id:
                await initializer.register_dependency(actor_id, actor.supervisor_id)
                logger.debug(f"Registered dependency: {actor_id} -> {actor.supervisor_id}")

            # Register report actor dependency
            if hasattr(actor, "report_actor_id") and actor.report_actor_id:
                await initializer.register_dependency(actor_id, actor.report_actor_id)
                logger.debug(f"Registered dependency: {actor_id} -> {actor.report_actor_id}")

            # Register visualization actor dependency
            if hasattr(actor, "visualization_actor_id") and actor.visualization_actor_id:
                await initializer.register_dependency(actor_id, actor.visualization_actor_id)
                logger.debug(f"Registered dependency: {actor_id} -> {actor.visualization_actor_id}")

            # Register tool actor dependencies for file actors
            if hasattr(actor, "tool_actor_ids") and actor.tool_actor_ids:
                for tool_id in actor.tool_actor_ids:
                    await initializer.register_dependency(actor_id, tool_id)
                    logger.debug(f"Registered dependency: {actor_id} -> {tool_id}")

        logger.info("Actor dependencies registered")

    async def _start_all_actors(self, initializer: Any, initialized_actors: Set[str], fail_fast: bool) -> Set[str]:
        """
        Start all initialized actors.

        Args:
            initializer: The actor initializer instance
            initialized_actors: Set of initialized actor IDs
            fail_fast: Whether to fail fast on errors

        Returns:
            Set of started actor IDs
        """
        started_actors = set()
        logger.info("Phase 2: Starting all actors")

        # Start the supervisor actor first
        supervisor_id = "supervisor_actor"
        if supervisor_id in initialized_actors:
            try:
                await initializer.start_actor(supervisor_id)
                started_actors.add(supervisor_id)
                logger.info(f"Started supervisor actor {supervisor_id}")
            except Exception as e:
                logger.error(f"Failed to start supervisor actor {supervisor_id}: {e}")
                if fail_fast:
                    raise

        # Start all other actors
        for actor_id in initialized_actors:
            if actor_id != supervisor_id and actor_id not in started_actors:
                try:
                    await initializer.start_actor(actor_id)
                    started_actors.add(actor_id)
                    logger.info(f"Started actor {actor_id}")
                except Exception as e:
                    logger.error(f"Failed to start actor {actor_id}: {e}")
                    if fail_fast:
                        raise

        logger.info(f"Started {len(started_actors)} actors")
        return started_actors

    async def _wait_for_actors_ready(self, initializer: Any) -> None:
        """
        Wait for all actors to be ready.

        Args:
            initializer: The actor initializer instance
        """
        logger.info("Waiting for all actors to be ready")

        # Wait for all actors to be ready with a timeout
        try:
            # Use a 30-second timeout to prevent hanging
            ready = await initializer.wait_for_all_ready(timeout=30.0)
            if ready:
                logger.info("All actors are ready")
            else:
                logger.warning("Timeout or readiness check failed while waiting for actors to be ready")
                # Don't raise an exception, just log the warning and continue
        except Exception as e:
            logger.error(f"Error waiting for actors to be ready: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # Don't re-raise the exception, just log it and continue
            logger.warning("Continuing despite error waiting for actors to be ready")

    async def _fallback_initialization(self) -> Set[str]:
        """
        Fall back to simple initialization if the initializer is not available.

        This method implements a simplified two-phase initialization process
        without using the ActorInitializer. It first initializes all actors,
        then starts them, mimicking the behavior of the full initialization process.

        Returns:
            Set of started actor IDs
        """
        initialized_actors: Set[str] = set()
        started_actors: Set[str] = set()
        logger.info("Using fallback initialization with enhanced error handling")

        # Phase 1: Initialize all actors
        logger.info("Phase 1: Initializing all actors (fallback method)")
        for actor_id, actor in self.actors.items():
            try:
                if hasattr(actor, "initialize") and callable(actor.initialize):
                    await actor.initialize()
                    initialized_actors.add(actor_id)
                    logger.info(f"Initialized actor {actor_id} using fallback method")
                else:
                    logger.warning(f"Actor {actor_id} does not have an initialize method, skipping initialization")
                    # Add to initialized actors anyway to allow starting
                    initialized_actors.add(actor_id)
            except Exception as e:
                logger.error(f"Failed to initialize actor {actor_id} using fallback method: {e}")
                import traceback
                logger.error(traceback.format_exc())
                # Continue with other actors

        # Check if any actors were initialized
        if not initialized_actors:
            logger.error("No actors were successfully initialized using fallback method")
            return set()

        logger.info(f"Initialized {len(initialized_actors)} actors using fallback method")

        # Phase 2: Start all actors
        logger.info("Phase 2: Starting all actors (fallback method)")
        for actor_id in initialized_actors:
            actor_instance: Optional[Actor] = self.actors.get(actor_id)
            if not actor_instance:
                continue

            try:
                if hasattr(actor_instance, "start") and callable(actor_instance.start):
                    await actor_instance.start()
                    started_actors.add(actor_id)
                    logger.info(f"Started actor {actor_id} using fallback method")
                else:
                    logger.warning(f"Actor {actor_id} does not have a start method, skipping start")
            except Exception as e:
                logger.error(f"Failed to start actor {actor_id} using fallback method: {e}")
                import traceback
                logger.error(traceback.format_exc())
                # Continue with other actors

        # Check if any actors were started
        if not started_actors:
            logger.error("No actors were successfully started using fallback method")
            return set()

        logger.info(f"Started {len(started_actors)} actors using fallback method")
        return started_actors

    async def _cleanup_actors(self, started_actors: Set[str], initialized_actors: Set[str]) -> None:
        """
        Clean up actors in case of initialization failure.

        Args:
            started_actors: Set of started actor IDs
            initialized_actors: Set of initialized actor IDs
        """
        logger.info("Cleaning up actors due to initialization failure")

        # Stop all started actors
        for actor_id in started_actors:
            actor = self.actors.get(actor_id)
            if actor and hasattr(actor, "stop") and callable(actor.stop):
                try:
                    await actor.stop()
                    logger.info(f"Stopped actor {actor_id} during cleanup")
                except Exception as e:
                    logger.error(f"Failed to stop actor {actor_id} during cleanup: {e}")

        # Clean up all initialized actors
        for actor_id in initialized_actors - started_actors:
            actor = self.actors.get(actor_id)
            if actor and hasattr(actor, "cleanup") and callable(actor.cleanup):
                try:
                    await actor.cleanup()
                    logger.info(f"Cleaned up actor {actor_id}")
                except Exception as e:
                    logger.error(f"Failed to clean up actor {actor_id}: {e}")

        logger.info("Actor cleanup complete")

    async def stop_actors(self) -> None:
        """
        Stop all actors in the actor system.

        Enhanced with robust error handling and cleanup.
        """
        logger.info("Stopping all actors")

        # Get the supervisor actor
        supervisor = self.actors.get("supervisor_actor")

        # Stop all actors through the supervisor if available
        if supervisor and hasattr(supervisor, "stop_all_actors") and callable(supervisor.stop_all_actors):
            try:
                await supervisor.stop_all_actors()
                logger.info("All actors stopped through supervisor")
                return
            except Exception as e:
                logger.error(f"Failed to stop actors through supervisor: {e}")
                # Fall back to direct stopping

        # Stop all actors directly
        for actor_id, actor in self.actors.items():
            if hasattr(actor, "stop") and callable(actor.stop):
                try:
                    await actor.stop()
                    logger.info(f"Stopped actor {actor_id}")
                except Exception as e:
                    logger.error(f"Failed to stop actor {actor_id}: {e}")

        logger.info("All actors stopped")
