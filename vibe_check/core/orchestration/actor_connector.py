"""
Actor Connector Module
===================

This module is responsible for connecting actors to each other to establish
communication paths. It implements the CAW principle of choreographed interactions
by setting up the initial conditions for emergent behavior among actors.

This module was extracted from the Orchestrator class to reduce complexity
and improve maintainability.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Set

from ..actor_system import Actor, get_registry

logger = logging.getLogger("vibe_check_actor_connector")


class ActorConnector:
    """
    Connector for the Vibe Check actor system.

    This class is responsible for connecting actors to each other to establish
    communication paths. It implements the CAW principle of choreographed interactions
    by setting up the initial conditions for emergent behavior among actors.
    
    Implementation:
        This class uses the Mediator pattern to establish communication paths
        between actors without them needing to know about each other directly.
        It handles the connection of project actors with file actors, file actors
        with tool actors, and all actors with report and visualization actors.
    """

    def __init__(self, actors: Dict[str, Actor]):
        """
        Initialize the actor connector.

        Args:
            actors: Dictionary of actor IDs to actor instances
        """
        self.actors = actors

    def connect_actors(self) -> None:
        """
        Connect all actors to establish communication paths.

        Enhanced to use the actor registry for dynamic discovery and communication.
        This sets up the choreography pattern of the CAW architecture,
        where each actor knows who to send messages to but the overall
        flow emerges from their interactions.
        """
        try:
            # Connect project actor with file actors
            self._connect_project_with_file_actors()

            # Connect file actors with tool actors
            self._connect_file_with_tool_actors()

            # Connect all actors with report and visualization actors
            self._connect_output_actors()

            # Set up reactive streams
            self._setup_reactive_streams()

            logger.info("All actors connected successfully")
        except Exception as e:
            import traceback
            logger.error(f"Error connecting actors: {e}\n{traceback.format_exc()}")
            raise

    def _connect_project_with_file_actors(self) -> None:
        """
        Connect the project actor with file actors.

        This is a helper method for the connect_actors method to make it less complex.
        """
        try:
            # Get the registry
            registry = get_registry()

            # Set up project actor with file actor IDs
            file_actor_ids = registry.get_actors_by_tag("file")
            file_actor_ids = [actor.actor_id for actor in file_actor_ids]

            # Get the project actor
            project_actor = self.actors.get("project_actor")
            if project_actor and hasattr(project_actor, "set_file_actors"):
                project_actor.set_file_actors(file_actor_ids)
                logger.info(f"Set {len(file_actor_ids)} file actors for project actor")
            else:
                logger.warning("Project actor does not have set_file_actors method")
        except Exception as e:
            import traceback
            logger.error(f"Error connecting project with file actors: {e}\n{traceback.format_exc()}")

    def _connect_file_with_tool_actors(self) -> None:
        """
        Connect file actors with tool actors.

        This is a helper method for the connect_actors method to make it less complex.
        """
        try:
            # Get the registry
            registry = get_registry()

            # Get tool actors
            tool_actors = registry.get_actors_by_tag("tool")
            tool_actor_ids = [actor.actor_id for actor in tool_actors]

            # Configure file actors with tool actor IDs
            file_actors = registry.get_actors_by_tag("file")
            for file_actor in file_actors:
                # Set tool actors for file actors
                if hasattr(file_actor, "set_tool_actor"):
                    for tool_id in tool_actor_ids:
                        file_actor.set_tool_actor(tool_id)
                        logger.debug(f"Set tool actor {tool_id} for file actor {file_actor.actor_id}")
                else:
                    logger.warning(f"File actor {file_actor.actor_id} does not have set_tool_actor method")

                # Set report actor ID for file actors
                if hasattr(file_actor, "set_report_actor"):
                    file_actor.set_report_actor("report_actor")
                    logger.debug(f"Set report actor for file actor {file_actor.actor_id}")
                else:
                    logger.warning(f"File actor {file_actor.actor_id} does not have set_report_actor method")
        except Exception as e:
            import traceback
            logger.error(f"Error connecting file with tool actors: {e}\n{traceback.format_exc()}")

    def _connect_output_actors(self) -> None:
        """
        Connect all actors with report and visualization actors.

        This is a helper method for the connect_actors method to make it less complex.
        """
        try:
            # Set report and visualization actors for all actors
            for actor_id, actor in self.actors.items():
                # Set report actor ID for all actors that need it
                if hasattr(actor, "set_report_actor"):
                    actor.set_report_actor("report_actor")
                    logger.debug(f"Set report actor for {actor_id}")

                # Set visualization actor ID for actors that need it
                if hasattr(actor, "set_visualization_actor"):
                    actor.set_visualization_actor("visualization_actor")
                    logger.debug(f"Set visualization actor for {actor_id}")
        except Exception as e:
            import traceback
            logger.error(f"Error connecting output actors: {e}\n{traceback.format_exc()}")

    def _setup_reactive_streams(self) -> None:
        """
        Set up reactive streams for all actors.

        This is a helper method for the connect_actors method to make it less complex.
        """
        try:
            # Get the registry
            registry = get_registry()

            # Create tasks for stream subscriptions
            tasks = []

            # Project status stream
            for actor in self.actors.values():
                tasks.append(asyncio.create_task(actor.subscribe("project_status")))

            # Metrics stream
            for actor in self.actors.values():
                tasks.append(asyncio.create_task(actor.subscribe("metrics")))

            # File analysis stream
            for actor in registry.get_actors_by_tag("file"):
                tasks.append(asyncio.create_task(actor.subscribe("file_analysis")))

            # Tool results stream
            for actor in registry.get_actors_by_tag("tool"):
                tasks.append(asyncio.create_task(actor.subscribe("tool_results")))

            # Report stream
            report_actor = self.actors.get("report_actor")
            if report_actor:
                tasks.append(asyncio.create_task(report_actor.subscribe("report")))

            # Visualization stream
            visualization_actor = self.actors.get("visualization_actor")
            if visualization_actor:
                tasks.append(asyncio.create_task(visualization_actor.subscribe("visualization")))

            logger.info(f"Created {len(tasks)} stream subscription tasks")
        except Exception as e:
            import traceback
            logger.error(f"Error setting up reactive streams: {e}\n{traceback.format_exc()}")
