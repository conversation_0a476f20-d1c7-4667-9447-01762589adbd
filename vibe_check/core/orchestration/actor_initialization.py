"""
Actor Initialization Module
===========================

This module provides functionality for initializing actors in the Vibe Check system.
It handles the two-phase initialization process with proper error handling and recovery.
"""

import logging
from typing import Any, Optional, Set, Tuple

from ..actor_system import Actor, ActorPool

logger = logging.getLogger("vibe_check_actor_initialization")


class ActorInitializationManager:
    """
    Manages the initialization process for actors in the system.
    
    This class implements a two-phase initialization process with explicit
    synchronization points to ensure all actors are properly initialized
    before they start communicating.
    """

    def __init__(self, actors: dict[str, Actor], file_actor_pool: Optional[ActorPool] = None):
        """
        Initialize the actor initialization manager.

        Args:
            actors: Dictionary of actor IDs to actor instances
            file_actor_pool: Optional file actor pool
        """
        self.actors = actors
        self.file_actor_pool = file_actor_pool

    async def setup_actor_initializer(self, config: dict[str, Any]) -> Tuple[Any, bool]:
        """
        Set up the actor initializer.

        Args:
            config: Configuration dictionary

        Returns:
            Tuple containing:
            - The initializer instance
            - The fail-fast flag
        """
        from ..actor_system.actor_initializer import (
            get_initializer,
            reset_initializer
        )

        # Reset the initializer to ensure a clean state
        reset_initializer()

        # Create initializer with fail-fast option from config
        fail_fast = config.get("actor_system", {}).get("fail_fast", False)
        initializer = get_initializer()
        initializer.set_fail_fast(fail_fast)

        logger.info(f"Reset actor initializer (fail_fast={fail_fast})")

        return initializer, fail_fast

    async def initialize_all_actors(self, initializer: Any, fail_fast: bool) -> Set[str]:
        """
        Initialize all actors in the system.

        Args:
            initializer: The actor initializer instance
            fail_fast: Whether to fail fast on errors

        Returns:
            Set of initialized actor IDs

        Raises:
            RuntimeError: If no actors were successfully initialized
        """
        initialized_actors: Set[str] = set()
        logger.info("Phase 1: Initializing all actors")

        # Initialize the supervisor actor first
        initialized_actors = await self.initialize_supervisor_actor(initialized_actors, fail_fast)

        # Initialize the file actor pool
        initialized_actors = await self.initialize_file_actor_pool(initialized_actors, fail_fast)

        # Initialize all other actors
        initialized_actors = await self.initialize_regular_actors(initialized_actors, fail_fast)

        # Check if we have enough actors initialized to proceed
        if not initialized_actors:
            raise RuntimeError("No actors were successfully initialized")

        return initialized_actors

    async def initialize_supervisor_actor(self, initialized_actors: Set[str], fail_fast: bool) -> Set[str]:
        """
        Initialize the supervisor actor.

        Args:
            initialized_actors: Set of already initialized actor IDs
            fail_fast: Whether to fail fast on errors

        Returns:
            Updated set of initialized actor IDs
        """
        supervisor = self.actors.get("supervisor_actor")
        if supervisor:
            try:
                await supervisor.initialize()
                initialized_actors.add("supervisor_actor")
                logger.info("Supervisor actor initialized")
            except Exception as e:
                logger.error(f"Failed to initialize supervisor actor: {e}")
                import traceback
                logger.error(traceback.format_exc())

                # If fail-fast is enabled, this will re-raise the exception
                if fail_fast:
                    raise

        return initialized_actors

    async def initialize_file_actor_pool(self, initialized_actors: Set[str], fail_fast: bool) -> Set[str]:
        """
        Initialize the file actor pool.

        Args:
            initialized_actors: Set of already initialized actor IDs
            fail_fast: Whether to fail fast on errors

        Returns:
            Updated set of initialized actor IDs
        """
        if self.file_actor_pool is not None:
            try:
                # Initialize the pool
                await self.file_actor_pool.initialize()

                # Add all pool actors to the initialized set
                for actor_id in self.file_actor_pool.get_actors().keys():
                    initialized_actors.add(actor_id)

                logger.info("File actor pool initialized")
            except Exception as e:
                logger.error(f"Failed to initialize file actor pool: {e}")
                import traceback
                logger.error(traceback.format_exc())

                # If fail-fast is enabled, this will re-raise the exception
                if fail_fast:
                    raise

        return initialized_actors

    async def initialize_regular_actors(self, initialized_actors: Set[str], fail_fast: bool) -> Set[str]:
        """
        Initialize all regular actors (not supervisor or file actor pool).

        Args:
            initialized_actors: Set of already initialized actor IDs
            fail_fast: Whether to fail fast on errors

        Returns:
            Updated set of initialized actor IDs
        """
        for actor_id, actor in self.actors.items():
            if actor_id != "supervisor_actor" and actor_id not in initialized_actors:  # Not already initialized
                try:
                    await actor.initialize()
                    initialized_actors.add(actor_id)
                    logger.info(f"Actor {actor_id} initialized")
                except Exception as e:
                    logger.error(f"Failed to initialize actor {actor_id}: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

                    # If fail-fast is enabled, this will re-raise the exception
                    if fail_fast:
                        raise

        return initialized_actors

    async def register_actor_dependencies(self, initializer: Any, initialized_actors: Set[str]) -> None:
        """
        Register dependencies between actors.

        Args:
            initializer: The actor initializer instance
            initialized_actors: Set of initialized actor IDs
        """
        logger.info("Registering actor dependencies")

        await self.register_supervisor_dependencies(initializer, initialized_actors)
        await self.register_tool_actor_dependencies(initializer, initialized_actors)
        await self.register_project_actor_dependencies(initializer, initialized_actors)
        await self.register_report_actor_dependencies(initializer, initialized_actors)

        logger.info("Actor dependencies registered")

    async def register_supervisor_dependencies(self, initializer: Any, initialized_actors: Set[str]) -> None:
        """
        Register dependencies related to the supervisor actor.

        Args:
            initializer: The actor initializer instance
            initialized_actors: Set of initialized actor IDs
        """
        if "supervisor_actor" in initialized_actors:
            for actor_id in initialized_actors:
                if actor_id != "supervisor_actor":
                    initializer.register_dependency(actor_id, "supervisor_actor")

    async def register_tool_actor_dependencies(self, initializer: Any, initialized_actors: Set[str]) -> None:
        """
        Register dependencies related to the tool actor.

        Args:
            initializer: The actor initializer instance
            initialized_actors: Set of initialized actor IDs
        """
        if "tool_actor" in initialized_actors:
            for actor_id in initialized_actors:
                if actor_id.startswith("file_actor_"):
                    initializer.register_dependency(actor_id, "tool_actor")

    async def register_project_actor_dependencies(self, initializer: Any, initialized_actors: Set[str]) -> None:
        """
        Register dependencies related to the project actor.

        Args:
            initializer: The actor initializer instance
            initialized_actors: Set of initialized actor IDs
        """
        if "project_actor" in initialized_actors:
            # Project actor depends on file actors
            for actor_id in initialized_actors:
                if actor_id.startswith("file_actor_"):
                    initializer.register_dependency("project_actor", actor_id)

    async def register_report_actor_dependencies(self, initializer: Any, initialized_actors: Set[str]) -> None:
        """
        Register dependencies related to the report actor.

        Args:
            initializer: The actor initializer instance
            initialized_actors: Set of initialized actor IDs
        """
        # Report actor depends on visualization actor
        if "report_actor" in initialized_actors and "visualization_actor" in initialized_actors:
            initializer.register_dependency("report_actor", "visualization_actor")
