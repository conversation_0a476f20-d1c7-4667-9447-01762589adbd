"""
Orchestration Package
=================

This package contains modules for orchestrating the actor system in Vibe Check.
It implements the CAW principle of choreographed interactions by setting up
the initial conditions for emergent behavior among actors.

The orchestration package was created by extracting functionality from the
Orchestrator class to reduce complexity and improve maintainability. It
provides a more modular and testable architecture for the actor system.

Modules:
- actor_system_builder: Responsible for creating and configuring the actor system
- actor_connector: Responsible for connecting actors to each other
- actor_lifecycle_manager: Responsible for starting, stopping, and monitoring actors
- execution_mode_manager: Responsible for managing the execution mode (parallel/sequential)
- analysis_coordinator: Responsible for coordinating the analysis of a project
- recovery_manager: Responsible for managing recovery from failures
- diagnostic_manager: Responsible for collecting and managing diagnostic information
"""

from .actor_system_builder import ActorSystemBuilder
from .actor_connector import ActorConnector
from .actor_lifecycle_manager import ActorLifecycleManager
from .execution_mode_manager import ExecutionModeManager, ExecutionMode
from .analysis_coordinator import AnalysisCoordinator
from .recovery_manager import RecoveryManager, RecoveryStrategy
from .diagnostic_manager import <PERSON>ag<PERSON><PERSON><PERSON><PERSON><PERSON>, DiagnosticLevel, InitializationStep

__all__ = [
    # Actor system components
    "ActorSystemBuilder",
    "ActorConnector",
    "ActorLifecycleManager",

    # Execution mode components
    "ExecutionModeManager",
    "ExecutionMode",

    # Analysis components
    "AnalysisCoordinator",

    # Recovery components
    "RecoveryManager",
    "RecoveryStrategy",

    # Diagnostic components
    "DiagnosticManager",
    "DiagnosticLevel",
    "InitializationStep"
]
