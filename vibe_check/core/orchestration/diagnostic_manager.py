"""
Diagnostic Manager Module
=====================

This module is responsible for collecting and managing diagnostic information
about the actor system. It implements the CAW principle of observability by
providing comprehensive logging, metrics, and diagnostics.

This module was extracted from the Orchestrator class to reduce complexity
and improve maintainability.
"""

import asyncio
import logging
import time
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union

logger = logging.getLogger("vibe_check_diagnostic_manager")


class DiagnosticLevel(Enum):
    """Diagnostic levels for the diagnostic manager."""
    MINIMAL = "minimal"
    BASIC = "basic"
    DETAILED = "detailed"
    VERBOSE = "verbose"
    DEBUG = "debug"


class InitializationStep(Enum):
    """Initialization steps for actor initialization tracking."""
    REGISTRATION = "registration"
    INITIALIZATION = "initialization"
    DEPENDENCY_RESOLUTION = "dependency_resolution"
    STARTUP = "startup"
    READY = "ready"


class DiagnosticManager:
    """
    Manager for diagnostic information.
    
    This class is responsible for collecting and managing diagnostic information
    about the actor system. It implements the CAW principle of observability by
    providing comprehensive logging, metrics, and diagnostics.
    
    Implementation:
        This class uses a comprehensive approach to diagnostics, including
        tracking actor initialization, message flow, system metrics, and
        error information. It also provides methods for retrieving and
        visualizing diagnostic information.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the diagnostic manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        
        # Set diagnostic level
        self.diagnostic_level = DiagnosticLevel(
            self.config.get("diagnostic_level", DiagnosticLevel.BASIC.value)
        )
        
        # Initialize diagnostic data
        self.actor_initialization: Dict[str, Dict[str, Any]] = {}
        self.message_flow: Dict[str, List[Dict[str, Any]]] = {}
        self.system_metrics: Dict[str, List[Dict[str, Any]]] = {}
        self.errors: List[Dict[str, Any]] = []
        
        # Initialize tracking
        self.start_time = time.time()
        self.last_update_time = self.start_time
        
        logger.info(f"Initialized diagnostic manager with level: {self.diagnostic_level.value}")
    
    async def record_actor_initialization(self, 
                                        actor_id: str, 
                                        step: InitializationStep,
                                        details: Optional[Dict[str, Any]] = None) -> None:
        """
        Record an actor initialization step.
        
        Args:
            actor_id: ID of the actor
            step: Initialization step
            details: Optional details about the step
        """
        # Skip if diagnostic level is minimal
        if self.diagnostic_level == DiagnosticLevel.MINIMAL:
            return
        
        # Initialize actor entry if not exists
        if actor_id not in self.actor_initialization:
            self.actor_initialization[actor_id] = {
                "steps": [],
                "start_time": time.time(),
                "end_time": None,
                "duration": None,
                "status": "initializing"
            }
        
        # Record step
        step_data = {
            "step": step.value,
            "timestamp": time.time(),
            "details": details or {}
        }
        
        self.actor_initialization[actor_id]["steps"].append(step_data)
        
        # Update status if step is READY
        if step == InitializationStep.READY:
            self.actor_initialization[actor_id]["status"] = "ready"
            self.actor_initialization[actor_id]["end_time"] = time.time()
            self.actor_initialization[actor_id]["duration"] = (
                self.actor_initialization[actor_id]["end_time"] -
                self.actor_initialization[actor_id]["start_time"]
            )
        
        # Log if diagnostic level is detailed or higher
        if self.diagnostic_level.value in (DiagnosticLevel.DETAILED.value, DiagnosticLevel.VERBOSE.value, DiagnosticLevel.DEBUG.value):
            logger.info(f"Actor {actor_id} {step.value}: {details}")
    
    async def record_message(self, 
                           message_id: str, 
                           sender: str,
                           receiver: str,
                           message_type: str,
                           details: Optional[Dict[str, Any]] = None) -> None:
        """
        Record a message between actors.
        
        Args:
            message_id: ID of the message
            sender: ID of the sender actor
            receiver: ID of the receiver actor
            message_type: Type of the message
            details: Optional details about the message
        """
        # Skip if diagnostic level is minimal or basic
        if self.diagnostic_level in (DiagnosticLevel.MINIMAL, DiagnosticLevel.BASIC):
            return
        
        # Initialize message flow entry if not exists
        if message_id not in self.message_flow:
            self.message_flow[message_id] = []
        
        # Record message
        message_data = {
            "timestamp": time.time(),
            "sender": sender,
            "receiver": receiver,
            "message_type": message_type,
            "details": details or {}
        }
        
        self.message_flow[message_id].append(message_data)
        
        # Log if diagnostic level is verbose or higher
        if self.diagnostic_level in (DiagnosticLevel.VERBOSE, DiagnosticLevel.DEBUG):
            logger.info(f"Message {message_id} from {sender} to {receiver}: {message_type}")
    
    async def record_system_metric(self, 
                                 metric_name: str, 
                                 value: Any,
                                 details: Optional[Dict[str, Any]] = None) -> None:
        """
        Record a system metric.
        
        Args:
            metric_name: Name of the metric
            value: Value of the metric
            details: Optional details about the metric
        """
        # Skip if diagnostic level is minimal
        if self.diagnostic_level == DiagnosticLevel.MINIMAL:
            return
        
        # Initialize metric entry if not exists
        if metric_name not in self.system_metrics:
            self.system_metrics[metric_name] = []
        
        # Record metric
        metric_data = {
            "timestamp": time.time(),
            "value": value,
            "details": details or {}
        }
        
        self.system_metrics[metric_name].append(metric_data)
        
        # Log if diagnostic level is detailed or higher
        if self.diagnostic_level in (DiagnosticLevel.DETAILED, DiagnosticLevel.VERBOSE, DiagnosticLevel.DEBUG):
            logger.info(f"System metric {metric_name}: {value}")
    
    async def record_error(self, 
                         error: Exception,
                         context: Optional[Dict[str, Any]] = None) -> None:
        """
        Record an error.
        
        Args:
            error: The error to record
            context: Optional context information about the error
        """
        # Record error
        error_data = {
            "timestamp": time.time(),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context or {}
        }
        
        self.errors.append(error_data)
        
        # Log the error
        logger.error(f"Error: {error_type}: {error_message}")
        if context:
            logger.debug(f"Error context: {context}")
    
    def get_actor_initialization_summary(self) -> Dict[str, Any]:
        """
        Get a summary of actor initialization.
        
        Returns:
            Summary of actor initialization
        """
        # Calculate summary statistics
        total_actors = len(self.actor_initialization)
        ready_actors = sum(1 for data in self.actor_initialization.values() if data["status"] == "ready")
        initializing_actors = total_actors - ready_actors
        
        # Calculate average initialization time
        initialization_times = [
            data["duration"] for data in self.actor_initialization.values()
            if data["duration"] is not None
        ]
        avg_initialization_time = (
            sum(initialization_times) / len(initialization_times)
            if initialization_times else 0.0
        )
        
        # Create summary
        summary = {
            "total_actors": total_actors,
            "ready_actors": ready_actors,
            "initializing_actors": initializing_actors,
            "avg_initialization_time": avg_initialization_time,
            "actors": {
                actor_id: {
                    "status": data["status"],
                    "duration": data["duration"],
                    "steps_completed": len(data["steps"])
                }
                for actor_id, data in self.actor_initialization.items()
            }
        }
        
        return summary
    
    def get_message_flow_summary(self) -> Dict[str, Any]:
        """
        Get a summary of message flow.
        
        Returns:
            Summary of message flow
        """
        # Calculate summary statistics
        total_messages = len(self.message_flow)
        message_counts_by_type = {}
        message_counts_by_sender = {}
        message_counts_by_receiver = {}
        
        # Count messages by type, sender, and receiver
        for message_id, messages in self.message_flow.items():
            for message in messages:
                message_type = message["message_type"]
                sender = message["sender"]
                receiver = message["receiver"]
                
                # Count by type
                if message_type not in message_counts_by_type:
                    message_counts_by_type[message_type] = 0
                message_counts_by_type[message_type] += 1
                
                # Count by sender
                if sender not in message_counts_by_sender:
                    message_counts_by_sender[sender] = 0
                message_counts_by_sender[sender] += 1
                
                # Count by receiver
                if receiver not in message_counts_by_receiver:
                    message_counts_by_receiver[receiver] = 0
                message_counts_by_receiver[receiver] += 1
        
        # Create summary
        summary = {
            "total_messages": total_messages,
            "message_counts_by_type": message_counts_by_type,
            "message_counts_by_sender": message_counts_by_sender,
            "message_counts_by_receiver": message_counts_by_receiver
        }
        
        return summary
    
    def get_system_metrics_summary(self) -> Dict[str, Any]:
        """
        Get a summary of system metrics.
        
        Returns:
            Summary of system metrics
        """
        # Calculate summary statistics for each metric
        summary = {}
        
        for metric_name, metrics in self.system_metrics.items():
            # Extract values
            values = [metric["value"] for metric in metrics if "value" in metric]
            
            # Calculate statistics
            if values:
                summary[metric_name] = {
                    "count": len(values),
                    "min": min(values) if all(isinstance(v, (int, float)) for v in values) else None,
                    "max": max(values) if all(isinstance(v, (int, float)) for v in values) else None,
                    "avg": sum(values) / len(values) if all(isinstance(v, (int, float)) for v in values) else None,
                    "latest": values[-1]
                }
            else:
                summary[metric_name] = {
                    "count": 0,
                    "min": None,
                    "max": None,
                    "avg": None,
                    "latest": None
                }
        
        return summary
    
    def get_error_summary(self) -> Dict[str, Any]:
        """
        Get a summary of errors.
        
        Returns:
            Summary of errors
        """
        # Calculate summary statistics
        total_errors = len(self.errors)
        error_counts_by_type = {}
        
        # Count errors by type
        for error in self.errors:
            error_type = error["error_type"]
            
            if error_type not in error_counts_by_type:
                error_counts_by_type[error_type] = 0
            error_counts_by_type[error_type] += 1
        
        # Create summary
        summary = {
            "total_errors": total_errors,
            "error_counts_by_type": error_counts_by_type,
            "latest_errors": self.errors[-10:] if self.errors else []
        }
        
        return summary
    
    def get_diagnostic_summary(self) -> Dict[str, Any]:
        """
        Get a comprehensive diagnostic summary.
        
        Returns:
            Comprehensive diagnostic summary
        """
        # Create summary
        summary = {
            "diagnostic_level": self.diagnostic_level.value,
            "start_time": self.start_time,
            "current_time": time.time(),
            "uptime": time.time() - self.start_time,
            "actor_initialization": self.get_actor_initialization_summary(),
            "message_flow": self.get_message_flow_summary(),
            "system_metrics": self.get_system_metrics_summary(),
            "errors": self.get_error_summary()
        }
        
        return summary
