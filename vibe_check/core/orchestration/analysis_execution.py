"""
Analysis Execution Module
=========================

This module provides functionality for executing project analysis in the Vibe Check system.
It handles both sequential and parallel execution modes with proper error handling.
"""

import asyncio
import logging
import traceback
from pathlib import Path
from typing import Any, Dict, Optional

from ..actor_system import ContextWave, Message, MessageType
from ..actor_system.actors.project_actor import ProjectActor

logger = logging.getLogger("vibe_check_analysis_execution")


class AnalysisExecutionManager:
    """
    Manages the execution of project analysis.
    
    This class handles both sequential and parallel execution modes,
    providing robust error handling and recovery mechanisms.
    """

    def __init__(self, project_actor: Optional[ProjectActor], config: Dict[str, Any]):
        """
        Initialize the analysis execution manager.

        Args:
            project_actor: The project actor instance
            config: Configuration dictionary
        """
        self.project_actor = project_actor
        self.config = config
        self.analysis_task: Optional[asyncio.Task] = None
        self.analysis_complete: Optional[asyncio.Future] = None

    def prepare_context_wave(self, context: Optional[Dict[str, Any]] = None) -> ContextWave:
        """
        Prepare the context wave for analysis.

        Args:
            context: Optional context dictionary

        Returns:
            Prepared context wave
        """
        # Create context wave with provided context or empty dict
        context_wave = ContextWave(
            value=context or {},
            amplitude=1.0,
            phase=0.0,
            frequency=1.0,
            context=context or {}
        )

        # Add execution mode to context
        execution_mode = self.config.get("execution_mode", "parallel")
        context_wave.context["execution_mode"] = execution_mode
        logger.info(f"Set execution mode in context: {execution_mode}")

        # Add output directory to context
        if "output_dir" in self.config:
            context_wave.context["output_dir"] = str(self.config["output_dir"])
            logger.info(f"Added output directory to context: {self.config['output_dir']}")

        # Add priorities to context if available
        if "priorities" in self.config:
            context_wave.metadata["project_priorities"] = self.config["priorities"]
            logger.info(f"Added priorities to context: {self.config['priorities']}")

        return context_wave

    async def create_and_send_analysis_message(self, project_path: Path, context_wave: ContextWave) -> Optional[Dict[str, str]]:
        """
        Create and send the analysis message to the project actor.

        Args:
            project_path: Path to the project directory
            context_wave: Context wave for the analysis

        Returns:
            None if successful, error dictionary if failed
        """
        try:
            if self.project_actor is None:
                error_msg = "Project actor is not available"
                logger.error(error_msg)
                return {"error": error_msg}

            # Create analysis message
            message = Message(
                sender_id="orchestrator",
                receiver_id="project_actor",
                message_type=MessageType.COMMAND,
                content={
                    "command": "analyze_project",
                    "project_path": str(project_path),
                    "context": context_wave.context,
                    "metadata": context_wave.metadata
                },
                context_wave=context_wave
            )

            logger.info(f"Sending analysis message to project actor for path: {project_path}")

            # Send the message to the project actor
            await self.project_actor.receive(message)

            logger.info("Analysis message sent successfully")
            return None

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Error creating and sending analysis message: {e}\n{error_details}")
            return {"error": str(e), "error_details": error_details}

    async def wait_for_analysis_completion(self) -> Dict[str, Any]:
        """
        Wait for the analysis to complete.

        Returns:
            Analysis results dictionary
        """
        try:
            if self.analysis_complete is None:
                logger.warning("No analysis completion future available")
                return {"error": "No analysis completion future available"}

            # Wait for the analysis to complete
            logger.info("Waiting for analysis to complete...")
            results = await self.analysis_complete

            logger.info("Analysis completed successfully")
            return results

        except asyncio.CancelledError:
            logger.warning("Analysis was cancelled")
            return {"error": "Analysis was cancelled"}
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Error waiting for analysis to complete: {e}\n{error_details}")
            return {"error": str(e), "error_details": error_details}

    async def execute_analysis_sequential(self, project_path: Path, context_wave: ContextWave) -> Dict[str, Any]:
        """
        Execute analysis in sequential mode for increased reliability.

        Args:
            project_path: Path to the project directory
            context_wave: Context wave for the analysis

        Returns:
            Analysis results dictionary
        """
        try:
            logger.info("Executing analysis in sequential mode")

            # Update context to indicate sequential mode
            context_wave.context["execution_mode"] = "sequential"

            # Send analysis message
            error_result = await self.create_and_send_analysis_message(project_path, context_wave)
            if error_result:
                return error_result

            # Wait for completion
            results = await self.wait_for_analysis_completion()

            logger.info("Sequential analysis completed")
            return results

        except Exception as e:
            logger.error(f"Error in sequential analysis: {e}")
            import traceback
            return {"error": str(e), "error_details": traceback.format_exc()}

    async def execute_analysis_with_recovery(self, project_path: Optional[Path] = None, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute analysis with automatic recovery from failures.

        Args:
            project_path: Path to the project directory
            context: Optional context dictionary

        Returns:
            Analysis results dictionary
        """
        max_retries = self.config.get("max_retries", 3)
        retry_delay = self.config.get("retry_delay", 5.0)
        current_retry = 0

        while current_retry <= max_retries:
            try:
                logger.info(f"Analysis attempt {current_retry + 1}/{max_retries + 1}")

                # Prepare context wave
                context_wave = self.prepare_context_wave(context)

                # Determine execution mode
                execution_mode = self.config.get("execution_mode", "parallel")

                if execution_mode == "sequential" or current_retry > 0:
                    # Use sequential mode for retries or if explicitly configured
                    if project_path:
                        return await self.execute_analysis_sequential(project_path, context_wave)
                    else:
                        return {"error": "Project path is required for sequential analysis"}
                else:
                    # Use parallel mode for first attempt
                    if project_path:
                        error_result = await self.create_and_send_analysis_message(project_path, context_wave)
                        if error_result:
                            raise Exception(error_result["error"])

                        return await self.wait_for_analysis_completion()
                    else:
                        return {"error": "Project path is required for analysis"}

            except Exception as e:
                current_retry += 1
                logger.error(f"Analysis attempt {current_retry} failed: {e}")

                if current_retry <= max_retries:
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                else:
                    logger.error("All analysis attempts failed")
                    return {"error": str(e), "error_details": traceback.format_exc()}

        # This should never be reached, but included for completeness
        return {"error": "Analysis failed after retries"}

    def analysis_completed(self, results: Dict[str, Any]) -> None:
        """
        Callback when analysis is completed.

        Args:
            results: Analysis results
        """
        try:
            logger.info("Analysis completed callback triggered")

            # Set the analysis completion future result
            if self.analysis_complete and not self.analysis_complete.done():
                self.analysis_complete.set_result(results)
                logger.info("Analysis completion future set with results")
            else:
                logger.warning("Analysis completion future is not available or already done")

        except Exception as e:
            logger.error(f"Error in analysis_completed: {e}")
            import traceback
            logger.error(traceback.format_exc())
