"""
Actor System Builder Module
========================

This module is responsible for creating and configuring the actor system.
It implements the CAW principle of choreographed interactions by setting up
the initial conditions for emergent behavior among actors.

This module was extracted from the Orchestrator class to reduce complexity
and improve maintainability.
"""

import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union

from ..actor_system import (
    Actor,
    ActorPool,
    SupervisorActor,
    get_registry,
    reset_registry,
)
from ..actor_system.actors.file_actor import FileActor
from ..actor_system.actors.project_actor import ProjectActor
from ..actor_system.actors.report_actor import ReportActor
from ..actor_system.actors.tool_actor import ToolActor
from ..actor_system.actors.visualization_actor import VisualizationActor

logger = logging.getLogger("vibe_check_actor_system_builder")


class ActorSystemBuilder:
    """
    Builder for the Vibe Check actor system.

    This class is responsible for creating and configuring the actor system,
    including creating all necessary actors and registering them with the registry.

    Implementation:
        This class uses the Builder pattern to create a complex actor system
        with proper configuration and registration. It handles the creation of
        all actors, including the supervisor, report, visualization, tool, and
        project actors, as well as the file actor pool.
    """

    def __init__(self, config: Dict[str, Any], output_dir: Path, project_path: Optional[Path] = None):
        """
        Initialize the actor system builder.

        Args:
            config: Configuration dictionary
            output_dir: Output directory for reports and visualizations
            project_path: Optional path to the project being analyzed
        """
        self.config = config
        self.output_dir = output_dir
        self.project_path = project_path
        self.actors: Dict[str, Actor] = {}
        self.file_actor_pool: Optional[ActorPool] = None
        self.project_actor: Optional[ProjectActor] = None
        self.report_actor: Optional[ReportActor] = None
        self.visualization_actor: Optional[VisualizationActor] = None

    def build(self) -> Dict[str, Actor]:
        """
        Build the actor system with all necessary actors.

        Returns:
            Dictionary of actor IDs to actor instances
        """
        try:
            # Reset the registry to ensure a clean state
            reset_registry()
            registry = get_registry()

            # Create state directory if it doesn't exist
            state_dir = self.output_dir / "state"
            os.makedirs(state_dir, exist_ok=True)

            # Build actors in the correct order
            self._build_supervisor_actor(registry, state_dir)
            self._build_report_actor(registry)
            self._build_visualization_actor(registry)
            self._build_tool_actor(registry)
            self._build_file_actor_pool(registry)
            self._build_project_actor(registry)

            return self.actors
        except Exception as e:
            import traceback
            logger.error(f"Error building actor system: {e}\n{traceback.format_exc()}")
            raise

    def _build_supervisor_actor(self, registry: Any, state_dir: Path) -> None:
        """
        Build the supervisor actor.

        Args:
            registry: Actor registry
            state_dir: Directory for state persistence
        """
        try:
            # Create the supervisor actor first
            supervisor = SupervisorActor(
                "supervisor_actor",
                state_dir=str(state_dir),
                actor_type="supervisor"  # Explicitly set actor_type
            )
            self.actors["supervisor_actor"] = supervisor
            registry.register_actor(
                actor_id="supervisor_actor",
                actor=supervisor,
                actor_type="supervisor",
                tags={"supervisor", "system"},
                capabilities={"supervision", "monitoring", "recovery"}
            )

            logger.info("Created supervisor actor")
        except Exception as e:
            import traceback
            logger.error(f"Error creating supervisor actor: {e}\n{traceback.format_exc()}")
            raise

    def _build_report_actor(self, registry: Any) -> None:
        """
        Build the report actor.

        Args:
            registry: Actor registry
        """
        # Create the report actor
        self.report_actor = ReportActor(
            actor_id="report_actor",
            output_dir=self.output_dir,
            supervisor_id="supervisor_actor",
            state_dir=str(self.output_dir / "state")
        )
        self.actors["report_actor"] = self.report_actor
        registry.register_actor(
            actor_id="report_actor",
            actor=self.report_actor,
            actor_type="report",
            tags={"report", "output"},
            capabilities={"reporting", "metrics_collection"}
        )

    def _build_visualization_actor(self, registry: Any) -> None:
        """
        Build the visualization actor.

        Args:
            registry: Actor registry
        """
        # Create the visualization actor
        self.visualization_actor = VisualizationActor(
            actor_id="visualization_actor",
            output_dir=self.output_dir,
            supervisor_id="supervisor_actor",
            state_dir=str(self.output_dir / "state")
        )
        self.actors["visualization_actor"] = self.visualization_actor
        registry.register_actor(
            actor_id="visualization_actor",
            actor=self.visualization_actor,
            actor_type="visualization",
            tags={"visualization", "output"},
            capabilities={"visualization", "chart_generation"}
        )

    def _build_tool_actor(self, registry: Any) -> None:
        """
        Build the tool actor.

        Args:
            registry: Actor registry
        """
        # Create the tool actor with configuration
        tool_config = self.config.get("tools", {})
        tool_actor = ToolActor(
            actor_id="tool_actor",
            tool_config=tool_config,
            supervisor_id="supervisor_actor",
            state_dir=str(self.output_dir / "state")
        )
        self.actors["tool_actor"] = tool_actor
        registry.register_actor(
            actor_id="tool_actor",
            actor=tool_actor,
            actor_type="tool",
            tags={"tool", "analysis"},
            capabilities={"code_analysis", "security_analysis", "quality_analysis"}
        )

    def _build_file_actor_pool(self, registry: Any) -> None:
        """
        Build the file actor pool.

        Args:
            registry: Actor registry
        """
        # Create file actor pool
        num_file_actors = self.config.get("num_file_actors", os.cpu_count() or 4)

        # Create a factory function for file actors
        def file_actor_factory(actor_id: str, **kwargs: Any) -> FileActor:
            # Create a temporary file path for initialization
            # The actual file path will be set when the actor is used
            temp_file_path = kwargs.get("file_path", Path("temp_file.py"))
            return FileActor(
                actor_id=actor_id,
                file_path=temp_file_path,
                project_metrics=self.project_actor.metrics if self.project_actor else None,
                supervisor_id="supervisor_actor"
            )

        # Create the file actor pool
        self.file_actor_pool = ActorPool(
            pool_id="file_actor_pool",
            actor_factory=file_actor_factory,
            min_size=max(1, num_file_actors // 2),
            max_size=num_file_actors * 2
        )

        # Create initial file actors
        for i in range(num_file_actors):
            # Create a temporary file path for initialization
            # The actual file path will be set when the actor is used
            temp_file_path = Path(f"temp_file_{i}.py")
            file_actor = FileActor(
                actor_id=f"file_actor_{i}",
                file_path=temp_file_path,
                project_metrics=self.project_actor.metrics if self.project_actor else None,
                supervisor_id="supervisor_actor"
            )
            self.actors[f"file_actor_{i}"] = file_actor
            registry.register_actor(
                actor_id=f"file_actor_{i}",
                actor=file_actor,
                actor_type="file",
                tags={"file", "analysis"},
                capabilities={"file_analysis", "code_parsing"}
            )

    def _build_project_actor(self, registry: Any) -> None:
        """
        Build the project actor.

        Args:
            registry: Actor registry
        """
        # Create the project actor last since it will coordinate with all other actors
        # Use a default path if project_path is not set yet
        project_path = Path(self.project_path) if self.project_path else Path(".")
        self.project_actor = ProjectActor(
            actor_id="project_actor",
            project_path=project_path,
            supervisor_id="supervisor_actor",
            state_dir=str(self.output_dir / "state")
        )
        self.actors["project_actor"] = self.project_actor
        registry.register_actor(
            actor_id="project_actor",
            actor=self.project_actor,
            actor_type="project",
            tags={"project", "coordination"},
            capabilities={"project_analysis", "coordination"}
        )

        # Set file actor pool for project actor
        if self.file_actor_pool and hasattr(self.project_actor, "set_file_actor_pool"):
            self.project_actor.set_file_actor_pool(self.file_actor_pool)
            logger.info("Set file actor pool for project actor")
