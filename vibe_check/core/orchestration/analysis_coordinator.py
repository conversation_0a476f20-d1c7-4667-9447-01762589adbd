"""
Analysis Coordinator Module
=======================

This module is responsible for coordinating the analysis of a project,
including preparing the context, executing the analysis, and handling
retries and recovery. It implements the CAW principle of adaptive execution
by dynamically adjusting the execution mode based on runtime conditions.

This module was extracted from the Orchestrator class to reduce complexity
and improve maintainability.
"""

import asyncio
import logging
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

from ..actor_system import ContextWave, Message, MessageType
from .execution_mode_manager import ExecutionMode, ExecutionModeManager

logger = logging.getLogger("vibe_check_analysis_coordinator")


class AnalysisCoordinator:
    """
    Coordinator for project analysis.
    
    This class is responsible for coordinating the analysis of a project,
    including preparing the context, executing the analysis, and handling
    retries and recovery. It implements the CAW principle of adaptive execution
    by dynamically adjusting the execution mode based on runtime conditions.
    
    Implementation:
        This class uses an adaptive execution approach with automatic retries
        and recovery. It monitors system stability and can switch between
        parallel and sequential execution modes based on runtime conditions.
        It also provides comprehensive logging and diagnostics.
    """
    
    def __init__(self, 
                config: Dict[str, Any],
                execution_mode_manager: ExecutionModeManager):
        """
        Initialize the analysis coordinator.
        
        Args:
            config: Configuration dictionary
            execution_mode_manager: Execution mode manager
        """
        self.config = config
        self.execution_mode_manager = execution_mode_manager
        
        # Initialize retry configuration
        self.max_retries = self.config.get("max_retries", 3)
        self.retry_delay = self.config.get("retry_delay", 5.0)  # seconds
        self.current_retry = 0
        
        # Initialize stability metrics
        self.stability_metrics = {
            "actor_failures": 0,
            "message_timeouts": 0,
            "recovery_attempts": 0,
            "consecutive_failures": 0,
            "consecutive_successes": 0,
            "error_categories": {}
        }
        
        # Set thresholds
        self.instability_threshold = self.config.get("instability_threshold", 10)
        self.stability_threshold = self.config.get("stability_threshold", 3)
        
        logger.info("Initialized analysis coordinator")
        
    async def coordinate_analysis(self, 
                                project_path: Union[str, Path],
                                project_actor: Any,
                                context: Optional[Dict[str, Any]] = None,
                                completion_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        Coordinate the analysis of a project.
        
        This method implements an adaptive execution approach with automatic
        retries and recovery. It monitors system stability and can switch between
        parallel and sequential execution modes based on runtime conditions.
        
        Args:
            project_path: Path to the project directory
            project_actor: Project actor to send the analysis message to
            context: Optional context metadata to initialize the analysis
            completion_callback: Optional callback to call when analysis completes
            
        Returns:
            Analysis results or error information
        """
        logger.info(f"Coordinating analysis of project: {project_path}")
        
        # Reset retry counter
        self.current_retry = 0
        
        # Initialize context if not provided
        context = context or {}
        
        # Try to analyze the project with retries
        while self.current_retry <= self.max_retries:
            try:
                # Prepare context wave
                context_wave = await self._prepare_context_wave(context)
                
                # Check instability score and decide on execution mode
                instability_score = self._calculate_instability_score()
                logger.info(f"Current system instability score: {instability_score}")
                
                # Get current execution mode
                execution_mode = self.execution_mode_manager.get_execution_mode()
                
                # Execute analysis based on current mode
                if execution_mode == ExecutionMode.PARALLEL:
                    # Execute in parallel mode
                    result = await self._execute_analysis_parallel(
                        project_path, 
                        project_actor, 
                        context_wave,
                        completion_callback
                    )
                else:
                    # Execute in sequential mode
                    result = await self._execute_analysis_sequential(
                        project_path, 
                        context_wave
                    )
                
                # Check result for error
                if isinstance(result, dict) and "error" in result:
                    error_msg = result.get("error", "Unknown error")
                    if error_msg:  # Only raise if there's an actual error message
                        raise RuntimeError(f"Analysis error: {error_msg}")
                
                # Success - reset consecutive failures
                self._record_success()
                
                return result
                
            except Exception as e:
                # Record failure
                self._record_failure(str(type(e).__name__))
                
                # Increment retry counter
                self.current_retry += 1
                
                if self.current_retry <= self.max_retries:
                    # Calculate retry delay with exponential backoff
                    retry_delay = self.retry_delay * (2 ** (self.current_retry - 1))
                    
                    logger.warning(f"Analysis failed (attempt {self.current_retry}/{self.max_retries}): {e}")
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    
                    # Record recovery attempt
                    self._record_recovery_attempt()
                    
                    # Switch to sequential mode if in parallel mode
                    if self.execution_mode_manager.get_execution_mode() == ExecutionMode.PARALLEL:
                        self.execution_mode_manager.set_execution_mode(ExecutionMode.SEQUENTIAL)
                    
                    # Wait before retrying
                    await asyncio.sleep(retry_delay)
                else:
                    logger.error(f"Analysis failed after {self.max_retries} attempts: {e}")
                    return {"error": f"Analysis failed after {self.max_retries} attempts: {e}"}
        
        # This should never be reached, but just in case
        return {"error": "Analysis failed for unknown reasons"}
    
    async def _prepare_context_wave(self, context: Dict[str, Any]) -> ContextWave:
        """
        Prepare a context wave for the analysis.
        
        Args:
            context: Context metadata
            
        Returns:
            Context wave
        """
        # Create a context wave with the provided context
        context_wave = ContextWave()
        
        # Add context metadata
        for key, value in context.items():
            context_wave.metadata[key] = value
        
        # Add system metadata
        context_wave.metadata["timestamp"] = time.time()
        context_wave.metadata["execution_mode"] = self.execution_mode_manager.get_execution_mode()
        
        return context_wave
    
    async def _execute_analysis_parallel(self, 
                                       project_path: Union[str, Path],
                                       project_actor: Any,
                                       context_wave: ContextWave,
                                       completion_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        Execute analysis in parallel mode.
        
        Args:
            project_path: Path to the project directory
            project_actor: Project actor to send the analysis message to
            context_wave: Context wave
            completion_callback: Optional callback to call when analysis completes
            
        Returns:
            Analysis results or error information
        """
        logger.info("Executing analysis in parallel mode")
        
        # Create analysis future to wait for completion
        analysis_complete = asyncio.Future()
        
        # Create the callback
        def _analysis_completed(result: Dict[str, Any]) -> None:
            if not analysis_complete.done():
                analysis_complete.set_result(result)
            if completion_callback:
                completion_callback(result)
        
        # Create the message payload
        payload = {
            "project_path": str(project_path),
            "completion_callback": _analysis_completed
        }
        
        # Create the message
        message = Message(
            message_type=MessageType.COMMAND,
            sender="orchestrator",
            receiver=project_actor.actor_id,
            payload=payload,
            context=context_wave
        )
        
        # Send the message
        await project_actor.receive_message(message)
        
        # Wait for analysis to complete
        try:
            result = await asyncio.wait_for(
                analysis_complete,
                timeout=self.config.get("analysis_timeout", 3600.0)  # 1 hour default
            )
            return result
        except asyncio.TimeoutError:
            logger.error("Analysis timed out")
            return {"error": "Analysis timed out"}
    
    async def _execute_analysis_sequential(self, 
                                         project_path: Union[str, Path],
                                         context_wave: ContextWave) -> Dict[str, Any]:
        """
        Execute analysis in sequential mode.
        
        Args:
            project_path: Path to the project directory
            context_wave: Context wave
            
        Returns:
            Analysis results or error information
        """
        logger.info("Executing analysis in sequential mode")
        
        # In sequential mode, we use the simple analyzer
        from ...core.simple_analyzer import simple_analyze_project
        
        # Convert context wave to dictionary
        context_dict = dict(context_wave.metadata)
        
        # Run the simple analyzer
        try:
            metrics = simple_analyze_project(
                project_path=project_path,
                config=self.config
            )
            
            return {"metrics": metrics}
        except Exception as e:
            logger.error(f"Error in sequential analysis: {e}")
            return {"error": f"Sequential analysis failed: {e}"}
    
    def _calculate_instability_score(self) -> float:
        """
        Calculate the instability score of the system.
        
        Returns:
            Instability score (0-100)
        """
        # Calculate instability score based on stability metrics
        score = 0.0
        
        # Add points for consecutive failures
        score += self.stability_metrics["consecutive_failures"] * 10.0
        
        # Add points for actor failures
        score += self.stability_metrics["actor_failures"] * 5.0
        
        # Add points for message timeouts
        score += self.stability_metrics["message_timeouts"] * 2.0
        
        # Add points for recovery attempts
        score += self.stability_metrics["recovery_attempts"] * 3.0
        
        # Subtract points for consecutive successes
        score -= self.stability_metrics["consecutive_successes"] * 5.0
        
        # Ensure score is between 0 and 100
        return max(0.0, min(100.0, score))
    
    def _record_failure(self, error_category: str) -> None:
        """
        Record a failure in the stability metrics.
        
        Args:
            error_category: Category of the error
        """
        # Increment consecutive failures
        self.stability_metrics["consecutive_failures"] += 1
        
        # Reset consecutive successes
        self.stability_metrics["consecutive_successes"] = 0
        
        # Increment actor failures if it's an actor error
        if "Actor" in error_category:
            self.stability_metrics["actor_failures"] += 1
        
        # Increment message timeouts if it's a timeout error
        if "Timeout" in error_category:
            self.stability_metrics["message_timeouts"] += 1
        
        # Record error category
        if error_category not in self.stability_metrics["error_categories"]:
            self.stability_metrics["error_categories"][error_category] = 0
        self.stability_metrics["error_categories"][error_category] += 1
    
    def _record_success(self) -> None:
        """Record a success in the stability metrics."""
        # Increment consecutive successes
        self.stability_metrics["consecutive_successes"] += 1
        
        # Reset consecutive failures
        self.stability_metrics["consecutive_failures"] = 0
    
    def _record_recovery_attempt(self) -> None:
        """Record a recovery attempt in the stability metrics."""
        # Increment recovery attempts
        self.stability_metrics["recovery_attempts"] += 1
