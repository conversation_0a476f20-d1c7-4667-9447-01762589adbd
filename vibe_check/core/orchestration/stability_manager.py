"""
Stability Manager Module
========================

This module provides functionality for managing system stability and recovery
in the Vibe Check system. It tracks failures, calculates instability scores,
and manages execution mode switching.
"""

import asyncio
import logging
import time
from typing import Any, Dict

from ..actor_system import Actor, Message, MessageType

logger = logging.getLogger("vibe_check_stability_manager")


class StabilityManager:
    """
    Manages system stability metrics and recovery mechanisms.
    
    This class tracks failures, calculates instability scores,
    and manages automatic switching between execution modes
    based on system stability.
    """

    def __init__(self, actors: Dict[str, Actor], config: Dict[str, Any]):
        """
        Initialize the stability manager.

        Args:
            actors: Dictionary of actor IDs to actor instances
            config: Configuration dictionary
        """
        self.actors = actors
        self.config = config

        # Initialize stability metrics
        self.stability_metrics: Dict[str, Any] = {
            "actor_failures": 0,
            "message_timeouts": 0,
            "recovery_attempts": 0,
            "last_mode_switch_time": 0,
            "consecutive_failures": 0,
            "error_categories": {}
        }

        # Get thresholds from config
        self.instability_threshold = config.get("instability_threshold", 10)
        self.stability_threshold = config.get("stability_threshold", 3)

        # Current execution mode
        self.execution_mode = config.get("execution_mode", "parallel")

    def record_failure(self, error_type: str) -> None:
        """
        Record a failure in the stability metrics.

        Args:
            error_type: Type of error that occurred
        """
        try:
            # Ensure stability_metrics is initialized
            if not isinstance(self.stability_metrics, dict):
                self.stability_metrics = {}

            # Increment failure counters
            actor_failures = self.stability_metrics.get("actor_failures", 0)
            consecutive_failures = self.stability_metrics.get("consecutive_failures", 0)
            error_categories = self.stability_metrics.get("error_categories", {})

            self.stability_metrics["actor_failures"] = actor_failures + 1
            self.stability_metrics["consecutive_failures"] = consecutive_failures + 1

            # Track error categories
            if error_type in error_categories:
                error_categories[error_type] += 1
            else:
                error_categories[error_type] = 1

            self.stability_metrics["error_categories"] = error_categories

            logger.debug(f"Recorded failure of type {error_type}. Total failures: {actor_failures + 1}")
        except Exception as e:
            logger.error(f"Error recording failure: {e}")

    def record_recovery_attempt(self) -> None:
        """Record a recovery attempt in the stability metrics."""
        try:
            # Ensure stability_metrics is initialized
            if not isinstance(self.stability_metrics, dict):
                self.stability_metrics = {}

            recovery_attempts = self.stability_metrics.get("recovery_attempts", 0)
            self.stability_metrics["recovery_attempts"] = recovery_attempts + 1
            logger.debug(f"Recorded recovery attempt. Total attempts: {recovery_attempts + 1}")
        except Exception as e:
            logger.error(f"Error recording recovery attempt: {e}")

    def record_success(self) -> None:
        """Record a successful operation in the stability metrics."""
        try:
            # Ensure stability_metrics is initialized
            if not isinstance(self.stability_metrics, dict):
                self.stability_metrics = {}

            self.stability_metrics["consecutive_failures"] = 0
            logger.debug("Reset consecutive failures counter")
        except Exception as e:
            logger.error(f"Error recording success: {e}")

    def calculate_instability_score(self) -> float:
        """
        Calculate a numeric score representing system instability.

        Returns:
            Instability score (higher means more unstable)
        """
        try:
            # Ensure stability_metrics is initialized
            if not isinstance(self.stability_metrics, dict):
                self.stability_metrics = {}

            actor_failures = self.stability_metrics.get("actor_failures", 0)
            message_timeouts = self.stability_metrics.get("message_timeouts", 0)
            consecutive_failures = self.stability_metrics.get("consecutive_failures", 0)
            recovery_attempts = self.stability_metrics.get("recovery_attempts", 0)

            # Calculate base score
            base_score = (
                actor_failures * 2.0 +
                message_timeouts * 1.5 +
                consecutive_failures * 3.0 +
                recovery_attempts * 1.0
            )

            # Apply time decay (recent failures are weighted more heavily)
            current_time = time.time()
            last_mode_switch_time = self.stability_metrics.get("last_mode_switch_time", 0)
            time_since_last_switch = current_time - last_mode_switch_time

            # If it's been a while since the last mode switch, reduce the score
            if time_since_last_switch > 300:  # 5 minutes
                base_score *= 0.8

            return float(base_score)
        except Exception as e:
            logger.error(f"Error calculating instability score: {e}")
            return 0.0

    async def switch_execution_mode(self, new_mode: str) -> None:
        """
        Switch the execution mode of the actor system.

        Args:
            new_mode: New execution mode ("parallel" or "sequential")
        """
        if new_mode == self.execution_mode:
            return

        logger.info(f"Switching execution mode from {self.execution_mode} to {new_mode}")

        # Update the last mode switch time
        self.stability_metrics["last_mode_switch_time"] = time.time()

        # Update the execution mode
        self.execution_mode = new_mode

        # Notify all actors about the mode change
        await self.notify_actors_of_mode_change(new_mode)

    async def notify_actors_of_mode_change(self, new_mode: str) -> None:
        """
        Notify all actors about the execution mode change.

        Args:
            new_mode: New execution mode ("parallel" or "sequential")
        """
        logger.info(f"Notifying {len(self.actors)} actors of mode change to {new_mode}")

        for actor in self.actors.values():
            try:
                # Create mode change message
                message = Message(
                    sender_id="orchestrator",
                    receiver_id=actor.actor_id,
                    message_type=MessageType.COMMAND,
                    content={
                        "command": "set_execution_mode",
                        "mode": new_mode
                    }
                )

                # Send the message asynchronously
                asyncio.create_task(actor.receive(message))

            except Exception as e:
                logger.error(f"Failed to notify actor {actor.actor_id} of mode change: {e}")

    def should_switch_to_sequential(self) -> bool:
        """
        Determine if the system should switch to sequential mode.

        Returns:
            True if should switch to sequential mode, False otherwise
        """
        if self.execution_mode == "sequential":
            return False

        instability_score = self.calculate_instability_score()
        return instability_score >= self.instability_threshold

    def should_switch_to_parallel(self) -> bool:
        """
        Determine if the system should switch to parallel mode.

        Returns:
            True if should switch to parallel mode, False otherwise
        """
        if self.execution_mode == "parallel":
            return False

        consecutive_failures = self.stability_metrics.get("consecutive_failures", 0)
        return consecutive_failures <= self.stability_threshold

    async def check_and_adjust_execution_mode(self) -> None:
        """
        Check system stability and adjust execution mode if necessary.
        """
        try:
            if self.should_switch_to_sequential():
                await self.switch_execution_mode("sequential")
            elif self.should_switch_to_parallel():
                await self.switch_execution_mode("parallel")
        except Exception as e:
            logger.error(f"Error checking and adjusting execution mode: {e}")

    def get_stability_report(self) -> Dict[str, Any]:
        """
        Get a comprehensive stability report.

        Returns:
            Dictionary containing stability metrics and analysis
        """
        instability_score = self.calculate_instability_score()
        
        return {
            "metrics": self.stability_metrics.copy(),
            "instability_score": instability_score,
            "current_execution_mode": self.execution_mode,
            "recommendations": {
                "should_switch_to_sequential": self.should_switch_to_sequential(),
                "should_switch_to_parallel": self.should_switch_to_parallel()
            }
        }
