"""
Trend Visualizer
============

This module provides functionality for visualizing trends in project metrics over time.
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from ...ui.visualization.interactive_charts import create_interactive_chart, export_interactive_chart


class TrendVisualizer:
    """
    Visualizer for project metrics trends.

    This class provides functionality for visualizing trends in project metrics over time.
    """

    def __init__(self, output_dir: Union[str, Path] = None):
        """
        Initialize the trend visualizer.

        Args:
            output_dir: Directory for storing visualizations
        """
        self.output_dir = Path(output_dir) if output_dir else None

    def visualize_trends(self, trend_data: Dict[str, Any],
                        output_dir: Optional[Union[str, Path]] = None) -> Dict[str, str]:
        """
        Visualize trends in project metrics.

        Args:
            trend_data: Trend analysis data
            output_dir: Optional output directory (overrides the one set in the constructor)

        Returns:
            Dictionary mapping chart names to file paths
        """
        # Use the provided output directory or the one set in the constructor
        output_dir = Path(output_dir) if output_dir else self.output_dir

        if output_dir is None:
            raise ValueError("Output directory must be provided")

        # Create the output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Check if we have historical data
        if not trend_data.get("has_historical_data", False) and not trend_data.get("has_progress_data", False):
            return {}

        # Generate trend charts
        charts = {}

        # Issue count trend
        if "issue_count" in trend_data["historical_trends"]:
            issue_trend = trend_data["historical_trends"]["issue_count"]
            charts["issue_trend"] = self._create_trend_chart(
                "Issue Count Trend",
                issue_trend["dates"],
                issue_trend["values"],
                "Issues",
                "line"
            )

        # Complexity trend
        if "avg_complexity" in trend_data["historical_trends"]:
            complexity_trend = trend_data["historical_trends"]["avg_complexity"]
            charts["complexity_trend"] = self._create_trend_chart(
                "Average Complexity Trend",
                complexity_trend["dates"],
                complexity_trend["values"],
                "Complexity",
                "line"
            )

        # Type coverage trend
        if "avg_type_coverage" in trend_data["historical_trends"]:
            type_coverage_trend = trend_data["historical_trends"]["avg_type_coverage"]
            charts["type_coverage_trend"] = self._create_trend_chart(
                "Type Coverage Trend",
                type_coverage_trend["dates"],
                type_coverage_trend["values"],
                "Coverage (%)",
                "line"
            )

        # Docstring coverage trend
        if "avg_docstring_coverage" in trend_data["historical_trends"]:
            doc_coverage_trend = trend_data["historical_trends"]["avg_docstring_coverage"]
            charts["doc_coverage_trend"] = self._create_trend_chart(
                "Documentation Coverage Trend",
                doc_coverage_trend["dates"],
                doc_coverage_trend["values"],
                "Coverage (%)",
                "line"
            )

        # Metric changes chart
        if "changes" in trend_data:
            charts["metric_changes"] = self._create_changes_chart(trend_data["changes"])

        # Issue severity changes chart
        if "severity_changes" in trend_data:
            charts["severity_changes"] = self._create_severity_changes_chart(trend_data["severity_changes"])

        # Export charts
        result = {}
        for chart_name, chart_spec in charts.items():
            output_path = os.path.join(output_dir, f"{chart_name}.html")
            result[chart_name] = export_interactive_chart(chart_spec, output_path)

        # Generate dashboard
        if trend_data.get("has_progress_data", False):
            dashboard_path = os.path.join(output_dir, "progress_dashboard.html")
        else:
            dashboard_path = os.path.join(output_dir, "trend_dashboard.html")

        result["dashboard"] = self._generate_trend_dashboard(charts, trend_data, dashboard_path)

        return result

    def visualize_progress(self, progress_data: Dict[str, Any],
                         output_dir: Optional[Union[str, Path]] = None) -> Dict[str, str]:
        """
        Visualize progress for a specific output directory.

        Args:
            progress_data: Progress analysis data
            output_dir: Optional output directory (overrides the one set in the constructor)

        Returns:
            Dictionary mapping chart names to file paths
        """
        # This is just a wrapper around visualize_trends that makes it clear
        # we're visualizing progress data
        return self.visualize_trends(progress_data, output_dir)

    def _create_trend_chart(self, title: str, dates: List[str], values: List[float],
                          y_axis_label: str, chart_type: str = "line") -> Dict[str, Any]:
        """
        Create a trend chart.

        Args:
            title: Chart title
            dates: List of dates
            values: List of values
            y_axis_label: Y-axis label
            chart_type: Chart type

        Returns:
            Chart specification
        """
        # Format dates for display
        formatted_dates = []
        for date_str in dates:
            try:
                date = datetime.fromisoformat(date_str)
                formatted_dates.append(date.strftime("%Y-%m-%d %H:%M"))
            except (ValueError, TypeError):
                formatted_dates.append(str(date_str))

        # Create chart data
        data = {
            "labels": formatted_dates,
            "datasets": [
                {
                    "label": y_axis_label,
                    "data": values,
                    "backgroundColor": "rgba(54, 162, 235, 0.5)",
                    "borderColor": "rgba(54, 162, 235, 1)",
                    "borderWidth": 1,
                    "tension": 0.1  # Smooth the line
                }
            ]
        }

        # Create chart options
        options = {
            "plugins": {
                "title": {
                    "display": True,
                    "text": title
                }
            },
            "scales": {
                "y": {
                    "title": {
                        "display": True,
                        "text": y_axis_label
                    }
                },
                "x": {
                    "title": {
                        "display": True,
                        "text": "Date"
                    }
                }
            }
        }

        # Create chart specification
        return create_interactive_chart(chart_type, data, options)

    def _create_changes_chart(self, changes: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a chart showing changes in metrics.

        Args:
            changes: Dictionary with metric changes

        Returns:
            Chart specification
        """
        # Define metrics to include and their display names
        metrics_to_include = {
            "avg_complexity": "Avg Complexity",
            "max_complexity": "Max Complexity",
            "issue_count": "Issue Count",
            "avg_type_coverage": "Type Coverage",
            "avg_docstring_coverage": "Doc Coverage"
        }

        # Extract data
        labels = []
        current_values = []
        previous_values = []

        for metric_key, display_name in metrics_to_include.items():
            if metric_key in changes:
                labels.append(display_name)
                current_values.append(changes[metric_key]["current"])
                previous_values.append(changes[metric_key]["previous"])

        # Create chart data
        data = {
            "labels": labels,
            "datasets": [
                {
                    "label": "Current",
                    "data": current_values,
                    "backgroundColor": "rgba(54, 162, 235, 0.5)",
                    "borderColor": "rgba(54, 162, 235, 1)",
                    "borderWidth": 1
                },
                {
                    "label": "Previous",
                    "data": previous_values,
                    "backgroundColor": "rgba(255, 99, 132, 0.5)",
                    "borderColor": "rgba(255, 99, 132, 1)",
                    "borderWidth": 1
                }
            ]
        }

        # Create chart options
        options = {
            "plugins": {
                "title": {
                    "display": True,
                    "text": "Metric Changes"
                }
            },
            "scales": {
                "y": {
                    "beginAtZero": True,
                    "title": {
                        "display": True,
                        "text": "Value"
                    }
                }
            }
        }

        # Create chart specification
        return create_interactive_chart("bar", data, options)

    def _create_severity_changes_chart(self, severity_changes: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a chart showing changes in issue severity.

        Args:
            severity_changes: Dictionary with issue severity changes

        Returns:
            Chart specification
        """
        # Extract data
        labels = []
        current_values = []
        previous_values = []

        # Define the order of severities
        severities = ["HIGH", "MEDIUM", "LOW", "INFO"]

        for severity in severities:
            if severity in severity_changes:
                labels.append(severity)
                current_values.append(severity_changes[severity]["current"])
                previous_values.append(severity_changes[severity]["previous"])

        # Create chart data
        data = {
            "labels": labels,
            "datasets": [
                {
                    "label": "Current",
                    "data": current_values,
                    "backgroundColor": "rgba(54, 162, 235, 0.5)",
                    "borderColor": "rgba(54, 162, 235, 1)",
                    "borderWidth": 1
                },
                {
                    "label": "Previous",
                    "data": previous_values,
                    "backgroundColor": "rgba(255, 99, 132, 0.5)",
                    "borderColor": "rgba(255, 99, 132, 1)",
                    "borderWidth": 1
                }
            ]
        }

        # Create chart options
        options = {
            "plugins": {
                "title": {
                    "display": True,
                    "text": "Issue Severity Changes"
                }
            },
            "scales": {
                "y": {
                    "beginAtZero": True,
                    "title": {
                        "display": True,
                        "text": "Count"
                    }
                }
            }
        }

        # Create chart specification
        return create_interactive_chart("bar", data, options)

    def _generate_trend_dashboard(self, charts: Dict[str, Any],
                                trend_data: Dict[str, Any],
                                output_path: Union[str, Path]) -> str:
        """
        Generate a dashboard with trend visualizations.

        Args:
            charts: Dictionary mapping chart names to chart specifications
            trend_data: Trend analysis data
            output_path: Output file path

        Returns:
            Path to the generated dashboard
        """
        # Create the output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Convert chart specifications to JSON
        charts_json = json.dumps(charts)

        # Get the trend summary
        if trend_data.get("has_progress_data", False):
            # This is progress data for a specific output directory
            progress_data = trend_data.get("progress", {})
            summary = progress_data.get("summary", "No progress summary available.")
            title = "Vibe Check Progress Dashboard"
            subtitle = f"Output Directory: {progress_data.get('output_dir', 'Unknown')}"
            subtitle += f" | Run Count: {progress_data.get('run_count', 0)}"
            subtitle += f" | First Run: {progress_data.get('first_run_date', 'Unknown')}"
        else:
            # This is regular trend data
            summary = trend_data.get("summary", "No trend summary available.")
            title = "Vibe Check Trend Dashboard"
            subtitle = ""

        # Generate HTML
        html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{title}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .dashboard {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }}
        .chart-container {{ border: 1px solid #ddd; padding: 10px; height: 400px; }}
        .full-width {{ grid-column: 1 / span 2; }}
        h1, h2, h3 {{ text-align: center; }}
        .summary {{ padding: 15px; background-color: #f8f9fa; border-radius: 5px; margin-bottom: 20px; }}
        .subtitle {{ text-align: center; color: #666; margin-top: -10px; margin-bottom: 20px; }}
    </style>
</head>
<body>
    <h1>{title}</h1>
    {f'<p class="subtitle">{subtitle}</p>' if subtitle else ''}

    <div class="summary full-width">
        <h3>{'Progress' if trend_data.get('has_progress_data', False) else 'Trend'} Summary</h3>
        <p>{summary}</p>
    </div>

    <div class="dashboard">
        <div class="chart-container">
            <h3>Issue Count Trend</h3>
            <canvas id="issue-trend-chart"></canvas>
        </div>
        <div class="chart-container">
            <h3>Complexity Trend</h3>
            <canvas id="complexity-trend-chart"></canvas>
        </div>
        <div class="chart-container">
            <h3>Type Coverage Trend</h3>
            <canvas id="type-coverage-trend-chart"></canvas>
        </div>
        <div class="chart-container">
            <h3>Documentation Coverage Trend</h3>
            <canvas id="doc-coverage-trend-chart"></canvas>
        </div>
        <div class="chart-container">
            <h3>Metric Changes</h3>
            <canvas id="metric-changes-chart"></canvas>
        </div>
        <div class="chart-container">
            <h3>Issue Severity Changes</h3>
            <canvas id="severity-changes-chart"></canvas>
        </div>
    </div>

    <script>
        // Chart specifications
        const charts = {charts_json};

        // Create charts
        if (charts.issue_trend) {{
            new Chart(document.getElementById('issue-trend-chart').getContext('2d'), charts.issue_trend);
        }}

        if (charts.complexity_trend) {{
            new Chart(document.getElementById('complexity-trend-chart').getContext('2d'), charts.complexity_trend);
        }}

        if (charts.type_coverage_trend) {{
            new Chart(document.getElementById('type-coverage-trend-chart').getContext('2d'), charts.type_coverage_trend);
        }}

        if (charts.doc_coverage_trend) {{
            new Chart(document.getElementById('doc-coverage-trend-chart').getContext('2d'), charts.doc_coverage_trend);
        }}

        if (charts.metric_changes) {{
            new Chart(document.getElementById('metric-changes-chart').getContext('2d'), charts.metric_changes);
        }}

        if (charts.severity_changes) {{
            new Chart(document.getElementById('severity-changes-chart').getContext('2d'), charts.severity_changes);
        }}
    </script>
</body>
</html>
"""

        # Write HTML to file
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(html)

        return str(output_path)
