"""
Trend Analyzer
===========

This module provides functionality for analyzing trends in project metrics over time.
"""

from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

from ..models.project_metrics import ProjectMetrics
from .trend_storage import TrendStorage


class TrendAnalyzer:
    """
    Analyzer for project metrics trends.

    This class provides functionality for analyzing trends in project metrics over time.
    """

    def __init__(self, storage: Optional[TrendStorage] = None):
        """
        Initialize the trend analyzer.

        Args:
            storage: Optional TrendStorage instance
        """
        self.storage = storage or TrendStorage()

    def analyze_trends(self, metrics: ProjectMetrics,
                     historical_limit: int = 5,
                     output_dir: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
        """
        Analyze trends in project metrics.

        Args:
            metrics: Current project metrics
            historical_limit: Maximum number of historical metrics to analyze
            output_dir: Optional output directory used for analysis

        Returns:
            Dictionary with trend analysis results
        """
        # Get historical metrics
        historical_metrics = self.storage.get_historical_metrics(
            metrics.project_path, limit=historical_limit
        )

        if not historical_metrics:
            # No historical data, store current metrics for future analysis
            self.storage.store_metrics(metrics, output_dir=output_dir)
            return {
                "has_historical_data": False,
                "message": "No historical data available. Current metrics stored for future analysis."
            }

        # Store current metrics for future analysis
        self.storage.store_metrics(metrics, output_dir=output_dir)

        # Convert current metrics to a dictionary
        current_metrics_dict = self.storage._metrics_to_dict(metrics, output_dir)

        # Analyze trends
        trends = self._calculate_trends(current_metrics_dict, historical_metrics)

        return {
            "has_historical_data": True,
            "trends": trends,
            "historical_data": historical_metrics,
            "current_data": current_metrics_dict
        }

    def _calculate_trends(self, current_metrics: Dict[str, Any],
                        historical_metrics: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate trends in project metrics.

        Args:
            current_metrics: Current project metrics dictionary
            historical_metrics: List of historical metrics dictionaries

        Returns:
            Dictionary with trend analysis results
        """
        # Get the most recent historical metrics
        previous_metrics = historical_metrics[0]

        # Calculate changes
        changes = {}
        for key in ["total_file_count", "total_line_count", "avg_complexity",
                   "max_complexity", "issue_count", "avg_type_coverage",
                   "avg_docstring_coverage", "directory_count"]:
            if key in current_metrics and key in previous_metrics:
                current_value = current_metrics[key]
                previous_value = previous_metrics[key]

                if isinstance(current_value, (int, float)) and isinstance(previous_value, (int, float)):
                    absolute_change = current_value - previous_value

                    if previous_value != 0:
                        percentage_change = (absolute_change / previous_value) * 100
                    else:
                        percentage_change = 0 if absolute_change == 0 else float('inf')

                    changes[key] = {
                        "current": current_value,
                        "previous": previous_value,
                        "absolute_change": absolute_change,
                        "percentage_change": percentage_change,
                        "trend": self._determine_trend(key, absolute_change)
                    }

        # Calculate issue severity changes
        current_issues_by_severity = current_metrics.get("issues_by_severity", {})
        previous_issues_by_severity = previous_metrics.get("issues_by_severity", {})

        severity_changes = {}
        for severity in set(current_issues_by_severity.keys()) | set(previous_issues_by_severity.keys()):
            current_count = current_issues_by_severity.get(severity, 0)
            previous_count = previous_issues_by_severity.get(severity, 0)

            absolute_change = current_count - previous_count

            if previous_count != 0:
                percentage_change = (absolute_change / previous_count) * 100
            else:
                percentage_change = 0 if absolute_change == 0 else float('inf')

            severity_changes[severity] = {
                "current": current_count,
                "previous": previous_count,
                "absolute_change": absolute_change,
                "percentage_change": percentage_change,
                "trend": "positive" if absolute_change < 0 else "negative" if absolute_change > 0 else "neutral"
            }

        # Calculate historical trends
        historical_trends = self._calculate_historical_trends(current_metrics, historical_metrics)

        # Generate summary
        summary = self._generate_trend_summary(changes, severity_changes)

        return {
            "changes": changes,
            "severity_changes": severity_changes,
            "historical_trends": historical_trends,
            "summary": summary,
            "last_analysis_date": previous_metrics.get("date", "Unknown")
        }

    def _determine_trend(self, metric_key: str, change: float) -> str:
        """
        Determine the trend direction for a metric.

        Args:
            metric_key: Metric key
            change: Absolute change in the metric

        Returns:
            Trend direction ("positive", "negative", or "neutral")
        """
        # For most metrics, a decrease is positive
        positive_when_decreasing = [
            "avg_complexity", "max_complexity", "issue_count"
        ]

        # For some metrics, an increase is positive
        positive_when_increasing = [
            "avg_type_coverage", "avg_docstring_coverage"
        ]

        # For some metrics, any change is neutral
        neutral_metrics = [
            "total_file_count", "total_line_count", "directory_count"
        ]

        if metric_key in neutral_metrics:
            return "neutral"
        elif metric_key in positive_when_decreasing:
            return "positive" if change < 0 else "negative" if change > 0 else "neutral"
        elif metric_key in positive_when_increasing:
            return "positive" if change > 0 else "negative" if change < 0 else "neutral"
        else:
            return "neutral"

    def _calculate_historical_trends(self, current_metrics: Dict[str, Any],
                                   historical_metrics: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate historical trends in project metrics.

        Args:
            current_metrics: Current project metrics dictionary
            historical_metrics: List of historical metrics dictionaries

        Returns:
            Dictionary with historical trend analysis results
        """
        # Add current metrics to the historical data for trend calculation
        all_metrics = [current_metrics] + historical_metrics

        # Calculate trends for key metrics
        trends = {}
        for key in ["total_file_count", "total_line_count", "avg_complexity",
                   "issue_count", "avg_type_coverage", "avg_docstring_coverage"]:
            values = []
            dates = []

            for metrics in all_metrics:
                if key in metrics and "date" in metrics:
                    values.append(metrics[key])
                    dates.append(metrics["date"])

            if values and dates:
                trends[key] = {
                    "values": values,
                    "dates": dates
                }

        return trends

    def _generate_trend_summary(self, changes: Dict[str, Any],
                              severity_changes: Dict[str, Any]) -> str:
        """
        Generate a summary of the trend analysis.

        Args:
            changes: Dictionary with metric changes
            severity_changes: Dictionary with issue severity changes

        Returns:
            Summary string
        """
        summary_parts = []

        # Check for significant changes in key metrics
        if "issue_count" in changes:
            issue_change = changes["issue_count"]
            if issue_change["absolute_change"] < 0:
                summary_parts.append(f"Issues decreased by {abs(issue_change['absolute_change'])} ({abs(issue_change['percentage_change']):.1f}%).")
            elif issue_change["absolute_change"] > 0:
                summary_parts.append(f"Issues increased by {issue_change['absolute_change']} ({issue_change['percentage_change']:.1f}%).")

        if "avg_complexity" in changes:
            complexity_change = changes["avg_complexity"]
            if complexity_change["absolute_change"] < -0.5:
                summary_parts.append(f"Average complexity decreased by {abs(complexity_change['absolute_change']):.1f} ({abs(complexity_change['percentage_change']):.1f}%).")
            elif complexity_change["absolute_change"] > 0.5:
                summary_parts.append(f"Average complexity increased by {complexity_change['absolute_change']:.1f} ({complexity_change['percentage_change']:.1f}%).")

        if "avg_type_coverage" in changes:
            type_coverage_change = changes["avg_type_coverage"]
            if type_coverage_change["absolute_change"] > 5:
                summary_parts.append(f"Type coverage improved by {type_coverage_change['absolute_change']:.1f} percentage points.")
            elif type_coverage_change["absolute_change"] < -5:
                summary_parts.append(f"Type coverage decreased by {abs(type_coverage_change['absolute_change']):.1f} percentage points.")

        if "avg_docstring_coverage" in changes:
            doc_coverage_change = changes["avg_docstring_coverage"]
            if doc_coverage_change["absolute_change"] > 5:
                summary_parts.append(f"Documentation coverage improved by {doc_coverage_change['absolute_change']:.1f} percentage points.")
            elif doc_coverage_change["absolute_change"] < -5:
                summary_parts.append(f"Documentation coverage decreased by {abs(doc_coverage_change['absolute_change']):.1f} percentage points.")

        # Check for changes in high severity issues
        if "HIGH" in severity_changes:
            high_severity_change = severity_changes["HIGH"]
            if high_severity_change["absolute_change"] < 0:
                summary_parts.append(f"High severity issues decreased by {abs(high_severity_change['absolute_change'])}.")
            elif high_severity_change["absolute_change"] > 0:
                summary_parts.append(f"High severity issues increased by {high_severity_change['absolute_change']}.")

        # Generate overall assessment
        positive_trends = sum(1 for change in changes.values() if change["trend"] == "positive")
        negative_trends = sum(1 for change in changes.values() if change["trend"] == "negative")

        if positive_trends > negative_trends:
            overall = "Overall, the codebase quality has improved since the last analysis."
        elif negative_trends > positive_trends:
            overall = "Overall, the codebase quality has declined since the last analysis."
        else:
            overall = "Overall, the codebase quality has remained stable since the last analysis."

        summary_parts.append(overall)

        return " ".join(summary_parts)

    def analyze_output_dir_progress(self, metrics: ProjectMetrics,
                                  output_dir: Union[str, Path]) -> Dict[str, Any]:
        """
        Analyze progress for a specific output directory.

        This is useful for tracking progress between analyses when the same output directory
        is used multiple times.

        Args:
            metrics: Current project metrics
            output_dir: Output directory used for analysis

        Returns:
            Dictionary with progress analysis results
        """
        # Get metrics for the output directory
        output_dir_metrics = self.storage.get_metrics_for_output_dir(
            metrics.project_path, output_dir
        )

        # If we have less than 2 metrics, we can't analyze progress
        if len(output_dir_metrics) < 2:
            # Store current metrics for future analysis
            self.storage.store_metrics(metrics, output_dir=output_dir)
            return {
                "has_progress_data": False,
                "message": "Not enough data to analyze progress. Current metrics stored for future analysis."
            }

        # Store current metrics for future analysis
        self.storage.store_metrics(metrics, output_dir=output_dir)

        # Convert current metrics to a dictionary
        current_metrics_dict = self.storage._metrics_to_dict(metrics, output_dir)

        # Sort metrics by timestamp (newest first)
        output_dir_metrics.sort(key=lambda x: x.get("timestamp", 0), reverse=True)

        # Analyze progress
        progress = self._calculate_trends(current_metrics_dict, output_dir_metrics[1:])

        # Add additional information
        progress["output_dir"] = str(Path(output_dir).resolve())
        progress["run_count"] = len(output_dir_metrics) + 1  # Include current run
        progress["first_run_date"] = output_dir_metrics[-1].get("date", "Unknown")

        return {
            "has_progress_data": True,
            "progress": progress,
            "historical_data": output_dir_metrics,
            "current_data": current_metrics_dict
        }
