"""
Trend Storage
==========

This module provides functionality for storing and retrieving historical project metrics.
"""

import json
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from ..models.project_metrics import ProjectMetrics


class TrendStorage:
    """
    Storage for historical project metrics.

    This class provides functionality for storing and retrieving historical project metrics
    for trend analysis.
    """

    def __init__(self, storage_dir: Union[str, Path] = None):
        """
        Initialize the trend storage.

        Args:
            storage_dir: Directory for storing historical data
        """
        if storage_dir is None:
            # Default to ~/.vibe_check/history
            storage_dir = os.path.join(os.path.expanduser("~"), ".vibe_check", "history")

        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)

    def store_metrics(self, metrics: ProjectMetrics, timestamp: Optional[float] = None,
                    output_dir: Optional[Union[str, Path]] = None) -> str:
        """
        Store project metrics for later trend analysis.

        Args:
            metrics: Project metrics to store
            timestamp: Optional timestamp (defaults to current time)
            output_dir: Optional output directory used for analysis

        Returns:
            Path to the stored metrics file
        """
        # Use current timestamp if not provided
        if timestamp is None:
            timestamp = time.time()

        # Create a directory for the project if it doesn't exist
        project_name = Path(metrics.project_path).name
        project_dir = self.storage_dir / project_name
        project_dir.mkdir(exist_ok=True)

        # Create a filename with the timestamp
        date_str = datetime.fromtimestamp(timestamp).strftime("%Y%m%d_%H%M%S")
        filename = f"{date_str}.json"
        file_path = project_dir / filename

        # Convert metrics to a serializable dictionary
        metrics_dict = self._metrics_to_dict(metrics, output_dir)

        # Add timestamp to the dictionary
        metrics_dict["timestamp"] = timestamp
        metrics_dict["date"] = datetime.fromtimestamp(timestamp).isoformat()

        # Write to file
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(metrics_dict, f, indent=2)

        return str(file_path)

    def get_historical_metrics(self, project_path: Union[str, Path],
                             limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get historical metrics for a project.

        Args:
            project_path: Path to the project
            limit: Maximum number of historical metrics to return

        Returns:
            List of historical metrics dictionaries, sorted by timestamp (newest first)
        """
        project_name = Path(project_path).name
        project_dir = self.storage_dir / project_name

        if not project_dir.exists():
            return []

        # Get all JSON files in the project directory
        files = list(project_dir.glob("*.json"))

        # Sort files by name (which includes the timestamp)
        files.sort(reverse=True)

        # Limit the number of files
        files = files[:limit]

        # Load metrics from files
        metrics_list = []
        for file_path in files:
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    metrics_dict = json.load(f)
                    metrics_list.append(metrics_dict)
            except Exception as e:
                print(f"Error loading metrics from {file_path}: {e}")

        return metrics_list

    def get_latest_metrics(self, project_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
        """
        Get the latest metrics for a project.

        Args:
            project_path: Path to the project

        Returns:
            Latest metrics dictionary, or None if no metrics are available
        """
        historical_metrics = self.get_historical_metrics(project_path, limit=1)
        if historical_metrics:
            return historical_metrics[0]
        return None

    def get_metrics_for_output_dir(self, project_path: Union[str, Path],
                                 output_dir: Union[str, Path]) -> List[Dict[str, Any]]:
        """
        Get metrics for a project that were generated with the same output directory.

        This is useful for tracking progress between analyses when the same output directory
        is used multiple times.

        Args:
            project_path: Path to the project
            output_dir: Output directory used for analysis

        Returns:
            List of metrics dictionaries that used the same output directory, sorted by timestamp (newest first)
        """
        # Get all historical metrics for the project
        all_metrics = self.get_historical_metrics(project_path, limit=100)

        # Filter metrics by output directory
        output_dir_str = str(Path(output_dir).resolve())
        matching_metrics = []

        for metrics in all_metrics:
            if metrics.get("output_dir") == output_dir_str:
                matching_metrics.append(metrics)

        return matching_metrics

    def _metrics_to_dict(self, metrics: ProjectMetrics, output_dir: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
        """
        Convert ProjectMetrics to a serializable dictionary.

        Args:
            metrics: Project metrics to convert
            output_dir: Optional output directory used for analysis

        Returns:
            Dictionary representation of the metrics
        """
        # Create a dictionary with the most important metrics
        metrics_dict = {
            "project_path": str(metrics.project_path),
            "total_file_count": metrics.total_file_count,
            "total_line_count": metrics.total_line_count,
            "avg_complexity": metrics.avg_complexity,
            "max_complexity": metrics.max_complexity,
            "issue_count": metrics.issue_count,
            "avg_type_coverage": metrics.avg_type_coverage,
            "avg_docstring_coverage": metrics.avg_docstring_coverage,
            "issues_by_severity": dict(metrics._issues_by_severity),
            "issues_by_tool": dict(metrics._issues_by_tool),
            "tool_results": list(metrics.tool_results.keys()),
            "file_count_by_type": dict(metrics._file_count_by_type),
            "directory_count": len(metrics.directories)
        }

        # Add output directory if provided
        if output_dir:
            metrics_dict["output_dir"] = str(Path(output_dir).resolve())

        return metrics_dict
