"""
Error Handling Decorators Module
===========================

This module provides decorators for error handling in the Vibe Check tool.
These decorators can be used to catch and handle errors in functions and methods.
"""

import functools
import logging
import traceback
from typing import Any, Callable, Dict, Optional, TypeVar, cast

from .handlers import handle_error

# Set up logging
logger = logging.getLogger("vibe_check_error_handler")

# Type definitions
F = TypeVar('F', bound=Callable[..., Any])
AsyncF = TypeVar('AsyncF', bound=Callable[..., Any])
ErrorHandler = Callable[[Exception, Dict[str, Any]], Any]


def catch_errors(
    default_return: Any = None,
    error_handler: Optional[ErrorHandler] = None
) -> Callable[[F], F]:
    """
    Decorator to catch and handle errors in synchronous functions.
    
    Args:
        default_return: Value to return if an error occurs
        error_handler: Optional function to handle the error
        
    Returns:
        Decorated function
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in {func.__name__}: {e}")
                logger.debug(traceback.format_exc())
                
                # Create error context
                error_context = {
                    "function": func.__name__,
                    "args": args,
                    "kwargs": kwargs,
                    "exception": e,
                    "traceback": traceback.format_exc()
                }
                
                # Call error handler if provided
                if error_handler:
                    try:
                        return error_handler(e, error_context)
                    except Exception as handler_error:
                        logger.error(f"Error handler failed: {handler_error}")
                else:
                    # Use default error handler
                    result = handle_error(e, error_context)
                    if result is not None:
                        return result
                
                # Return default value
                return default_return
                
        return cast(F, wrapper)
        
    return decorator


def async_catch_errors(
    default_return: Any = None,
    error_handler: Optional[ErrorHandler] = None
) -> Callable[[AsyncF], AsyncF]:
    """
    Decorator to catch and handle errors in asynchronous functions.
    
    Args:
        default_return: Value to return if an error occurs
        error_handler: Optional function to handle the error
        
    Returns:
        Decorated function
    """
    def decorator(func: AsyncF) -> AsyncF:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in {func.__name__}: {e}")
                logger.debug(traceback.format_exc())
                
                # Create error context
                error_context = {
                    "function": func.__name__,
                    "args": args,
                    "kwargs": kwargs,
                    "exception": e,
                    "traceback": traceback.format_exc()
                }
                
                # Call error handler if provided
                if error_handler:
                    try:
                        return await error_handler(e, error_context)
                    except Exception as handler_error:
                        logger.error(f"Error handler failed: {handler_error}")
                else:
                    # Use default error handler
                    result = handle_error(e, error_context)
                    if result is not None:
                        return result
                
                # Return default value
                return default_return
                
        return cast(AsyncF, wrapper)
        
    return decorator
