"""
Error Handling Package
==================

This package provides centralized error handling functionality for the Vibe Check tool.
It implements robust error catching, logging, and recovery mechanisms to ensure
the system can gracefully handle failures during analysis.
"""

from .decorators import catch_errors, async_catch_errors
from .exceptions import (
    VibeCheckError,
    ToolError,
    FileError,
    ActorError,
    PluginError,
    ConfigError,
    NetworkError,
    TimeoutError,
)
from .handlers import (
    format_error_for_user,
    log_error,
    recover_from_error,
    handle_error,
    handle_errors,
)
from .error_manager import ErrorManager, get_error_manager

__all__ = [
    # Decorators
    'catch_errors',
    'async_catch_errors',

    # Exceptions
    'VibeCheckError',
    'ToolError',
    'FileError',
    'ActorError',
    'PluginError',
    'ConfigError',
    'NetworkError',
    'TimeoutError',

    # Handlers
    'format_error_for_user',
    'log_error',
    'recover_from_error',
    'handle_error',
    'handle_errors',

    # Manager
    'ErrorManager',
    'get_error_manager',
]
