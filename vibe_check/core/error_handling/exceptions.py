"""
Error Exceptions Module
==================

This module defines the exception hierarchy for the Vibe Check tool.
It provides a set of custom exceptions that can be used to represent
different types of errors that can occur during analysis.
"""

from typing import Any, Dict, Optional


class VibeCheckError(Exception):
    """
    Base exception class for all Vibe Check errors.
    
    This class provides a common interface for all Vibe Check errors,
    including error details and context information.
    """
    
    def __init__(self, 
                message: str, 
                details: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the exception.
        
        Args:
            message: Error message
            details: Optional dictionary with additional error details
        """
        super().__init__(message)
        self.details = details or {}
        
    def __str__(self) -> str:
        """
        Return a string representation of the exception.
        
        Returns:
            String representation
        """
        if self.details:
            return f"{super().__str__()} - {self.details}"
        return super().__str__()


class ToolError(VibeCheckError):
    """
    Exception raised when a tool fails to run or produces invalid results.
    """
    
    def __init__(self, 
                message: str, 
                tool_name: str, 
                details: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the exception.
        
        Args:
            message: Error message
            tool_name: Name of the tool that failed
            details: Optional dictionary with additional error details
        """
        super().__init__(message, details)
        self.tool_name = tool_name


class FileError(VibeCheckError):
    """
    Exception raised when a file cannot be read, parsed, or processed.
    """
    
    def __init__(self, 
                message: str, 
                file_path: str, 
                details: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the exception.
        
        Args:
            message: Error message
            file_path: Path to the file that caused the error
            details: Optional dictionary with additional error details
        """
        super().__init__(message, details)
        self.file_path = file_path


class ActorError(VibeCheckError):
    """
    Exception raised when an actor fails to initialize, process a message, or stop.
    """
    
    def __init__(self, 
                message: str, 
                actor_id: str, 
                details: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the exception.
        
        Args:
            message: Error message
            actor_id: ID of the actor that failed
            details: Optional dictionary with additional error details
        """
        super().__init__(message, details)
        self.actor_id = actor_id


class PluginError(VibeCheckError):
    """
    Exception raised when a plugin fails to load, initialize, or execute.
    """
    
    def __init__(self, 
                message: str, 
                plugin_name: str, 
                details: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the exception.
        
        Args:
            message: Error message
            plugin_name: Name of the plugin that failed
            details: Optional dictionary with additional error details
        """
        super().__init__(message, details)
        self.plugin_name = plugin_name


class ConfigError(VibeCheckError):
    """
    Exception raised when a configuration is invalid or cannot be loaded.
    """
    
    def __init__(self, 
                message: str, 
                config_path: Optional[str] = None, 
                details: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the exception.
        
        Args:
            message: Error message
            config_path: Optional path to the configuration file
            details: Optional dictionary with additional error details
        """
        super().__init__(message, details)
        self.config_path = config_path


class NetworkError(VibeCheckError):
    """
    Exception raised when a network operation fails.
    """
    
    def __init__(self, 
                message: str, 
                url: Optional[str] = None, 
                details: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the exception.
        
        Args:
            message: Error message
            url: Optional URL that caused the error
            details: Optional dictionary with additional error details
        """
        super().__init__(message, details)
        self.url = url


class TimeoutError(VibeCheckError):
    """
    Exception raised when an operation times out.
    """
    
    def __init__(self, 
                message: str, 
                operation: Optional[str] = None, 
                timeout: Optional[float] = None, 
                details: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the exception.
        
        Args:
            message: Error message
            operation: Optional name of the operation that timed out
            timeout: Optional timeout value in seconds
            details: Optional dictionary with additional error details
        """
        super().__init__(message, details)
        self.operation = operation
        self.timeout = timeout
