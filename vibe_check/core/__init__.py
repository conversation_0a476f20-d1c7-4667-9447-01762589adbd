"""
Vibe Check Project Analysis Core Module
==============================

This is the core module for Vibe Check (Project Analysis Tool). It provides the
foundational components for analyzing Python projects, including:

1. Actor System - Implements the CAW pattern with choreographed actors
2. Models - Data models for analysis results
3. Utilities - Common utilities for configuration, file handling, etc.

The refactored architecture follows Clean Architecture principles with clear
separation between the domain (models), application logic (actors), and
infrastructure (utilities).
"""

# Version information
from .version import __version__
__author__ = "Vibe Check Team"
__license__ = "MIT"

# Core imports
from .models import (
    FileMetrics,
    ProjectMetrics,
    DirectoryMetrics,
)

# Actor system imports
from .actor_system import (
    Actor,
    Message,
    MessageType,
    ContextWave,
)

# Expose the main entry points
from .orchestrator import analyze_project
from .utils import logger

__all__ = [
    # Version info
    "__version__",
    "__author__",
    "__license__",

    # Core models
    "FileMetrics",
    "ProjectMetrics",
    "DirectoryMetrics",

    # Actor system
    "Actor",
    "Message",
    "MessageType",
    "ContextWave",

    # Main entry point
    "analyze_project",

    # Utilities
    "logger",
]
