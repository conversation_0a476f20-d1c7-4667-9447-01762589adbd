"""
GitIgnore Utilities
================

This module provides utilities for parsing and using .gitignore files
to exclude files from analysis.
"""

import os
from pathlib import Path
from typing import List, Optional


def parse_gitignore(project_path: str) -> List[str]:
    """
    Parse .gitignore file and return patterns as a list of strings.
    
    Args:
        project_path: Path to the project directory
        
    Returns:
        List of gitignore patterns
    """
    gitignore_path = Path(project_path) / ".gitignore"
    patterns: List[str] = []
    
    if not gitignore_path.exists():
        return patterns
    
    try:
        with open(gitignore_path, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                # Skip empty lines and comments
                if not line or line.startswith("#"):
                    continue
                
                # Convert .gitignore pattern to glob pattern
                # Remove leading slash if present (makes pattern relative to project root)
                if line.startswith("/"):
                    line = line[1:]
                
                # Add ** prefix for any directory
                if not line.startswith("**/"):
                    line = "**/" + line
                
                patterns.append(line)
    except Exception as e:
        print(f"Error parsing .gitignore: {e}")
    
    return patterns


def get_exclude_patterns(project_path: str, use_gitignore: bool = True, 
                         base_patterns: Optional[List[str]] = None) -> List[str]:
    """
    Get exclude patterns for a project, optionally including .gitignore patterns.
    
    Args:
        project_path: Path to the project directory
        use_gitignore: Whether to include patterns from .gitignore
        base_patterns: Base patterns to include
        
    Returns:
        List of exclude patterns
    """
    patterns = base_patterns or []
    
    if use_gitignore:
        gitignore_patterns = parse_gitignore(project_path)
        patterns.extend(gitignore_patterns)
    
    return patterns
