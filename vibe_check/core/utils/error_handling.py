"""
Error Handling Utilities
=======================

This module provides custom exceptions and error handling utilities for the Vibe Check project.
"""

from typing import Any, Dict, Optional, Type


class VibeCheckError(Exception):
    """Base exception class for all Vibe Check errors."""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        """
        Initialize a VibeCheckError.

        Args:
            message: Error message
            details: Optional dictionary with additional error details
        """
        self.message = message
        self.details = details or {}
        super().__init__(message)


class FileSystemError(VibeCheckError):
    """Exception raised for file system related errors."""

    def __init__(self, message: str, path: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        """
        Initialize a FileSystemError.

        Args:
            message: Error message
            path: Optional path that caused the error
            details: Optional dictionary with additional error details
        """
        self.path = path
        details = details or {}
        if path:
            details["path"] = path
        super().__init__(message, details)


class ConfigurationError(VibeCheckError):
    """Exception raised for configuration related errors."""

    def __init__(self, message: str, config_key: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        """
        Initialize a ConfigurationError.

        Args:
            message: Error message
            config_key: Optional configuration key that caused the error
            details: Optional dictionary with additional error details
        """
        self.config_key = config_key
        details = details or {}
        if config_key:
            details["config_key"] = config_key
        super().__init__(message, details)


class AnalysisError(VibeCheckError):
    """Exception raised for analysis related errors."""

    def __init__(self, message: str, analyzer: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        """
        Initialize an AnalysisError.

        Args:
            message: Error message
            analyzer: Optional analyzer name that caused the error
            details: Optional dictionary with additional error details
        """
        self.analyzer = analyzer
        details = details or {}
        if analyzer:
            details["analyzer"] = analyzer
        super().__init__(message, details)


class ReportingError(VibeCheckError):
    """Exception raised for reporting related errors."""

    def __init__(self, message: str, report_type: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        """
        Initialize a ReportingError.

        Args:
            message: Error message
            report_type: Optional report type that caused the error
            details: Optional dictionary with additional error details
        """
        self.report_type = report_type
        details = details or {}
        if report_type:
            details["report_type"] = report_type
        super().__init__(message, details)


def handle_error(error: Exception, error_type: Type[Exception] = VibeCheckError, 
                 message: Optional[str] = None, details: Optional[Dict[str, Any]] = None) -> Exception:
    """
    Handle an error by wrapping it in a custom exception.

    Args:
        error: Original exception
        error_type: Type of exception to wrap the error in
        message: Optional custom message
        details: Optional dictionary with additional error details

    Returns:
        Wrapped exception
    """
    if isinstance(error, error_type):
        return error

    details = details or {}
    details["original_error"] = str(error)
    details["original_error_type"] = type(error).__name__

    return error_type(
        message or f"Error: {str(error)}",
        details=details
    )
