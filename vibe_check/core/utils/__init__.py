"""
Core Utilities for Project Analysis
=================================

This module provides utility functions and helpers used throughout the Vibe Check tool.
These utilities include logging, file operations, async utilities, and other common functionality.

The utilities have been consolidated and organized into logical modules for better maintainability
and consistency.
"""

import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union

# Configure logging
logger = logging.getLogger("vibe_check")
logger.setLevel(logging.INFO)

# Export file system utilities
from .fs_utils import (
    get_file_content, write_file, ensure_directory, ensure_dir, normalize_path,
    is_excluded, list_files, find_files, find_python_files, count_lines,
    get_file_stats, copy_file, get_relative_path, get_file_extension
)

# Export configuration utilities
from .config_utils import load_config, merge_configs, read_config
from .gitignore_utils import parse_gitignore, get_exclude_patterns
from .preset_manager import get_available_presets, load_preset, apply_preset

# Export async utilities
from .async_utils import (
    run_async, run_async_with_timeout, ensure_event_loop,
    with_timeout, to_thread, gather_with_concurrency
)

# Export tool utilities
from .tool_utils import run_tool_on_file, extract_issues_from_result, extract_complexity_from_result

# Export reporting utilities
from .report_utils import generate_reports, generate_json_report, generate_markdown_report, generate_html_report

# Export dictionary utilities
from .dict_utils import deep_merge

__all__ = [
    # Logging
    'logger',

    # File system utilities
    'get_file_content',
    'write_file',
    'ensure_directory',
    'ensure_dir',
    'normalize_path',
    'is_excluded',
    'list_files',
    'find_files',
    'find_python_files',
    'count_lines',
    'get_file_stats',
    'copy_file',
    'get_relative_path',
    'get_file_extension',

    # Configuration utilities
    'load_config',
    'merge_configs',
    'read_config',
    'parse_gitignore',
    'get_exclude_patterns',
    'get_available_presets',
    'load_preset',
    'apply_preset',

    # Async utilities
    'run_async',
    'run_async_with_timeout',
    'ensure_event_loop',
    'with_timeout',
    'to_thread',
    'gather_with_concurrency',

    # Tool utilities
    'run_tool_on_file',
    'extract_issues_from_result',
    'extract_complexity_from_result',

    # Reporting utilities
    'generate_reports',
    'generate_json_report',
    'generate_markdown_report',
    'generate_html_report',

    # Dictionary utilities
    'deep_merge',
]
