"""
Configuration Utility Functions
=============================

This module provides utility functions for loading and managing configuration.
"""

import os
import yaml
import logging
from pathlib import Path
from typing import Any, Dict, Optional, Union

# Import dict_utils for deep_merge
from .dict_utils import deep_merge

# Set up logger
logger = logging.getLogger("vibe_check_config_utils")


def load_config(config_path: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
    """
    Load configuration from a YAML file.

    Args:
        config_path: Path to the configuration file, or None to use default

    Returns:
        Dictionary containing the configuration
    """
    # If no config path is provided, use the default config
    if config_path is None:
        # Try to find the default config in the package
        default_config_path = Path(__file__).parent.parent.parent / "config" / "default_config.yaml"
        logger.debug(f"Looking for default config at: {default_config_path}")
        if default_config_path.exists():
            config_path = default_config_path
            logger.debug(f"Using default config: {config_path}")
        else:
            logger.debug("Default config not found, returning empty config")
            return {}  # Return empty config if default doesn't exist

    path_obj = Path(config_path)
    logger.debug(f"Loading config from: {path_obj}")

    if not path_obj.exists():
        logger.warning(f"Config file does not exist: {path_obj}")
        return {}  # Return empty config if file doesn't exist

    try:
        with open(path_obj, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f) or {}
        logger.debug(f"Loaded config: {config}")
        return config
    except yaml.YAMLError as e:
        # Log error and re-raise
        logger.error(f"Error loading configuration from {config_path}: {e}")
        raise
    except Exception as e:
        # Log error and return empty config
        logger.error(f"Error loading configuration from {config_path}: {e}")
        return {}


# Alias for backward compatibility
read_config = load_config


def merge_configs(base_config: Dict[str, Any],
                 override_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge two configurations, with the override config taking precedence.

    This performs a deep merge of the two dictionaries.

    Args:
        base_config: Base configuration
        override_config: Configuration to override with

    Returns:
        Merged configuration
    """
    # Use the deep_merge function from dict_utils
    return deep_merge(base_config, override_config)


def get_config_with_defaults(config_path: Optional[Union[str, Path]] = None,
                           default_config_path: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
    """
    Load and merge configuration with default values.

    Args:
        config_path: Path to the user configuration file, or None to use default
        default_config_path: Optional path to the default configuration file

    Returns:
        Merged configuration with defaults
    """
    # Load default configuration if provided
    if default_config_path:
        default_config = load_config(default_config_path)
    else:
        # Use the package default config
        default_config_path = Path(__file__).parent.parent.parent / "config" / "default_config.yaml"
        if default_config_path.exists():
            default_config = load_config(default_config_path)
        else:
            default_config = {}

    # Load user configuration if provided
    if config_path:
        user_config = load_config(config_path)
    else:
        user_config = {}

    # Merge the configurations
    return merge_configs(default_config, user_config)


def get_tool_config(config: Dict[str, Any], tool_name: str) -> Dict[str, Any]:
    """
    Get configuration for a specific tool.

    Args:
        config: Full configuration dictionary
        tool_name: Name of the tool

    Returns:
        Tool configuration
    """
    # Extract tool configuration
    tool_configs = config.get("tools", {})
    tool_config = tool_configs.get(tool_name, {})

    # Add common configuration that applies to all tools
    common_config = config.get("common", {})

    # Merge common config with tool-specific config
    return merge_configs(common_config, tool_config)
