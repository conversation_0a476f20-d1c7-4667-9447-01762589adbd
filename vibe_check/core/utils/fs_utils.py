"""
File System Utilities
==================

This module provides comprehensive utility functions for working with the file system.
It consolidates functionality from the original fs_utils.py and file_utils.py modules
to provide a single, consistent interface for file operations.
"""

import fnmatch
import os
import shutil
from pathlib import Path
from typing import Dict, Generator, List, Optional, Set, Tuple, Union

from .error_handling import FileSystemError


def get_file_content(file_path: Union[str, Path]) -> str:
    """
    Read the content of a file.

    Args:
        file_path: Path to the file

    Returns:
        Content of the file as a string

    Raises:
        FileNotFoundError: If the file does not exist
        FileSystemError: If the file cannot be read
    """
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            return f.read()
    except FileNotFoundError:
        raise
    except Exception as e:
        raise FileSystemError(f"Failed to read file {file_path}: {e}")


def write_file(file_path: Union[str, Path], content: str) -> None:
    """
    Write content to a file.

    Args:
        file_path: Path to the file
        content: Content to write

    Raises:
        FileSystemError: If the file cannot be written
    """
    try:
        # Create directory if it doesn't exist
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    except Exception as e:
        raise FileSystemError(f"Failed to write file {file_path}: {e}")


def ensure_directory(directory: Union[str, Path]) -> Path:
    """
    Ensure a directory exists, creating it if necessary.

    Args:
        directory: Directory path to ensure exists

    Returns:
        Path object of the directory

    Raises:
        FileSystemError: If the directory cannot be created
        ValueError: If the path exists but is not a directory
    """
    try:
        path_obj = Path(directory)
        if not path_obj.exists():
            path_obj.mkdir(parents=True, exist_ok=True)
        elif not path_obj.is_dir():
            raise ValueError(f"{directory} exists but is not a directory")
        return path_obj
    except ValueError:
        raise
    except Exception as e:
        raise FileSystemError(f"Failed to create directory {directory}: {e}")


# Aliases for backward compatibility
ensure_dir = ensure_directory


def normalize_path(path: Union[str, Path], base_path: Optional[Union[str, Path]] = None) -> str:
    """
    Normalize a path to a standard format.

    Args:
        path: Path to normalize
        base_path: Optional base path to make the path relative to

    Returns:
        Normalized path as a string
    """
    # Convert to Path objects
    path_obj = Path(path)

    # Make the path absolute if it's not already
    if not path_obj.is_absolute() and base_path is not None:
        path_obj = Path(base_path) / path_obj

    # Resolve the path
    try:
        path_obj = path_obj.resolve()
    except (FileNotFoundError, RuntimeError):
        # If the path doesn't exist, just normalize it
        pass

    # Return the normalized path as a string with forward slashes
    return str(path_obj).replace('\\', '/')


def is_excluded(path: Union[str, Path], exclude_patterns: Optional[List[str]] = None) -> bool:
    """
    Check if a path matches any exclude pattern.

    Args:
        path: Path to check
        exclude_patterns: Patterns to exclude

    Returns:
        True if the path should be excluded, False otherwise
    """
    if not exclude_patterns:
        return False

    # Normalize the path
    norm_path = normalize_path(path)
    path_str = str(path)

    # Check if the path matches any pattern
    for pattern in exclude_patterns:
        # Handle special case for directory patterns
        if "**/" in pattern:
            parts = path_str.split(os.sep)
            for i in range(len(parts)):
                subpath = os.sep.join(parts[i:])
                if fnmatch.fnmatch(subpath, pattern.replace("**/", "")):
                    return True
                subpath = os.sep.join(parts[:i+1])
                if fnmatch.fnmatch(subpath, pattern.replace("/**", "")):
                    return True

        # Direct match
        if fnmatch.fnmatch(norm_path, pattern):
            return True

        # Also check the basename
        basename = os.path.basename(norm_path)
        if fnmatch.fnmatch(basename, pattern):
            return True

    return False


def list_files(directory: Union[str, Path],
              file_extensions: Optional[List[str]] = None,
              exclude_patterns: Optional[List[str]] = None,
              recursive: bool = True) -> List[str]:
    """
    List files in a directory with optional filtering.

    Args:
        directory: Directory to list files from
        file_extensions: Optional list of file extensions to include
        exclude_patterns: Optional list of patterns to exclude
        recursive: Whether to recursively list files in subdirectories

    Returns:
        List of file paths
    """
    directory = Path(directory)
    result = []

    # Ensure file extensions start with a dot
    if file_extensions:
        file_extensions = [ext if ext.startswith('.') else f'.{ext}' for ext in file_extensions]

    if recursive:
        # Recursively walk the directory
        for root, dirs, files in os.walk(directory):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if not is_excluded(os.path.join(root, d), exclude_patterns)]

            # Process files
            for file in files:
                file_path = os.path.join(root, file)

                # Skip excluded files
                if is_excluded(file_path, exclude_patterns):
                    continue

                # Filter by extension if needed
                if file_extensions and not any(file.endswith(ext) for ext in file_extensions):
                    continue

                result.append(normalize_path(file_path))
    else:
        # Only list files in the top-level directory
        for item in directory.iterdir():
            if item.is_file():
                # Skip excluded files
                if is_excluded(item, exclude_patterns):
                    continue

                # Filter by extension if needed
                if file_extensions and not any(item.name.endswith(ext) for ext in file_extensions):
                    continue

                result.append(normalize_path(item))

    return sorted(result)


def find_files(directory: Union[str, Path],
              file_extensions: Optional[List[str]] = None,
              exclude_patterns: Optional[List[str]] = None) -> List[Path]:
    """
    Find files in a directory matching the given criteria.

    Args:
        directory: Directory to search
        file_extensions: List of file extensions to include
        exclude_patterns: List of glob patterns to exclude

    Returns:
        List of file paths

    Raises:
        FileNotFoundError: If the directory does not exist
        FileSystemError: If the directory cannot be searched
    """
    try:
        directory_path = Path(directory)
        if not directory_path.exists():
            raise FileNotFoundError(f"Directory does not exist: {directory}")

        if not directory_path.is_dir():
            raise FileSystemError(f"Not a directory: {directory}")

        # Use list_files to get the files
        file_paths = list_files(
            directory=directory,
            file_extensions=file_extensions,
            exclude_patterns=exclude_patterns,
            recursive=True
        )

        return [Path(p) for p in file_paths]
    except FileNotFoundError:
        raise
    except Exception as e:
        if isinstance(e, FileSystemError):
            raise
        raise FileSystemError(f"Failed to search directory {directory}: {e}")


def find_python_files(directory: Union[str, Path]) -> List[Path]:
    """
    Find all Python files in a directory.

    Args:
        directory: Directory to search

    Returns:
        List of Python file paths
    """
    return find_files(directory, file_extensions=['.py'])


def count_lines(file_path: Union[str, Path]) -> int:
    """
    Count the number of lines in a file.

    Args:
        file_path: Path to the file

    Returns:
        Number of lines

    Raises:
        FileSystemError: If the file cannot be read
    """
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            return sum(1 for _ in f)
    except Exception as e:
        raise FileSystemError(f"Failed to count lines in file {file_path}: {e}")


def get_file_stats(file_path: Union[str, Path]) -> Dict[str, Union[int, float]]:
    """
    Get statistics for a file.

    Args:
        file_path: Path to the file

    Returns:
        Dictionary with file statistics

    Raises:
        FileSystemError: If the file statistics cannot be retrieved
    """
    try:
        path_obj = Path(file_path)
        stats = path_obj.stat()

        return {
            "size": stats.st_size,
            "modified": stats.st_mtime,
            "created": stats.st_ctime,
            "accessed": stats.st_atime
        }
    except Exception as e:
        raise FileSystemError(f"Failed to get file statistics for {file_path}: {e}")


def copy_file(source: Union[str, Path], destination: Union[str, Path]) -> None:
    """
    Copy a file from source to destination.

    Args:
        source: Source file path
        destination: Destination file path

    Raises:
        FileSystemError: If the file cannot be copied
    """
    try:
        shutil.copy2(source, destination)
    except Exception as e:
        raise FileSystemError(f"Failed to copy file from {source} to {destination}: {e}")


def get_relative_path(path: Union[str, Path], base_path: Union[str, Path]) -> str:
    """
    Get the relative path from base_path to path.

    Args:
        path: Path to get relative path for
        base_path: Base path

    Returns:
        Relative path as a string
    """
    # Normalize paths to handle trailing slashes
    path_obj = Path(os.path.normpath(str(path)))
    base_path_obj = Path(os.path.normpath(str(base_path)))

    # Get the relative path
    try:
        rel_path = path_obj.relative_to(base_path_obj)
    except ValueError:
        # If the path is not relative to the base path, return the original path
        return str(path_obj)

    # Convert to string and handle the case where the path is the same
    if str(rel_path) == '.':
        return '.'

    return str(rel_path)


def get_file_extension(file_path: Union[str, Path]) -> str:
    """
    Get the file extension of a file.

    Args:
        file_path: Path to the file

    Returns:
        File extension (including the dot)
    """
    path_str = str(file_path)

    # Special case for dotfiles like .gitignore
    if os.path.basename(path_str).startswith('.') and '.' not in os.path.basename(path_str)[1:]:
        return os.path.basename(path_str)

    return os.path.splitext(path_str)[1]
