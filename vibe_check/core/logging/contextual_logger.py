"""
Contextual Logger Module
===================

This module provides the ContextualLogger class, which adds context information
to log messages. It allows for tracking related log messages across components.
"""

import logging
from typing import Any, Dict, Optional


class ContextLogger:
    """
    Logger that adds context information to log messages.
    
    This class wraps a standard logger and adds context information
    to each log message. It allows for tracking related log messages
    across components.
    """
    
    def __init__(self, 
                logger: logging.Logger, 
                context: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the context logger.
        
        Args:
            logger: Standard logger to wrap
            context: Optional context dictionary
        """
        self.logger = logger
        self.context = context or {}
        
    def _format_message(self, msg: str) -> str:
        """
        Format a message with context information.
        
        Args:
            msg: Original message
            
        Returns:
            Formatted message with context
        """
        if not self.context:
            return msg
            
        context_str = " ".join(f"{k}={v}" for k, v in self.context.items())
        return f"{msg} [{context_str}]"
        
    def debug(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log a debug message."""
        self.logger.debug(self._format_message(msg), *args, **kwargs)
        
    def info(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log an info message."""
        self.logger.info(self._format_message(msg), *args, **kwargs)
        
    def warning(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log a warning message."""
        self.logger.warning(self._format_message(msg), *args, **kwargs)
        
    def error(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log an error message."""
        self.logger.error(self._format_message(msg), *args, **kwargs)
        
    def critical(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log a critical message."""
        self.logger.critical(self._format_message(msg), *args, **kwargs)
        
    def with_context(self, **context: Any) -> 'ContextLogger':
        """
        Create a new logger with additional context.
        
        Args:
            **context: Context key-value pairs
            
        Returns:
            New context logger with updated context
        """
        new_context = {**self.context, **context}
        return ContextLogger(self.logger, new_context)


class ContextualLogger:
    """
    Factory for creating context loggers.
    
    This class provides a way to create context loggers with different
    context information. It allows for tracking related log messages
    across components.
    """
    
    def __init__(self, logger: logging.Logger) -> None:
        """
        Initialize the contextual logger.
        
        Args:
            logger: Standard logger to wrap
        """
        self.logger = logger
        self.context: Dict[str, Any] = {}
        
    def with_context(self, **context: Any) -> ContextLogger:
        """
        Create a context logger with the specified context.
        
        Args:
            **context: Context key-value pairs
            
        Returns:
            Context logger with the specified context
        """
        return ContextLogger(self.logger, context)
        
    def debug(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log a debug message."""
        self.logger.debug(msg, *args, **kwargs)
        
    def info(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log an info message."""
        self.logger.info(msg, *args, **kwargs)
        
    def warning(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log a warning message."""
        self.logger.warning(msg, *args, **kwargs)
        
    def error(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log an error message."""
        self.logger.error(msg, *args, **kwargs)
        
    def critical(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Log a critical message."""
        self.logger.critical(msg, *args, **kwargs)
