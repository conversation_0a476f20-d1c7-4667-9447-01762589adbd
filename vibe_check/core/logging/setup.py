"""
Logging Setup Module
================

This module provides functions for setting up logging in the Vibe Check tool.
It configures logging handlers, formatters, and levels.
"""

import logging
import os
import sys
from pathlib import Path
from typing import Any, Dict, Optional, Union

from .contextual_logger import ContextualLogger

# Default log format
DEFAULT_LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Default log level
DEFAULT_LOG_LEVEL = logging.INFO


def setup_logging(
    log_level: Optional[Union[int, str]] = None,
    log_file: Optional[Union[str, Path]] = None,
    log_format: Optional[str] = None,
    debug: bool = False,
    quiet: bool = False
) -> logging.Logger:
    """
    Set up logging configuration.
    
    Args:
        log_level: Logging level (default: INFO)
        log_file: Path to log file (default: None, logs to console only)
        log_format: Log format string (default: DEFAULT_LOG_FORMAT)
        debug: Enable debug logging (overrides log_level)
        quiet: Enable quiet mode (minimal logging, overrides log_level and debug)
        
    Returns:
        Configured logger
    """
    # Determine log level based on flags
    if quiet:
        level = logging.WARNING
    elif debug:
        level = logging.DEBUG
    elif isinstance(log_level, str):
        level = getattr(logging, log_level.upper(), DEFAULT_LOG_LEVEL)
    else:
        level = log_level or DEFAULT_LOG_LEVEL
        
    # Use default log format if none provided
    format_str = log_format or DEFAULT_LOG_FORMAT
    
    # Configure basic logging
    logging.basicConfig(
        level=level,
        format=format_str,
        filename=log_file,
        filemode='a' if log_file else None  # type: ignore[arg-type]
    )
    
    # Create logger
    logger = logging.getLogger("vibe_check")
    logger.setLevel(level)
    
    # If no file handler is specified and we're not using basicConfig's filename,
    # add a console handler
    if not log_file and not any(isinstance(h, logging.StreamHandler) for h in logger.handlers):
        # Create formatter
        formatter = logging.Formatter(format_str)
        
        # Create console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
    return logger


def get_logger(name: str) -> ContextualLogger:
    """
    Get a logger with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    logger = logging.getLogger(f"vibe_check.{name}")
    return ContextualLogger(logger)
