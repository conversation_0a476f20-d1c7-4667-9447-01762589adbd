"""
Log Correlation Module
=================

This module provides log correlation functionality for the Vibe Check tool.
It allows for tracking related log messages across components and processes.
"""

import contextvars
import uuid
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Set


@dataclass
class CorrelationContext:
    """
    Correlation context for log messages.
    
    This class represents a correlation context for log messages.
    It allows for tracking related log messages across components.
    """
    
    correlation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    operation_name: str = ""
    parent_id: Optional[str] = None
    flow_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the correlation context to a dictionary.
        
        Returns:
            Dictionary representation of the correlation context
        """
        return {
            "correlation_id": self.correlation_id,
            "operation_name": self.operation_name,
            "parent_id": self.parent_id,
            "flow_id": self.flow_id,
            "metadata": self.metadata
        }
        
    def create_child(self, operation_name: str) -> 'CorrelationContext':
        """
        Create a child correlation context.
        
        Args:
            operation_name: Name of the operation
            
        Returns:
            Child correlation context
        """
        return CorrelationContext(
            correlation_id=str(uuid.uuid4()),
            operation_name=operation_name,
            parent_id=self.correlation_id,
            flow_id=self.flow_id or self.correlation_id,
            metadata=self.metadata.copy()
        )


# Context variable for correlation context
correlation_context_var: contextvars.ContextVar[Optional[CorrelationContext]] = contextvars.ContextVar('correlation_context', default=None)

# Global correlator instance
_correlator = None


class LogCorrelator:
    """
    Log correlator for the Vibe Check tool.
    
    This class provides log correlation functionality for the Vibe Check tool.
    It allows for tracking related log messages across components and processes.
    """
    
    def __init__(self) -> None:
        """Initialize the log correlator."""
        self.active_contexts: Dict[str, CorrelationContext] = {}
        self.message_flows: Dict[str, List[str]] = {}
        
    def create_context(self, 
                      operation_name: str, 
                      metadata: Optional[Dict[str, Any]] = None) -> CorrelationContext:
        """
        Create a new correlation context.
        
        Args:
            operation_name: Name of the operation
            metadata: Optional metadata dictionary
            
        Returns:
            New correlation context
        """
        context = CorrelationContext(
            operation_name=operation_name,
            metadata=metadata or {}
        )
        
        # Store the context
        self.active_contexts[context.correlation_id] = context
        
        # Initialize message flow
        self.message_flows[context.correlation_id] = []
        
        return context
        
    def get_context(self, correlation_id: str) -> Optional[CorrelationContext]:
        """
        Get a correlation context by ID.
        
        Args:
            correlation_id: Correlation ID
            
        Returns:
            Correlation context or None if not found
        """
        return self.active_contexts.get(correlation_id)
        
    def add_message_flow(self, 
                        correlation_id: str, 
                        message: str) -> None:
        """
        Add a message to a message flow.
        
        Args:
            correlation_id: Correlation ID
            message: Message to add
        """
        if correlation_id in self.message_flows:
            self.message_flows[correlation_id].append(message)
            
    def get_message_flow(self, correlation_id: str) -> List[str]:
        """
        Get a message flow by correlation ID.
        
        Args:
            correlation_id: Correlation ID
            
        Returns:
            List of messages in the flow
        """
        return self.message_flows.get(correlation_id, [])
        
    def clear_context(self, correlation_id: str) -> None:
        """
        Clear a correlation context.
        
        Args:
            correlation_id: Correlation ID
        """
        if correlation_id in self.active_contexts:
            del self.active_contexts[correlation_id]
            
        if correlation_id in self.message_flows:
            del self.message_flows[correlation_id]


def get_correlator() -> LogCorrelator:
    """
    Get the global log correlator instance.
    
    Returns:
        Global log correlator instance
    """
    global _correlator
    if _correlator is None:
        _correlator = LogCorrelator()
    return _correlator


def create_correlation_context(
    operation_name: str,
    metadata: Optional[Dict[str, Any]] = None
) -> CorrelationContext:
    """
    Create a new correlation context and set it as the current context.
    
    Args:
        operation_name: Name of the operation
        metadata: Optional metadata dictionary
        
    Returns:
        New correlation context
    """
    correlator = get_correlator()
    context = correlator.create_context(operation_name, metadata)
    correlation_context_var.set(context)
    return context


def get_correlation_context() -> Optional[CorrelationContext]:
    """
    Get the current correlation context.
    
    Returns:
        Current correlation context or None if not set
    """
    return correlation_context_var.get()


def add_message_flow(message: str) -> None:
    """
    Add a message to the current message flow.
    
    Args:
        message: Message to add
    """
    context = get_correlation_context()
    if context:
        correlator = get_correlator()
        correlator.add_message_flow(context.correlation_id, message)
