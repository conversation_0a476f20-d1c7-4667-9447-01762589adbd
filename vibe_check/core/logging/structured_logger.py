"""
Structured Logger Module
===================

This module provides the StructuredLogger class, which adds structured
logging capabilities to the Vibe Check tool. It allows for logging
messages with structured data that can be easily parsed and analyzed.
"""

import enum
import json
import logging
from dataclasses import asdict, dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional, Union


class LogLevel(enum.IntEnum):
    """Log levels for structured logging."""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


@dataclass
class StructuredLogRecord:
    """
    Structured log record.
    
    This class represents a structured log record with metadata
    and context information.
    """
    
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    level: LogLevel = LogLevel.INFO
    message: str = ""
    logger_name: str = ""
    context: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the log record to a dictionary.
        
        Returns:
            Dictionary representation of the log record
        """
        return asdict(self)
        
    def to_json(self) -> str:
        """
        Convert the log record to a JSON string.
        
        Returns:
            JSON string representation of the log record
        """
        return json.dumps(self.to_dict())


class JsonFormatter(logging.Formatter):
    """
    JSON formatter for structured logging.
    
    This class formats log records as JSON strings.
    """
    
    def format(self, record: logging.LogRecord) -> str:
        """
        Format a log record as a JSON string.
        
        Args:
            record: Log record to format
            
        Returns:
            JSON string representation of the log record
        """
        # Create a structured log record
        structured_record = StructuredLogRecord(
            timestamp=datetime.fromtimestamp(record.created).isoformat(),
            level=LogLevel(record.levelno),
            message=record.getMessage(),
            logger_name=record.name,
            context=getattr(record, "context", {}),
            metadata={
                "pathname": record.pathname,
                "lineno": record.lineno,
                "funcName": record.funcName,
                "threadName": record.threadName,
                "processName": record.processName,
                "process": record.process,
                "thread": record.thread,
                "exc_info": record.exc_info,
                "exc_text": record.exc_text,
                "stack_info": record.stack_info,
            }
        )
        
        # Convert to JSON
        return structured_record.to_json()


class StructuredLogger:
    """
    Structured logger for the Vibe Check tool.
    
    This class provides structured logging capabilities for the Vibe Check tool.
    It allows for logging messages with structured data that can be easily
    parsed and analyzed.
    """
    
    def __init__(self, 
                name: str, 
                level: LogLevel = LogLevel.INFO,
                context: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the structured logger.
        
        Args:
            name: Logger name
            level: Log level
            context: Optional context dictionary
        """
        self.name = name
        self.level = level
        self.context = context or {}
        
        # Create a standard logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)
        
        # Add a JSON formatter if not already present
        if not any(isinstance(h.formatter, JsonFormatter) for h in self.logger.handlers):
            # Create a handler with a JSON formatter
            handler = logging.StreamHandler()
            handler.setFormatter(JsonFormatter())
            self.logger.addHandler(handler)
            
    def log(self, 
           level: LogLevel, 
           message: str, 
           context: Optional[Dict[str, Any]] = None,
           metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Log a message with structured data.
        
        Args:
            level: Log level
            message: Log message
            context: Optional context dictionary
            metadata: Optional metadata dictionary
        """
        # Combine context dictionaries
        combined_context = {**self.context}
        if context:
            combined_context.update(context)
            
        # Create a log record
        record = self.logger.makeRecord(
            name=self.name,
            level=level,
            fn="",
            lno=0,
            msg=message,
            args=(),
            exc_info=None,
            extra={"context": combined_context, "metadata": metadata or {}}
        )
        
        # Log the record
        self.logger.handle(record)
        
    def debug(self, 
             message: str, 
             context: Optional[Dict[str, Any]] = None,
             metadata: Optional[Dict[str, Any]] = None) -> None:
        """Log a debug message."""
        self.log(LogLevel.DEBUG, message, context, metadata)
        
    def info(self, 
            message: str, 
            context: Optional[Dict[str, Any]] = None,
            metadata: Optional[Dict[str, Any]] = None) -> None:
        """Log an info message."""
        self.log(LogLevel.INFO, message, context, metadata)
        
    def warning(self, 
               message: str, 
               context: Optional[Dict[str, Any]] = None,
               metadata: Optional[Dict[str, Any]] = None) -> None:
        """Log a warning message."""
        self.log(LogLevel.WARNING, message, context, metadata)
        
    def error(self, 
             message: str, 
             context: Optional[Dict[str, Any]] = None,
             metadata: Optional[Dict[str, Any]] = None) -> None:
        """Log an error message."""
        self.log(LogLevel.ERROR, message, context, metadata)
        
    def critical(self, 
                message: str, 
                context: Optional[Dict[str, Any]] = None,
                metadata: Optional[Dict[str, Any]] = None) -> None:
        """Log a critical message."""
        self.log(LogLevel.CRITICAL, message, context, metadata)
        
    def with_context(self, **context: Any) -> 'StructuredLogger':
        """
        Create a new logger with additional context.
        
        Args:
            **context: Context key-value pairs
            
        Returns:
            New structured logger with updated context
        """
        new_context = {**self.context, **context}
        return StructuredLogger(self.name, self.level, new_context)
