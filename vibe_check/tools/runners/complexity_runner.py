"""
Complexity Analyzer Runner
=====================

This module provides a runner for analyzing code complexity.

The complexity analyzer calculates McCabe cyclomatic complexity and other
complexity metrics to identify code that may need refactoring.
"""

import ast
import logging
import math
from pathlib import Path
from typing import Any, Dict, List, Tuple, Union

from .base_runner import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .tool_registry import register_tool

logger = logging.getLogger("pat_complexity_runner")


class ComplexityVisitor(ast.NodeVisitor):
    """AST visitor that calculates cyclomatic complexity."""
    
    def __init__(self):
        """Initialize the visitor."""
        self.complexity = 1  # Start with 1 for the function itself
        self.functions = {}
        self.current_function = None
        self.line_counts = {}  # Lines of code per function
        self.nesting_level = 0
        self.max_nesting = 0
    
    def visit_FunctionDef(self, node):
        """
        Visit a function definition.
        
        Args:
            node: Function definition node
        """
        old_function = self.current_function
        old_complexity = self.complexity
        old_nesting = self.nesting_level
        
        self.current_function = node.name
        self.complexity = 1
        self.nesting_level = 0
        self.max_nesting = 0
        
        # Visit the function body
        for child in node.body:
            self.visit(child)
        
        # Record the function complexity
        start_line = node.lineno
        end_line = node.end_lineno if hasattr(node, 'end_lineno') else start_line
        
        # Calculate lines of code (excluding comments and blank lines)
        lines_of_code = end_line - start_line + 1
        
        self.functions[node.name] = {
            "complexity": self.complexity,
            "start_line": start_line,
            "end_line": end_line,
            "lines_of_code": lines_of_code,
            "max_nesting": self.max_nesting
        }
        
        # Restore previous state
        self.current_function = old_function
        self.complexity = old_complexity
        self.nesting_level = old_nesting
    
    def visit_AsyncFunctionDef(self, node):
        """
        Visit an async function definition.
        
        Args:
            node: Async function definition node
        """
        self.visit_FunctionDef(node)
    
    def visit_ClassDef(self, node):
        """
        Visit a class definition.
        
        Args:
            node: Class definition node
        """
        # Visit all children in the class
        for child in node.body:
            self.visit(child)
    
    def _inc_nesting(self):
        """Increment the nesting level."""
        self.nesting_level += 1
        self.max_nesting = max(self.max_nesting, self.nesting_level)
    
    def _dec_nesting(self):
        """Decrement the nesting level."""
        self.nesting_level -= 1
    
    def visit_If(self, node):
        """
        Visit an if statement.
        
        Args:
            node: If statement node
        """
        self.complexity += 1
        self._inc_nesting()
        self.generic_visit(node)
        self._dec_nesting()
    
    def visit_For(self, node):
        """
        Visit a for loop.
        
        Args:
            node: For loop node
        """
        self.complexity += 1
        self._inc_nesting()
        self.generic_visit(node)
        self._dec_nesting()
    
    def visit_AsyncFor(self, node):
        """
        Visit an async for loop.
        
        Args:
            node: Async for loop node
        """
        self.complexity += 1
        self._inc_nesting()
        self.generic_visit(node)
        self._dec_nesting()
    
    def visit_While(self, node):
        """
        Visit a while loop.
        
        Args:
            node: While loop node
        """
        self.complexity += 1
        self._inc_nesting()
        self.generic_visit(node)
        self._dec_nesting()
    
    def visit_Try(self, node):
        """
        Visit a try statement.
        
        Args:
            node: Try statement node
        """
        # Add 1 for the try block itself
        self.complexity += 1
        
        # Add 1 for each except clause
        self.complexity += len(node.handlers)
        
        self._inc_nesting()
        self.generic_visit(node)
        self._dec_nesting()
    
    def visit_With(self, node):
        """
        Visit a with statement.
        
        Args:
            node: With statement node
        """
        self._inc_nesting()
        self.generic_visit(node)
        self._dec_nesting()
    
    def visit_AsyncWith(self, node):
        """
        Visit an async with statement.
        
        Args:
            node: Async with statement node
        """
        self._inc_nesting()
        self.generic_visit(node)
        self._dec_nesting()
    
    def visit_BoolOp(self, node):
        """
        Visit a boolean operation.
        
        Args:
            node: Boolean operation node
        """
        # Add 1 for each boolean operator (and, or)
        # Since we have N values, we have N-1 operators
        self.complexity += len(node.values) - 1
        self.generic_visit(node)


class ComplexityRunner(ToolRunner):
    """Runner for code complexity analysis."""
    
    def __init__(self, name: str = "complexity", config: Dict[str, Any] = None):
        """
        Initialize the complexity runner.
        
        Args:
            name: Tool name
            config: Tool configuration
        """
        super().__init__(name, config)
    
    async def run(self, file_path: Union[str, Path], content: str) -> Dict[str, Any]:
        """
        Calculate code complexity.
        
        Args:
            file_path: Path to the file being analyzed
            content: File content as a string
            
        Returns:
            Dictionary with analysis results
        """
        try:
            # Parse the code into an AST
            tree = ast.parse(content)
            
            # Visit the tree with our complexity visitor
            visitor = ComplexityVisitor()
            visitor.visit(tree)
            
            # Collect module-level metrics
            line_count = len(content.splitlines())
            non_blank_lines = sum(1 for line in content.splitlines() if line.strip())
            comment_lines = sum(1 for line in content.splitlines() if line.strip().startswith('#'))
            
            # Calculate total complexity
            total_complexity = sum(func_info["complexity"] for func_info in visitor.functions.values())
            
            # Calculate average complexity
            avg_complexity = (
                total_complexity / len(visitor.functions)
                if visitor.functions else 0
            )
            
            # Find maximum complexity
            max_complexity = (
                max(func_info["complexity"] for func_info in visitor.functions.values())
                if visitor.functions else 0
            )
            
            # Find the most complex function
            most_complex_function = None
            if visitor.functions:
                most_complex_function = max(
                    visitor.functions.items(),
                    key=lambda item: item[1]["complexity"]
                )[0]
            
            # Calculate overall cognitive weight
            # This is a heuristic that combines complexity metrics
            cognitive_weight = (
                total_complexity * 0.5 +
                max_complexity * 0.3 + 
                (avg_complexity * len(visitor.functions)) * 0.2
            )
            
            # Normalize to a 0-100 scale
            if cognitive_weight > 0:
                # Log transformation to handle large values
                normalized_weight = min(100, 10 * math.log(cognitive_weight + 1))
            else:
                normalized_weight = 0
            
            # Calculate maintainability index
            # Based on the Microsoft implementation (0-100 scale)
            if line_count > 0 and max_complexity > 0:
                maintainability = max(0, min(100, (
                    171 - 
                    5.2 * math.log(total_complexity) -
                    0.23 * max_complexity - 
                    16.2 * math.log(line_count)
                )))
            else:
                maintainability = 100
            
            # Generate function-level metrics
            functions = []
            for name, metrics in visitor.functions.items():
                functions.append({
                    "name": name,
                    "complexity": metrics["complexity"],
                    "start_line": metrics["start_line"],
                    "end_line": metrics["end_line"],
                    "lines_of_code": metrics["lines_of_code"],
                    "max_nesting": metrics["max_nesting"],
                })
            
            # Generate summary
            summary = {
                "file_path": str(file_path),
                "line_count": line_count,
                "non_blank_lines": non_blank_lines,
                "comment_lines": comment_lines,
                "function_count": len(visitor.functions),
                "total_complexity": total_complexity,
                "average_complexity": avg_complexity,
                "max_complexity": max_complexity,
                "most_complex_function": most_complex_function,
                "cognitive_weight": cognitive_weight,
                "normalized_weight": normalized_weight,
                "maintainability_index": maintainability
            }
            
            return {
                "summary": summary,
                "functions": functions,
                "complexity_score": normalized_weight,
                "maintainability_score": maintainability
            }
            
        except SyntaxError as e:
            logger.error(f"Syntax error in {file_path}: {e}")
            return {
                "error": f"Syntax error: {e}",
                "summary": {
                    "file_path": str(file_path),
                    "error": f"Syntax error: {e}"
                },
                "functions": [],
                "complexity_score": 0,
                "maintainability_score": 0
            }
        except Exception as e:
            logger.error(f"Error analyzing complexity of {file_path}: {e}")
            return {
                "error": str(e),
                "summary": {
                    "file_path": str(file_path),
                    "error": str(e)
                },
                "functions": [],
                "complexity_score": 0,
                "maintainability_score": 0
            }
    
    def is_available(self) -> bool:
        """
        Check if complexity analysis is available.
        
        Returns:
            True (complexity analysis is always available as it uses built-in modules)
        """
        return True


# Register this tool runner
register_tool("complexity", ComplexityRunner)
