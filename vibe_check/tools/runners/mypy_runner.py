"""
MyPy Tool Runner
===========

This module provides a runner for the MyPy static type checker.

MyPy is used to perform static type checking of Python code.
"""

import json
import logging
import os
import shutil
from pathlib import Path
from typing import Any, Dict, List, Union

from .base_runner import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .tool_registry import register_tool, check_command_availability

logger = logging.getLogger("pat_mypy_runner")


class MypyRunner(ToolRunner):
    """Runner for the MyPy type checker."""

    def __init__(self, name: str = "mypy", config: Dict[str, Any] = None):
        """
        Initialize the MyPy runner.

        Args:
            name: Tool name
            config: Tool configuration
        """
        super().__init__(name, config)

        # Default config
        if self.config is None:
            self.config = {}

        if "mypy_ini" not in self.config:
            # Check for mypy.ini in various locations
            possible_paths = [
                Path.cwd() / "mypy.ini",
                Path.cwd() / ".mypy.ini",
                Path.cwd() / "setup.cfg"
            ]

            for path in possible_paths:
                if path.is_file():
                    self.config["mypy_ini"] = str(path)
                    break

    async def run(self, file_path: Union[str, Path], content: str) -> Dict[str, Any]:
        """
        Run MyPy on the file content.

        Args:
            file_path: Path to the file being analyzed
            content: File content as a string

        Returns:
            Dictionary with analysis results
        """
        try:
            # Create a temporary file with the content
            temp_path = self.create_temp_file(content, suffix='.py')

            try:
                # Build the command
                cmd = ["mypy", "--show-column-numbers", "--json", temp_path]

                # Add config file if available
                if "mypy_ini" in self.config:
                    cmd.extend(["--config-file", self.config["mypy_ini"]])

                # Add any additional arguments from config
                cmd.extend(self.get_config_args())

                # Run the command
                process_result = await self.run_process(cmd)

                # Parse the output
                if process_result["returncode"] == 0:
                    # No issues found
                    return {
                        "issues": [],
                        "summary": {
                            "error_count": 0,
                            "warning_count": 0,
                            "note_count": 0
                        }
                    }
                else:
                    try:
                        stdout = process_result["stdout"]
                        data = json.loads(stdout)
                        issues = []

                        # Process each issue
                        for msg in data.get("messages", []):
                            # Skip if this is for a different file
                            if Path(msg.get("file", "")).name != Path(temp_path).name:
                                continue

                            issues.append({
                                "line": msg.get("line", 0),
                                "column": msg.get("column", 0),
                                "message": msg.get("message", ""),
                                "code": f"mypy.{msg.get('category', 'error')}",
                                "severity": self._get_severity(msg.get("severity", "error"))
                            })

                        # Count by category
                        error_count = sum(1 for i in issues if i["severity"] == "HIGH")
                        warning_count = sum(1 for i in issues if i["severity"] == "MEDIUM")
                        note_count = sum(1 for i in issues if i["severity"] == "LOW")

                        return {
                            "issues": issues,
                            "summary": {
                                "error_count": error_count,
                                "warning_count": warning_count,
                                "note_count": note_count,
                                "total": len(issues)
                            },
                            "type_coverage": self._extract_type_coverage(process_result["stderr"])
                        }
                    except json.JSONDecodeError:
                        # Fall back to text parsing if JSON parsing fails
                        issues = []
                        lines = process_result["stderr"].splitlines()
                        for line in lines:
                            if ':' in line:
                                parts = line.split(':', 4)
                                if len(parts) >= 4:
                                    issues.append({
                                        "file": parts[0],
                                        "line": int(parts[1]) if parts[1].strip().isdigit() else 0,
                                        "column": int(parts[2]) if parts[2].strip().isdigit() else 0,
                                        "severity": self._get_severity(parts[3].strip()),
                                        "message": parts[4].strip() if len(parts) > 4 else "",
                                        "code": "mypy.error"
                                    })

                        return {
                            "issues": issues,
                            "summary": {
                                "error_count": sum(1 for i in issues if i["severity"] == "HIGH"),
                                "warning_count": sum(1 for i in issues if i["severity"] == "MEDIUM"),
                                "note_count": sum(1 for i in issues if i["severity"] == "LOW"),
                                "total": len(issues)
                            }
                        }
            finally:
                # Clean up the temporary file
                self.cleanup_temp_file(temp_path)

        except Exception as e:
            logger.error(f"Error running MyPy: {e}")
            return {
                "issues": [],
                "summary": {
                    "error_count": 0,
                    "warning_count": 0,
                    "note_count": 0,
                    "total": 0
                },
                "error": str(e)
            }

    def _get_severity(self, mypy_severity: str) -> str:
        """
        Map MyPy severity to standard severity level.

        Args:
            mypy_severity: MyPy severity string

        Returns:
            Standardized severity level
        """
        if mypy_severity in ("error", "fatal"):
            return "HIGH"
        elif mypy_severity in ("warning", "note"):
            return "MEDIUM"
        else:
            return "LOW"

    def _extract_type_coverage(self, stderr: str) -> float:
        """
        Extract type coverage from the MyPy output.

        Args:
            stderr: Standard error output from MyPy

        Returns:
            Type coverage percentage or 0.0 if not found
        """
        # First try to extract from the standard mypy output
        for line in stderr.splitlines():
            if "files checked" in line and "errors" in line:
                if "Your code" in line and "is" in line and "% type-annotated" in line:
                    # Example: "Your code is 85.42% type-annotated."
                    parts = line.split("Your code is ")
                    if len(parts) > 1:
                        percentage_part = parts[1].split("%")[0]
                        try:
                            return float(percentage_part)
                        except ValueError:
                            pass

        # If we couldn't extract from standard output, use a more detailed approach
        # by analyzing the content directly (this would be done by the caller)
        return 0.0

    def is_available(self) -> bool:
        """
        Check if MyPy is available on the system.

        Returns:
            True if MyPy is available, False otherwise
        """
        return check_command_availability("mypy")


# Register this tool runner
register_tool("mypy", MypyRunner)
