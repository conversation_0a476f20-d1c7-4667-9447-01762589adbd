"""
Tool Registry
==========

This module provides a registry for tool runners, allowing them to be
dynamically registered and retrieved based on their name.

The registry pattern enables decoupled extensibility of the tool system.
"""

import importlib
import logging
import shutil
from typing import Dict, List, Optional, Type

from .base_runner import ToolRunner

logger = logging.getLogger("vibe_check_tool_registry")

# Registry of tool runners
_TOOL_REGISTRY: Dict[str, Type[ToolRunner]] = {}


def register_tool(name: str, runner_class: Type[ToolRunner]) -> None:
    """
    Register a tool runner.

    Args:
        name: Name of the tool
        runner_class: Tool runner class
    """
    _TOOL_REGISTRY[name] = runner_class
    logger.debug(f"Registered tool runner for {name}")


def get_runner_for_tool(name: str, config: Optional[Dict] = None) -> Optional[ToolRunner]:
    """
    Get a runner for the specified tool.

    Args:
        name: Name of the tool
        config: Tool configuration

    Returns:
        Tool runner instance or None if no runner is registered
    """
    if name not in _TOOL_REGISTRY:
        logger.warning(f"No runner registered for tool {name}")
        return None

    runner_class = _TOOL_REGISTRY[name]
    runner = runner_class(name, config)

    # Check if the tool is available
    if not runner.is_available():
        logger.warning(f"Tool {name} is not available on the system")
        return None

    return runner


def is_tool_available(name: str) -> bool:
    """
    Check if a tool is available on the system.

    Args:
        name: Name of the tool

    Returns:
        True if the tool is registered and available, False otherwise
    """
    # Check in registry first
    if name not in _TOOL_REGISTRY:
        return False

    # Then check if it's available on the system
    return _TOOL_REGISTRY[name](name).is_available()


def list_available_tools() -> List[str]:
    """
    List all available tools.

    Returns:
        List of available tool names
    """
    available_tools = []
    for name in _TOOL_REGISTRY:
        if is_tool_available(name):
            available_tools.append(name)
    return available_tools


def register_builtin_tools() -> None:
    """Register all built-in tool runners."""
    # Import runners to trigger registration
    from . import ruff_runner
    from . import mypy_runner
    from . import bandit_runner
    from . import complexity_runner
    from . import doc_analyzer_runner
    # Note: markdown_runner is not available in this version


def check_command_availability(command: str) -> bool:
    """
    Check if a command is available on the system.

    Args:
        command: Command name

    Returns:
        True if the command is available, False otherwise
    """
    return shutil.which(command) is not None


# Register built-in tools when this module is imported
register_builtin_tools()
