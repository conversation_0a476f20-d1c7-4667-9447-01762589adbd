"""
Pylint Tool Runner
==============

This module provides a runner for the Pylint linter.

Pylint is a comprehensive linter that checks for coding standards and errors.
"""

import json
import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Union

from .base_runner import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .tool_registry import register_tool, check_command_availability

logger = logging.getLogger("vibe_check_pylint_runner")


class PylintRunner(ToolRunner):
    """Runner for the Pylint linter."""
    
    def __init__(self, name: str = "pylint", config: Dict[str, Any] = None):
        """
        Initialize the Pylint runner.
        
        Args:
            name: Tool name
            config: Tool configuration
        """
        super().__init__(name, config)
        
        # Default config
        if self.config is None:
            self.config = {}
        
        if "pylintrc" not in self.config:
            # Check for pylintrc in various locations
            possible_paths = [
                Path.cwd() / ".pylintrc",
                Path.cwd() / "pylintrc",
                Path.cwd() / "pyproject.toml",
                Path.cwd() / "setup.cfg"
            ]
            
            for path in possible_paths:
                if path.is_file():
                    self.config["pylintrc"] = str(path)
                    break
    
    async def run(self, file_path: Union[str, Path], content: str) -> Dict[str, Any]:
        """
        Run Pylint on the file content.
        
        Args:
            file_path: Path to the file being analyzed
            content: File content as a string
            
        Returns:
            Dictionary with analysis results
        """
        try:
            # Create a temporary file with the content
            temp_path = self.create_temp_file(content, suffix='.py')
            
            try:
                # Build the command
                cmd = ["pylint", "--output-format=json", temp_path]
                
                # Add config file if available
                if "pylintrc" in self.config:
                    cmd.extend(["--rcfile", self.config["pylintrc"]])
                
                # Add any additional arguments from config
                cmd.extend(self.get_config_args())
                
                # Run the command
                process_result = await self.run_process(cmd)
                
                # Parse the output
                if process_result["returncode"] == 0:
                    # No issues found
                    return {
                        "issues": [],
                        "summary": {
                            "error_count": 0,
                            "warning_count": 0,
                            "convention_count": 0,
                            "refactor_count": 0,
                            "total": 0
                        }
                    }
                else:
                    try:
                        stdout = process_result["stdout"]
                        data = json.loads(stdout)
                        issues = []
                        
                        # Process each issue
                        for msg in data:
                            # Skip if this is for a different file
                            if Path(msg.get("path", "")).name != Path(temp_path).name:
                                continue
                                
                            issues.append({
                                "line": msg.get("line", 0),
                                "column": msg.get("column", 0),
                                "message": msg.get("message", ""),
                                "code": f"pylint.{msg.get('symbol', 'error')}",
                                "severity": self._get_severity(msg.get("type", "error"))
                            })
                        
                        # Count by category
                        error_count = sum(1 for i in issues if i["severity"] == "HIGH")
                        warning_count = sum(1 for i in issues if i["severity"] == "MEDIUM")
                        convention_count = sum(1 for i in issues if i["severity"] == "LOW")
                        refactor_count = sum(1 for i in issues if i["severity"] == "LOW")
                        
                        return {
                            "issues": issues,
                            "summary": {
                                "error_count": error_count,
                                "warning_count": warning_count,
                                "convention_count": convention_count,
                                "refactor_count": refactor_count,
                                "total": len(issues)
                            }
                        }
                    except json.JSONDecodeError:
                        # Fall back to text parsing if JSON parsing fails
                        issues = []
                        lines = process_result["stderr"].splitlines()
                        for line in lines:
                            if ':' in line:
                                parts = line.split(':', 4)
                                if len(parts) >= 4:
                                    issues.append({
                                        "file": parts[0],
                                        "line": int(parts[1]) if parts[1].strip().isdigit() else 0,
                                        "column": 0,
                                        "severity": self._get_severity(parts[2].strip()),
                                        "message": parts[3].strip() if len(parts) > 3 else "",
                                        "code": "pylint.error"
                                    })
                        
                        return {
                            "issues": issues,
                            "summary": {
                                "error_count": sum(1 for i in issues if i["severity"] == "HIGH"),
                                "warning_count": sum(1 for i in issues if i["severity"] == "MEDIUM"),
                                "convention_count": sum(1 for i in issues if i["severity"] == "LOW"),
                                "refactor_count": 0,
                                "total": len(issues)
                            }
                        }
            finally:
                # Clean up the temporary file
                self.cleanup_temp_file(temp_path)
                
        except Exception as e:
            logger.error(f"Error running Pylint: {e}")
            return {
                "issues": [],
                "summary": {
                    "error_count": 0,
                    "warning_count": 0,
                    "convention_count": 0,
                    "refactor_count": 0,
                    "total": 0
                },
                "error": str(e)
            }
    
    def _get_severity(self, pylint_type: str) -> str:
        """
        Map Pylint message type to standard severity level.
        
        Args:
            pylint_type: Pylint message type
            
        Returns:
            Standardized severity level
        """
        if pylint_type in ("error", "fatal"):
            return "HIGH"
        elif pylint_type in ("warning"):
            return "MEDIUM"
        else:  # convention, refactor, info
            return "LOW"
    
    def is_available(self) -> bool:
        """
        Check if Pylint is available on the system.
        
        Returns:
            True if Pylint is available, False otherwise
        """
        return check_command_availability("pylint")


# Register this tool runner
register_tool("pylint", PylintRunner)
