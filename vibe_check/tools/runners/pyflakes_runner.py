"""
Pyflakes Tool Runner
===============

This module provides a runner for the Pyflakes linter.

Pyflakes is a simple but fast tool for finding errors in Python code.
"""

import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Union

from .base_runner import <PERSON><PERSON><PERSON>un<PERSON>
from .tool_registry import register_tool, check_command_availability

logger = logging.getLogger("vibe_check_pyflakes_runner")


class PyflakesRunner(ToolRunner):
    """Runner for the Pyflakes linter."""
    
    def __init__(self, name: str = "pyflakes", config: Dict[str, Any] = None):
        """
        Initialize the Pyflakes runner.
        
        Args:
            name: Tool name
            config: Tool configuration
        """
        super().__init__(name, config)
        
        # Default config
        if self.config is None:
            self.config = {}
    
    async def run(self, file_path: Union[str, Path], content: str) -> Dict[str, Any]:
        """
        Run Pyflakes on the file content.
        
        Args:
            file_path: Path to the file being analyzed
            content: File content as a string
            
        Returns:
            Dictionary with analysis results
        """
        try:
            # Create a temporary file with the content
            temp_path = self.create_temp_file(content, suffix='.py')
            
            try:
                # Build the command
                cmd = ["pyflakes", temp_path]
                
                # Add any additional arguments from config
                cmd.extend(self.get_config_args())
                
                # Run the command
                process_result = await self.run_process(cmd)
                
                # Parse the output
                if process_result["returncode"] == 0:
                    # No issues found
                    return {
                        "issues": [],
                        "summary": {
                            "error_count": 0,
                            "total": 0
                        }
                    }
                else:
                    # Parse the output
                    issues = []
                    lines = process_result["stdout"].splitlines()
                    
                    for line in lines:
                        if ':' in line:
                            parts = line.split(':', 3)
                            if len(parts) >= 3:
                                # Extract line number and message
                                try:
                                    line_num = int(parts[1])
                                    message = parts[2].strip()
                                    if len(parts) > 3:
                                        message += ": " + parts[3].strip()
                                        
                                    issues.append({
                                        "line": line_num,
                                        "column": 0,  # Pyflakes doesn't provide column info
                                        "message": message,
                                        "code": "pyflakes.error",
                                        "severity": "MEDIUM"  # All Pyflakes issues are warnings
                                    })
                                except ValueError:
                                    # Skip lines that don't match the expected format
                                    continue
                    
                    return {
                        "issues": issues,
                        "summary": {
                            "error_count": 0,
                            "warning_count": len(issues),
                            "total": len(issues)
                        }
                    }
            finally:
                # Clean up the temporary file
                self.cleanup_temp_file(temp_path)
                
        except Exception as e:
            logger.error(f"Error running Pyflakes: {e}")
            return {
                "issues": [],
                "summary": {
                    "error_count": 0,
                    "warning_count": 0,
                    "total": 0
                },
                "error": str(e)
            }
    
    def is_available(self) -> bool:
        """
        Check if Pyflakes is available on the system.
        
        Returns:
            True if Pyflakes is available, False otherwise
        """
        return check_command_availability("pyflakes")


# Register this tool runner
register_tool("pyflakes", PyflakesRunner)
