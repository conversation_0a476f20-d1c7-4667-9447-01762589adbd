"""
Pylint Parser
==========

This module provides a parser for Pylint output.
"""

import logging
from typing import Any, Dict, List

from .base_parser import ToolParser
from .parser_registry import register_parser

logger = logging.getLogger("vibe_check_pylint_parser")


class PylintParser(ToolParser):
    """Parser for Pylint output."""
    
    def parse(self, output: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """
        Parse the Pylint output into a standardized format.
        
        Args:
            output: Raw Pylint output
            file_path: Path to the file that was analyzed
            
        Returns:
            Parsed Pylint output in a standardized format
        """
        # Check for errors in the output
        if "error" in output:
            logger.warning(f"Error in Pylint output: {output['error']}")
            return {
                "issues": [],
                "summary": {
                    "total": 0,
                    "by_type": {},
                    "error": output["error"]
                }
            }
            
        # Extract issues from the output
        raw_issues = output.get("issues", [])
        normalized_issues = self._normalize_issues(raw_issues, file_path)
        
        # Create or extract summary
        summary = output.get("summary", {})
        if not summary or "by_type" not in summary:
            # Create summary if not provided or incomplete
            summary = self._create_summary(normalized_issues, summary)
        
        return {
            "issues": normalized_issues,
            "summary": summary
        }
    
    def _normalize_issues(self, raw_issues: List[Dict[str, Any]], file_path: str) -> List[Dict[str, Any]]:
        """
        Normalize Pylint issues into a standardized format.
        
        Args:
            raw_issues: Raw Pylint issues
            file_path: Path to the file that was analyzed
            
        Returns:
            Normalized issues
        """
        normalized = []
        
        for issue in raw_issues:
            # Extract basic issue information
            line = issue.get("line", 0)
            column = issue.get("column", 0)
            message = issue.get("message", "")
            code = issue.get("code", "pylint.error")
            severity = issue.get("severity", "MEDIUM")
            
            # Determine category from the code
            parts = code.split(".")
            category = parts[1] if len(parts) > 1 else "error"
            
            # Create normalized issue
            normalized_issue = {
                "tool": "pylint",
                "code": code,
                "message": message,
                "location": {
                    "file": file_path,
                    "line": line,
                    "column": column
                },
                "severity": severity,
                "category": category,
                "type": "style_error"
            }
            
            normalized.append(normalized_issue)
            
        return normalized
    
    def _create_summary(self, normalized_issues: List[Dict[str, Any]], 
                      base_summary: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a summary of the normalized issues.
        
        Args:
            normalized_issues: Normalized issues
            base_summary: Base summary from the output
            
        Returns:
            Summary dictionary
        """
        total = len(normalized_issues)
        
        # Count by severity
        by_severity = {
            "HIGH": 0,
            "MEDIUM": 0,
            "LOW": 0
        }
        
        # Count by category
        by_category: Dict[str, int] = {}
        
        for issue in normalized_issues:
            # Count by severity
            severity = issue.get("severity", "MEDIUM")
            by_severity[severity] = by_severity.get(severity, 0) + 1
            
            # Count by category
            category = issue.get("category", "error")
            by_category[category] = by_category.get(category, 0) + 1
        
        # Create summary
        summary = {
            "total": total,
            "by_severity": by_severity,
            "by_category": by_category
        }
        
        # Copy additional fields from base_summary
        for key, value in base_summary.items():
            if key not in summary:
                summary[key] = value
        
        return summary


# Register this parser
register_parser("pylint", PylintParser)
