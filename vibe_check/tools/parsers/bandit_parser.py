"""
Bandit Parser
==========

This module provides a parser for Bandit security analyzer output.
"""

import logging
from typing import Any, Dict, List, Optional

from .base_parser import ToolParser
from .parser_registry import register_parser

logger = logging.getLogger("pat_bandit_parser")


class BanditParser(ToolParser):
    """Parser for the Bandit security analyzer output."""

    def __init__(self, name: str = "bandit", config: Optional[Dict[str, Any]] = None):
        """
        Initialize the Bandit parser.

        Args:
            name: Tool name
            config: Parser configuration
        """
        super().__init__(name, config)

    def parse(self, output: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """
        Parse the Bandit output into a standardized format.

        Args:
            output: Raw Bandit output
            file_path: Path to the file that was analyzed

        Returns:
            Parsed Bandit output in a standardized format
        """
        # Check for errors in the output
        if "error" in output:
            logger.warning(f"Error in Bandit output: {output['error']}")
            return {
                "issues": [],
                "summary": {
                    "total": 0,
                    "by_type": {},
                    "error": output["error"]
                },
                "security_score": 100.0  # Assume perfect if we can't assess
            }

        # Extract issues from the output
        raw_issues = output.get("issues", [])
        normalized_issues = self._normalize_issues(raw_issues, file_path)

        # Extract or create summary
        summary = self._create_summary(normalized_issues, output.get("summary", {}))

        # Calculate security score (100 - weighted issue count)
        # Higher severity issues have more impact on the score
        high_count = summary.get("by_severity", {}).get("HIGH", 0)
        medium_count = summary.get("by_severity", {}).get("MEDIUM", 0)
        low_count = summary.get("by_severity", {}).get("LOW", 0)

        # Weighted calculation: high=5, medium=3, low=1
        weighted_count = (high_count * 5) + (medium_count * 3) + (low_count * 1)
        max_score = 100.0
        min_score = 50.0

        # Calculate score - deduct points for issues, but don't go below min_score
        security_score = max(min_score, max_score - (weighted_count * 2))

        return {
            "issues": normalized_issues,
            "summary": summary,
            "security_score": security_score
        }

    def _normalize_issues(self, raw_issues: List[Dict[str, Any]],
                        file_path: str) -> List[Dict[str, Any]]:
        """
        Normalize Bandit issues to a standard format.

        Args:
            raw_issues: Raw issues from Bandit
            file_path: Path to the file that was analyzed

        Returns:
            Normalized issues
        """
        normalized = []

        for issue in raw_issues:
            # Extract basic issue information
            code = issue.get("code", "")
            name = issue.get("name", "")
            message = issue.get("message", "")
            line = issue.get("line", 0)
            severity = issue.get("severity", "MEDIUM")
            confidence = issue.get("confidence", "MEDIUM")
            more_info = issue.get("more_info", "")

            # Standardize severity
            if isinstance(severity, str):
                severity = severity.upper()

            # Create normalized issue
            normalized_issue = {
                "tool": "bandit",
                "code": code,
                "name": name or code,
                "message": message,
                "location": {
                    "file": file_path,
                    "line": line,
                    "column": 0  # Bandit doesn't provide column information
                },
                "severity": severity,
                "confidence": confidence,
                "type": "security",
                "more_info": more_info
            }

            normalized.append(normalized_issue)

        return normalized

    def _create_summary(self, normalized_issues: List[Dict[str, Any]],
                      base_summary: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a summary of the normalized issues.

        Args:
            normalized_issues: Normalized issues
            base_summary: Base summary from the output

        Returns:
            Summary dictionary
        """
        total = len(normalized_issues)

        # Count by severity
        by_severity = {
            "HIGH": 0,
            "MEDIUM": 0,
            "LOW": 0
        }

        # Count by category/test_id
        by_category: Dict[str, int] = {}

        # Populate the counts
        for issue in normalized_issues:
            # Count by severity
            severity = issue.get("severity", "MEDIUM")
            by_severity[severity] = by_severity.get(severity, 0) + 1

            # Count by category (code)
            code = issue.get("code", "unknown")
            by_category[code] = by_category.get(code, 0) + 1

        # Use the base summary counts if available, but only if we don't have our own counts
        # This ensures we use our actual counts from normalized_issues
        if total == 0:
            if "high" in base_summary:
                by_severity["HIGH"] = base_summary.get("high", 0)
            if "medium" in base_summary:
                by_severity["MEDIUM"] = base_summary.get("medium", 0)
            if "low" in base_summary:
                by_severity["LOW"] = base_summary.get("low", 0)

        # Create summary
        summary = {
            "total": total,
            "by_severity": by_severity,
            "by_category": by_category
        }

        return summary


# Register this parser
register_parser("bandit", BanditParser)
