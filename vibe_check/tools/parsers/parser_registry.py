"""
Parser Registry
==========

This module provides a registry for tool parsers, allowing them to be
dynamically registered and retrieved based on their name.

The registry pattern enables decoupled extensibility of the parser system.
"""

import logging
from typing import Dict, Optional, Type

from .base_parser import ToolParser

logger = logging.getLogger("vibe_check_parser_registry")

# Registry of tool parsers
_PARSER_REGISTRY: Dict[str, Type[ToolParser]] = {}


def register_parser(name: str, parser_class: Type[ToolParser]) -> None:
    """
    Register a tool parser.

    Args:
        name: Name of the tool
        parser_class: Tool parser class
    """
    _PARSER_REGISTRY[name] = parser_class
    logger.debug(f"Registered tool parser for {name}")


def get_parser_for_tool(name: str, config: Optional[Dict] = None) -> Optional[ToolParser]:
    """
    Get a parser for the specified tool.

    Args:
        name: Name of the tool
        config: Parser configuration

    Returns:
        Tool parser instance or None if no parser is registered
    """
    if name not in _PARSER_REGISTRY:
        logger.warning(f"No parser registered for tool {name}")
        return None

    parser_class = _PARSER_REGISTRY[name]
    return parser_class(name, config)


def register_builtin_parsers() -> None:
    """Register all built-in tool parsers."""
    # Import parsers to trigger registration
    from . import ruff_parser
    from . import mypy_parser
    from . import bandit_parser
    from . import complexity_parser
    # Note: markdown_parser is not available in this version


# Register built-in parsers when this module is imported
register_builtin_parsers()
