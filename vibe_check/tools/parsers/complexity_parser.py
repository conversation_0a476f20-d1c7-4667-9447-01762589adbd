"""
Complexity Parser
=============

This module provides a parser for code complexity analysis output.
"""

import logging
from typing import Any, Dict, List, Optional

from .base_parser import ToolParser
from .parser_registry import register_parser

logger = logging.getLogger("pat_complexity_parser")


class ComplexityParser(ToolParser):
    """Parser for code complexity analyzer output."""
    
    def __init__(self, name: str = "complexity", config: Optional[Dict[str, Any]] = None):
        """
        Initialize the complexity parser.
        
        Args:
            name: Tool name
            config: Parser configuration
        """
        super().__init__(name, config)
    
    def parse(self, output: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """
        Parse the complexity analyzer output into a standardized format.
        
        Args:
            output: Raw complexity analyzer output
            file_path: Path to the file that was analyzed
            
        Returns:
            Parsed complexity output in a standardized format
        """
        # Check for errors in the output
        if "error" in output:
            logger.warning(f"Error in complexity output: {output['error']}")
            return {
                "error": output["error"],
                "complexity_metrics": {},
                "complexity_score": 0,
                "maintainability_score": 0,
                "function_metrics": []
            }
        
        # Extract complexity scores
        complexity_score = output.get("complexity_score", 0)
        maintainability_score = output.get("maintainability_score", 100)
        
        # Extract summary metrics
        summary = output.get("summary", {})
        
        # Extract function metrics and sort by complexity (descending)
        functions = output.get("functions", [])
        sorted_functions = sorted(
            functions,
            key=lambda f: f.get("complexity", 0),
            reverse=True
        )
        
        # Create complexity ratings for functions
        rated_functions = []
        for func in sorted_functions:
            complexity = func.get("complexity", 0)
            
            # Assign rating based on complexity
            if complexity <= 5:
                rating = "low"
                severity = "LOW"
            elif complexity <= 10:
                rating = "medium"
                severity = "MEDIUM"
            else:
                rating = "high"
                severity = "HIGH"
            
            rated_functions.append({
                "name": func.get("name", ""),
                "complexity": complexity,
                "rating": rating,
                "severity": severity,
                "start_line": func.get("start_line", 0),
                "end_line": func.get("end_line", 0),
                "lines_of_code": func.get("lines_of_code", 0),
                "max_nesting": func.get("max_nesting", 0)
            })
        
        # Create issues for complex functions
        issues = []
        for func in rated_functions:
            if func["severity"] != "LOW":
                issues.append({
                    "tool": "complexity",
                    "code": "high_complexity",
                    "name": "High Complexity",
                    "message": f"Function {func['name']} has a complexity of {func['complexity']}",
                    "location": {
                        "file": file_path,
                        "line": func["start_line"],
                        "column": 0
                    },
                    "severity": func["severity"],
                    "type": "complexity",
                    "function": func["name"],
                    "complexity": func["complexity"]
                })
        
        # Create a rating for the whole file
        if complexity_score <= 25:
            file_rating = "low"
            file_severity = "LOW"
        elif complexity_score <= 60:
            file_rating = "medium"
            file_severity = "MEDIUM"
        else:
            file_rating = "high"
            file_severity = "HIGH"
        
        # Create a maintainability rating
        if maintainability_score >= 80:
            maintainability_rating = "high"
        elif maintainability_score >= 60:
            maintainability_rating = "medium"
        else:
            maintainability_rating = "low"
        
        # Create detailed complexity metrics
        complexity_metrics = {
            "complexity_score": complexity_score,
            "complexity_rating": file_rating,
            "complexity_severity": file_severity,
            "maintainability_score": maintainability_score,
            "maintainability_rating": maintainability_rating,
            "function_count": summary.get("function_count", 0),
            "line_count": summary.get("line_count", 0),
            "non_blank_lines": summary.get("non_blank_lines", 0),
            "comment_lines": summary.get("comment_lines", 0),
            "total_complexity": summary.get("total_complexity", 0),
            "average_complexity": summary.get("average_complexity", 0),
            "max_complexity": summary.get("max_complexity", 0),
            "most_complex_function": summary.get("most_complex_function", "")
        }
        
        # Add complexity score distributions
        low_complexity = sum(1 for f in rated_functions if f["severity"] == "LOW")
        medium_complexity = sum(1 for f in rated_functions if f["severity"] == "MEDIUM")
        high_complexity = sum(1 for f in rated_functions if f["severity"] == "HIGH")
        
        complexity_metrics["complexity_distribution"] = {
            "low": low_complexity,
            "medium": medium_complexity,
            "high": high_complexity
        }
        
        # Add nesting metrics if available
        max_nesting = max([f.get("max_nesting", 0) for f in functions], default=0)
        avg_nesting = (
            sum(f.get("max_nesting", 0) for f in functions) / len(functions)
            if functions else 0
        )
        
        complexity_metrics["max_nesting"] = max_nesting
        complexity_metrics["average_nesting"] = avg_nesting
        
        # Add summary of function metrics
        function_summary = {}
        for func in rated_functions:
            function_summary[func["name"]] = {
                "complexity": func["complexity"],
                "rating": func["rating"],
                "lines": func["lines_of_code"]
            }
        
        return {
            "issues": issues,
            "complexity_metrics": complexity_metrics,
            "complexity_score": complexity_score,
            "maintainability_score": maintainability_score,
            "function_metrics": rated_functions,
            "function_summary": function_summary
        }


# Register this parser
register_parser("complexity", ComplexityParser)
