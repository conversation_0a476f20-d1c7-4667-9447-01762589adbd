"""
Ruff Parser
=========

This module provides a parser for Ruff linter output.
"""

import logging
from typing import Any, Dict, List, Optional

from .base_parser import ToolParser
from .parser_registry import register_parser

logger = logging.getLogger("pat_ruff_parser")


class RuffParser(ToolParser):
    """Parser for the Ruff linter output."""
    
    def __init__(self, name: str = "ruff", config: Optional[Dict[str, Any]] = None):
        """
        Initialize the Ruff parser.
        
        Args:
            name: Tool name
            config: Parser configuration
        """
        super().__init__(name, config)
    
    def parse(self, output: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """
        Parse the Ruff output into a standardized format.
        
        Args:
            output: Raw Ruff output
            file_path: Path to the file that was analyzed
            
        Returns:
            Parsed Ruff output in a standardized format
        """
        # Check for errors in the output
        if "error" in output:
            logger.warning(f"Error in Ruff output: {output['error']}")
            return {
                "issues": [],
                "summary": {
                    "total": 0,
                    "by_type": {},
                    "error": output["error"]
                }
            }
            
        # Extract issues from the output
        raw_issues = output.get("issues", [])
        normalized_issues = self._normalize_issues(raw_issues, file_path)
        
        # Extract or create summary
        summary = output.get("summary", {})
        if not summary:
            # Create summary if not provided
            summary = self._create_summary(normalized_issues)
            
        return {
            "issues": normalized_issues,
            "summary": summary
        }
    
    def _normalize_issues(self, raw_issues: List[Dict[str, Any]], 
                        file_path: str) -> List[Dict[str, Any]]:
        """
        Normalize Ruff issues to a standard format.
        
        Args:
            raw_issues: Raw issues from Ruff
            file_path: Path to the file that was analyzed
            
        Returns:
            Normalized issues
        """
        normalized = []
        
        for issue in raw_issues:
            # Extract basic issue information
            code = issue.get("code", "")
            message = issue.get("message", "")
            line = issue.get("line", 0)
            column = issue.get("column", 0)
            
            # Determine severity based on rule code
            severity = self._get_severity_from_code(code)
            
            # Create normalized issue
            normalized_issue = {
                "tool": "ruff",
                "code": code,
                "message": message,
                "location": {
                    "file": file_path,
                    "line": line,
                    "column": column
                },
                "severity": severity
            }
            
            normalized.append(normalized_issue)
            
        return normalized
    
    def _get_severity_from_code(self, code: str) -> str:
        """
        Determine severity based on Ruff error code.
        
        Args:
            code: Ruff error code (e.g., "E501", "F401")
            
        Returns:
            Severity level: "HIGH", "MEDIUM", or "LOW"
        """
        # Ruff error code prefixes and their severities
        severity_map = {
            # Errors (high severity)
            "E": "HIGH",    # PEP8 errors
            "F": "HIGH",    # Pyflakes errors (undefined names, imports)
            
            # Warnings (medium severity)
            "W": "MEDIUM",  # PEP8 warnings
            "C": "MEDIUM",  # McCabe complexity
            "I": "MEDIUM",  # Isort
            
            # Style (low severity)
            "D": "LOW",     # Docstring issues
            "S": "HIGH",    # Security issues are actually high severity
            "B": "MEDIUM",  # Bugbear
            "N": "LOW",     # Naming
            "A": "LOW",     # Annotations
        }
        
        if not code:
            return "MEDIUM"
            
        # Get the first character of the code
        prefix = code[0] if code else ""
        
        # Return the mapped severity or medium as default
        return severity_map.get(prefix, "MEDIUM")
    
    def _create_summary(self, normalized_issues: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create a summary of the normalized issues.
        
        Args:
            normalized_issues: Normalized issues
            
        Returns:
            Summary dictionary
        """
        total = len(normalized_issues)
        
        # Count by severity
        by_severity = {
            "HIGH": 0,
            "MEDIUM": 0,
            "LOW": 0
        }
        
        # Count by type (first letter of the code)
        by_type: Dict[str, int] = {}
        
        for issue in normalized_issues:
            # Count by severity
            severity = issue.get("severity", "MEDIUM")
            by_severity[severity] = by_severity.get(severity, 0) + 1
            
            # Count by type
            code = issue.get("code", "")
            type_code = code[0] if code else "?"
            by_type[type_code] = by_type.get(type_code, 0) + 1
            
        return {
            "total": total,
            "by_severity": by_severity,
            "by_type": by_type
        }


# Register this parser
register_parser("ruff", RuffParser)
