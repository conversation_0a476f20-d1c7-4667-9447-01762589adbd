"""
Vibe Check Tool Parsers
=============

This module provides parsers for various analysis tool outputs.
Parsers are responsible for processing the raw output from tools and
converting it into standardized data structures.

Tool parsers follow a common interface, allowing them to be used interchangeably
and dynamically loaded based on configuration.
"""

from .base_parser import ToolParser
from .parser_registry import register_parser, get_parser_for_tool as get_parser
from .ruff_parser import RuffParser
from .mypy_parser import MypyParser
from .bandit_parser import BanditParser
from .complexity_parser import ComplexityParser
from .pylint_parser import PylintParser
from .pyflakes_parser import PyflakesParser
from .custom_rules_parser import CustomRulesParser
# Note: MarkdownParser is not available in this version

__all__ = [
    "ToolParser",
    "register_parser",
    "get_parser",
    "RuffParser",
    "MypyParser",
    "BanditParser",
    "ComplexityParser",
    "PylintParser",
    "PyflakesParser",
    "CustomRulesParser",
]
