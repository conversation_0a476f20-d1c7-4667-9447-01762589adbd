"""
Base Tool Parser
============

This module defines the base class for all tool parsers.
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

logger = logging.getLogger("pat_tool_parser")


class ToolParser(ABC):
    """
    Base class for tool parsers.
    
    Tool parsers are responsible for processing the raw output from tools
    and converting it into standardized data structures that can be used
    by the rest of the system.
    """
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the tool parser.
        
        Args:
            name: Tool name
            config: Parser configuration
        """
        self.name = name
        self.config = config or {}
    
    @abstractmethod
    def parse(self, output: Dict[str, Any], file_path: str) -> Dict[str, Any]:
        """
        Parse the tool output into a standardized format.
        
        Args:
            output: Raw tool output
            file_path: Path to the file that was analyzed
            
        Returns:
            Parsed tool output in a standardized format
        """
        pass
    
    def extract_issues(self, raw_issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract and normalize issues from the raw tool output.
        
        Args:
            raw_issues: Raw issues from the tool
            
        Returns:
            Normalized issues
        """
        return raw_issues
    
    def get_severity(self, raw_severity: Any) -> str:
        """
        Map a raw severity value to a standard severity level.
        
        Args:
            raw_severity: Raw severity value from the tool
            
        Returns:
            Standardized severity level: "HIGH", "MEDIUM", or "LOW"
        """
        if raw_severity is None:
            return "MEDIUM"
            
        severity_str = str(raw_severity).upper()
        
        if severity_str in ("HIGH", "CRITICAL", "ERROR", "SEVERE"):
            return "HIGH"
        elif severity_str in ("MEDIUM", "WARNING", "WARN", "MODERATE"):
            return "MEDIUM"
        else:
            return "LOW"
