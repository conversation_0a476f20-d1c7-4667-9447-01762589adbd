from typing import List, Dict, Any
"""
Custom Python Rules
===============

This module provides custom rules for detecting common issues in Python codebases.
"""

import ast
import re
from typing import Dict, List, Any, Tuple, Optional, Set


class PythonRuleChecker:
    """
    Custom rule checker for Python code.
    
    This class analyzes Python code to detect common issues that might not be
    caught by standard linters.
    """
    
    def __init__(self) -> None:
        """Initialize the rule checker."""
        self.issues: List[Dict[str, Any]] = []
        self.current_file = ""
    
    def check_file(self, file_path: str, content: str) -> Dict[str, Any]:
        """
        Check a file for issues.
        
        Args:
            file_path: Path to the file
            content: File content as a string
            
        Returns:
            Dictionary with analysis results
        """
        self.issues = []
        self.current_file = file_path
        
        # Run all checks
        self._check_ast(content)
        self._check_patterns(content)
        
        # Create summary
        summary: Dict[str, Any] = {
            "total": len(self.issues),
            "by_category": {},
            "by_severity": {
                "HIGH": 0,
                "MEDIUM": 0,
                "LOW": 0
            }
        }
        
        # Count issues by category and severity
        for issue in self.issues:
            category = issue.get("category", "unknown")
            severity = issue.get("severity", "MEDIUM")
            if category not in summary["by_category"]:  # type: ignore[operator]
                summary["by_category"][category] = 0
            summary["by_category"][category] += 1
            summary["by_severity"][severity] += 1
        return {
            "issues": self.issues,
            "summary": summary
        }
    
    def _check_ast(self, content: str) -> None:
        """
        Check the code using AST analysis.
        
        Args:
            content: File content as a string
        """
        try:
            tree = ast.parse(content)
            
            # Check for various issues using AST
            self._check_mutable_defaults(tree)
            self._check_exception_handling(tree)
            self._check_resource_management(tree)
            self._check_global_variables(tree)
            
        except SyntaxError:
            # If we can't parse the file, skip AST checks
            pass
    
    def _check_patterns(self, content: str) -> None:
        """
        Check the code using regex patterns.
        
        Args:
            content: File content as a string
        """
        lines = content.splitlines()
        
        # Check for various issues using regex
        self._check_print_statements(lines)
        self._check_hardcoded_paths(lines)
        self._check_todo_comments(lines)
        self._check_commented_code(lines)
    
    def _check_mutable_defaults(self, tree: ast.AST) -> None:
        """
        Check for mutable default arguments.
        
        Args:
            tree: AST tree
        """
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                for default in node.args.defaults:
                    if isinstance(default, (ast.List, ast.Dict, ast.Set)):
                        self.issues.append({
                            "tool": "custom_rules",
                            "code": "custom.mutable_default",
                            "message": f"Mutable default argument in function '{node.name}'",
                            "location": {
                                "file": self.current_file,
                                "line": node.lineno,
                                "column": node.col_offset
                            },
                            "severity": "MEDIUM",
                            "category": "mutable_default",
                            "type": "bug"
                        })
    
    def _check_exception_handling(self, tree: ast.AST) -> None:
        """
        Check for bare exception handling.
        
        Args:
            tree: AST tree
        """
        for node in ast.walk(tree):
            if isinstance(node, ast.ExceptHandler):
                if node.type is None:
                    self.issues.append({
                        "tool": "custom_rules",
                        "code": "custom.bare_except",
                        "message": "Bare except clause",
                        "location": {
                            "file": self.current_file,
                            "line": node.lineno,
                            "column": node.col_offset
                        },
                        "severity": "MEDIUM",
                        "category": "exception_handling",
                        "type": "bug"
                    })
    
    def _check_resource_management(self, tree: ast.AST) -> None:
        """
        Check for proper resource management.
        
        Args:
            tree: AST tree
        """
        # Track open() calls that are not in with statements
        open_calls = set()
        with_contexts = set()
        
        # Find all open() calls
        for node in ast.walk(tree):
            if isinstance(node, ast.Call) and isinstance(node.func, ast.Name) and node.func.id == 'open':
                open_calls.add(node)
        
        # Find all with statements using open()
        for node in ast.walk(tree):
            if isinstance(node, ast.With):
                for item in node.items:
                    if isinstance(item.context_expr, ast.Call) and isinstance(item.context_expr.func, ast.Name) and item.context_expr.func.id == 'open':
                        with_contexts.add(item.context_expr)
        
        # Report open() calls not in with statements
        for call in open_calls:
            if call not in with_contexts:
                self.issues.append({
                    "tool": "custom_rules",
                    "code": "custom.open_without_with",
                    "message": "File opened without using 'with' statement",
                    "location": {
                        "file": self.current_file,
                        "line": call.lineno,
                        "column": call.col_offset
                    },
                    "severity": "MEDIUM",
                    "category": "resource_management",
                    "type": "bug"
                })
    
    def _check_global_variables(self, tree: ast.AST) -> None:
        """
        Check for excessive use of global variables.
        
        Args:
            tree: AST tree
        """
        # Count global variables
        global_vars = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Global):
                global_vars.extend(node.names)
        
        # Report if there are too many global variables
        if len(global_vars) > 5:
            self.issues.append({
                "tool": "custom_rules",
                "code": "custom.too_many_globals",
                "message": f"Too many global variables ({len(global_vars)})",
                "location": {
                    "file": self.current_file,
                    "line": 1,
                    "column": 0
                },
                "severity": "MEDIUM",
                "category": "global_variables",
                "type": "maintainability"
            })
    
    def _check_print_statements(self, lines: List[str]) -> None:
        """
        Check for print statements in production code.
        
        Args:
            lines: Lines of code
        """
        for i, line in enumerate(lines):
            if re.search(r'\bprint\s*\(', line) and not line.strip().startswith('#'):
                self.issues.append({
                    "tool": "custom_rules",
                    "code": "custom.print_statement",
                    "message": "Print statement in production code",
                    "location": {
                        "file": self.current_file,
                        "line": i + 1,
                        "column": 0
                    },
                    "severity": "LOW",
                    "category": "debugging",
                    "type": "style"
                })
    
    def _check_hardcoded_paths(self, lines: List[str]) -> None:
        """
        Check for hardcoded file paths.
        
        Args:
            lines: Lines of code
        """
        path_pattern = r'[\'"](?:/[^/\'"]+)+[\'"]|[\'"](?:[A-Za-z]:\\[^\\\'"]+(\\[^\\\'"])*)[\'"]'
        
        for i, line in enumerate(lines):
            if re.search(path_pattern, line) and not line.strip().startswith('#'):
                self.issues.append({
                    "tool": "custom_rules",
                    "code": "custom.hardcoded_path",
                    "message": "Hardcoded file path",
                    "location": {
                        "file": self.current_file,
                        "line": i + 1,
                        "column": 0
                    },
                    "severity": "MEDIUM",
                    "category": "hardcoded_values",
                    "type": "maintainability"
                })
    
    def _check_todo_comments(self, lines: List[str]) -> None:
        """
        Check for TODO comments.
        
        Args:
            lines: Lines of code
        """
        for i, line in enumerate(lines):
            if re.search(r'#\s*TODO', line, re.IGNORECASE):
                self.issues.append({
                    "tool": "custom_rules",
                    "code": "custom.todo",
                    "message": "TODO comment",
                    "location": {
                        "file": self.current_file,
                        "line": i + 1,
                        "column": 0
                    },
                    "severity": "LOW",
                    "category": "todo",
                    "type": "info"
                })
    
    def _check_commented_code(self, lines: List[str]) -> None:
        """
        Check for commented-out code.
        
        Args:
            lines: Lines of code
        """
        code_patterns = [
            r'#\s*def\s+\w+',
            r'#\s*class\s+\w+',
            r'#\s*if\s+.*:',
            r'#\s*for\s+.*:',
            r'#\s*while\s+.*:',
            r'#\s*return\s+',
            r'#\s*import\s+',
            r'#\s*from\s+.*\s+import\s+'
        ]
        
        for i, line in enumerate(lines):
            for pattern in code_patterns:
                if re.search(pattern, line):
                    self.issues.append({
                        "tool": "custom_rules",
                        "code": "custom.commented_code",
                        "message": "Commented-out code",
                        "location": {
                            "file": self.current_file,
                            "line": i + 1,
                            "column": 0
                        },
                        "severity": "LOW",
                        "category": "commented_code",
                        "type": "style"
                    })
                    break
