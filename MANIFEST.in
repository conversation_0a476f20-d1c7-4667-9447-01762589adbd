# Include package metadata and documentation
include README.md
include CHANGELOG.md
include CONTRIBUTING.md
include LICENSE
include requirements.txt

# Include configuration files
recursive-include vibe_check/config *.yaml *.json *.toml
recursive-include vibe_check/ui/web/templates *.html
recursive-include vibe_check/ui/web/static *.css *.js *.png *.jpg *.svg

# Include documentation
recursive-include docs *.md *.rst *.txt *.png *.jpg *.svg

# Include examples
recursive-include examples *.py *.yaml *.json *.md

# Exclude development and temporary files
exclude .gitignore
exclude pyproject.toml
exclude MANIFEST.in
recursive-exclude * __pycache__
recursive-exclude * *.py[co]
recursive-exclude * *.orig
recursive-exclude * *.rej
recursive-exclude * *.bak
recursive-exclude * *~
recursive-exclude * .DS_Store
recursive-exclude legacy *
recursive-exclude test_projects *
recursive-exclude tests *
recursive-exclude scripts *
recursive-exclude venv *
recursive-exclude .git *
