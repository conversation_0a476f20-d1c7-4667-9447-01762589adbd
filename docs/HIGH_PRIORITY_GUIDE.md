# High-Priority File Extraction Guide

## Overview

The High-Priority File Extraction feature is an integral part of the Vibe Check workflow. It identifies and prioritizes files that require immediate attention based on various metrics like complexity, security issues, type errors, and more.

This guide explains how to use the high-priority extraction feature to focus your refactoring efforts on the most critical files in your codebase.

## How It Works

The high-priority extraction feature:

1. Analyzes the Vibe Check output to identify files with critical issues
2. Ranks files based on a priority score calculated from various metrics
3. Generates focused prompts for the highest-priority files
4. Creates a summary report of high-priority issues

### Priority Score Calculation

Files are ranked based on a priority score calculated from these metrics:

- **Complexity**: Files with high cyclomatic complexity (>10)
- **File Size**: Large files (>300 lines)
- **Security Issues**: Files with security vulnerabilities detected by bandit
- **Type Errors**: Files with type errors detected by mypy or pyright
- **Lint Issues**: Files with lint issues detected by ruff
- **Circular Imports**: Files with circular import issues
- **Many Imports**: Files with excessive imports (>15)
- **Low Test Coverage**: Files with low test coverage (<70%)

Each metric is weighted differently, with security issues and type errors having higher weights than other issues.

## Using the High-Priority Extraction

### Option 1: Integrated Vibe Check Workflow

The easiest way to use the high-priority extraction is with the integrated Vibe Check workflow:

```bash
vibe-check analyze <project_path> --priority-extraction [--top N] [--min-score SCORE]
```

This will:
1. Run the Vibe Check tool on your project
2. Extract high-priority files from the analysis output
3. Generate focused prompts for the highest-priority files
4. Create a summary report of high-priority issues

### Option 2: Standalone Extraction

If you've already run the Vibe Check tool and want to extract high-priority files from the output:

```bash
vibe-check extract-priorities <output_dir> [--top N] [--min-score SCORE] [--output-dir DIR]
```

This will:
1. Extract high-priority files from the specified analysis output directory
2. Generate focused prompts for the highest-priority files
3. Create a summary report of high-priority issues

### Arguments

- `<project_path>`: Path to the project to analyze (for integrated workflow)
- `<output_dir>`: Path to the Vibe Check output directory (for standalone extraction)
- `--top N`: Number of top files to extract (default: 10)
- `--min-score SCORE`: Minimum priority score to include a file (default: 5)
- `--output-dir DIR`: Directory to save the high-priority prompts (default: high_priority_prompts)

### Understanding the Parameters

#### `--top N`

This parameter limits the output to the top N highest-priority files, even if more files meet the minimum score threshold. It's useful when you want to focus on just the most critical files.

For example:
- `--top 5`: Only include the 5 highest-scoring files
- `--top 10`: Include up to 10 highest-scoring files (default)
- `--top 20`: Include up to 20 highest-scoring files for a more comprehensive analysis

#### `--min-score SCORE`

This parameter sets the minimum priority score threshold. Only files with a priority score at or above this threshold will be included in the high-priority list.

The priority score is calculated based on various metrics, with different weights:
- Security issues (weight: 5.0)
- Circular imports (weight: 3.0)
- Type errors (weight: 2.0)
- Complexity (weight: 1.0 per point above 10)
- Low test coverage (weight: 1.0)
- Lint issues (weight: 1.0)
- Many imports (weight: 0.5 per import above 15)
- Lines of code (weight: 0.01 per line above 300)

Typical threshold values:
- `--min-score 1`: Very inclusive, catches most files with any issues
- `--min-score 5`: Moderate threshold, catches files with significant issues (default)
- `--min-score 10`: High threshold, only catches files with critical issues

#### Combining Parameters

When used together, these parameters give you fine-grained control over which files to focus on:

- `--top 5 --min-score 3`: Extract up to 5 files with a score of 3 or higher
- `--top 10 --min-score 10`: Extract up to 10 files with a score of 10 or higher (very critical issues)
- `--top 20 --min-score 1`: Extract up to 20 files with a score of 1 or higher (more inclusive)

## Output Files

The high-priority extraction generates the following files:

### Prompt Files

For each high-priority file, a prompt file is generated with the following structure:

```
# HIGH PRIORITY FILE: <file_path>

## Priority Score: <score>

## Purpose of this Prompt
This file requires immediate attention due to critical issues that affect code quality, maintainability, and security.

## Critical Issues Identified
- **<issue_1>**
- **<issue_2>**
...

## File Information
- **Lines:** <line_count>
- **Complexity:** <complexity>
- **Imports:** <import_count>
- **Classes:** <class_count>
- **Functions:** <function_count>

## Refactoring Instructions
Please provide specific recommendations for refactoring this file to address the identified issues:

1. **<instruction_1>**
2. **<instruction_2>**
...

Please provide code examples where appropriate and ensure all recommendations align with CAW principles.
```

### Summary Report

A summary report is generated with the following structure:

```
# High-Priority Files Summary

Found <count> high-priority files with score >= <min_score>.

## Files Ranked by Priority

| Rank | File | Priority Score | Prompt |
|------|------|---------------|--------|
| 1 | <file_1> | <score_1> | [<prompt_1>](<prompt_1>) |
| 2 | <file_2> | <score_2> | [<prompt_2>](<prompt_2>) |
...
```

## Using the Prompts with LLMs

The generated prompts are designed to be fed to an LLM (like Claude or GPT-4) for further analysis and refactoring recommendations. Each prompt includes:

1. **Purpose Statement**: Explains why the file needs attention
2. **Critical Issues**: Specific issues that need to be addressed
3. **File Information**: Basic metadata about the file
4. **Refactoring Instructions**: Specific guidance for improving the file

To use the prompts with an LLM:

1. Copy the content of a prompt file
2. Paste it into a conversation with an LLM
3. Ask the LLM to provide specific refactoring recommendations
4. Implement the recommendations in your codebase

## Best Practices

1. **Focus on high-priority files first**: Address files with the highest priority scores first
2. **Implement recommendations incrementally**: Make small, focused changes based on the recommendations
3. **Verify changes**: Test your changes to ensure they don't break functionality
4. **Run the analysis regularly**: Use the high-priority extraction regularly to track progress

## Conclusion

The High-Priority File Extraction feature helps you focus your refactoring efforts on the most critical files in your codebase. By addressing these high-priority files first, you can make significant improvements to your codebase's quality, maintainability, and security.
