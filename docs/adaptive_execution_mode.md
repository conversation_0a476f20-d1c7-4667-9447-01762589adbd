# Adaptive Execution Mode Component

The Adaptive Execution Mode component provides sophisticated execution mode management for the Vibe Check actor system. It enables automatic switching between different execution modes based on system conditions, including workload, resource availability, and custom policies.

## Overview

The Adaptive Execution Mode component consists of the following subcomponents:

1. **ExecutionModeManager**: Manages execution modes and transitions between them
2. **WorkloadMonitor**: Monitors workload metrics and triggers mode switches
3. **ResourceMonitor**: Monitors resource metrics and triggers mode switches
4. **PolicyManager**: Manages custom policies for mode switching
5. **ExecutionManager**: Integrates the execution components with the actor system

## Execution Modes

The component supports the following execution modes:

- **Parallel**: Fully parallel execution with maximum concurrency
- **Sequential**: Sequential execution with minimal concurrency
- **Hybrid**: Hybrid mode with controlled concurrency
- **Adaptive**: Adaptive mode that automatically adjusts concurrency based on workload
- **Emergency**: Emergency mode for critical situations

## Configuration

The Adaptive Execution Mode component is configured through a configuration dictionary with the following structure:

```yaml
# Execution mode manager settings
execution_mode_manager:
  execution_mode: "adaptive"  # Initial execution mode
  mode_switch_cooldown: 10.0  # Minimum time (seconds) between mode switches
  mode_properties:
    parallel:
      concurrency_limit: 10
      message_batch_size: 10
      priority_threshold: 0
    sequential:
      concurrency_limit: 1
      message_batch_size: 1
      priority_threshold: 5
    hybrid:
      concurrency_limit: 5
      message_batch_size: 5
      priority_threshold: 3
    adaptive:
      concurrency_limit: 5
      message_batch_size: 5
      priority_threshold: 2
    emergency:
      concurrency_limit: 1
      message_batch_size: 1
      priority_threshold: 8

# Workload monitor settings
workload_monitor:
  enabled: true
  check_interval: 1.0  # Check interval in seconds
  metrics_history_size: 60  # Number of metrics to keep in history
  prediction_window: 10  # Number of metrics to use for trend prediction
  thresholds:
    queue_depth:
      very_low: 1
      low: 5
      normal: 20
      high: 50
      very_high: 100
      critical: 200
  mode_mapping:
    very_low: "parallel"
    low: "parallel"
    normal: "adaptive"
    high: "hybrid"
    very_high: "sequential"
    critical: "emergency"

# Resource monitor settings
resource_monitor:
  enabled: true
  check_interval: 1.0  # Check interval in seconds
  metrics_history_size: 60  # Number of metrics to keep in history
  thresholds:
    cpu:
      abundant: 20
      sufficient: 50
      limited: 70
      scarce: 90
      critical: 95
  mode_mapping:
    abundant: "parallel"
    sufficient: "parallel"
    limited: "adaptive"
    scarce: "sequential"
    critical: "emergency"

# Policy manager settings
policy_manager:
  enabled: true
  check_interval: 1.0  # Check interval in seconds
  max_event_history: 100  # Maximum number of events to keep in history
  policies:
    high_workload_policy:
      type: "rule_based"
      enabled: true
      weight: 1.0
      rules:
        - name: "high_workload_rule"
          enabled: true
          priority: 10
          conditions:
            - type: "workload_level"
              parameters:
                operator: ">="
                value: "high"
          actions:
            - type: "switch_mode"
              mode: "sequential"
              reason: "policy"
              priority: 10
```

## Integration with Actor System

The Adaptive Execution Mode component is integrated with the actor system through the `ExecutionManager` class, which provides methods for the actor system to check if messages should be processed, get batch sizes, and get resource allocations.

### Initialization

```python
from vibe_check.core.actor_system.execution.integration import (
    ExecutionManager,
    initialize_execution_manager
)

# Initialize the execution manager
await initialize_execution_manager(config)

# Get the execution manager
execution_manager = get_execution_manager()
```

### Usage

```python
# Get the current execution mode
current_mode = execution_manager.get_execution_mode()

# Set the execution mode
await execution_manager.set_execution_mode(
    mode=ExecutionMode.HYBRID,
    reason=ModeTransitionReason.MANUAL,
    context={"user_initiated": True}
)

# Check if a message should be processed
should_process = execution_manager.should_process_message(
    actor_id="actor1",
    message_id="message1",
    priority=1
)

# Get the batch size for an actor
batch_size = execution_manager.get_batch_size("actor1")

# Get the resource allocation for an actor
cpu_allocation = execution_manager.get_resource_allocation("actor1", "cpu")
```

## Testing

The Adaptive Execution Mode component includes comprehensive unit and integration tests:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test the interaction between components
- **Actor System Integration Tests**: Test the integration with the actor system

To run the tests, use the `run_adaptive_execution_tests.py` script:

```bash
# Run all tests
python run_adaptive_execution_tests.py

# Run only unit tests
python run_adaptive_execution_tests.py --unit-tests-only

# Run only integration tests
python run_adaptive_execution_tests.py --integration-tests-only

# Run with verbose output
python run_adaptive_execution_tests.py --verbose

# Run specific test files
python run_adaptive_execution_tests.py --test-files tests/core/actor_system/execution/test_adaptive_execution.py
```

## Performance Considerations

The Adaptive Execution Mode component is designed to optimize the performance of the actor system based on system conditions. It monitors workload and resource metrics to automatically switch between execution modes, ensuring optimal performance under varying conditions.

- **Parallel Mode**: Maximizes throughput by processing messages in parallel
- **Sequential Mode**: Minimizes resource usage by processing messages sequentially
- **Hybrid Mode**: Balances throughput and resource usage
- **Adaptive Mode**: Automatically adjusts concurrency based on workload
- **Emergency Mode**: Minimizes resource usage and prioritizes critical messages

## Future Enhancements

Planned enhancements for the Adaptive Execution Mode component include:

1. **Machine Learning-Based Adaptation**: Use machine learning to predict workload and resource usage
2. **Fine-Grained Actor-Level Adaptation**: Apply different execution modes to different actors
3. **Dynamic Resource Allocation**: Dynamically allocate resources based on actor importance
4. **Predictive Mode Switching**: Switch modes proactively based on predicted workload
5. **Custom Execution Modes**: Allow users to define custom execution modes
