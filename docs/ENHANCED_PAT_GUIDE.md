# Enhanced Vibe Check Guide

This guide explains how to use the enhanced Vibe Check (formerly Project Analysis Tool or PAT) scripts to analyze your codebase more effectively.

## Overview

The enhanced Vibe Check scripts provide several improvements over the basic tool:

1. **All Tools Enabled**: Run Vibe Check with all available tools enabled for comprehensive analysis
2. **Syntax Error Fixing**: Automatically fix common syntax errors in your codebase
3. **Issue-Specific Prompts**: Generate focused prompts for individual issues
4. **Parallel Processing**: Analyze your codebase in parallel for improved performance

## Available Commands

### 1. Run Vibe Check with All Tools

```bash
vibe-check analyze <project_path> --config full_config.yaml
```

This command:
- Fixes syntax errors in your codebase
- Runs Vibe Check with all available tools enabled
- Extracts high-priority files
- Generates issue-specific prompts

### 2. Run Vibe Check in Parallel

```bash
vibe-check analyze <project_path> --parallel --workers N
```

This command:
- Divides your codebase into chunks
- Analyzes each chunk in parallel
- Merges the results
- Generates issue-specific prompts
- Extracts high-priority files

The `--workers` parameter controls the number of parallel processes (default: CPU count).

### 3. Fix Syntax Errors

```bash
vibe-check fix-syntax <project_path>
```

This command:
- Finds all Python files in your project
- Checks for syntax errors
- Attempts to fix common syntax errors automatically

## Configuration

The enhanced Vibe Check tool uses a comprehensive configuration file that enables all available tools:

- **full_config.yaml**: Enables all available tools with optimized settings

You can customize this configuration file to enable or disable specific tools.

## Output Files

The enhanced Vibe Check tool generates the following output files:

### Analysis Results

- **vibe_check_merged_analysis.json**: Merged analysis results from all chunks
- **vibe_check_report.md**: Detailed report of the analysis

### Issue-Specific Prompts

- **issue_prompts/**: Directory containing issue-specific prompts
- **issue_prompts/issue_prompts_summary.md**: Summary of all issue-specific prompts

### High-Priority Files

- **high_priority/**: Directory containing high-priority file prompts
- **high_priority/high_priority_summary.md**: Summary of high-priority files

## Best Practices

1. **Start with Syntax Error Fixing**: Fix syntax errors before running the full analysis
2. **Use Parallel Processing for Large Codebases**: For large codebases, use the parallel script for better performance
3. **Focus on High-Priority Files First**: Address high-priority files before moving on to individual issues
4. **Use Issue-Specific Prompts for Targeted Fixes**: Use issue-specific prompts to address specific types of issues

## Troubleshooting

### Common Issues

1. **Missing Dependencies**: Ensure all required dependencies are installed
2. **Syntax Errors**: If syntax errors cannot be fixed automatically, fix them manually
3. **Memory Issues**: For large codebases, reduce the number of workers or analyze in smaller chunks

### Getting Help

If you encounter issues with the enhanced PAT scripts, check the error messages and logs for more information.

## Conclusion

The enhanced PAT scripts provide a more comprehensive and efficient way to analyze your codebase. By enabling all tools, fixing syntax errors, generating issue-specific prompts, and using parallel processing, you can get more valuable insights from your codebase analysis.
