# Python
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.mypy_cache/
.pytest_cache/

# Test projects and outputs (keep test_projects/ for development)
test_results/
debug_results/
vibe_check_output/
analysis_results/
full_analysis_results/
*_output/
*_results/

# Documentation
docs/_build/

# Environments and local settings
.env
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/
/venv/
.env.local
.env.development.local
.env.test.local
.env.production.local
.python-version
pipfile.lock
Pipfile.lock
local_settings.py
settings_local.py

# Project specific outputs and temporary files
*/PAT_output/
PAT_output/
actor_state/
diagnostics/
visualizations/

# OS specific
.DS_Store

# IDE / Editors
.idea/
.vscode/
*.swp
*.swo
*.sublime-workspace
*.sublime-project
.project
.pydevproject
.spyderproject
.spyproject
.ropeproject
.ipynb_checkpoints

# Logs and databases
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log
*.sql
*.sqlite
*.sqlite3
.dbdata/

# Profiling data
*.prof
*.lprof
profile_output/
prof/

# Generated visualizations
visualizations/
*.png
*.jpg
*.jpeg
*.gif
*.svg
*.pdf
!docs/**/*.png
!docs/**/*.jpg
!docs/**/*.svg
!docs/**/*.pdf

# Analysis outputs and debug files
**/output/
output/
debug_output/
*.log
analysis_*.log
debug_*.log
actor_system_*.log

# CI/CD
.coverage
.pytest_report.xml
.pylint-report.xml
junit-*.xml
artifacts/

# Security
*.pem
*.key
*.cert
*.crt
*.p12
*.pfx
.secrets/
.credentials/
.tokens/
.auth/
secrets.yaml
secrets.json
credentials.yaml
credentials.json

# Cython / C extensions
cython_debug/
*.so
*.pyd
*.dylib
*.dll
*.a
*.o
*.obj

# Temporary files
.temp/
.tmp/
tmp/
*.tmp
*.bak
*.orig
*~
.~*
Session.vim
*.swp
*.swo
*.swn
*\#
.#*
\#*\#
*/.dynamodb/

# Type checking
.pyre/
.pytype/
typings/
.dmypy.json
dmypy.json
.ruff_cache/
