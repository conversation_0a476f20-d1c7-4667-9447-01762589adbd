# Vibe Check - Core Dependencies
# For development setup, use: pip install -e ".[dev]"
# For full installation, use: pip install -e ".[full]"

# Core dependencies
click>=8.0.0
pyyaml>=6.0
networkx>=2.6.3
typing-extensions>=4.0.0
rich>=12.0.0

# Note: This file is provided for backward compatibility.
# The recommended way to install dependencies is through pyproject.toml:
# pip install -e .
# pip install -e ".[dev]"  # for development
# pip install -e ".[full]" # for all features
