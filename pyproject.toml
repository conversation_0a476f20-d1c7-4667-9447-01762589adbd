[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "vibe-check"
version = "1.0.0"
description = "A comprehensive Python project analysis tool with CAW architecture"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Vibe Check Development Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Vibe Check Development Team", email = "<EMAIL>"}
]
keywords = ["code-analysis", "python", "quality", "architecture", "caw"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: Quality Assurance",
    "Topic :: Software Development :: Testing",
]
requires-python = ">=3.8"
dependencies = [
    "click>=8.0.0",
    "pyyaml>=6.0",
    "networkx>=2.6.3",
    "typing-extensions>=4.0.0",
    "rich>=12.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "mypy>=0.950",
    "ruff>=0.0.128",
    "pre-commit>=2.20.0",
]
web = [
    "streamlit>=1.10.0",
    "altair>=4.2.0",
    "pandas>=1.4.0",
    "graphviz>=0.20",
    "matplotlib>=3.5.0",
]
tui = [
    "textual>=0.9.0",
    "keyboard>=0.13.5",
]
security = [
    "bandit>=1.7.4",
    "safety>=2.0.0",
]
full = [
    "vibe-check[dev,web,tui,security]",
]

[project.scripts]
vibe-check = "vibe_check.cli:main"

[project.urls]
Homepage = "https://github.com/ptzajac/vibe_check"
Repository = "https://github.com/ptzajac/vibe_check.git"
Documentation = "https://github.com/ptzajac/vibe_check/blob/main/README.md"
"Bug Tracker" = "https://github.com/ptzajac/vibe_check/issues"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
addopts = [
    "--cov=vibe_check",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--strict-markers",
    "--disable-warnings",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.black]
line-length = 88
target-version = ["py38", "py39", "py310", "py311", "py312"]
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | venv
  | legacy
)/
'''

[tool.ruff]
line-length = 88
target-version = "py38"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "C",  # flake8-comprehensions
    "B",  # flake8-bugbear
    "N",  # pep8-naming
    "UP", # pyupgrade
    "YTT", # flake8-2020
    "S",  # bandit
    "BLE", # flake8-blind-except
    "FBT", # flake8-boolean-trap
    "A",  # flake8-builtins
    "COM", # flake8-commas
    "C4", # flake8-comprehensions
    "DTZ", # flake8-datetimez
    "T10", # flake8-debugger
    "EM", # flake8-errmsg
    "EXE", # flake8-executable
    "ISC", # flake8-implicit-str-concat
    "ICN", # flake8-import-conventions
    "G",  # flake8-logging-format
    "INP", # flake8-no-pep420
    "PIE", # flake8-pie
    "T20", # flake8-print
    "PYI", # flake8-pyi
    "PT", # flake8-pytest-style
    "Q",  # flake8-quotes
    "RSE", # flake8-raise
    "RET", # flake8-return
    "SLF", # flake8-self
    "SIM", # flake8-simplify
    "TID", # flake8-tidy-imports
    "ARG", # flake8-unused-arguments
    "PTH", # flake8-use-pathlib
    "ERA", # eradicate
    "PD", # pandas-vet
    "PGH", # pygrep-hooks
    "PL", # pylint
    "TRY", # tryceratops
    "NPY", # NumPy-specific rules
    "RUF", # Ruff-specific rules
]
ignore = [
    "E501",   # line too long, handled by black
    "S101",   # use of assert
    "S603",   # subprocess call: check for execution of untrusted input
    "S607",   # starting a process with a partial executable path
    "PLR0913", # too many arguments to function call
    "PLR0912", # too many branches
    "PLR0915", # too many statements
    "C901",   # too complex
    "PD901",  # avoid using the generic variable name df for DataFrames
]

[tool.ruff.per-file-ignores]
"tests/*" = ["S101", "PLR2004", "SLF001", "ARG001"]
"scripts/*" = ["T201", "S602", "S603", "S607"]
"legacy/*" = ["ALL"]

[tool.ruff.isort]
known-first-party = ["vibe_check"]
force-sort-within-sections = true

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
strict_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict = true
show_error_codes = true
show_column_numbers = true
exclude = [
    "legacy/",
    "build/",
    "dist/",
]

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false

[[tool.mypy.overrides]]
module = "scripts.*"
disallow_untyped_defs = false

[tool.coverage.run]
source = ["vibe_check"]
omit = ["tests/*", "vibe_check/ui/web/templates/*", "vibe_check/ui/web/static/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
    "raise ImportError",
    "if TYPE_CHECKING:",
    "@abstractmethod",
]
precision = 2
show_missing = true
skip_covered = false

[tool.bandit]
exclude_dirs = ["tests", "legacy", "scripts"]
skips = ["B101", "B601"]

[tool.bandit.assert_used]
skips = ["*_test.py", "*/test_*.py"]
