#!/usr/bin/env python3
"""
PAT CAW Actors Choreography Prototype

This is a simplified prototype implementation of the PAT tool using the CAW
(Contextual Adaptive Wave) paradigm with actor-based choreography. This demonstrates
how the refactored PAT could leverage CAW principles.
"""

import asyncio
import json
import logging
from dataclasses import asdict, dataclass, field
from enum import Enum, auto
from pathlib import Path
from queue import Queue
from typing import Any, Dict, List, Optional, Set, Type, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("pat_caw")

# ====================================================================
# CAW Core Components
# ====================================================================

class MessageType(Enum):
    """Types of messages that can be sent between actors."""
    INIT_ANALYSIS = auto()
    FILE_METADATA = auto()
    REQUEST_ANALYSIS = auto()
    EXECUTE_TOOL = auto()
    FILE_CONTENT = auto()
    ANALYSIS_RESULT = auto()
    GENERATE_REPORT = auto()
    REQUEST_VISUALIZATION = auto()
    VISUALIZATION = auto()
    FINAL_REPORT = auto()
    UNKNOWN = auto()

@dataclass
class ContextWave:
    """
    Context wave that propagates between actors.
    
    Implements the CAW principle of contextual propagation, carrying
    metadata, configuration, history, and adaptive parameters.
    """
    metadata: Dict[str, Any] = field(default_factory=dict)
    configuration: Dict[str, Any] = field(default_factory=dict)
    history: List[Dict[str, Any]] = field(default_factory=list)
    adaptive_params: Dict[str, Any] = field(default_factory=dict)
    
    def propagate(self, source_context: Optional['ContextWave'] = None) -> 'ContextWave':
        """
        Propagate this context wave, optionally merging with another.
        
        Args:
            source_context: Optional source context to merge with
            
        Returns:
            A new context wave with propagated values
        """
        # Create a new context with propagation rules
        new_context = ContextWave()
        new_context.metadata = self.metadata.copy()
        new_context.configuration = self.configuration.copy()
        new_context.history = self.history.copy()
        new_context.adaptive_params = self.adaptive_params.copy()
        
        # Update with source context using adaptation rules
        if source_context:
            new_context._update_with_adaptation(source_context)
            
        return new_context
    
    def _update_with_adaptation(self, other_context: 'ContextWave') -> None:
        """
        Update this context with values from another using adaptation rules.
        
        This is where CAW's contextual adaptation happens.
        
        Args:
            other_context: The context to adapt from
        """
        self._adapt_metadata(other_context.metadata)
        self._adapt_configuration(other_context.configuration)
        self._append_history(other_context.history)
        self._adapt_parameters(other_context.adaptive_params)
    
    def _adapt_metadata(self, metadata: Dict[str, Any]) -> None:
        """
        Adapt the metadata from another context.
        
        Args:
            metadata: The metadata to adapt from
        """
        # Preserve existing metadata, but add new values
        for key, value in metadata.items():
            if key not in self.metadata:
                self.metadata[key] = value
    
    def _adapt_configuration(self, configuration: Dict[str, Any]) -> None:
        """
        Adapt configuration from another context.
        
        Args:
            configuration: The configuration to adapt from
        """
        # More specific configurations override general ones
        self.configuration.update(configuration)
    
    def _append_history(self, history: List[Dict[str, Any]]) -> None:
        """
        Append history entries from another context.
        
        Args:
            history: The history entries to append
        """
        # History accumulates
        self.history.extend(history)
    
    def _adapt_parameters(self, params: Dict[str, Any]) -> None:
        """
        Adapt parameters from another context.
        
        Args:
            params: The parameters to adapt from
        """
        # Apply adaptation rules for parameters
        for key, value in params.items():
            if key in self.adaptive_params:
                # If parameter exists, adapt it based on type
                if isinstance(value, (int, float)) and isinstance(self.adaptive_params[key], (int, float)):
                    # For numeric params, take the average
                    self.adaptive_params[key] = (self.adaptive_params[key] + value) / 2
                elif isinstance(value, dict) and isinstance(self.adaptive_params[key], dict):
                    # For dictionaries, recursively update
                    self.adaptive_params[key].update(value)
                elif isinstance(value, (list, set)) and isinstance(self.adaptive_params[key], (list, set)):
                    # For lists/sets, combine uniquely
                    if isinstance(self.adaptive_params[key], list):
                        self.adaptive_params[key] = list(set(self.adaptive_params[key] + list(value)))
                    else:
                        self.adaptive_params[key] = self.adaptive_params[key].union(value)
                else:
                    # For other types, use the newer value
                    self.adaptive_params[key] = value
            else:
                # If parameter doesn't exist, add it
                self.adaptive_params[key] = value

@dataclass
class Message:
    """
    Message passed between actors.
    
    Implements the CAW principle of wave-particle duality by combining
    concrete message data (particle) with contextual information (wave).
    """
    type: MessageType
    payload: Dict[str, Any] = field(default_factory=dict)
    context: Optional[ContextWave] = None
    
    def __post_init__(self):
        if self.context is None:
            self.context = ContextWave()
            
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary."""
        return {
            "type": self.type.name,
            "payload": self.payload,
            "context": asdict(self.context)
        }

class Actor:
    """
    Base actor class for the CAW choreography system.
    
    Implements the CAW principle of adaptive actors that communicate
    via messages with propagating context.
    """
    
    def __init__(self, actor_id: str):
        """
        Initialize the actor.
        
        Args:
            actor_id: Unique ID for this actor
        """
        self.actor_id = actor_id
        self.mailbox = asyncio.Queue()
        self.context_wave = ContextWave()
        self.is_running = False
        self._known_actors = {}
    
    def register_actor(self, actor_id: str, actor: 'Actor') -> None:
        """
        Register another actor that this actor can send messages to.
        
        Args:
            actor_id: ID of the actor to register
            actor: The actor instance
        """
        self._known_actors[actor_id] = actor
    
    async def send(self, recipient_id: str, msg_type: MessageType, 
                  payload: Dict[str, Any], context: Optional[ContextWave] = None) -> None:
        """
        Send a message to another actor.
        
        Args:
            recipient_id: ID of the recipient actor
            msg_type: Type of the message
            payload: Message payload
            context: Optional context to propagate
        """
        if recipient_id not in self._known_actors:
            logger.error(f"Actor {self.actor_id} tried to send to unknown actor {recipient_id}")
            return
            
        recipient = self._known_actors[recipient_id]
        propagated_context = self.context_wave.propagate(context)
        
        # Add sender info to context metadata
        propagated_context.metadata["sender_id"] = self.actor_id
        
        # Add history entry
        history_entry = {
            "timestamp": asyncio.get_event_loop().time(),
            "sender": self.actor_id,
            "recipient": recipient_id,
            "type": msg_type.name
        }
        propagated_context.history.append(history_entry)
        
        # Create and send the message
        message = Message(msg_type, payload, propagated_context)
        await recipient.receive(message)
        
        logger.debug(f"Actor {self.actor_id} sent {msg_type.name} to {recipient_id}")
    
    async def receive(self, message: Message) -> None:
        """
        Receive a message from another actor.
        
        Args:
            message: The message to receive
        """
        await self.mailbox.put(message)
    
    async def process_messages(self) -> None:
        """Process messages from the mailbox."""
        while self.is_running:
            try:
                message = await self.mailbox.get()
                await self._handle_message(message)
                self.mailbox.task_done()
            except Exception as e:
                logger.error(f"Error processing message in {self.actor_id}: {e}")
    
    async def _handle_message(self, message: Message) -> None:
        """
        Handle an incoming message.
        
        Args:
            message: The message to handle
        """
        # Get the handler method for this message type
        handler_name = f"handle_{message.type.name.lower()}"
        handler = getattr(self, handler_name, self.handle_unknown)
        
        # Call the handler
        try:
            await handler(message.payload, message.context)
        except Exception as e:
            logger.error(f"Error in {self.actor_id}.{handler_name}: {e}")
    
    async def handle_unknown(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle an unknown message type.
        
        Args:
            payload: Message payload
            context: Message context
        """
        logger.warning(f"Actor {self.actor_id} received unknown message type")
    
    async def start(self) -> None:
        """Start the actor."""
        self.is_running = True
        asyncio.create_task(self.process_messages())
        logger.info(f"Actor {self.actor_id} started")
    
    async def stop(self) -> None:
        """Stop the actor."""
        self.is_running = False
        logger.info(f"Actor {self.actor_id} stopped")

# ====================================================================
# PAT Specific Actors
# ====================================================================

class ProjectActor(Actor):
    """
    Actor representing the project being analyzed.
    
    This is the main entry point for the analysis process.
    """
    
    def __init__(self, actor_id: str, project_path: Union[str, Path]):
        """
        Initialize the project actor.
        
        Args:
            actor_id: Unique ID for this actor
            project_path: Path to the project to analyze
        """
        super().__init__(actor_id)
        self.project_path = Path(project_path)
        self.file_actors = {}
        self.tool_actors = {}
        self.results = {}
        self.report_actor = None
        
    async def start_analysis(self) -> None:
        """Start the analysis process."""
        # Create the initial context
        ctx = ContextWave(
            metadata={"project_name": self.project_path.name},
            configuration={"exclude_patterns": ["__pycache__", ".git", "venv"]},
            adaptive_params={"complexity_threshold": 10, "max_line_length": 100}
        )
        
        logger.info(f"Starting analysis of {self.project_path}")
        
        # Discover python files
        files = list(self.project_path.glob("**/*.py"))
        logger.info(f"Found {len(files)} Python files")
        
        # Create file actors
        for file_path in files:
            file_id = f"file-{file_path.relative_to(self.project_path)}"
            file_actor = FileActor(file_id, file_path)
            await file_actor.start()
            self.register_actor(file_id, file_actor)
            file_actor.register_actor(self.actor_id, self)
            self.file_actors[file_id] = file_actor
            
            # Start analysis for this file
            await self.send(file_id, MessageType.INIT_ANALYSIS, 
                          {"project_path": str(self.project_path)}, ctx)
    
    async def handle_file_metadata(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle file metadata received from a file actor.
        
        Args:
            payload: Message payload with file metadata
            context: Message context
        """
        file_id = context.metadata.get("sender_id")
        file_path = payload.get("file_path")
        metadata = payload.get("metadata", {})
        
        logger.info(f"Received metadata for {file_path}")
        
        # Adapt the complexity threshold based on the file complexity
        complexity = metadata.get("complexity", 0)
        if complexity > context.adaptive_params.get("complexity_threshold", 10):
            # If file is complex, adjust our threshold upward for future files
            new_threshold = (context.adaptive_params.get("complexity_threshold", 10) + complexity) / 2
            context.adaptive_params["complexity_threshold"] = new_threshold
            logger.debug(f"Adjusted complexity threshold to {new_threshold}")
        
        # Select appropriate tools based on metadata
        tools = self._select_tools(metadata)
        
        # Request analysis from each tool
        for tool_id, tool_config in tools.items():
            if tool_id in self._known_actors:
                # Adapt tool configuration based on file metadata
                adapted_config = self._adapt_tool_config(tool_config, metadata)
                
                # Create a new context for this tool with the adapted configuration
                tool_context = context.propagate()
                tool_context.configuration.update(adapted_config)
                
                # Send analysis request to tool
                await self.send(tool_id, MessageType.REQUEST_ANALYSIS, {
                    "file_id": file_id,
                    "file_path": file_path
                }, tool_context)
    
    async def handle_analysis_result(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle analysis result from a tool actor.
        
        Args:
            payload: Message payload with analysis result
            context: Message context
        """
        tool_id = context.metadata.get("sender_id")
        file_id = payload.get("file_id")
        result = payload.get("result", {})
        
        logger.info(f"Received {tool_id} analysis result for {file_id}")
        
        # Store the result
        if file_id not in self.results:
            self.results[file_id] = {}
        self.results[file_id][tool_id] = result
        
        # Check if all tools have reported for this file
        if self._all_tools_reported(file_id):
            logger.info(f"All tools reported for {file_id}, generating report")
            
            # If we have a report actor, send the results
            if self.report_actor and self.report_actor.actor_id in self._known_actors:
                await self.send(self.report_actor.actor_id, MessageType.GENERATE_REPORT, {
                    "file_id": file_id,
                    "results": self.results[file_id]
                }, context)
    
    async def handle_final_report(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle final report from the report actor.
        
        Args:
            payload: Message payload with the report
            context: Message context
        """
        file_id = payload.get("file_id")
        report = payload.get("report", {})
        
        logger.info(f"Received final report for {file_id}")
        
        # Here you would do something with the final report
        # For this prototype, we'll just log it
        logger.info(f"Report for {file_id}: {json.dumps(report, indent=2)}")
    
    def _select_tools(self, metadata: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """
        Select appropriate tools based on file metadata.
        
        Args:
            metadata: File metadata
        
        Returns:
            Dictionary mapping tool IDs to their configurations
        """
        # In a real implementation, this would be more sophisticated
        # and would select tools based on file characteristics
        tools = {}
        
        # Always include ruff for linting
        tools["tool-ruff"] = {
            "tool": "ruff",
            "args": ["--format=json"]
        }
        
        # Include bandit for security analysis if file is large
        if metadata.get("line_count", 0) > 100:
            tools["tool-bandit"] = {
                "tool": "bandit",
                "args": ["-f", "json"]
            }
        
        # Include mypy for type checking if file has type annotations
        if metadata.get("has_type_annotations", False):
            tools["tool-mypy"] = {
                "tool": "mypy",
                "args": ["--show-error-codes"]
            }
        
        return tools
    
    def _adapt_tool_config(self, tool_config: Dict[str, Any], metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Adapt tool configuration based on file metadata.
        
        Args:
            tool_config: Tool configuration
            metadata: File metadata
        
        Returns:
            Adapted tool configuration
        """
        # Clone the configuration so we don't modify the original
        config = tool_config.copy()
        
        # Adapt configuration based on metadata
        if "complexity" in metadata:
            complexity = metadata["complexity"]
            
            # For complex files, use more strict linting
            if complexity > 10 and config.get("tool") == "ruff":
                if "args" not in config:
                    config["args"] = []
                config["args"].append("--select=E,F,W,C90")
        
        # For large files, allocate more resources
        if metadata.get("line_count", 0) > 500:
            config["timeout"] = 60  # Increase timeout for large files
        
        return config
    
    def _all_tools_reported(self, file_id: str) -> bool:
        """
        Check if all tools have reported for a file.
        
        Args:
            file_id: ID of the file to check
        
        Returns:
            True if all tools have reported, False otherwise
        """
        # Get the tools that should report for this file
        tools = self._select_tools({})  # Simplified for the prototype
        
        # Check if we have results from all tools
        if file_id not in self.results:
            return False
        
        return all(tool_id in self.results[file_id] for tool_id in tools)

class FileActor(Actor):
    """
    Actor representing a file in the project.
    
    Responsible for file-level operations like reading content
    and extracting metadata.
    """
    
    def __init__(self, actor_id: str, file_path: Union[str, Path]):
        """
        Initialize the file actor.
        
        Args:
            actor_id: Unique ID for this actor
            file_path: Path to the file
        """
        super().__init__(actor_id)
        self.file_path = Path(file_path)
        self.content = None
        self.metadata = None
    
    async def handle_init_analysis(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle initiative analysis request.
        
        Args:
            payload: Message payload
            context: Message context
        """
        logger.info(f"Initializing analysis for {self.file_path}")
        
        # Extract metadata from the file
        self.metadata = await self._extract_metadata()
        
        # Send metadata back to the project actor
        sender_id = context.metadata.get("sender_id")
        if sender_id:
            await self.send(sender_id, MessageType.FILE_METADATA, {
                "file_path": str(self.file_path),
                "metadata": self.metadata
            }, context)
    
    async def handle_execute_tool(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle execute tool request from a tool actor.
        
        Args:
            payload: Message payload
            context: Message context
        """
        tool_id = context.metadata.get("sender_id")
        logger.info(f"Executing tool {tool_id} on {self.file_path}")
        
        # Load the file content if not already loaded
        if self.content is None:
            self.content = await self._read_content()
        
        # Send the content back to the tool actor
        await self.send(tool_id, MessageType.FILE_CONTENT, {
            "file_path": str(self.file_path),
            "content": self.content
        }, context)
    
    async def _extract_metadata(self) -> Dict[str, Any]:
        """
        Extract metadata from the file.
        
        Returns:
            Dictionary of file metadata
        """
        # Read the file content
        content = await self._read_content()
        
        # Count lines
        line_count = len(content.splitlines())
        
        # Calculate a simple complexity score based on indentation levels
        complexity = 0
        max_indent = 0
        for line in content.splitlines():
            if line.strip() and not line.strip().startswith("#"):
                indent = len(line) - len(line.lstrip())
                max_indent = max(max_indent, indent)
                if "if " in line or "for " in line or "while " in line:
                    complexity += 1
        
        # Complexity is a function of control flow statements and indentation
        complexity += max_indent // 4
        
        # Check for type annotations
        has_type_annotations = ":" in content and "->" in content
        
        return {
            "line_count": line_count,
            "complexity": complexity,
            "has_type_annotations": has_type_annotations,
            "size_bytes": self.file_path.stat().st_size
        }
    
    async def _read_content(self) -> str:
        """
        Read the file content.
        
        Returns:
            File content as a string
        """
        try:
            with open(self.file_path, "r", encoding="utf-8") as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading {self.file_path}: {e}")
            return ""

class ToolActor(Actor):
    """
    Actor representing an external analysis tool.
    
    Responsible for executing the tool on a file and returning results.
    """
    
    def __init__(self, actor_id: str, tool_name: str):
        """
        Initialize the tool actor.
        
        Args:
            actor_id: Unique ID for this actor
            tool_name: Name of the tool to execute
        """
        super().__init__(actor_id)
        self.tool_name = tool_name
    
    async def handle_request_analysis(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle analysis request from the project actor.
        
        Args:
            payload: Message payload
            context: Message context
        """
        file_id = payload.get("file_id")
        file_path = payload.get("file_path")
        
        logger.info(f"Tool {self.tool_name} requested to analyze {file_path}")
        
        # Request file content from the file actor
        await self.send(file_id, MessageType.EXECUTE_TOOL, {
            "tool": self.tool_name
        }, context)
    
    async def handle_file_content(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle file content from the file actor.
        
        Args:
            payload: Message payload with file content
            context: Message context
        """
        file_path = payload.get("file_path")
        content = payload.get("content", "")
        
        logger.info(f"Tool {self.tool_name} received content for {file_path}")
        
        # Run the tool on the file content
        result = await self._run_tool(file_path, content, context)
        
        # Send the result back to the project actor (assuming it's the original sender)
        sender_id = None
        for entry in reversed(context.history):
            if entry.get("type") == MessageType.REQUEST_ANALYSIS.name:
                sender_id = entry.get("sender")
                break
        
        if sender_id:
            # Get the file_id from the context history
            file_id = None
            for entry in reversed(context.history):
                payload = entry.get("payload", {})
                if "file_id" in payload:
                    file_id = payload["file_id"]
                    break
            
            await self.send(sender_id, MessageType.ANALYSIS_RESULT, {
                "file_id": file_id,
                "file_path": file_path,
                "tool": self.tool_name,
                "result": result
            }, context)
    
    async def _run_tool(self, file_path: str, content: str, context: ContextWave) -> Dict[str, Any]:
        """
        Run the tool on the file content.
        
        Args:
            file_path: Path to the file
            content: File content
            context: Message context with tool configuration
        
        Returns:
            Tool results as a dictionary
        """
        # Get tool configuration from context
        config = context.configuration
        
        # In a real implementation, this would execute the actual tool
        # For this prototype, we'll simulate tool execution
        
        if self.tool_name == "ruff":
            # Simulate ruff linting
            issues = []
            
            # Check for long lines
            max_line_length = config.get("max_line_length", 100)
            for i, line in enumerate(content.splitlines()):
                if len(line) > max_line_length:
                    issues.append({
                        "code": "E501",
                        "message": f"Line too long ({len(line)} > {max_line_length})",
                        "location": {"row": i + 1, "column": max_line_length + 1}
                    })
            
            # Check for unused variables (very simple check)
            variables = set()
            used = set()
            for line in content.splitlines():
                # Very naive check - just for prototype
                if "=" in line and not line.strip().startswith("#"):
                    var = line.split("=")[0].strip()
                    variables.add(var)
                
                for var in variables:
                    if var in line and "=" not in line.split(var)[0]:
                        used.add(var)
            
            for var in variables - used:
                issues.append({
                    "code": "F841",
                    "message": f"Unused variable '{var}'",
                    "location": {"row": 1, "column": 1}  # Simplified
                })
            
            return {
                "issues": issues,
                "summary": {
                    "total": len(issues),
                    "by_type": {"E": sum(1 for i in issues if i["code"].startswith("E")),
                               "F": sum(1 for i in issues if i["code"].startswith("F"))}
                }
            }
            
        elif self.tool_name == "bandit":
            # Simulate bandit security analysis
            issues = []
            
            # Check for hardcoded passwords
            for i, line in enumerate(content.splitlines()):
                if "password" in line.lower() and "=" in line and ("'" in line or '"' in line):
                    issues.append({
                        "code": "B105",
                        "message": "Possible hardcoded password",
                        "line": i + 1,
                        "severity": "HIGH"
                    })
            
            return {
                "issues": issues,
                "summary": {
                    "total": len(issues),
                    "by_severity": {"HIGH": sum(1 for i in issues if i["severity"] == "HIGH"),
                                   "MEDIUM": sum(1 for i in issues if i["severity"] == "MEDIUM"),
                                   "LOW": sum(1 for i in issues if i["severity"] == "LOW")}
                }
            }
            
        elif self.tool_name == "mypy":
            # Simulate mypy type checking
            issues = []
            
            # Check for missing type annotations
            for i, line in enumerate(content.splitlines()):
                if "def " in line and "->" not in line:
                    issues.append({
                        "code": "annotation-unchecked",
                        "message": "Function is missing a return type annotation",
                        "line": i + 1
                    })
            
            return {
                "issues": issues,
                "summary": {
                    "total": len(issues)
                }
            }
        
        # Default empty result
        return {}

class ReportActor(Actor):
    """
    Actor responsible for generating reports from analysis results.
    """
    
    def __init__(self, actor_id: str):
        """
        Initialize the report actor.
        
        Args:
            actor_id: Unique ID for this actor
        """
        super().__init__(actor_id)
        self.visualization_actor = None
    
    def set_visualization_actor(self, actor: Actor) -> None:
        """
        Set the visualization actor.
        
        Args:
            actor: The visualization actor
        """
        self.visualization_actor = actor
        self.register_actor(actor.actor_id, actor)
    
    async def handle_generate_report(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle report generation request.
        
        Args:
            payload: Message payload with analysis results
            context: Message context
        """
        file_id = payload.get("file_id")
        results = payload.get("results", {})
        
        logger.info(f"Generating report for {file_id}")
        
        # Generate a report from the results
        report = self._generate_report(file_id, results)
        
        # If we have a visualization actor, request visualizations
        if self.visualization_actor:
            await self.send(self.visualization_actor.actor_id, MessageType.REQUEST_VISUALIZATION, {
                "file_id": file_id,
                "results": results
            }, context)
            
            # In a real implementation, we would wait for the visualization
            # and include it in the report, but for this prototype we'll
            # skip that step and just send the report directly
        
        # Send the report back to the project actor
        sender_id = context.metadata.get("sender_id")
        if sender_id:
            await self.send(sender_id, MessageType.FINAL_REPORT, {
                "file_id": file_id,
                "report": report
            }, context)
    
    def _generate_report(self, file_id: str, results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate a report from analysis results.
        
        Args:
            file_id: ID of the file to generate a report for
            results: Analysis results from tools
            
        Returns:
            A dictionary with the report data
        """
        # Extract information from the results
        issues = []
        summary = {
            "tools_run": len(results),
            "total_issues": 0,
            "severity": {"HIGH": 0, "MEDIUM": 0, "LOW": 0}
        }
        
        # Process each tool's results
        for tool_id, result in results.items():
            tool_name = tool_id.split('-')[1] if '-' in tool_id else tool_id
            
            # Extract issues from the tool's result
            tool_issues = result.get("issues", [])
            
            # Add tool-specific context to each issue
            for issue in tool_issues:
                # Create a unified issue representation
                unified_issue = {
                    "tool": tool_name,
                    "code": issue.get("code", "unknown"),
                    "message": issue.get("message", "Unknown issue"),
                    "location": {},
                    "severity": issue.get("severity", "MEDIUM")
                }
                
                # Extract location information with different tool formats
                if "location" in issue:
                    unified_issue["location"] = issue["location"]
                elif "line" in issue:
                    unified_issue["location"] = {"row": issue["line"], "column": 1}
                
                # Add to the issues list
                issues.append(unified_issue)
                
                # Update summary statistics
                summary["total_issues"] += 1
                severity = unified_issue["severity"]
                summary["severity"][severity] = summary["severity"].get(severity, 0) + 1
        
        # Generate recommendations based on issues
        recommendations = self._generate_recommendations(issues)
        
        # Construct the final report
        return {
            "file_id": file_id,
            "summary": summary,
            "issues": issues,
            "recommendations": recommendations
        }
    
    def _generate_recommendations(self, issues: List[Dict[str, Any]]) -> List[str]:
        """
        Generate recommendations based on issues.
        
        Args:
            issues: List of issues to generate recommendations for
            
        Returns:
            List of recommendation strings
        """
        recommendations = []
        
        # Group issues by type
        issue_types = {}
        for issue in issues:
            code = issue.get("code", "unknown")
            if code not in issue_types:
                issue_types[code] = []
            issue_types[code].append(issue)
        
        # Generate recommendations for each issue type
        for code, code_issues in issue_types.items():
            if code.startswith("E"):
                recommendations.append(f"Fix {len(code_issues)} style issues (code: {code})")
            elif code.startswith("F"):
                recommendations.append(f"Address {len(code_issues)} potential bugs (code: {code})")
            elif code.startswith("B"):
                recommendations.append(f"Resolve {len(code_issues)} security issues (code: {code})")
            elif "annotation" in code:
                recommendations.append(f"Add type annotations to {len(code_issues)} functions")
            else:
                recommendations.append(f"Address {len(code_issues)} issues of type {code}")
        
        return recommendations

class VisualizationActor(Actor):
    """
    Actor responsible for creating visualizations from analysis results.
    """
    
    def __init__(self, actor_id: str):
        """
        Initialize the visualization actor.
        
        Args:
            actor_id: Unique ID for this actor
        """
        super().__init__(actor_id)
    
    async def handle_request_visualization(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """
        Handle visualization request from the report actor.
        
        Args:
            payload: Message payload with analysis results
            context: Message context
        """
        file_id = payload.get("file_id")
        results = payload.get("results", {})
        
        logger.info(f"Generating visualization for {file_id}")
        
        # Generate a visualization from the results
        visualization = self._generate_visualization(file_id, results)
        
        # Send the visualization back to the report actor
        sender_id = context.metadata.get("sender_id")
        if sender_id:
            await self.send(sender_id, MessageType.VISUALIZATION, {
                "file_id": file_id,
                "visualization": visualization
            }, context)
    
    def _generate_visualization(self, file_id: str, results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate a visualization from analysis results.
        
        Args:
            file_id: ID of the file to generate a visualization for
            results: Analysis results from tools
            
        Returns:
            A dictionary with the visualization data
        """
        # In a real implementation, this would generate actual visualizations
        # For this prototype, we'll just return a simple structure
        
        # Extract issue counts by tool
        issue_counts = {}
        for tool_id, result in results.items():
            tool_name = tool_id.split('-')[1] if '-' in tool_id else tool_id
            issue_counts[tool_name] = len(result.get("issues", []))
        
        return {
            "type": "bar_chart",
            "title": f"Issues by Tool - {file_id}",
            "data": issue_counts,
            "mime_type": "application/json"  # In a real implementation, this might be "image/png"
        }

# ====================================================================
# Demonstration
# ====================================================================

async def run_demo(project_path: str):
    """
    Run a demonstration of the CAW actor choreography system.
    
    Args:
        project_path: Path to the project to analyze
    """
    logger.info("Starting PAT CAW demonstration")
    
    # Create actors
    project_actor = ProjectActor("project", project_path)
    await project_actor.start()
    
    # Create tool actors
    tool_ruff = ToolActor("tool-ruff", "ruff")
    await tool_ruff.start()
    project_actor.register_actor("tool-ruff", tool_ruff)
    tool_ruff.register_actor("project", project_actor)
    
    tool_bandit = ToolActor("tool-bandit", "bandit")
    await tool_bandit.start()
    project_actor.register_actor("tool-bandit", tool_bandit)
    tool_bandit.register_actor("project", project_actor)
    
    tool_mypy = ToolActor("tool-mypy", "mypy")
    await tool_mypy.start()
    project_actor.register_actor("tool-mypy", tool_mypy)
    tool_mypy.register_actor("project", project_actor)
    
    # Create report actor
    report_actor = ReportActor("report")
    await report_actor.start()
    project_actor.register_actor("report", report_actor)
    report_actor.register_actor("project", project_actor)
    project_actor.report_actor = report_actor
    
    # Create visualization actor
    viz_actor = VisualizationActor("visualization")
    await viz_actor.start()
    report_actor.register_actor("visualization", viz_actor)
    viz_actor.register_actor("report", report_actor)
    report_actor.set_visualization_actor(viz_actor)
    
    # Start the analysis
    await project_actor.start_analysis()
    
    # Wait for some time to allow the analysis to complete
    await asyncio.sleep(5)
    
    # Stop all actors
    await project_actor.stop()
    await tool_ruff.stop()
    await tool_bandit.stop()
    await tool_mypy.stop()
    await report_actor.stop()
    await viz_actor.stop()
    
    logger.info("PAT CAW demonstration completed")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python pat_caw_actors_prototype.py <project_path>")
        sys.exit(1)
    
    project_path = sys.argv[1]
    asyncio.run(run_demo(project_path))
