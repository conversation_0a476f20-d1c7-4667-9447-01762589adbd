# PAT CAW Actors Choreography Prototype

This prototype demonstrates how the PAT (Project Analysis Tool) could be refactored to use the Contextual Adaptive Wave (CAW) paradigm with actor-based choreography.

## Overview

The prototype implements a simple version of the PAT tool using core CAW principles:

1. **Wave-Particle Duality**: Messages combine concrete payload (particle) with contextual information (wave)
2. **Contextual Propagation**: Context flows between actors, carrying metadata and adaptive parameters
3. **Adaptive Dimensionality**: Analysis configuration adapts based on project characteristics
4. **Choreographed Interactions**: Actors interact via choreography protocols rather than central orchestration

## Actor System

The prototype includes the following actors:

- **ProjectActor**: Coordinates the analysis of the entire project
- **FileActor**: Represents a file being analyzed, extracting metadata and content
- **ToolActor**: Runs analysis tools on files (e.g., ruff, bandit, mypy)
- **ReportActor**: Generates reports from analysis results
- **VisualizationActor**: Creates visualizations from analysis results

## Key CAW Concepts Demonstrated

### ContextWave

The `ContextWave` class demonstrates how context propagates through the system:

- **Contextual Adaptation**: Context adjusts as it flows through the system
- **Metadata Propagation**: Information about the project and files flows through the system
- **History Tracking**: Messages maintain a history of their path through the system
- **Parameter Adaptation**: Configuration parameters adapt based on file characteristics

### Message System

The message system shows the wave-particle duality:

- **Particle Aspect**: Concrete payloads with specific data
- **Wave Aspect**: Contextual information that propagates with messages
- **Type System**: Messages have specific types that define their purpose

## Running the Prototype

To run the prototype, use the following command:

```bash
python pat_caw_actors_prototype.py <project_path>
```

Where `<project_path>` is the path to the project you want to analyze.

## Architecture Highlights

### Adaptive Tool Selection

The system demonstrates adaptive tool selection based on file characteristics:

- Always includes ruff for linting
- Uses bandit for security analysis on larger files
- Uses mypy for type checking on files with type annotations

### Contextual Configuration

Tool configurations adapt based on file metadata:

- For complex files, stricter linting rules are applied
- For large files, timeouts are increased

### Parallel Analysis

Files are analyzed in parallel through independent actors:

1. Project actor discovers files and creates file actors
2. File actors extract metadata and send it to the project actor
3. Project actor selects appropriate tools for each file
4. Tool actors analyze files and send results back to the project actor
5. Report actor generates reports from the results
6. Visualization actor creates visualizations from the results

## Example Output

```
INFO:pat_caw:Starting PAT CAW demonstration
INFO:pat_caw:Actor project started
INFO:pat_caw:Actor tool-ruff started
INFO:pat_caw:Actor tool-bandit started
INFO:pat_caw:Actor tool-mypy started
INFO:pat_caw:Actor report started
INFO:pat_caw:Actor visualization started
INFO:pat_caw:Starting analysis of /path/to/project
INFO:pat_caw:Found 42 Python files
INFO:pat_caw:Actor file-src/main.py started
INFO:pat_caw:Initializing analysis for /path/to/project/src/main.py
INFO:pat_caw:Received metadata for /path/to/project/src/main.py
INFO:pat_caw:Tool ruff requested to analyze /path/to/project/src/main.py
INFO:pat_caw:Executing tool tool-ruff on /path/to/project/src/main.py
INFO:pat_caw:Tool ruff received content for /path/to/project/src/main.py
INFO:pat_caw:Received tool-ruff analysis result for file-src/main.py
INFO:pat_caw:All tools reported for file-src/main.py, generating report
INFO:pat_caw:Generating report for file-src/main.py
INFO:pat_caw:Generating visualization for file-src/main.py
INFO:pat_caw:Report for file-src/main.py: {"file_id": "file-src/main.py", "summary": {"tools_run": 1, "total_issues": 2, "severity": {"MEDIUM": 2}}, "issues": [{"tool": "ruff", "code": "E501", "message": "Line too long (120 > 100)", "location": {"row": 42, "column": 101}, "severity": "MEDIUM"}, {"tool": "ruff", "code": "F841", "message": "Unused variable 'result'", "location": {"row": 1, "column": 1}, "severity": "MEDIUM"}], "recommendations": ["Fix 1 style issues (code: E501)", "Address 1 potential bugs (code: F841)"]}
```

## Implementation Notes

This prototype is a simplified implementation focusing on demonstrating CAW principles. In a full implementation:

1. Real tool execution would replace the simulated tools
2. More sophisticated actor lifecycle management would be implemented
3. Error handling and recovery would be more robust
4. Visualizations would be actual graphical outputs rather than JSON structures
5. Context adaptation would be more sophisticated with machine learning elements

## Extending the Prototype

To extend this prototype:

1. Add more tool actors for additional analysis tools
2. Enhance context adaptation with more sophisticated rules
3. Add persistence for results and visualizations
4. Implement more detailed reporting and visualization
5. Add user interaction via a web or terminal UI
