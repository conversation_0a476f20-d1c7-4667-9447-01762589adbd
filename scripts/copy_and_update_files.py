#!/usr/bin/env python3
"""
Script to copy files from pat_project_analysis to vibe_check and update imports.
"""

import os
import re
import shutil
from pathlib import Path

# Define the source and destination packages
SOURCE_PACKAGE = "pat_project_analysis"
DEST_PACKAGE = "vibe_check"

# Define the root directory
ROOT_DIR = Path(__file__).parent.parent

# Define the source and destination directories
SOURCE_DIR = ROOT_DIR / SOURCE_PACKAGE
DEST_DIR = ROOT_DIR / DEST_PACKAGE

# Define the modules to copy
MODULES_TO_COPY = [
    "core",
    "ui",
    "tools",
]

# Define the import pattern to replace
IMPORT_PATTERN = re.compile(r"from\s+{0}(\..*?)\s+import".format(SOURCE_PACKAGE))
IMPORT_PATTERN_2 = re.compile(r"import\s+{0}(\..*?)(\s+as|\s*$)".format(SOURCE_PACKAGE))

def copy_file(source_file, dest_file):
    """Copy a file and update imports."""
    # Create the destination directory if it doesn't exist
    dest_file.parent.mkdir(parents=True, exist_ok=True)
    
    # Read the source file
    with open(source_file, "r") as f:
        content = f.read()
    
    # Update imports
    content = IMPORT_PATTERN.sub(r"from {0}\1 import".format(DEST_PACKAGE), content)
    content = IMPORT_PATTERN_2.sub(r"import {0}\1\2".format(DEST_PACKAGE), content)
    
    # Write the updated content to the destination file
    with open(dest_file, "w") as f:
        f.write(content)
    
    print(f"Copied and updated: {source_file} -> {dest_file}")

def copy_module(module_name):
    """Copy a module and all its files."""
    source_module = SOURCE_DIR / module_name
    dest_module = DEST_DIR / module_name
    
    # Create the destination directory if it doesn't exist
    dest_module.mkdir(parents=True, exist_ok=True)
    
    # Copy all Python files
    for source_file in source_module.glob("**/*.py"):
        # Skip __pycache__ directories
        if "__pycache__" in str(source_file):
            continue
        
        # Calculate the relative path from the source module
        rel_path = source_file.relative_to(source_module)
        
        # Calculate the destination file path
        dest_file = dest_module / rel_path
        
        # Copy and update the file
        copy_file(source_file, dest_file)

def main():
    """Main function."""
    # Create the destination directory if it doesn't exist
    DEST_DIR.mkdir(parents=True, exist_ok=True)
    
    # Copy each module
    for module_name in MODULES_TO_COPY:
        copy_module(module_name)
    
    print("Done!")

if __name__ == "__main__":
    main()
