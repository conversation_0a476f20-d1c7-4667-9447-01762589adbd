#!/usr/bin/env python3
"""
Build Script for Vibe Check
========================

This script builds the Vibe Check package for distribution.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_dirs():
    """Clean build directories."""
    dirs_to_clean = ["build", "dist", "*.egg-info"]
    for dir_pattern in dirs_to_clean:
        for path in Path(".").glob(dir_pattern):
            if path.is_dir():
                shutil.rmtree(path)
                print(f"Removed {path}")

def build_package():
    """Build the package."""
    print("Building package...")
    subprocess.run(["python", "-m", "build"], check=True)
    print("Package built successfully!")

def check_build():
    """Check the built package."""
    print("Checking built package...")
    subprocess.run(["twine", "check", "dist/*"], check=True)
    print("Package check passed!")

def main():
    """Main entry point."""
    # Clean build directories
    clean_build_dirs()
    
    # Build package
    build_package()
    
    # Check build
    check_build()
    
    print("\nBuild completed successfully!")
    print("To upload the package to PyPI, run:")
    print("  python -m twine upload dist/*")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
