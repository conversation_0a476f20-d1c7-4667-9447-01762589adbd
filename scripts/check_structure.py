#!/usr/bin/env python3
"""
Script to check the structure of the modularized pat_webui package.
"""
import os
import sys

print(f"Current directory: {os.getcwd()}")
print(f"Python executable: {sys.executable}")

# Check if the pat_webui directory exists
if os.path.isdir('pat_webui'):
    print("SUCCESS: pat_webui directory exists")
    
    # Check if the __init__.py file exists
    if os.path.isfile('pat_webui/__init__.py'):
        print("SUCCESS: pat_webui/__init__.py exists")
        
        # List the contents of the pat_webui directory
        print("\nContents of pat_webui directory:")
        for item in os.listdir('pat_webui'):
            print(f"- {item}")
            
        # Check if the components and pages directories exist
        if os.path.isdir('pat_webui/components'):
            print("\nSUCCESS: pat_webui/components directory exists")
            print("Contents of pat_webui/components directory:")
            for item in os.listdir('pat_webui/components'):
                print(f"- {item}")
        else:
            print("\nERROR: pat_webui/components directory does not exist")
            
        if os.path.isdir('pat_webui/pages'):
            print("\nSUCCESS: pat_webui/pages directory exists")
            print("Contents of pat_webui/pages directory:")
            for item in os.listdir('pat_webui/pages'):
                print(f"- {item}")
        else:
            print("\nERROR: pat_webui/pages directory does not exist")
    else:
        print("ERROR: pat_webui/__init__.py does not exist")
else:
    print("ERROR: pat_webui directory does not exist")
