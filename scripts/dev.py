#!/usr/bin/env python3
"""
Development helper script for Vibe Check.

This script provides common development tasks like running tests,
formatting code, and checking code quality.
"""

import argparse
import subprocess
import sys
from pathlib import Path


def run_command(cmd: list[str], description: str) -> bool:
    """Run a command and return True if successful."""
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False


def format_code() -> bool:
    """Format code using black."""
    return run_command(["black", "."], "Formatting code with black")


def lint_code() -> bool:
    """Lint code using ruff."""
    return run_command(["ruff", "check", "."], "Linting code with ruff")


def type_check() -> bool:
    """Type check code using mypy."""
    return run_command(["mypy", "."], "Type checking with mypy")


def run_tests() -> bool:
    """Run tests using pytest."""
    return run_command(["pytest"], "Running tests with pytest")


def run_tests_with_coverage() -> bool:
    """Run tests with coverage."""
    return run_command(
        ["pytest", "--cov=vibe_check", "--cov-report=term", "--cov-report=html"],
        "Running tests with coverage"
    )


def security_check() -> bool:
    """Run security checks using bandit."""
    return run_command(
        ["bandit", "-r", "vibe_check/", "-f", "json"],
        "Running security checks with bandit"
    )


def run_all_checks() -> bool:
    """Run all code quality checks."""
    checks = [
        ("Format", format_code),
        ("Lint", lint_code),
        ("Type Check", type_check),
        ("Security", security_check),
        ("Tests", run_tests),
    ]
    
    results = []
    for name, check_func in checks:
        print(f"\n{'='*50}")
        print(f"Running {name} Check")
        print('='*50)
        success = check_func()
        results.append((name, success))
        if not success:
            print(f"❌ {name} check failed!")
        else:
            print(f"✅ {name} check passed!")
    
    print(f"\n{'='*50}")
    print("Summary")
    print('='*50)
    
    all_passed = True
    for name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{name:12} {status}")
        if not success:
            all_passed = False
    
    return all_passed


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Vibe Check development helper")
    parser.add_argument(
        "command",
        choices=["format", "lint", "type-check", "test", "test-cov", "security", "all"],
        help="Command to run"
    )
    
    args = parser.parse_args()
    
    # Change to project root
    project_root = Path(__file__).parent.parent
    import os
    os.chdir(project_root)
    
    success = False
    if args.command == "format":
        success = format_code()
    elif args.command == "lint":
        success = lint_code()
    elif args.command == "type-check":
        success = type_check()
    elif args.command == "test":
        success = run_tests()
    elif args.command == "test-cov":
        success = run_tests_with_coverage()
    elif args.command == "security":
        success = security_check()
    elif args.command == "all":
        success = run_all_checks()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
