#!/usr/bin/env python3
"""
Test script for the vibe_check package.
"""

import sys
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_import():
    """Test importing the vibe_check package."""
    try:
        import vibe_check
        print(f"Successfully imported vibe_check package (version {vibe_check.__version__})")
        return True
    except ImportError as e:
        print(f"Error importing vibe_check package: {e}")
        return False

def test_analyze_project():
    """Test the analyze_project function."""
    try:
        from vibe_check import analyze_project
        print("Successfully imported analyze_project function")
        return True
    except ImportError as e:
        print(f"Error importing analyze_project function: {e}")
        return False

def test_core_modules():
    """Test importing core modules."""
    modules = [
        "vibe_check.core.models",
        "vibe_check.core.actor_system",
        "vibe_check.core.utils",
        "vibe_check.core.orchestrator",
    ]
    
    success = True
    for module in modules:
        try:
            __import__(module)
            print(f"Successfully imported {module}")
        except ImportError as e:
            print(f"Error importing {module}: {e}")
            success = False
    
    return success

def test_ui_modules():
    """Test importing UI modules."""
    modules = [
        "vibe_check.ui.cli",
        "vibe_check.ui.web",
        "vibe_check.ui.tui",
    ]
    
    success = True
    for module in modules:
        try:
            __import__(module)
            print(f"Successfully imported {module}")
        except ImportError as e:
            print(f"Error importing {module}: {e}")
            success = False
    
    return success

def test_tools_modules():
    """Test importing tools modules."""
    modules = [
        "vibe_check.tools.parsers",
        "vibe_check.tools.runners",
    ]
    
    success = True
    for module in modules:
        try:
            __import__(module)
            print(f"Successfully imported {module}")
        except ImportError as e:
            print(f"Error importing {module}: {e}")
            success = False
    
    return success

def test_plugins_modules():
    """Test importing plugins modules."""
    modules = [
        "vibe_check.plugins",
    ]
    
    success = True
    for module in modules:
        try:
            __import__(module)
            print(f"Successfully imported {module}")
        except ImportError as e:
            print(f"Error importing {module}: {e}")
            success = False
    
    return success

def main():
    """Main function."""
    print("Testing vibe_check package...")
    
    # Run tests
    tests = [
        test_import,
        test_analyze_project,
        test_core_modules,
        test_ui_modules,
        test_tools_modules,
        test_plugins_modules,
    ]
    
    success = True
    for test in tests:
        print(f"\nRunning {test.__name__}...")
        if not test():
            success = False
    
    # Print summary
    print("\nTest Summary:")
    if success:
        print("All tests passed!")
    else:
        print("Some tests failed. See above for details.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
