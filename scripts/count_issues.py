#!/usr/bin/env python3
"""
Count Issues Script

This script counts the number of Pyright and Ruff issues in the PAT analysis JSON file.

Usage:
    python count_issues.py <pat_analysis_json>
"""

import json
import os
import sys


def main():
    """Main function."""
    if len(sys.argv) < 2:
        print(f"Usage: {sys.argv[0]} <pat_analysis_json>")
        return 1
    
    analysis_json_path = sys.argv[1]
    if not os.path.exists(analysis_json_path):
        print(f"Error: File {analysis_json_path} does not exist")
        return 1
    
    # Load the analysis data
    try:
        with open(analysis_json_path, 'r') as f:
            analysis_data = json.load(f)
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        return 1
    
    # Count issues
    pyright_count = 0
    ruff_count = 0
    
    for file_key, file_data in analysis_data.get("files", {}).items():
        if not isinstance(file_data, dict):
            continue
        
        tool_results = file_data.get("tool_results", {})
        if not isinstance(tool_results, dict):
            continue
        
        # Count Pyright issues
        pyright_data = tool_results.get("pyright", {})
        if isinstance(pyright_data, dict):
            errors = pyright_data.get("errors", [])
            if isinstance(errors, list):
                pyright_count += len(errors)
        
        # Count Ruff issues
        ruff_data = tool_results.get("ruff", {})
        if isinstance(ruff_data, dict):
            errors = ruff_data.get("errors", [])
            if isinstance(errors, list):
                ruff_count += len(errors)
    
    print(f"Pyright issues: {pyright_count}")
    print(f"Ruff issues: {ruff_count}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
