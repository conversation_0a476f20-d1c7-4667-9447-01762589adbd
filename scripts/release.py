#!/usr/bin/env python3
"""
Release Script for Vibe Check
==========================

This script prepares a new release of the Vibe Check package.
It updates version numbers, creates a release commit, and tags the release.
"""

import os
import re
import sys
import subprocess
from pathlib import Path
from datetime import datetime

# Constants
VERSION_PATTERN = r'__version__\s*=\s*[\'"]([^\'"]*)[\'"]'
CHANGELOG_PATH = Path("CHANGELOG.md")
VIBE_CHECK_INIT = Path("vibe_check/__init__.py")
PAT_INIT = Path("pat_project_analysis/__init__.py")
SETUP_PY = Path("setup.py")


def get_current_version():
    """Get the current version from the package."""
    with open(VIBE_CHECK_INIT, "r") as f:
        content = f.read()
        match = re.search(VERSION_PATTERN, content)
        if match:
            return match.group(1)
    raise ValueError("Could not find version in vibe_check/__init__.py")


def update_version(new_version):
    """Update version numbers in all relevant files."""
    # Update vibe_check/__init__.py
    with open(VIBE_CHECK_INIT, "r") as f:
        content = f.read()
    content = re.sub(VERSION_PATTERN, f'__version__ = "{new_version}"', content)
    with open(VIBE_CHECK_INIT, "w") as f:
        f.write(content)
    
    # Update pat_project_analysis/__init__.py
    with open(PAT_INIT, "r") as f:
        content = f.read()
    content = re.sub(VERSION_PATTERN, f'__version__ = "{new_version}"', content)
    with open(PAT_INIT, "w") as f:
        f.write(content)
    
    # Update setup.py
    with open(SETUP_PY, "r") as f:
        content = f.read()
    content = re.sub(r'version="[^"]*"', f'version="{new_version}"', content)
    with open(SETUP_PY, "w") as f:
        f.write(content)
    
    print(f"Updated version to {new_version} in all files")


def update_changelog(new_version, changes):
    """Update the changelog with the new version and changes."""
    today = datetime.now().strftime("%Y-%m-%d")
    new_entry = f"## [{new_version}] - {today}\n\n{changes}\n\n"
    
    if CHANGELOG_PATH.exists():
        with open(CHANGELOG_PATH, "r") as f:
            content = f.read()
        
        # Find the position to insert the new entry
        if "# Changelog" in content:
            # Insert after the header
            content = content.replace("# Changelog\n", f"# Changelog\n\n{new_entry}")
        else:
            # Create a new changelog
            content = f"# Changelog\n\n{new_entry}{content}"
    else:
        # Create a new changelog
        content = f"# Changelog\n\n{new_entry}"
    
    with open(CHANGELOG_PATH, "w") as f:
        f.write(content)
    
    print(f"Updated CHANGELOG.md with version {new_version}")


def create_release_commit(version):
    """Create a release commit and tag."""
    # Add all files
    subprocess.run(["git", "add", "."], check=True)
    
    # Create commit
    subprocess.run(["git", "commit", "-m", f"Release version {version}"], check=True)
    
    # Create tag
    subprocess.run(["git", "tag", f"v{version}"], check=True)
    
    print(f"Created release commit and tag for version {version}")


def build_package():
    """Build the package."""
    subprocess.run(["python", "setup.py", "sdist", "bdist_wheel"], check=True)
    print("Built package")


def upload_package():
    """Upload the package to PyPI."""
    subprocess.run(["twine", "upload", "dist/*"], check=True)
    print("Uploaded package to PyPI")


def main():
    """Main entry point."""
    if len(sys.argv) < 2:
        print("Usage: python release.py <new_version> [--no-commit] [--no-build] [--no-upload]")
        return 1
    
    new_version = sys.argv[1]
    no_commit = "--no-commit" in sys.argv
    no_build = "--no-build" in sys.argv
    no_upload = "--no-upload" in sys.argv
    
    current_version = get_current_version()
    print(f"Current version: {current_version}")
    print(f"New version: {new_version}")
    
    # Get changes
    changes = input("Enter changes for this release (or press Enter to skip):\n")
    if not changes:
        changes = "* Bug fixes and improvements"
    
    # Update version numbers
    update_version(new_version)
    
    # Update changelog
    update_changelog(new_version, changes)
    
    # Create release commit and tag
    if not no_commit:
        create_release_commit(new_version)
    
    # Build package
    if not no_build:
        build_package()
    
    # Upload package
    if not no_upload:
        upload_package()
    
    print(f"Release {new_version} completed successfully!")
    return 0


if __name__ == "__main__":
    sys.exit(main())
