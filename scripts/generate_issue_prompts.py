#!/usr/bin/env python3
"""
Issue-Specific Prompt Generator for PAT

This script generates individual prompts for each issue identified in the PAT output.
Each prompt focuses on a single issue (rule violation, complexity, etc.) and provides
detailed information about the issue and how to fix it.

Usage:
    python generate_issue_prompts.py <pat_output_dir> [--output-dir DIR]

Arguments:
    pat_output_dir: Path to the PAT output directory
    --output-dir: Directory to save the issue-specific prompts (default: issue_prompts)
"""

import argparse
import json
import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Set, Tuple


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Generate issue-specific prompts from PAT output")
    parser.add_argument("pat_output_dir", help="Path to the PAT output directory")
    parser.add_argument("--output-dir", default="issue_prompts", help="Directory to save the issue-specific prompts")
    return parser.parse_args()

def load_pat_json(pat_output_dir: Path) -> Dict[str, Any]:
    """Load the PAT JSON output file."""
    # Find the JSON report file
    json_files = list(pat_output_dir.glob("PAT_*_analysis.json"))
    if not json_files:
        raise FileNotFoundError(f"No PAT JSON report found in {pat_output_dir}")
    
    print(f"Loading PAT JSON from {json_files[0]}")
    
    # Load the JSON file
    with open(json_files[0], 'r') as f:
        data = json.load(f)
    
    # Check if the data is in the expected format
    if isinstance(data, dict) and "files" in data and isinstance(data["files"], list):
        print(f"Found {len(data['files'])} files in standard format")
        return data
    
    # If not, try to parse it differently
    print(f"Warning: PAT JSON format is not as expected. Attempting to parse differently.")
    
    # Create a structured format
    structured_data = {"files": []}
    
    # If data is a list, assume it's a list of file metrics
    if isinstance(data, list):
        print(f"Data is a list with {len(data)} items")
        structured_data["files"] = data
    # If data is a dict without a 'files' key, assume it's a single file's metrics
    elif isinstance(data, dict):
        print(f"Data is a dict with keys: {list(data.keys())[:10]}...")
        
        # Check if it has file metrics keys
        if any(key in data for key in ["path", "complexity", "lines", "imports"]):
            structured_data["files"] = [data]
        # Check if it has a 'metrics' key
        elif "metrics" in data and isinstance(data["metrics"], dict):
            print(f"Found 'metrics' key with {len(data['metrics'])} items")
            for path, metrics in data["metrics"].items():
                if isinstance(metrics, dict):
                    metrics["path"] = path
                    structured_data["files"].append(metrics)
        # Check if it has a 'file_metrics' key
        elif "file_metrics" in data and isinstance(data["file_metrics"], list):
            print(f"Found 'file_metrics' key with {len(data['file_metrics'])} items")
            structured_data["files"] = data["file_metrics"]
        # Check if it has a 'files' key with a different structure
        elif "files" in data and not isinstance(data["files"], list):
            print(f"Found 'files' key with non-list type: {type(data['files'])}")
            if isinstance(data["files"], dict):
                for path, file_data in data["files"].items():
                    if isinstance(file_data, dict):
                        file_data["path"] = path
                        structured_data["files"].append(file_data)
        # Try to extract file metrics from the PAT format
        elif all(key in data for key in ["project_name", "timestamp", "files"]):
            print(f"Found PAT format with {len(data['files'])} files")
            for file_info in data["files"]:
                if isinstance(file_info, dict) and "path" in file_info:
                    # Extract metrics from the file info
                    file_metrics = {
                        "path": file_info["path"],
                        "complexity": file_info.get("complexity", 0),
                        "lines": file_info.get("lines", 0),
                        "imports": file_info.get("imports", []),
                        "classes": file_info.get("classes", []),
                        "functions": file_info.get("functions", []),
                        "tool_results": {}
                    }
                    
                    # Extract tool results if available
                    if "tool_results" in file_info and isinstance(file_info["tool_results"], dict):
                        file_metrics["tool_results"] = file_info["tool_results"]
                    
                    structured_data["files"].append(file_metrics)
        # Otherwise, try to extract file metrics from the dict
        else:
            for key, value in data.items():
                if isinstance(value, dict) and any(k in value for k in ["path", "complexity", "lines", "imports"]):
                    file_metrics = value.copy()
                    file_metrics["path"] = key
                    structured_data["files"].append(file_metrics)
    
    print(f"Extracted {len(structured_data['files'])} file metrics")
    return structured_data

def extract_issues(pat_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract individual issues from the PAT data."""
    issues = []
    
    # Process each file
    for file_metrics in pat_data.get("files", []):
        # Skip if not a dict
        if not isinstance(file_metrics, dict):
            continue
        
        # Get file path
        path = file_metrics.get("path", "")
        if not path:
            continue
        
        # Check for complexity issues
        complexity = file_metrics.get("complexity", 0)
        if isinstance(complexity, (int, float)) and complexity > 10:
            issues.append({
                "file": path,
                "type": "complexity",
                "severity": "high" if complexity > 20 else "medium",
                "details": {
                    "complexity": complexity,
                    "threshold": 10
                },
                "message": f"High cyclomatic complexity: {complexity} (threshold: 10)"
            })
        
        # Check for large file issues
        lines = file_metrics.get("lines", 0)
        if isinstance(lines, (int, float)) and lines > 600:
            issues.append({
                "file": path,
                "type": "large_file",
                "severity": "high" if lines > 1000 else "medium",
                "details": {
                    "lines": lines,
                    "threshold": 600
                },
                "message": f"Large file: {lines} lines (threshold: 600)"
            })
        
        # Check for many imports
        imports = file_metrics.get("imports", [])
        import_count = 0
        if isinstance(imports, list):
            import_count = len(imports)
        elif isinstance(imports, str):
            import_count = imports.count(",") + 1
        
        if import_count > 15:
            issues.append({
                "file": path,
                "type": "many_imports",
                "severity": "medium",
                "details": {
                    "import_count": import_count,
                    "threshold": 15,
                    "imports": imports if isinstance(imports, list) else []
                },
                "message": f"Many imports: {import_count} (threshold: 15)"
            })
        
        # Check for tool-specific issues
        tool_results = file_metrics.get("tool_results", {})
        if isinstance(tool_results, dict):
            # Check for security issues (bandit)
            if "bandit" in tool_results and isinstance(tool_results["bandit"], dict) and tool_results["bandit"].get("issues"):
                for issue in tool_results["bandit"].get("issues", []):
                    issues.append({
                        "file": path,
                        "type": "security",
                        "severity": "high",
                        "details": issue,
                        "message": f"Security issue: {issue.get('message', 'Unknown security issue')}"
                    })
            
            # Check for type errors (mypy)
            if "mypy" in tool_results and isinstance(tool_results["mypy"], dict) and tool_results["mypy"].get("errors"):
                for error in tool_results["mypy"].get("errors", []):
                    # Check for circular imports
                    if "circular" in str(error).lower():
                        issues.append({
                            "file": path,
                            "type": "circular_import",
                            "severity": "high",
                            "details": error,
                            "message": f"Circular import: {error}"
                        })
                    else:
                        issues.append({
                            "file": path,
                            "type": "type_error",
                            "severity": "medium",
                            "details": error,
                            "message": f"Type error: {error}"
                        })
            
            # Check for type errors (pyright)
            if "pyright" in tool_results and isinstance(tool_results["pyright"], dict) and tool_results["pyright"].get("errors"):
                for error in tool_results["pyright"].get("errors", []):
                    issues.append({
                        "file": path,
                        "type": "type_error",
                        "severity": "medium",
                        "details": error,
                        "message": f"Type error: {error}"
                    })
            
            # Check for lint issues (ruff)
            if "ruff" in tool_results and isinstance(tool_results["ruff"], dict) and tool_results["ruff"].get("errors"):
                for error in tool_results["ruff"].get("errors", []):
                    issues.append({
                        "file": path,
                        "type": "lint_issue",
                        "severity": "low",
                        "details": error,
                        "message": f"Lint issue: {error}"
                    })
            
            # Check for low test coverage
            if "coverage" in tool_results and isinstance(tool_results["coverage"], dict) and "coverage" in tool_results["coverage"]:
                coverage = tool_results["coverage"].get("coverage", 100)
                if isinstance(coverage, (int, float)) and coverage < 70:
                    issues.append({
                        "file": path,
                        "type": "low_coverage",
                        "severity": "medium",
                        "details": {
                            "coverage": coverage,
                            "threshold": 70
                        },
                        "message": f"Low test coverage: {coverage:.1f}% (threshold: 70%)"
                    })
    
    return issues

def generate_issue_prompt(issue: Dict[str, Any]) -> str:
    """Generate a prompt for a specific issue."""
    file_path = issue.get("file", "")
    issue_type = issue.get("type", "")
    severity = issue.get("severity", "")
    message = issue.get("message", "")
    details = issue.get("details", {})
    
    # Start with the issue title
    prompt = f"# {severity.upper()} {issue_type.replace('_', ' ').title()} Issue in {file_path}\n\n"
    
    # Add a purpose statement
    prompt += "## Purpose of this Prompt\n"
    prompt += f"This prompt addresses a specific {issue_type.replace('_', ' ')} issue in the file. "
    prompt += "Fixing this issue will improve code quality, maintainability, and reliability.\n\n"
    
    # Add issue details
    prompt += "## Issue Details\n"
    prompt += f"- **File**: {file_path}\n"
    prompt += f"- **Issue Type**: {issue_type.replace('_', ' ').title()}\n"
    prompt += f"- **Severity**: {severity.title()}\n"
    prompt += f"- **Message**: {message}\n\n"
    
    # Add specific details based on issue type
    if issue_type == "complexity":
        complexity = details.get("complexity", 0)
        threshold = details.get("threshold", 10)
        prompt += "## Complexity Analysis\n"
        prompt += f"The file has a cyclomatic complexity of {complexity}, which exceeds the threshold of {threshold}. "
        prompt += "High complexity makes code harder to understand, test, and maintain.\n\n"
        prompt += "### Impact of High Complexity\n"
        prompt += "- Increased risk of bugs and defects\n"
        prompt += "- Difficulty in understanding the code\n"
        prompt += "- Challenges in testing all code paths\n"
        prompt += "- Higher maintenance costs\n\n"
        prompt += "## Refactoring Recommendations\n"
        prompt += "1. **Extract Methods**: Break down complex methods into smaller, more focused functions\n"
        prompt += "2. **Simplify Conditionals**: Replace complex conditional expressions with clearer alternatives\n"
        prompt += "3. **Use Polymorphism**: Replace complex switch/if-else chains with polymorphic objects\n"
        prompt += "4. **Introduce Design Patterns**: Apply appropriate design patterns to simplify the code\n"
    
    elif issue_type == "large_file":
        lines = details.get("lines", 0)
        threshold = details.get("threshold", 600)
        prompt += "## File Size Analysis\n"
        prompt += f"The file has {lines} lines of code, which exceeds the threshold of {threshold}. "
        prompt += "Large files often violate the Single Responsibility Principle and are harder to understand and maintain.\n\n"
        prompt += "### Impact of Large Files\n"
        prompt += "- Difficulty in understanding the code's purpose\n"
        prompt += "- Increased likelihood of merge conflicts\n"
        prompt += "- Challenges in testing and maintaining the code\n"
        prompt += "- Potential for code duplication\n\n"
        prompt += "## Refactoring Recommendations\n"
        prompt += "1. **Split by Responsibility**: Divide the file into multiple files based on responsibilities\n"
        prompt += "2. **Extract Classes**: Identify cohesive groups of functions and extract them into separate classes\n"
        prompt += "3. **Apply SRP**: Ensure each file follows the Single Responsibility Principle\n"
        prompt += "4. **Create Packages**: Organize related files into packages or modules\n"
    
    elif issue_type == "many_imports":
        import_count = details.get("import_count", 0)
        threshold = details.get("threshold", 15)
        imports = details.get("imports", [])
        prompt += "## Import Analysis\n"
        prompt += f"The file has {import_count} imports, which exceeds the threshold of {threshold}. "
        prompt += "Too many imports often indicate that a file has too many responsibilities or dependencies.\n\n"
        if imports and len(imports) > 0:
            prompt += "### Current Imports\n"
            for imp in imports[:10]:  # Show only the first 10 imports
                prompt += f"- {imp}\n"
            if len(imports) > 10:
                prompt += f"- ... and {len(imports) - 10} more\n"
            prompt += "\n"
        prompt += "### Impact of Many Imports\n"
        prompt += "- Increased coupling between components\n"
        prompt += "- Difficulty in understanding dependencies\n"
        prompt += "- Potential for circular dependencies\n"
        prompt += "- Slower compilation and loading times\n\n"
        prompt += "## Refactoring Recommendations\n"
        prompt += "1. **Consolidate Imports**: Group related imports from the same module\n"
        prompt += "2. **Split File**: Divide the file into multiple files with fewer responsibilities\n"
        prompt += "3. **Use Dependency Injection**: Reduce direct dependencies through dependency injection\n"
        prompt += "4. **Create Facade**: Introduce a facade to hide complex dependencies\n"
    
    elif issue_type == "security":
        prompt += "## Security Issue Analysis\n"
        prompt += "Security issues can lead to vulnerabilities that may be exploited by attackers. "
        prompt += "Addressing these issues is critical for maintaining the security of the application.\n\n"
        prompt += "### Impact of Security Issues\n"
        prompt += "- Potential for data breaches\n"
        prompt += "- Unauthorized access to sensitive information\n"
        prompt += "- Compromise of system integrity\n"
        prompt += "- Legal and reputational consequences\n\n"
        prompt += "## Remediation Recommendations\n"
        prompt += "1. **Follow Security Best Practices**: Implement secure coding practices\n"
        prompt += "2. **Use Secure APIs**: Replace insecure functions with secure alternatives\n"
        prompt += "3. **Input Validation**: Validate and sanitize all inputs\n"
        prompt += "4. **Apply Principle of Least Privilege**: Limit access to only what is necessary\n"
    
    elif issue_type == "circular_import":
        prompt += "## Circular Import Analysis\n"
        prompt += "Circular imports occur when two or more modules import each other, directly or indirectly. "
        prompt += "This creates dependencies that can lead to import errors, initialization issues, and code that is difficult to maintain.\n\n"
        prompt += "### Impact of Circular Imports\n"
        prompt += "- Import errors and initialization issues\n"
        prompt += "- Tight coupling between modules\n"
        prompt += "- Difficulty in testing modules in isolation\n"
        prompt += "- Challenges in understanding the code structure\n\n"
        prompt += "## Refactoring Recommendations\n"
        prompt += "1. **Restructure Modules**: Reorganize the code to eliminate circular dependencies\n"
        prompt += "2. **Extract Common Code**: Move shared code to a separate module\n"
        prompt += "3. **Use Dependency Inversion**: Depend on abstractions rather than concrete implementations\n"
        prompt += "4. **Apply Lazy Imports**: Use imports inside functions to break circular dependencies\n"
    
    elif issue_type == "type_error":
        prompt += "## Type Error Analysis\n"
        prompt += "Type errors occur when the type of a value does not match the expected type. "
        prompt += "These errors can lead to runtime exceptions and unexpected behavior.\n\n"
        prompt += "### Impact of Type Errors\n"
        prompt += "- Runtime exceptions and crashes\n"
        prompt += "- Unexpected behavior and bugs\n"
        prompt += "- Difficulty in understanding the code's intent\n"
        prompt += "- Challenges in maintaining and refactoring the code\n\n"
        prompt += "## Remediation Recommendations\n"
        prompt += "1. **Add Type Annotations**: Provide explicit type hints for variables, parameters, and return values\n"
        prompt += "2. **Fix Type Mismatches**: Ensure that values match their expected types\n"
        prompt += "3. **Use Type Guards**: Add runtime checks to handle potential type mismatches\n"
        prompt += "4. **Leverage Type Inference**: Let the type checker infer types where possible\n"
    
    elif issue_type == "lint_issue":
        prompt += "## Lint Issue Analysis\n"
        prompt += "Lint issues are code style and quality issues that may not affect functionality but can impact readability and maintainability. "
        prompt += "Addressing these issues helps maintain a consistent code style and quality.\n\n"
        prompt += "### Impact of Lint Issues\n"
        prompt += "- Reduced code readability\n"
        prompt += "- Inconsistent code style\n"
        prompt += "- Potential for subtle bugs\n"
        prompt += "- Difficulty in maintaining the code\n\n"
        prompt += "## Remediation Recommendations\n"
        prompt += "1. **Follow Style Guidelines**: Adhere to the project's style guidelines\n"
        prompt += "2. **Use Automated Formatters**: Apply tools like Black or Prettier to format the code\n"
        prompt += "3. **Fix Specific Issues**: Address the specific lint issues identified\n"
        prompt += "4. **Add Linting to CI/CD**: Integrate linting into the continuous integration process\n"
    
    elif issue_type == "low_coverage":
        coverage = details.get("coverage", 0)
        threshold = details.get("threshold", 70)
        prompt += "## Test Coverage Analysis\n"
        prompt += f"The file has a test coverage of {coverage:.1f}%, which is below the threshold of {threshold}%. "
        prompt += "Low test coverage increases the risk of undetected bugs and regressions.\n\n"
        prompt += "### Impact of Low Test Coverage\n"
        prompt += "- Increased risk of undetected bugs\n"
        prompt += "- Difficulty in refactoring with confidence\n"
        prompt += "- Challenges in understanding the code's behavior\n"
        prompt += "- Higher maintenance costs\n\n"
        prompt += "## Remediation Recommendations\n"
        prompt += "1. **Write Unit Tests**: Add tests for untested code paths\n"
        prompt += "2. **Use Test-Driven Development**: Write tests before implementing new features\n"
        prompt += "3. **Add Integration Tests**: Test interactions between components\n"
        prompt += "4. **Set Coverage Targets**: Establish and enforce minimum coverage thresholds\n"
    
    # Add a conclusion
    prompt += "\n## Conclusion\n"
    prompt += f"Addressing this {issue_type.replace('_', ' ')} issue will improve the quality, maintainability, and reliability of the code. "
    prompt += "The recommendations provided should help guide the refactoring process.\n\n"
    prompt += "Please provide specific code examples and refactoring suggestions that align with the CAW principles.\n"
    
    return prompt

def generate_issue_prompts(pat_output_dir: Path, output_dir: Path):
    """Generate issue-specific prompts from PAT output."""
    # Load PAT JSON data
    pat_data = load_pat_json(pat_output_dir)
    
    # Extract issues
    issues = extract_issues(pat_data)
    
    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate prompts for each issue
    issue_prompts = []
    issue_counts: Dict[str, int] = {}
    
    for i, issue in enumerate(issues):
        # Get issue type and file
        issue_type = issue.get("type", "unknown")
        file_path = issue.get("file", "")
        severity = issue.get("severity", "medium")
        
        # Update issue count
        issue_counts[issue_type] = issue_counts.get(issue_type, 0) + 1
        
        # Generate prompt
        prompt = generate_issue_prompt(issue)
        
        # Save prompt to file
        file_name = f"{severity}_{issue_type}_{issue_counts[issue_type]:03d}_{Path(file_path).name}.md"
        prompt_path = output_dir / file_name
        with open(prompt_path, 'w') as f:
            f.write(prompt)
        
        issue_prompts.append((issue, prompt_path))
    
    # Generate summary report
    summary = "# Issue-Specific Prompts Summary\n\n"
    summary += f"Generated {len(issues)} issue-specific prompts from PAT output.\n\n"
    
    # Add issue type counts
    summary += "## Issue Types\n\n"
    summary += "| Issue Type | Count |\n"
    summary += "|------------|-------|\n"
    for issue_type, count in sorted(issue_counts.items(), key=lambda x: x[1], reverse=True):
        summary += f"| {issue_type.replace('_', ' ').title()} | {count} |\n"
    
    # Add severity counts
    severity_counts: Dict[str, int] = {}
    for issue in issues:
        severity = issue.get("severity", "medium")
        severity_counts[severity] = severity_counts.get(severity, 0) + 1
    
    summary += "\n## Severity Levels\n\n"
    summary += "| Severity | Count |\n"
    summary += "|----------|-------|\n"
    for severity in ["high", "medium", "low"]:
        count = severity_counts.get(severity, 0)
        summary += f"| {severity.title()} | {count} |\n"
    
    # Add file counts
    file_counts: Dict[str, int] = {}
    for issue in issues:
        file_path = issue.get("file", "")
        file_counts[file_path] = file_counts.get(file_path, 0) + 1
    
    summary += "\n## Top Files with Issues\n\n"
    summary += "| File | Issue Count |\n"
    summary += "|------|------------|\n"
    for file_path, count in sorted(file_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
        summary += f"| {file_path} | {count} |\n"
    
    # Add issue list
    summary += "\n## All Issues\n\n"
    summary += "| Severity | Issue Type | File | Prompt |\n"
    summary += "|----------|------------|------|--------|\n"
    for issue, prompt_path in issue_prompts:
        severity = issue.get("severity", "medium")
        issue_type = issue.get("type", "unknown")
        file_path = issue.get("file", "")
        summary += f"| {severity.title()} | {issue_type.replace('_', ' ').title()} | {file_path} | [{prompt_path.name}]({prompt_path.name}) |\n"
    
    # Save summary report
    with open(output_dir / "issue_prompts_summary.md", 'w') as f:
        f.write(summary)
    
    print(f"Generated {len(issues)} issue-specific prompts in {output_dir}")
    print(f"Summary report saved to {output_dir / 'issue_prompts_summary.md'}")

def main():
    """Main function."""
    args = parse_args()
    pat_output_dir = Path(args.pat_output_dir)
    output_dir = Path(args.output_dir)
    
    if not pat_output_dir.exists():
        print(f"Error: PAT output directory {pat_output_dir} does not exist")
        return 1
    
    try:
        generate_issue_prompts(pat_output_dir, output_dir)
        return 0
    except Exception as e:
        print(f"Error generating issue-specific prompts: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
