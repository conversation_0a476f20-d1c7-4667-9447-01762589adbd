#!/usr/bin/env python3
"""
Similarity Report Generator for PAT

This script analyzes the PAT output and generates a report that groups files,
functions, and classes by similarity to help identify redundancy in the codebase.

Usage:
    python generate_similarity_report.py <pat_output_dir> [--output-file FILENAME]

Arguments:
    pat_output_dir: Path to the PAT output directory
    --output-file: Name of the output file (default: similarity_report.md)
"""

import argparse
import difflib
import json
import math
import os
import re
import sys
import traceback
from collections import defaultdict
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Set, Tuple

try:
    # Try to import sklearn for more advanced similarity metrics
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("Warning: scikit-learn not available. Using simpler similarity metrics.")

@dataclass
class CodeEntity:
    """Class to represent a code entity (function, class, method)."""
    name: str
    file_path: str
    type: str  # 'function', 'class', 'method'
    content: str
    parent: str = None  # For methods, the parent class
    line_count: int = 0
    complexity: int = 0
    
    def __post_init__(self):
        # Calculate line count if not provided
        if self.line_count == 0:
            self.line_count = len(self.content.split('\n'))

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Generate similarity report from PAT output")
    parser.add_argument("pat_output_dir", help="Path to the PAT output directory")
    parser.add_argument("--output-file", default="similarity_report.md", help="Name of the output file")
    parser.add_argument("--min-similarity", type=float, default=0.7, help="Minimum similarity threshold (0.0-1.0)")
    parser.add_argument("--max-entities", type=int, default=1000, help="Maximum number of entities to analyze")
    return parser.parse_args()

def load_pat_json(pat_output_dir: Path) -> Dict[str, Any]:
    """Load the PAT JSON output file."""
    # Find the JSON report file
    json_files = list(pat_output_dir.glob("PAT_*_analysis.json"))
    if not json_files:
        raise FileNotFoundError(f"No PAT JSON report found in {pat_output_dir}")

    print(f"Loading PAT JSON from {json_files[0]}")

    # Load the JSON file
    with open(json_files[0], 'r') as f:
        data = json.load(f)

    return data

def extract_code_entities(pat_data: Dict[str, Any]) -> List[CodeEntity]:
    """Extract code entities (functions, classes, methods) from PAT data."""
    entities = []
    
    # Process files
    files = pat_data.get("files", {})
    if not isinstance(files, dict):
        print(f"Warning: 'files' is not a dictionary. Type: {type(files)}")
        if isinstance(files, list):
            # Convert list to dict
            files_dict = {}
            for file_data in files:
                if isinstance(file_data, dict) and "path" in file_data:
                    files_dict[file_data["path"]] = file_data
            files = files_dict
        else:
            return entities
    
    print(f"Processing {len(files)} files...")
    
    for file_path, file_data in files.items():
        if not isinstance(file_data, dict):
            continue
        
        # Extract file content if available
        file_content = ""
        if "content" in file_data and isinstance(file_data["content"], str):
            file_content = file_data["content"]
        elif "path" in file_data and isinstance(file_data["path"], str):
            # Try to read the file content
            try:
                with open(file_data["path"], 'r', encoding='utf-8', errors='ignore') as f:
                    file_content = f.read()
            except Exception as e:
                pass
        
        # Extract functions
        functions = file_data.get("functions", [])
        if isinstance(functions, list):
            for func_name in functions:
                # Try to extract function content from file content
                func_content = extract_function_content(file_content, func_name)
                complexity = get_entity_complexity(file_data, func_name)
                
                entities.append(CodeEntity(
                    name=func_name,
                    file_path=file_path,
                    type="function",
                    content=func_content,
                    complexity=complexity
                ))
        
        # Extract classes and methods
        classes = file_data.get("classes", [])
        if isinstance(classes, list):
            for class_name in classes:
                # Try to extract class content from file content
                class_content = extract_class_content(file_content, class_name)
                complexity = get_entity_complexity(file_data, class_name)
                
                entities.append(CodeEntity(
                    name=class_name,
                    file_path=file_path,
                    type="class",
                    content=class_content,
                    complexity=complexity
                ))
                
                # Extract methods for this class
                methods = extract_methods_from_class(file_content, class_name)
                for method_name, method_content in methods.items():
                    full_method_name = f"{class_name}.{method_name}"
                    complexity = get_entity_complexity(file_data, full_method_name)
                    
                    entities.append(CodeEntity(
                        name=method_name,
                        file_path=file_path,
                        type="method",
                        content=method_content,
                        parent=class_name,
                        complexity=complexity
                    ))
    
    print(f"Extracted {len(entities)} code entities")
    return entities

def get_entity_complexity(file_data: Dict[str, Any], entity_name: str) -> int:
    """Get the complexity of an entity from file data."""
    # Check if there's a complexity dict
    if "complexity_details" in file_data and isinstance(file_data["complexity_details"], dict):
        return file_data["complexity_details"].get(entity_name, 0)
    return 0

def extract_function_content(file_content: str, function_name: str) -> str:
    """Extract function content from file content."""
    if not file_content:
        return ""
    
    # Simple regex to find function definition
    pattern = rf"def\s+{re.escape(function_name)}\s*\("
    match = re.search(pattern, file_content)
    if not match:
        return ""
    
    # Find the function body
    start_pos = match.start()
    
    # Find the end of the function
    lines = file_content[start_pos:].split('\n')
    indent_level = None
    end_line = 0
    
    for i, line in enumerate(lines):
        if i == 0:
            # First line, determine the indentation level
            continue
        
        # Skip empty lines
        if not line.strip():
            end_line = i
            continue
        
        # Check indentation level
        current_indent = len(line) - len(line.lstrip())
        
        if indent_level is None:
            # First non-empty line after function definition
            indent_level = current_indent
            end_line = i
        elif current_indent <= 0 or (indent_level > 0 and current_indent < indent_level):
            # Found a line with less indentation, end of function
            break
        else:
            end_line = i
    
    # Extract the function content
    function_content = '\n'.join(lines[:end_line+1])
    return function_content

def extract_class_content(file_content: str, class_name: str) -> str:
    """Extract class content from file content."""
    if not file_content:
        return ""
    
    # Simple regex to find class definition
    pattern = rf"class\s+{re.escape(class_name)}\s*(\(|:)"
    match = re.search(pattern, file_content)
    if not match:
        return ""
    
    # Find the class body
    start_pos = match.start()
    
    # Find the end of the class
    lines = file_content[start_pos:].split('\n')
    indent_level = None
    end_line = 0
    
    for i, line in enumerate(lines):
        if i == 0:
            # First line, determine the indentation level
            continue
        
        # Skip empty lines
        if not line.strip():
            end_line = i
            continue
        
        # Check indentation level
        current_indent = len(line) - len(line.lstrip())
        
        if indent_level is None:
            # First non-empty line after class definition
            indent_level = current_indent
            end_line = i
        elif current_indent <= 0 or (indent_level > 0 and current_indent < indent_level):
            # Found a line with less indentation, end of class
            break
        else:
            end_line = i
    
    # Extract the class content
    class_content = '\n'.join(lines[:end_line+1])
    return class_content

def extract_methods_from_class(file_content: str, class_name: str) -> Dict[str, str]:
    """Extract methods from a class."""
    methods = {}
    
    if not file_content:
        return methods
    
    # Extract class content first
    class_content = extract_class_content(file_content, class_name)
    if not class_content:
        return methods
    
    # Find all method definitions in the class
    method_pattern = r"def\s+(\w+)\s*\("
    for match in re.finditer(method_pattern, class_content):
        method_name = match.group(1)
        method_content = extract_function_content(class_content, method_name)
        methods[method_name] = method_content
    
    return methods

def calculate_similarity_matrix(entities: List[CodeEntity], min_similarity: float) -> Dict[Tuple[int, int], float]:
    """Calculate similarity matrix between all entities."""
    similarity_matrix = {}
    
    if SKLEARN_AVAILABLE:
        # Use TF-IDF and cosine similarity for more accurate results
        print("Using scikit-learn for similarity calculation...")
        
        # Prepare documents
        documents = [entity.content for entity in entities]
        
        # Create TF-IDF vectorizer
        vectorizer = TfidfVectorizer(analyzer='word', ngram_range=(1, 3), min_df=0.0, stop_words='english')
        
        try:
            # Transform documents to TF-IDF features
            tfidf_matrix = vectorizer.fit_transform(documents)
            
            # Calculate cosine similarity
            cosine_sim = cosine_similarity(tfidf_matrix, tfidf_matrix)
            
            # Convert to dictionary
            for i in range(len(entities)):
                for j in range(i+1, len(entities)):
                    similarity = cosine_sim[i, j]
                    if similarity >= min_similarity:
                        similarity_matrix[(i, j)] = similarity
        except Exception as e:
            print(f"Error calculating TF-IDF similarity: {e}")
            print("Falling back to simpler similarity metric...")
            SKLEARN_AVAILABLE = False
    
    if not SKLEARN_AVAILABLE:
        # Use simpler similarity metric based on difflib
        print("Using difflib for similarity calculation...")
        
        for i in range(len(entities)):
            for j in range(i+1, len(entities)):
                # Skip if entities are too different in size
                size_ratio = min(len(entities[i].content), len(entities[j].content)) / max(len(entities[i].content), len(entities[j].content))
                if size_ratio < 0.5:
                    continue
                
                # Calculate similarity using difflib
                similarity = difflib.SequenceMatcher(None, entities[i].content, entities[j].content).ratio()
                
                if similarity >= min_similarity:
                    similarity_matrix[(i, j)] = similarity
    
    print(f"Found {len(similarity_matrix)} similar entity pairs")
    return similarity_matrix

def group_similar_entities(entities: List[CodeEntity], similarity_matrix: Dict[Tuple[int, int], float]) -> List[List[Tuple[CodeEntity, float]]]:
    """Group similar entities based on similarity matrix."""
    # Create a graph of similar entities
    graph = defaultdict(list)
    for (i, j), similarity in similarity_matrix.items():
        graph[i].append((j, similarity))
        graph[j].append((i, similarity))
    
    # Find connected components (groups of similar entities)
    visited = set()
    groups = []
    
    for i in range(len(entities)):
        if i in visited:
            continue
        
        # Start a new group
        group = []
        queue = [(i, 1.0)]  # (entity_index, similarity_to_seed)
        visited.add(i)
        
        while queue:
            node, sim = queue.pop(0)
            group.append((entities[node], sim))
            
            for neighbor, edge_sim in graph[node]:
                if neighbor not in visited:
                    visited.add(neighbor)
                    # Similarity to seed is the product of similarities along the path
                    queue.append((neighbor, sim * edge_sim))
        
        if len(group) > 1:
            # Sort group by similarity (descending)
            group.sort(key=lambda x: x[1], reverse=True)
            groups.append(group)
    
    # Sort groups by size (descending)
    groups.sort(key=len, reverse=True)
    
    return groups

def generate_similarity_report(entities: List[CodeEntity], similarity_groups: List[List[Tuple[CodeEntity, float]]], min_similarity: float) -> str:
    """Generate a markdown report of similar entities."""
    report = f"# Code Similarity Analysis Report\n\n"
    report += f"This report identifies potential code redundancy by grouping similar functions, classes, and methods.\n\n"
    report += f"**Similarity Threshold:** {min_similarity:.2f}\n"
    report += f"**Total Entities Analyzed:** {len(entities)}\n"
    report += f"**Similar Groups Found:** {len(similarity_groups)}\n\n"
    
    # Add summary of entity types
    entity_types = defaultdict(int)
    for entity in entities:
        entity_types[entity.type] += 1
    
    report += "## Entity Types\n\n"
    report += "| Type | Count |\n"
    report += "|------|-------|\n"
    for entity_type, count in entity_types.items():
        report += f"| {entity_type} | {count} |\n"
    report += "\n"
    
    # Add similarity groups
    report += "## Similarity Groups\n\n"
    report += "The following groups contain code entities that are similar to each other. "
    report += "Higher similarity scores indicate more similar code.\n\n"
    
    for i, group in enumerate(similarity_groups):
        report += f"### Group {i+1} ({len(group)} entities)\n\n"
        
        # Add table of entities in this group
        report += "| Entity | Type | File | Lines | Complexity | Similarity |\n"
        report += "|--------|------|------|-------|------------|------------|\n"
        
        for entity, similarity in group:
            entity_name = entity.name
            if entity.type == "method" and entity.parent:
                entity_name = f"{entity.parent}.{entity.name}"
            
            report += f"| {entity_name} | {entity.type} | {entity.file_path} | {entity.line_count} | {entity.complexity} | {similarity:.2f} |\n"
        
        report += "\n"
        
        # Add code snippets for the top 2 entities
        report += "#### Code Snippets\n\n"
        for entity, similarity in group[:min(2, len(group))]:
            entity_name = entity.name
            if entity.type == "method" and entity.parent:
                entity_name = f"{entity.parent}.{entity.name}"
            
            report += f"**{entity_name}** ({entity.file_path}):\n"
            report += "```python\n"
            # Limit to first 20 lines
            content_lines = entity.content.split('\n')
            if len(content_lines) > 20:
                report += '\n'.join(content_lines[:20]) + "\n...\n"
            else:
                report += entity.content + "\n"
            report += "```\n\n"
        
        # Add a note about potential refactoring
        report += "**Potential Refactoring:**\n"
        report += "- Consider extracting common functionality into a shared utility function/class\n"
        report += "- Review for potential design pattern application\n"
        report += "- Check if these implementations can be consolidated\n\n"
    
    # Add recommendations section
    report += "## Recommendations\n\n"
    report += "Based on the similarity analysis, consider the following refactoring strategies:\n\n"
    report += "1. **Extract Common Functionality**: Identify and extract shared code into reusable utilities\n"
    report += "2. **Apply Design Patterns**: Use appropriate design patterns to eliminate redundancy\n"
    report += "3. **Consolidate Similar Classes**: Merge similar classes using inheritance or composition\n"
    report += "4. **Create Shared Interfaces**: Define interfaces for similar behaviors\n"
    report += "5. **Implement Template Methods**: Use template method pattern for similar algorithms with slight variations\n\n"
    
    report += "## Next Steps\n\n"
    report += "1. Review each similarity group in detail\n"
    report += "2. Prioritize groups with highest similarity and complexity for refactoring\n"
    report += "3. Create unit tests before refactoring to ensure behavior is preserved\n"
    report += "4. Refactor incrementally, validating each change\n"
    
    return report

def main():
    """Main function."""
    args = parse_args()
    pat_output_dir = Path(args.pat_output_dir)
    output_file = Path(args.output_file)
    min_similarity = args.min_similarity
    max_entities = args.max_entities
    
    if not pat_output_dir.exists():
        print(f"Error: PAT output directory {pat_output_dir} does not exist")
        return 1
    
    try:
        # Load PAT data
        pat_data = load_pat_json(pat_output_dir)
        
        # Extract code entities
        entities = extract_code_entities(pat_data)
        
        # Limit the number of entities to analyze
        if len(entities) > max_entities:
            print(f"Warning: Limiting analysis to {max_entities} entities (out of {len(entities)})")
            entities = entities[:max_entities]
        
        # Calculate similarity matrix
        similarity_matrix = calculate_similarity_matrix(entities, min_similarity)
        
        # Group similar entities
        similarity_groups = group_similar_entities(entities, similarity_matrix)
        
        # Generate report
        report = generate_similarity_report(entities, similarity_groups, min_similarity)
        
        # Save report
        with open(output_file, 'w') as f:
            f.write(report)
        
        print(f"Similarity report saved to {output_file}")
        return 0
    except Exception as e:
        print(f"Error generating similarity report: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
