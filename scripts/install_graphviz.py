#!/usr/bin/env python3
"""
Graphviz Installation Helper

This script checks if Graphviz is installed and available for use with the PAT tool.
If not, it provides instructions for installing Graphviz on different platforms.
"""

import os
import platform
import subprocess
import sys
from pathlib import Path


def check_graphviz_installed():
    """Check if Graphviz is installed and available."""
    try:
        # Try to import graphviz Python package
        import graphviz
        print("✅ Graphviz Python package is installed.")
        
        # Check if the dot command is available
        try:
            result = subprocess.run(
                ["dot", "-V"], 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE, 
                text=True,
                check=False
            )
            if result.returncode == 0:
                version = result.stderr.strip() or result.stdout.strip()
                print(f"✅ Graphviz dot command is available: {version}")
                return True
            else:
                print("❌ Graphviz dot command is not available in PATH.")
                return False
        except FileNotFoundError:
            print("❌ Graphviz dot command is not available in PATH.")
            return False
    except ImportError:
        print("❌ Graphviz Python package is not installed.")
        return False

def install_graphviz_package():
    """Install the graphviz Python package."""
    print("\nInstalling graphviz Python package...")
    try:
        subprocess.run(
            [sys.executable, "-m", "pip", "install", "graphviz"],
            check=True
        )
        print("✅ Graphviz Python package installed successfully.")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install graphviz Python package: {e}")
        return False

def provide_installation_instructions():
    """Provide platform-specific instructions for installing Graphviz."""
    system = platform.system().lower()
    
    print("\n" + "=" * 80)
    print("GRAPHVIZ INSTALLATION INSTRUCTIONS")
    print("=" * 80)
    
    if system == "darwin":  # macOS
        print("""
To install Graphviz on macOS:

Using Homebrew:
    brew install graphviz

Using MacPorts:
    sudo port install graphviz
        """)
    elif system == "linux":
        print("""
To install Graphviz on Linux:

Ubuntu/Debian:
    sudo apt-get update
    sudo apt-get install graphviz

Fedora/RHEL/CentOS:
    sudo dnf install graphviz  # or use yum for older versions

Arch Linux:
    sudo pacman -S graphviz
        """)
    elif system == "windows":
        print("""
To install Graphviz on Windows:

1. Using Chocolatey:
    choco install graphviz

2. Using the official installer:
   a. Download from https://graphviz.org/download/
   b. Run the installer
   c. Add the Graphviz bin directory to your PATH environment variable
        """)
    else:
        print(f"Please visit https://graphviz.org/download/ for instructions on installing Graphviz on {platform.system()}.")
    
    print("\nAfter installing Graphviz, restart your terminal/command prompt and run this script again to verify the installation.")
    print("=" * 80)

def main():
    """Main function to check and install Graphviz."""
    print("Checking Graphviz installation...\n")
    
    graphviz_installed = check_graphviz_installed()
    
    if not graphviz_installed:
        # Try to install the Python package if not already installed
        try:
            import graphviz
        except ImportError:
            install_graphviz_package()
        
        # Provide instructions for installing the system package
        provide_installation_instructions()
        
        print("\nNote: The PAT tool will still work without Graphviz, but some visualizations")
        print("will use NetworkX instead, which may not be as visually appealing.")
        return False
    else:
        print("\n✅ Graphviz is properly installed and ready to use with the PAT tool.")
        return True

if __name__ == "__main__":
    sys.exit(0 if main() else 1)
