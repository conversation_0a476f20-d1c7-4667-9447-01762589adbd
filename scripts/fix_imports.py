#!/usr/bin/env python3
"""
Fix Imports Script

This script fixes all relative imports in the PAT_tool directory by replacing them with
absolute imports with fallback to local imports.

Usage:
    python fix_imports.py
"""

import os
import re
from pathlib import Path


def fix_imports_in_file(file_path):
    """Fix imports in a single file."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find all relative imports
    relative_import_pattern = r'from\s+\.\s*([a-zA-Z0-9_]+)\s+import\s+([^#\n]+)'
    matches = re.findall(relative_import_pattern, content)
    
    if not matches:
        return False  # No changes needed
    
    # Replace relative imports with try-except block
    for module, imports in matches:
        old_import = f"from .{module} import {imports}"
        new_import = f"""try:
    # When running as a package
    from PAT_tool.{module} import {imports}
except ImportError:
    # When running as a script
    from {module} import {imports}"""
        content = content.replace(old_import, new_import)
    
    # Write the modified content back to the file
    with open(file_path, 'w') as f:
        f.write(content)
    
    return True  # Changes made

def main():
    """Main function to fix imports in all Python files."""
    # Get the absolute path to the PAT_tool directory
    pat_tool_dir = Path(__file__).parent / "PAT_tool"
    
    # Find all Python files in the PAT_tool directory
    python_files = list(pat_tool_dir.glob("**/*.py"))
    
    # Fix imports in each file
    fixed_files = []
    for file_path in python_files:
        if fix_imports_in_file(file_path):
            fixed_files.append(file_path)
    
    # Print summary
    print(f"Fixed imports in {len(fixed_files)} files:")
    for file_path in fixed_files:
        print(f"  - {file_path.relative_to(Path(__file__).parent)}")
    
    # Create __init__.py file in PAT_tool directory if it doesn't exist
    init_path = pat_tool_dir / "__init__.py"
    if not init_path.exists():
        with open(init_path, "w") as f:
            f.write("# Auto-generated by fix_imports.py\n")
        print(f"Created {init_path.relative_to(Path(__file__).parent)}")

if __name__ == "__main__":
    main()
