"""
This is a test file to see if the prompt generation works.
"""

class ComplexClass:
    """A complex class with high complexity."""
    
    def __init__(self, value):
        self.value = value
        self.items = []
        
    def complex_method(self, x, y, z):
        """A complex method with high cyclomatic complexity."""
        result = 0
        if x > 10:
            if y > 20:
                if z > 30:
                    result = x * y * z
                else:
                    result = x * y
            else:
                if z > 30:
                    result = x * z
                else:
                    result = x
        else:
            if y > 20:
                if z > 30:
                    result = y * z
                else:
                    result = y
            else:
                if z > 30:
                    result = z
                else:
                    result = 1
        
        for i in range(100):
            if i % 2 == 0:
                result += i
            else:
                result -= i
                
        return result
    
    def another_complex_method(self, a, b, c):
        """Another complex method."""
        result = 0
        for i in range(a):
            for j in range(b):
                for k in range(c):
                    result += i * j * k
        return result

# Create a large number of functions to increase file size
def function1(): return 1
def function2(): return 2
def function3(): return 3
def function4(): return 4
def function5(): return 5
def function6(): return 6
def function7(): return 7
def function8(): return 8
def function9(): return 9
def function10(): return 10
def function11(): return 11
def function12(): return 12
def function13(): return 13
def function14(): return 14
def function15(): return 15
def function16(): return 16
def function17(): return 17
def function18(): return 18
def function19(): return 19
def function20(): return 20
