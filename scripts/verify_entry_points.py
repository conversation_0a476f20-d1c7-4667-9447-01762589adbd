#!/usr/bin/env python3
"""
Entry Points Verification Script
===============================

This script verifies that all Vibe Check entry points work correctly
and that no legacy structures remain.
"""

import subprocess
import sys
from pathlib import Path
from typing import List, Tuple


def run_command(cmd: List[str], description: str) -> Tuple[bool, str]:
    """Run a command and return success status and output."""
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=30,
            cwd=Path(__file__).parent.parent
        )
        return result.returncode == 0, result.stdout + result.stderr
    except subprocess.TimeoutExpired:
        return False, "Command timed out"
    except Exception as e:
        return False, str(e)


def test_python_api() -> bool:
    """Test Python API import."""
    print("🔍 Testing Python API...")
    
    # Test basic import
    success, output = run_command([
        sys.executable, "-c", 
        "import vibe_check; print(f'✅ API Version: {vibe_check.__version__}')"
    ], "Python API import")
    
    if success:
        print("  ✅ Python API import works")
        print(f"  📦 {output.strip()}")
        return True
    else:
        print("  ❌ Python API import failed")
        print(f"  📝 {output}")
        return False


def test_module_entry_point() -> bool:
    """Test python -m vibe_check entry point."""
    print("🔍 Testing Module Entry Point...")
    
    success, output = run_command([
        sys.executable, "-m", "vibe_check", "--help"
    ], "Module entry point")
    
    if success and "Vibe Check - A project analysis tool" in output:
        print("  ✅ Module entry point works")
        return True
    else:
        print("  ❌ Module entry point failed")
        print(f"  📝 {output}")
        return False


def test_cli_commands() -> bool:
    """Test CLI commands."""
    print("🔍 Testing CLI Commands...")
    
    commands_to_test = [
        (["python", "-m", "vibe_check", "--version"], "Version command"),
        (["python", "-m", "vibe_check", "--help"], "Help command"),
        (["python", "-m", "vibe_check", "analyze", "--help"], "Analyze help"),
        (["python", "-m", "vibe_check", "tui", "--help"], "TUI help"),
        (["python", "-m", "vibe_check", "web", "--help"], "Web help"),
        (["python", "-m", "vibe_check", "plugin", "--help"], "Plugin help"),
        (["python", "-m", "vibe_check", "debug", "--help"], "Debug help"),
    ]
    
    all_passed = True
    for cmd, description in commands_to_test:
        success, output = run_command(cmd, description)
        if success:
            print(f"  ✅ {description}")
        else:
            print(f"  ❌ {description} failed")
            print(f"  📝 {output}")
            all_passed = False
    
    return all_passed


def check_legacy_removal() -> bool:
    """Check that legacy structures have been removed."""
    print("🔍 Checking Legacy Structure Removal...")
    
    # Check that PAT command doesn't exist in pyproject.toml
    pyproject_path = Path(__file__).parent.parent / "pyproject.toml"
    if pyproject_path.exists():
        content = pyproject_path.read_text()
        if 'pat = "vibe_check.cli:main"' in content:
            print("  ❌ Legacy PAT command still exists in pyproject.toml")
            return False
        else:
            print("  ✅ Legacy PAT command removed from pyproject.toml")
    
    # Check that deprecated initialization package is gone
    deprecated_init = Path(__file__).parent.parent / "vibe_check" / "core" / "actor_system" / "initialization"
    if deprecated_init.exists():
        print("  ❌ Deprecated initialization package still exists")
        return False
    else:
        print("  ✅ Deprecated initialization package removed")
    
    # Check that legacy scripts are moved
    legacy_script = Path(__file__).parent / "copy_plugins.py"
    if legacy_script.exists():
        print("  ❌ Legacy script copy_plugins.py still in scripts/")
        return False
    else:
        print("  ✅ Legacy scripts moved to legacy/")
    
    return True


def check_package_structure() -> bool:
    """Check that package structure is clean."""
    print("🔍 Checking Package Structure...")
    
    required_files = [
        "vibe_check/__init__.py",
        "vibe_check/__main__.py", 
        "vibe_check/cli/main.py",
        "vibe_check/core/__init__.py",
        "pyproject.toml",
        "README.md",
        "ENTRY_POINTS_GUIDE.md"
    ]
    
    project_root = Path(__file__).parent.parent
    all_exist = True
    
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ Missing: {file_path}")
            all_exist = False
    
    return all_exist


def main():
    """Main verification function."""
    print("🎯 Vibe Check Entry Points Verification")
    print("=" * 50)
    
    tests = [
        ("Python API", test_python_api),
        ("Module Entry Point", test_module_entry_point),
        ("CLI Commands", test_cli_commands),
        ("Legacy Removal", check_legacy_removal),
        ("Package Structure", check_package_structure),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
    
    print("\n" + "=" * 50)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if not success:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Vibe Check has clean, singular entry points")
        print("✅ No legacy structures remain")
        print("✅ Ready for production use")
    else:
        print("❌ SOME TESTS FAILED!")
        print("🔧 Please fix the issues above")
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    sys.exit(main())
