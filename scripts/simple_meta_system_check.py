#!/usr/bin/env python3
"""
Simple Meta-System Separation Checker

A standalone script to check for meta-system separation violations in the person_suit project.
This script doesn't rely on the PAT tool infrastructure and has minimal dependencies.
"""

import argparse
import json
import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple

# Try importing visualization libraries
try:
    import matplotlib.pyplot as plt
    import networkx as nx
    HAS_VISUALIZATION = True
except ImportError:
    HAS_VISUALIZATION = False

# Define meta-systems and their module prefixes
META_SYSTEMS = {
    "PC": ["persona_core"],
    "AN": ["analyst"],
    "PR": ["prediction", "predictor"],
    "SIO": ["shared/io", "io_layer", "sio"],
    "UI": ["ui", "user_interface"],
    "CORE": ["core"]
}

def map_to_meta_system(file_path: str) -> str:
    """Map a file path to its meta-system.

    Args:
        file_path: Path of the file
    Returns:
        Meta-system label
    """
    path = file_path.lower()

    # Check if module is in a meta-system
    for meta, prefixes in META_SYSTEMS.items():
        for prefix in prefixes:
            if f"meta_systems/{prefix}" in path or f"meta_systems\\{prefix}" in path:
                return meta

    # If not in a meta-system, check if it's in core
    if "/core/" in path or "\\core\\" in path:
        return "CORE"

    # If not in a meta-system or core, mark as OTHER
    return "OTHER"

def extract_imports(file_path: str) -> List[str]:
    """Extract imports from a Python file.

    Args:
        file_path: Path to the Python file
    Returns:
        List of imported modules
    """
    imports = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Extract imports using regex
        import_pattern = r'^(?:from|import)\s+([\w.]+)(?:\s+import|\s*$)'
        for line in content.split('\n'):
            line = line.strip()
            match = re.match(import_pattern, line)
            if match:
                module = match.group(1)
                imports.append(module)
    except Exception as e:
        print(f"Error extracting imports from {file_path}: {e}")

    return imports

def analyze_meta_system_separation(project_path: str) -> Tuple[List[Dict], Dict[str, Dict[str, int]]]:
    """Analyze meta-system separation in the project.

    Args:
        project_path: Path to the project root
    Returns:
        Tuple of (violations list, meta-system connections)
    """
    project_path = Path(project_path)
    violations = []

    # Map modules to meta-systems
    module_to_meta = {}

    # Track connections between meta-systems
    meta_connections = {}
    for meta in META_SYSTEMS.keys():
        meta_connections[meta] = {}
        for other_meta in META_SYSTEMS.keys():
            meta_connections[meta][other_meta] = 0

    # Find all Python files
    python_files = list(project_path.glob('**/*.py'))
    print(f"Found {len(python_files)} Python files")

    # First pass: map modules to meta-systems
    for file_path in python_files:
        rel_path = file_path.relative_to(project_path)
        module_name = str(rel_path).replace('/', '.').replace('\\', '.').replace('.py', '')
        meta_system = map_to_meta_system(str(rel_path))
        module_to_meta[module_name] = meta_system

    # Second pass: analyze imports for violations and connections
    for file_path in python_files:
        rel_path = file_path.relative_to(project_path)
        module_name = str(rel_path).replace('/', '.').replace('\\', '.').replace('.py', '')
        source_meta = module_to_meta.get(module_name, "OTHER")

        # Skip if module is not in a meta-system
        if source_meta == "OTHER":
            continue

        # Extract imports
        imports = extract_imports(file_path)

        # Check each import
        for imp in imports:
            # Skip if import is not in the project
            if imp not in module_to_meta:
                continue

            target_meta = module_to_meta[imp]

            # Skip if import is not in a meta-system
            if target_meta == "OTHER":
                continue

            # Track connection (even within same meta-system)
            if source_meta in meta_connections and target_meta in meta_connections[source_meta]:
                meta_connections[source_meta][target_meta] += 1

            # Skip if import is in the same meta-system
            if source_meta == target_meta:
                continue

            # Skip if import is from CORE (allowed)
            if target_meta == "CORE":
                continue

            # If we get here, we have a violation
            violation = {
                "source_module": module_name,
                "source_meta": source_meta,
                "target_module": imp,
                "target_meta": target_meta,
                "file_path": str(rel_path)
            }

            violations.append(violation)

    return violations, meta_connections

def generate_visualization(meta_connections: Dict[str, Dict[str, int]], output_dir: str, project_name: str) -> str:
    """Generate a visualization of meta-system connections.

    Args:
        meta_connections: Dictionary of connections between meta-systems
        output_dir: Directory to save the visualization
        project_name: Name of the project
    Returns:
        Path to the generated visualization file
    """
    if not HAS_VISUALIZATION:
        print("Warning: Visualization libraries (matplotlib, networkx) not available. Skipping visualization.")
        return ""

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Create a directed graph
    G = nx.DiGraph()

    # Add nodes for each meta-system
    for meta in META_SYSTEMS.keys():
        G.add_node(meta)

    # Add edges for connections
    for source, targets in meta_connections.items():
        for target, count in targets.items():
            if count > 0:
                G.add_edge(source, target, weight=count)

    # Set up the plot
    plt.figure(figsize=(12, 10))

    # Define node colors
    node_colors = {
        "PC": "blue",
        "AN": "green",
        "PR": "red",
        "SIO": "purple",
        "UI": "orange",
        "CORE": "gray"
    }

    # Get node colors
    colors = [node_colors.get(node, "lightgray") for node in G.nodes()]

    # Draw the graph
    pos = nx.spring_layout(G, seed=42)
    nx.draw_networkx_nodes(G, pos, node_color=colors, node_size=1000, alpha=0.8)
    nx.draw_networkx_labels(G, pos, font_size=12, font_weight="bold")

    # Draw edges with width proportional to weight
    edges = G.edges()
    weights = [G[u][v]['weight'] for u, v in edges]
    max_weight = max(weights) if weights else 1
    normalized_weights = [w / max_weight * 5 for w in weights]

    nx.draw_networkx_edges(G, pos, edgelist=edges, width=normalized_weights,
                          arrowsize=20, connectionstyle="arc3,rad=0.1")

    # Add edge labels (connection counts)
    edge_labels = {(u, v): G[u][v]['weight'] for u, v in G.edges()}
    nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=10)

    # Add a title
    plt.title(f"{project_name} Meta-System Connections", fontsize=16)

    # Add a legend
    legend_elements = []
    import matplotlib.patches as mpatches
    for meta, color in node_colors.items():
        if meta in G.nodes():
            legend_elements.append(mpatches.Patch(color=color, label=meta))
    plt.legend(handles=legend_elements, loc="upper right")

    # Save the figure
    output_path = os.path.join(output_dir, f"{project_name}_meta_system_graph.png")
    plt.savefig(output_path, bbox_inches="tight")
    plt.close()

    print(f"Meta-system graph saved to {output_path}")

    # Generate HTML report
    html_path = os.path.join(output_dir, f"{project_name}_meta_system_report.html")

    # Convert the graph data to JSON for the HTML report
    nodes = []
    for meta in META_SYSTEMS.keys():
        nodes.append({
            "id": meta,
            "label": meta,
            "color": node_colors.get(meta, "lightgray")
        })

    links = []
    for source, targets in meta_connections.items():
        for target, count in targets.items():
            if count > 0:
                links.append({
                    "source": source,
                    "target": target,
                    "value": count
                })

    # Create HTML content
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Meta-System Connections - {project_name}</title>
        <script src="https://d3js.org/d3.v7.min.js"></script>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
            }}
            .container {{
                display: flex;
                height: 100vh;
            }}
            .graph {{
                flex: 2;
                border-right: 1px solid #ccc;
            }}
            .details {{
                flex: 1;
                padding: 20px;
                overflow-y: auto;
            }}
            .node {{
                cursor: pointer;
            }}
            .link {{
                stroke-width: 2px;
            }}
            .meta-system {{
                padding: 5px 10px;
                border-radius: 3px;
                color: white;
                display: inline-block;
                margin-right: 5px;
            }}
            .PC {{ background-color: #1f77b4; }}
            .AN {{ background-color: #2ca02c; }}
            .PR {{ background-color: #d62728; }}
            .SIO {{ background-color: #9467bd; }}
            .UI {{ background-color: #ff7f0e; }}
            .CORE {{ background-color: #7f7f7f; }}
            .OTHER {{ background-color: #c7c7c7; }}
            .summary {{
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
            }}
            .legend {{
                display: flex;
                flex-wrap: wrap;
                margin-bottom: 15px;
            }}
            .legend-item {{
                display: flex;
                align-items: center;
                margin-right: 15px;
                margin-bottom: 5px;
            }}
            .legend-color {{
                width: 15px;
                height: 15px;
                margin-right: 5px;
                border-radius: 3px;
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                margin-top: 20px;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }}
            th {{
                background-color: #f2f2f2;
            }}
            tr:nth-child(even) {{
                background-color: #f9f9f9;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="graph" id="graph"></div>
            <div class="details">
                <h2>Meta-System Connections Analysis</h2>
                <div class="summary">
                    <h3>Summary</h3>
                    <p>This visualization shows the connections between meta-systems in the {project_name} project.</p>
                    <p>Meta-systems should not import from each other directly, but can use common underlying components from the core.</p>
                </div>

                <div class="legend">
                    <h3>Meta-Systems</h3>
                    <div style="display: flex; flex-wrap: wrap;">
                        <div class="legend-item">
                            <div class="legend-color PC"></div>
                            <div>PC (Persona Core)</div>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color AN"></div>
                            <div>AN (Analyst)</div>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color PR"></div>
                            <div>PR (Predictor)</div>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color SIO"></div>
                            <div>SIO (Shared I/O)</div>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color UI"></div>
                            <div>UI (User Interface)</div>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color CORE"></div>
                            <div>CORE</div>
                        </div>
                    </div>
                </div>

                <h3>Connection Matrix</h3>
                <table>
                    <tr>
                        <th>Source / Target</th>
                        {' '.join([f'<th>{meta}</th>' for meta in META_SYSTEMS.keys()])}
                    </tr>
                    {' '.join([f'<tr><th>{source}</th>' +
                              ' '.join([f'<td>{targets.get(target, 0)}</td>' for target in META_SYSTEMS.keys()]) +
                              '</tr>' for source, targets in meta_connections.items()])}
                </table>
            </div>
        </div>

        <script>
        // Graph data
        const nodes = {json.dumps(nodes)};
        const links = {json.dumps(links)};

        // Set up the SVG
        const width = document.getElementById('graph').clientWidth;
        const height = document.getElementById('graph').clientHeight;

        const svg = d3.select('#graph')
            .append('svg')
            .attr('width', width)
            .attr('height', height);

        // Define arrow markers
        svg.append('defs').append('marker')
            .attr('id', 'arrowhead')
            .attr('viewBox', '-0 -5 10 10')
            .attr('refX', 20)
            .attr('refY', 0)
            .attr('orient', 'auto')
            .attr('markerWidth', 6)
            .attr('markerHeight', 6)
            .attr('xoverflow', 'visible')
            .append('svg:path')
            .attr('d', 'M 0,-5 L 10 ,0 L 0,5')
            .attr('fill', '#999')
            .style('stroke', 'none');

        // Create a force simulation
        const simulation = d3.forceSimulation(nodes)
            .force('link', d3.forceLink(links).id(d => d.id).distance(150))
            .force('charge', d3.forceManyBody().strength(-500))
            .force('center', d3.forceCenter(width / 2, height / 2))
            .force('collision', d3.forceCollide().radius(60));

        // Create the links
        const link = svg.append('g')
            .selectAll('line')
            .data(links)
            .enter().append('path')
            .attr('class', 'link')
            .attr('stroke', '#999')
            .attr('stroke-width', d => Math.sqrt(d.value))
            .attr('marker-end', 'url(#arrowhead)');

        // Create the nodes
        const node = svg.append('g')
            .selectAll('g')
            .data(nodes)
            .enter().append('g')
            .attr('class', 'node')
            .call(d3.drag()
                .on('start', dragstarted)
                .on('drag', dragged)
                .on('end', dragended));

        // Add circles to the nodes
        node.append('circle')
            .attr('r', 30)
            .attr('fill', d => d.color)
            .attr('stroke', '#fff')
            .attr('stroke-width', 2);

        // Add labels to the nodes
        node.append('text')
            .attr('text-anchor', 'middle')
            .attr('dy', '.35em')
            .attr('fill', 'white')
            .attr('font-weight', 'bold')
            .text(d => d.id);

        // Add title for tooltip
        node.append('title')
            .text(d => d.id);

        // Add labels to the links
        const linkText = svg.append('g')
            .selectAll('text')
            .data(links)
            .enter().append('text')
            .attr('font-size', 10)
            .attr('text-anchor', 'middle')
            .text(d => d.value);

        // Update positions on each tick
        simulation.on('tick', function() {
            link.attr('d', function(d) {
                const dx = d.target.x - d.source.x;
                const dy = d.target.y - d.source.y;
                const dr = Math.sqrt(dx * dx + dy * dy) * 1.5; // Curve factor
                return 'M' + d.source.x + ',' + d.source.y + 'A' + dr + ',' + dr + ' 0 0,1 ' + d.target.x + ',' + d.target.y;
            });

            node.attr('transform', function(d) {
                // Keep nodes within bounds
                d.x = Math.max(50, Math.min(width - 50, d.x));
                d.y = Math.max(50, Math.min(height - 50, d.y));
                return 'translate(' + d.x + ',' + d.y + ')';
            });

            // Position link labels
            linkText.attr('x', function(d) { return (d.source.x + d.target.x) / 2; })
                .attr('y', function(d) { return (d.source.y + d.target.y) / 2; });
        });

        // Drag functions
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }
        </script>
    </body>
    </html>
    """

    with open(html_path, "w") as f:
        f.write(html_content)

    print(f"Interactive meta-system report saved to {html_path}")

    return output_path

def main():
    """Run the meta-system separation analysis."""
    parser = argparse.ArgumentParser(description="Check for meta-system separation violations in the person_suit project.")
    parser.add_argument("project_path", help="Path to the project root")
    parser.add_argument("--output-dir", "-o", default="analysis", help="Directory to save output files (default: analysis)")
    parser.add_argument("--no-viz", action="store_true", help="Skip visualization generation")
    parser.add_argument("--open-browser", action="store_true", help="Open the HTML report in a browser")
    args = parser.parse_args()

    # Set up project path
    project_path = Path(args.project_path).resolve()
    if not project_path.is_dir():
        print(f"Error: {project_path} is not a valid directory.")
        sys.exit(1)

    # Set up output directory
    output_dir = Path(args.output_dir).resolve()
    output_dir.mkdir(parents=True, exist_ok=True)

    print(f"Analyzing meta-system separation in {project_path}...")
    violations, meta_connections = analyze_meta_system_separation(project_path)

    # Print results
    if violations:
        print(f"Found {len(violations)} meta-system separation violations:")
        for i, violation in enumerate(violations[:10], 1):
            print(f"{i}. {violation['source_meta']} imports from {violation['target_meta']} in {violation['file_path']}")
        if len(violations) > 10:
            print(f"...and {len(violations) - 10} more violations.")
    else:
        print("No meta-system separation violations found. Great job!")

    # Generate visualization
    if not args.no_viz:
        project_name = project_path.name
        try:
            html_path = generate_visualization(meta_connections, str(output_dir), project_name)

            # Open the HTML report in a browser if requested
            if args.open_browser and html_path:
                import webbrowser
                html_path = os.path.join(output_dir, f"{project_name}_meta_system_report.html")
                if os.path.exists(html_path):
                    print(f"Opening {html_path} in browser...")
                    webbrowser.open(f"file://{os.path.abspath(html_path)}")
        except Exception as e:
            print(f"Error generating visualization: {e}")

    # Save violations to JSON
    violations_path = os.path.join(output_dir, "meta_system_violations.json")
    try:
        with open(violations_path, "w") as f:
            json.dump({
                "violations": violations,
                "meta_connections": meta_connections
            }, f, indent=2)
        print(f"Violations saved to {violations_path}")
    except Exception as e:
        print(f"Error saving violations: {e}")

    # Return exit code based on violations
    return 1 if violations else 0

if __name__ == "__main__":
    sys.exit(main())
