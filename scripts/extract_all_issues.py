#!/usr/bin/env python3
"""
Extract All Issues Script

This script extracts all Pyright and Ruff issues from the PAT analysis JSON file
and generates a detailed log without truncation.

Usage:
    python extract_all_issues.py <pat_analysis_json> <output_log>
"""

import json
import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional


def extract_pyright_issues(analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract all Pyright issues from the analysis data."""
    pyright_issues = []

    for file_key, file_data in analysis_data.get("files", {}).items():
        if not isinstance(file_data, dict):
            continue

        # The actual file path is stored in the 'path' field
        file_path = file_data.get("path", file_key)

        tool_results = file_data.get("tool_results", {})
        if not isinstance(tool_results, dict):
            continue

        pyright_data = tool_results.get("pyright", {})
        if not isinstance(pyright_data, dict):
            continue

        errors = pyright_data.get("errors", [])
        if not isinstance(errors, list):
            continue

        for error in errors:
            if isinstance(error, dict):
                error["file_path"] = file_path
                pyright_issues.append(error)

    return pyright_issues

def extract_ruff_issues(analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract all Ruff issues from the analysis data."""
    ruff_issues = []

    for file_key, file_data in analysis_data.get("files", {}).items():
        if not isinstance(file_data, dict):
            continue

        # The actual file path is stored in the 'path' field
        file_path = file_data.get("path", file_key)

        tool_results = file_data.get("tool_results", {})
        if not isinstance(tool_results, dict):
            continue

        ruff_data = tool_results.get("ruff", {})
        if not isinstance(ruff_data, dict):
            continue

        errors = ruff_data.get("errors", [])
        if not isinstance(errors, list):
            continue

        for error in errors:
            if isinstance(error, dict):
                error["file_path"] = file_path
                ruff_issues.append(error)

    return ruff_issues

def format_pyright_issues(issues: List[Dict[str, Any]]) -> str:
    """Format Pyright issues as a readable string."""
    if not issues:
        return "No Pyright issues found."

    # Group issues by severity
    issues_by_severity = {}
    for issue in issues:
        severity = issue.get("severity", "unknown")
        if severity not in issues_by_severity:
            issues_by_severity[severity] = []
        issues_by_severity[severity].append(issue)

    # Format the issues
    result = []
    result.append(f"# Pyright Issues: {len(issues)} total\n")

    for severity, severity_issues in sorted(issues_by_severity.items()):
        result.append(f"## {severity.upper()} Issues: {len(severity_issues)}\n")

        # Group by rule
        issues_by_rule = {}
        for issue in severity_issues:
            rule = issue.get("rule", "unknown")
            if rule not in issues_by_rule:
                issues_by_rule[rule] = []
            issues_by_rule[rule].append(issue)

        for rule, rule_issues in sorted(issues_by_rule.items()):
            result.append(f"### {rule}: {len(rule_issues)}\n")

            for i, issue in enumerate(rule_issues, 1):
                file_path = issue.get("file_path", "unknown")
                message = issue.get("message", "No message")
                line_start = issue.get("range", {}).get("start", {}).get("line", "?")
                line_end = issue.get("range", {}).get("end", {}).get("line", "?")

                result.append(f"{i}. **{file_path}** (Line {line_start}-{line_end}): {message}\n")

            result.append("\n")

    return "\n".join(result)

def format_ruff_issues(issues: List[Dict[str, Any]]) -> str:
    """Format Ruff issues as a readable string."""
    if not issues:
        return "No Ruff issues found."

    # Group issues by code
    issues_by_code = {}
    for issue in issues:
        code = issue.get("code", "unknown")
        if code not in issues_by_code:
            issues_by_code[code] = []
        issues_by_code[code].append(issue)

    # Format the issues
    result = []
    result.append(f"# Ruff Issues: {len(issues)} total\n")

    for code, code_issues in sorted(issues_by_code.items()):
        result.append(f"## {code}: {len(code_issues)}\n")

        for i, issue in enumerate(code_issues, 1):
            file_path = issue.get("file_path", "unknown")
            message = issue.get("message", "No message")
            line = issue.get("line", "?")
            column = issue.get("column", "?")

            result.append(f"{i}. **{file_path}** (Line {line}, Col {column}): {message}\n")

        result.append("\n")

    return "\n".join(result)

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print(f"Usage: {sys.argv[0]} <pat_analysis_json> [<output_log>]")
        return 1

    analysis_json_path = sys.argv[1]
    if not os.path.exists(analysis_json_path):
        print(f"Error: File {analysis_json_path} does not exist")
        return 1

    output_log_path = sys.argv[2] if len(sys.argv) > 2 else None

    # Load the analysis data
    try:
        with open(analysis_json_path, 'r') as f:
            analysis_data = json.load(f)
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        return 1

    # Count issues first to check if we need to limit output
    pyright_count = 0
    ruff_count = 0

    for file_key, file_data in analysis_data.get("files", {}).items():
        if not isinstance(file_data, dict):
            continue

        tool_results = file_data.get("tool_results", {})
        if not isinstance(tool_results, dict):
            continue

        # Count Pyright issues
        pyright_data = tool_results.get("pyright", {})
        if isinstance(pyright_data, dict):
            errors = pyright_data.get("errors", [])
            if isinstance(errors, list):
                pyright_count += len(errors)

        # Count Ruff issues
        ruff_data = tool_results.get("ruff", {})
        if isinstance(ruff_data, dict):
            errors = ruff_data.get("errors", [])
            if isinstance(errors, list):
                ruff_count += len(errors)

    print(f"Found {pyright_count} Pyright issues and {ruff_count} Ruff issues")

    # Extract issues
    pyright_issues = extract_pyright_issues(analysis_data)
    ruff_issues = extract_ruff_issues(analysis_data)

    # Format issues
    pyright_log = format_pyright_issues(pyright_issues)
    ruff_log = format_ruff_issues(ruff_issues)

    # Combine logs
    combined_log = f"""# PAT Analysis Issues Log
Generated from {analysis_json_path}

{pyright_log}

{ruff_log}
"""

    # Output the log
    if output_log_path:
        try:
            with open(output_log_path, 'w') as f:
                f.write(combined_log)
            print(f"Issues log written to {output_log_path}")
        except Exception as e:
            print(f"Error writing log file: {e}")
            return 1
    else:
        print(combined_log)

    return 0

if __name__ == "__main__":
    sys.exit(main())
