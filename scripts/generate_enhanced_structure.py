#!/usr/bin/env python3
"""
Enhanced Directory Structure Generator

This script generates an enhanced directory structure document that includes
each file with its complexity score, with the highest scores bolded for emphasis.

Usage:
    python generate_enhanced_structure.py <pat_analysis_json> <output_md>
"""

import json
import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple


def extract_file_scores(analysis_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
    """Extract file scores from the analysis data."""
    file_scores = {}

    for file_key, file_data in analysis_data.get("files", {}).items():
        if not isinstance(file_data, dict):
            continue

        file_path = file_data.get("path", file_key)
        complexity = file_data.get("complexity", 0)
        lines = file_data.get("lines", 0)
        description = file_data.get("description", "No description available")

        # Calculate a score based on complexity and lines
        # Higher score means higher priority for attention
        score = complexity * 0.7 + (lines / 100) * 0.3

        file_scores[file_path] = {
            "complexity": complexity,
            "lines": lines,
            "description": description,
            "score": score
        }

    return file_scores

def build_directory_tree(file_scores: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    """Build a directory tree from the file scores."""
    tree = {}

    for file_path, file_data in file_scores.items():
        parts = file_path.split('/')
        current = tree

        # Build the directory structure
        for i, part in enumerate(parts):
            if i == len(parts) - 1:
                # This is a file
                current[part] = file_data
            else:
                # This is a directory
                if part not in current:
                    current[part] = {}
                current = current[part]

    return tree

def format_directory_tree(tree: Dict[str, Any], prefix: str = "", is_last: bool = True,
                         path: str = "", max_scores: Dict[str, float] = None) -> List[str]:
    """Format the directory tree as a list of strings."""
    if max_scores is None:
        # Find the maximum scores for complexity and lines
        max_scores = {
            "complexity": 0,
            "lines": 0,
            "score": 0
        }

        def find_max_scores(node, current_path=""):
            for key, value in node.items():
                if isinstance(value, dict) and "complexity" in value:
                    # This is a file
                    max_scores["complexity"] = max(max_scores["complexity"], value["complexity"])
                    max_scores["lines"] = max(max_scores["lines"], value["lines"])
                    max_scores["score"] = max(max_scores["score"], value["score"])
                elif isinstance(value, dict):
                    # This is a directory
                    find_max_scores(value, current_path + "/" + key if current_path else key)

        find_max_scores(tree)

    result = []

    # Sort the items: directories first, then files
    items = sorted(tree.items(), key=lambda x: (not isinstance(x[1], dict) or "complexity" in x[1], x[0]))

    for i, (name, node) in enumerate(items):
        is_last_item = i == len(items) - 1

        # Determine the current path
        current_path = path + "/" + name if path else name

        if isinstance(node, dict) and "complexity" in node:
            # This is a file
            complexity = node["complexity"]
            lines = node["lines"]
            score = node["score"]
            description = node["description"]

            # Determine if this file has a high score (top 10%)
            is_high_score = score > max_scores["score"] * 0.9
            is_high_complexity = complexity > max_scores["complexity"] * 0.9
            is_high_lines = lines > max_scores["lines"] * 0.9

            # Format the file line
            score_str = f"Score: {score:.2f}"
            complexity_str = f"Complexity: {complexity}"
            lines_str = f"Lines: {lines}"

            # Use emoji indicators for high values
            indicators = ""
            if is_high_score:
                indicators += "⚠️ "
                score_str = f"[{score_str}]"
            if is_high_complexity:
                indicators += "🔄 "
                complexity_str = f"[{complexity_str}]"
            if is_high_lines:
                indicators += "📏 "
                lines_str = f"[{lines_str}]"

            # Add file name prefix based on priority
            if is_high_score or is_high_complexity or is_high_lines:
                file_prefix = "❗️"
            else:
                file_prefix = "📄"

            file_line = f"{prefix}{'└─' if is_last else '├─'} {file_prefix} {indicators}{name} # {score_str}, {complexity_str}, {lines_str} - {description}"
            result.append(file_line)
        else:
            # This is a directory
            dir_line = f"{prefix}{'└─' if is_last else '├─'} 📁 {name}/"

            # Add description if available
            if isinstance(node, dict) and "description" in node:
                dir_line += f" # {node['description']}"

            result.append(dir_line)

            # Add the children
            new_prefix = prefix + ("    " if is_last else "│   ")
            child_results = format_directory_tree(
                node, new_prefix, is_last_item, current_path, max_scores
            )
            result.extend(child_results)

    return result

def main():
    """Main function."""
    if len(sys.argv) < 3:
        print(f"Usage: {sys.argv[0]} <pat_analysis_json> <output_md>")
        return 1

    analysis_json_path = sys.argv[1]
    output_md_path = sys.argv[2]

    if not os.path.exists(analysis_json_path):
        print(f"Error: File {analysis_json_path} does not exist")
        return 1

    # Load the analysis data
    try:
        with open(analysis_json_path, 'r') as f:
            analysis_data = json.load(f)
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        return 1

    # Extract file scores
    file_scores = extract_file_scores(analysis_data)

    # Build the directory tree
    tree = build_directory_tree(file_scores)

    # Format the directory tree
    tree_lines = format_directory_tree(tree)

    # Generate the markdown content
    markdown_content = f"""# Enhanced Directory Structure with Scores

This document shows the directory structure of the project with complexity scores, line counts, and overall scores for each file.
Files with high values are highlighted with special indicators:

- ❗️ - High priority file (has at least one high value)
- ⚠️ - High score (top 10%)
- 🔄 - High complexity (top 10%)
- 📏 - High line count (top 10%)

## Directory Structure

```
{''.join(line + '\n' for line in tree_lines)}```

## Score Explanation

The score is calculated as:
- 70% based on cyclomatic complexity
- 30% based on lines of code (normalized by dividing by 100)

Higher scores indicate files that need more attention for refactoring.

## Legend

- 📁 - Directory
- 📄 - Normal file
- ❗️ - High priority file
- ⚠️ - High score
- 🔄 - High complexity
- 📏 - High line count
- [Value] - Value exceeds threshold (top 10%)
"""

    # Write the markdown content to the output file
    try:
        with open(output_md_path, 'w') as f:
            f.write(markdown_content)
        print(f"Enhanced directory structure written to {output_md_path}")
    except Exception as e:
        print(f"Error writing output file: {e}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
