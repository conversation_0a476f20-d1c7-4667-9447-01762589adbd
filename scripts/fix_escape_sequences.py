#!/usr/bin/env python3
"""
Escape Sequence Fixer Script

This script specifically fixes the unterminated string literals in the codebase
caused by incorrect escape sequences in logger names and other strings.

Usage:
    python fix_escape_sequences.py <project_path>
"""

import os
import re
import sys
from pathlib import Path
from typing import List, <PERSON><PERSON>


def find_python_files(directory: Path) -> List[Path]:
    """Find all Python files in the given directory."""
    return list(directory.glob("**/*.py"))

def fix_escape_sequences(file_path: Path) -> Tuple[bool, str]:
    """Fix escape sequences in the file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix common patterns of incorrect escape sequences
        
        # Pattern 1: Escaped quotes in logger names
        # Example: logging.getLogger(\"person_suit.folded_mind...")
        pattern1 = r'getLogger\(\\\"([^\"]+)\"'
        replacement1 = r'getLogger("\1"'
        content = re.sub(pattern1, replacement1, content)
        
        # Pattern 2: Escaped quotes in other strings
        # Example: \"some string\"
        pattern2 = r'\\\"([^\"]+)\"'
        replacement2 = r'"\1"'
        content = re.sub(pattern2, replacement2, content)
        
        # Pattern 3: Escaped quotes at the end of strings
        # Example: "some string\"
        pattern3 = r'\"([^\"]+)\\\"'
        replacement3 = r'"\1"'
        content = re.sub(pattern3, replacement3, content)
        
        # Check if content was modified
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True, "Fixed escape sequences"
        
        return False, "No changes needed"
    
    except Exception as e:
        return False, f"Error: {str(e)}"

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python fix_escape_sequences.py <project_path>")
        return 1
    
    project_path = Path(sys.argv[1])
    if not project_path.exists():
        print(f"Error: Path {project_path} does not exist")
        return 1
    
    # Get list of files with syntax errors from the previous run
    files_with_errors = [
        "person_suit/meta_systems/persona_core/memory/constructive_monitoring.py",
        "person_suit/meta_systems/persona_core/memory/integration.py",
        "person_suit/meta_systems/persona_core/interfaces/memory_features.py",
        "person_suit/meta_systems/persona_core/folded_mind/error/examples.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/neurochemical/persistence.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/neurochemical/config.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/neurochemical/optimization.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/memory/storage.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/memory/indexing.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/dream_integration.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/core.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/retrieval/nl_query_interface.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/wave_particle/telemetry.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/wave_particle/memory_orchestration_integration.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/wave_particle/neurochemical_integration_optimized.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/logging/run_examples.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/atomization/benchmark/optimization_tools.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/atomization/validation/psychological_mapping.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/atomization/integration/control/audit_trail.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/emotional_metaphor/backups/therapeutic_application_original.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/simulation.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/temporal.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/persistence.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/perspective.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/boundaries.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/coherence.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/prompting.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/creative_invention_experience.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/counterfactual.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/module_template.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/sensory.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/concept_blending.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/transitions.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/resources.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/imagination/modules/creative_invention.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/neurochemical/visualization/dashboard.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/neurochemical/visualization/base.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/neurochemical/components/emotional_effects.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/neurochemical/components/mood_effects.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/neurochemical/components/social_effects.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/neurochemical/components/cognitive_effects.py",
        "person_suit/meta_systems/persona_core/folded_mind/SEM/neurochemical/components/memory_effects.py"
    ]
    
    # Convert to Path objects
    files_to_fix = [Path(file_path) for file_path in files_with_errors]
    
    # Try to fix escape sequences
    fixed_files = []
    failed_files = []
    
    for file_path in files_to_fix:
        if not file_path.exists():
            failed_files.append((file_path, "File not found"))
            continue
        
        print(f"Attempting to fix {file_path}...")
        success, message = fix_escape_sequences(file_path)
        if success:
            fixed_files.append(file_path)
            print(f"  Success: {message}")
        else:
            failed_files.append((file_path, message))
            print(f"  Failed: {message}")
    
    # Print summary
    print("\nSummary:")
    print(f"  Total files with syntax errors: {len(files_to_fix)}")
    print(f"  Successfully fixed: {len(fixed_files)}")
    print(f"  Failed to fix: {len(failed_files)}")
    
    if failed_files:
        print("\nFailed files:")
        for file_path, message in failed_files:
            print(f"  {file_path}: {message}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
