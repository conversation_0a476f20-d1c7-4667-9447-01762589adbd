#!/usr/bin/env python3
"""
Test Installation Script for Vibe Check
==================================

This script tests the installation of the Vibe Check package.
It creates a virtual environment, installs the package, and runs a simple test.
"""

import os
import sys
import shutil
import subprocess
import tempfile
from pathlib import Path

def create_venv(venv_dir):
    """Create a virtual environment."""
    print(f"Creating virtual environment in {venv_dir}...")
    subprocess.run([sys.executable, "-m", "venv", venv_dir], check=True)
    print("Virtual environment created successfully!")

def install_package(venv_dir):
    """Install the package in the virtual environment."""
    print("Installing package...")
    
    # Determine the pip executable
    if os.name == "nt":  # Windows
        pip_exe = os.path.join(venv_dir, "Scripts", "pip")
    else:  # Unix/Linux/Mac
        pip_exe = os.path.join(venv_dir, "bin", "pip")
    
    # Install the package
    subprocess.run([pip_exe, "install", "-e", "."], check=True)
    print("Package installed successfully!")

def run_test(venv_dir):
    """Run a simple test to verify the installation."""
    print("Running test...")
    
    # Determine the python executable
    if os.name == "nt":  # Windows
        python_exe = os.path.join(venv_dir, "Scripts", "python")
    else:  # Unix/Linux/Mac
        python_exe = os.path.join(venv_dir, "bin", "python")
    
    # Create a test script
    test_script = """
import sys
try:
    import vibe_check
    print(f"Successfully imported vibe_check (version {vibe_check.__version__})")
    sys.exit(0)
except ImportError as e:
    print(f"Error importing vibe_check: {e}")
    sys.exit(1)
"""
    
    # Write the test script to a temporary file
    with tempfile.NamedTemporaryFile(suffix=".py", delete=False) as f:
        f.write(test_script.encode())
        test_script_path = f.name
    
    # Run the test script
    try:
        subprocess.run([python_exe, test_script_path], check=True)
        print("Test passed!")
        return True
    except subprocess.CalledProcessError:
        print("Test failed!")
        return False
    finally:
        # Clean up the temporary file
        os.unlink(test_script_path)

def test_backward_compatibility(venv_dir):
    """Test backward compatibility with the old package name."""
    print("Testing backward compatibility...")
    
    # Determine the python executable
    if os.name == "nt":  # Windows
        python_exe = os.path.join(venv_dir, "Scripts", "python")
    else:  # Unix/Linux/Mac
        python_exe = os.path.join(venv_dir, "bin", "python")
    
    # Create a test script
    test_script = """
import sys
try:
    import pat_project_analysis
    print(f"Successfully imported pat_project_analysis (version {pat_project_analysis.__version__})")
    sys.exit(0)
except ImportError as e:
    print(f"Error importing pat_project_analysis: {e}")
    sys.exit(1)
"""
    
    # Write the test script to a temporary file
    with tempfile.NamedTemporaryFile(suffix=".py", delete=False) as f:
        f.write(test_script.encode())
        test_script_path = f.name
    
    # Run the test script
    try:
        subprocess.run([python_exe, test_script_path], check=True)
        print("Backward compatibility test passed!")
        return True
    except subprocess.CalledProcessError:
        print("Backward compatibility test failed!")
        return False
    finally:
        # Clean up the temporary file
        os.unlink(test_script_path)

def main():
    """Main entry point."""
    # Create a temporary directory for the virtual environment
    with tempfile.TemporaryDirectory() as temp_dir:
        venv_dir = os.path.join(temp_dir, "venv")
        
        # Create virtual environment
        create_venv(venv_dir)
        
        # Install package
        install_package(venv_dir)
        
        # Run test
        test_success = run_test(venv_dir)
        
        # Test backward compatibility
        compat_success = test_backward_compatibility(venv_dir)
        
        # Print summary
        print("\nTest Summary:")
        print(f"- Package installation: {'Success' if test_success else 'Failure'}")
        print(f"- Backward compatibility: {'Success' if compat_success else 'Failure'}")
        
        if test_success and compat_success:
            print("\nAll tests passed!")
            return 0
        else:
            print("\nSome tests failed!")
            return 1

if __name__ == "__main__":
    sys.exit(main())
